2025-07-31 20:27:04:728 ==>> MES查站成功:
查站序号:P5100010053132B4验证通过
2025-07-31 20:27:04:732 ==>> 扫码结果:P5100010053132B4
2025-07-31 20:27:04:734 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:27:04:736 ==>> 测试参数版本:2024.10.11
2025-07-31 20:27:04:737 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:27:04:739 ==>> 检测【打开透传】
2025-07-31 20:27:04:741 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:27:04:855 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:27:05:078 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:27:05:169 ==>> 检测【检测接地电压】
2025-07-31 20:27:05:172 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:27:05:250 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:27:05:449 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:27:05:451 ==>> 检测【打开小电池】
2025-07-31 20:27:05:453 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:27:05:552 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:27:05:721 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:27:05:723 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:27:05:725 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:27:05:853 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:27:05:992 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:27:05:996 ==>> 检测【等待设备启动】
2025-07-31 20:27:05:999 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:27:06:342 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:27:06:539 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:27:07:031 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:27:07:196 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:27:07:592 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:27:08:064 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:27:08:067 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:27:08:386 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:27:08:390 ==>> 检测【产品通信】
2025-07-31 20:27:08:394 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:27:08:537 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:27:08:704 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:27:08:706 ==>> 检测【初始化完成检测】
2025-07-31 20:27:08:709 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:27:08:747 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:27:08:852 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:

2025-07-31 20:27:08:882 ==>> 51][COMM]SE50 init success!


2025-07-31 20:27:08:998 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:27:09:001 ==>> 检测【关闭大灯控制1】
2025-07-31 20:27:09:004 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:27:09:152 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:09:257 ==>>                                                                                        event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 20:27:09:276 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:27:09:278 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:27:09:280 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:27:09:287 ==>> [D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:27:09:452 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:27:09:566 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:27:09:569 ==>> 检测【关闭仪表供电】
2025-07-31 20:27:09:570 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:27:09:737 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:27:09:857 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:27:09:859 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:27:09:861 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:27:10:010 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:27:10:143 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:27:10:146 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:27:10:148 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:27:10:175 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:10:326 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:27:10:423 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:27:10:426 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:27:10:427 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:27:10:629 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:27:10:744 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:27:10:747 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:27:10:748 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:27:10:856 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:27:10:962 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 21
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:27:11:101 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:27:11:104 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:27:11:106 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:27:11:172 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:11:247 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:27:11:464 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:27:11:467 ==>> 该项需要延时执行
2025-07-31 20:27:11:731 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5018. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5018. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5019. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5019. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5019. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5020. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5020. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5020. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5021. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5021. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5022. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5022
[D][05:17:54][COMM]CAN message

2025-07-31 20:27:11:761 ==>>  bat fault change: 0x0001802E->0x01B987FE 5022


2025-07-31 20:27:12:187 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:12:536 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:27:13:032 ==>>                                                                                                                                                            Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_

2025-07-31 20:27:13:137 ==>> process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][C

2025-07-31 20:27:13:244 ==>> OMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE

2025-07-31 20:27:13:302 ==>>  ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 20:27:13:332 ==>>                                                                                                                                          

2025-07-31 20:27:14:212 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:14:953 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:27:15:238 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:15:466 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:27:15:469 ==>> 检测【33V输入电压ADC】
2025-07-31 20:27:15:472 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:27:15:753 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:17:58][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:9 mv
[D][05:17:58][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:27:16:046 ==>> 【33V输入电压ADC】通过,【32450mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:27:16:052 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:27:16:054 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:27:16:166 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2776mV
Get AD_V6 1989mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:27:16:241 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:27:16:343 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:16:347 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:27:16:383 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:16:386 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:27:16:388 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 20:27:16:431 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:27:16:433 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:27:16:480 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:27:16:496 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:27:16:523 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:27:16:526 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:27:16:600 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10009
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10009
1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2775mV
Get AD_V6 1989mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:27:16:805 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:16:810 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:27:16:823 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:16:829 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:27:16:833 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 20:27:16:861 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:27:16:863 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:27:16:888 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:27:16:892 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:27:16:925 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:27:16:929 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:27:16:978 ==>> [D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:27:17:053 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2776mV
Get AD_V6 1992mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:27:17:216 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:17:222 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:27:17:235 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:17:242 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:27:17:246 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 20:27:17:254 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:27:17:256 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:27:17:273 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:27:17:276 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:27:17:298 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:27:17:303 ==>> 检测【打开WIFI(1)】
2025-07-31 20:27:17:306 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:27:17:488 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,

2025-07-31 20:27:17:533 ==>> -1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:27:17:589 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:27:17:593 ==>> 检测【清空消息队列(1)】
2025-07-31 20:27:17:595 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:27:17:908 ==>>                                                                                                                                                                                                                                                                                                         sg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087736304

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539129

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"

2025-07-31 20:27:17:938 ==>> cmiot","","",0



2025-07-31 20:27:18:141 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:27:18:149 ==>> 检测【打开GPS(1)】
2025-07-31 20:27:18:152 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:27:18:256 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:27:18:346 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:27:18:451 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:27:18:454 ==>> 检测【打开GSM联网】
2025-07-31 20:27:18:457 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:27:18:636 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:27:18:750 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:27:18:753 ==>> 检测【打开仪表供电1】
2025-07-31 20:27:18:755 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:27:18:846 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:27:18:951 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][C

2025-07-31 20:27:18:981 ==>> OMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:27:19:042 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:27:19:044 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:27:19:057 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:27:19:239 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:27:19:324 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:27:19:326 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:27:19:328 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:27:19:529 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33409]


2025-07-31 20:27:19:607 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:27:19:610 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:27:19:612 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:19:847 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:19:881 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:27:19:884 ==>> 检测【AD_V20电压】
2025-07-31 20:27:19:887 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:19:983 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:20:043 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:27:20:163 ==>> 本次取值间隔时间:171ms
2025-07-31 20:27:20:180 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:20:268 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:02][COMM]13727 imu init OK


2025-07-31 20:27:20:283 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:20:373 ==>>                                                        -- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]S->M yaw:INVALID
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:27:20:600 ==>> 本次取值间隔时间:307ms
2025-07-31 20:27:20:618 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:20:720 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:21:065 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:0

2025-07-31 20:27:21:095 ==>> 3][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 20:27:21:170 ==>> 本次取值间隔时间:442ms
2025-07-31 20:27:21:200 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:27:21:470 ==>> 本次取值间隔时间:292ms
2025-07-31 20:27:21:530 ==>>                                                                                                                                                                                                                                                                                 [D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:27:21:650 ==>> 本次取值间隔时间:169ms
2025-07-31 20:27:21:840 ==>> 本次取值间隔时间:186ms
2025-07-31 20:27:21:844 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:21:946 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:22:006 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:27:22:051 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:27:22:156 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097

2025-07-31 20:27:22:186 ==>> 152*7A



2025-07-31 20:27:22:431 ==>> 本次取值间隔时间:473ms
2025-07-31 20:27:22:456 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:22:570 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:22:645 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:27:22:840 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:27:22:855 ==>> 本次取值间隔时间:278ms
2025-07-31 20:27:22:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:22:989 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:23:109 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,60,,,44,33,,,39,42,,,38,39,,,37,1*7C

$GBGSV,2,2,06,59,,,37,14,,,36,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.588,1637.588,52.370,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
                                         

2025-07-31 20:27:23:229 ==>> 本次取值间隔时间:232ms
2025-07-31 20:27:23:247 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:23:259 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:27:23:349 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:23:427 ==>> 本次取值间隔时间:73ms
2025-07-31 20:27:23:457 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:27:23:840 ==>> 本次取值间隔时间:410ms
2025-07-31 20:27:23:869 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:23:960 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,60,,,41,33,,,41,3,,,41,14,,,40,1*45

$GBGSV,3,2,11,24,,,40,42,,,39,59,,,39,39,,,38,1*7E

$GBGSV,3,3,11,13,,,37,41,,,37,1,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1639.823,1639.823,52.376,2097152,2097152,2097152*4A



2025-07-31 20:27:23:975 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:24:065 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:24:080 ==>> 本次取值间隔时间:90ms
2025-07-31 20:27:24:112 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:24:215 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:24:293 ==>> 本次取值间隔时间:71ms
2025-07-31 20:27:24:464 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:24:705 ==>> 本次取值间隔时间:408ms
2025-07-31 20:27:24:784 ==>> 本次取值间隔时间:66ms
2025-07-31 20:27:24:979 ==>> 本次取值间隔时间:188ms
2025-07-31 20:27:24:984 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:25:009 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,60,,,41,33,,,41,3,,,40,14,,,40,1*44

$GBGSV,5,2,17,24,,,40,42,,,39,59,,,39,39,,,39,1*7F

$GBGSV,5,3,17,13,,,37,1,,,37,2,,,37,38,,,35,1*7E

$GBGSV,5,4,17,41,,,34,4,,,34,5,,,32,44,,,32,1*75

$GBGSV,5,5,17,25,,,41,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1546.917,1546.917,49.491,2097152,2097152,2097152*4E



2025-07-31 20:27:25:084 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:25:115 ==>> [D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:27:25:145 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:27:25:418 ==>> 本次取值间隔时间:324ms
2025-07-31 20:27:25:491 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:27:25:600 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:27:25:646 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150


2025-07-31 20:27:25:736 ==>> 本次取值间隔时间:131ms
2025-07-31 20:27:25:810 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:27:25:813 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:27:25:819 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:27:26:010 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,24,,,41,60,,,40,25,,,40,1*7F

$GBGSV,5,2,18,3,,,40,14,,,40,59,,,40,42,,,39,1*4A

$GBGSV,5,3,18,39,,,38,1,,,38,13,,,36,2,,,36,1*72

$GBGSV,5,4,18,38,,,36,41,,,35,4,,,34,5,,,32,1*74

$GBGSV,5,5,18,44,,,32,16,,,43,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1558.349,1558.349,49.857,2097152,2097152,2097152*48



2025-07-31 20:27:26:162 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:27:26:165 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:27:26:168 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:27:26:372 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:27:026 ==>> $GBGGA,122730.822,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,24,,,40,60,,,40,25,,,40,1*74

$GBGSV,6,2,22,3,,,40,14,,,40,59,,,40,42,,,39,1*40

$GBGSV,6,3,22,39,,,39,1,,,38,16,,,37,13,,,37,1*4C

$GBGSV,6,4,22,2,,,36,38,,,36,41,,,35,9,,,34,1*70

$GBGSV,6,5,22,4,,,33,44,,,33,7,,,33,5,,,32,1*42

$GBGSV,6,6,22,10,,,32,40,,,36,1*77

$GBRMC,122730.822,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122730.822,0.000,764.571,764.571,699.219,2097152,2097152,2097152*60



2025-07-31 20:27:27:116 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:27:27:191 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:27:27:344 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:27:753 ==>> $GBGGA,122731.522,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,41,24,,,41,3,,,40,1*41

$GBGSV,6,2,23,59,,,40,14,,,40,25,,,40,39,,,39,1*79

$GBGSV,6,3,23,42,,,39,13,,,37,1,,,37,16,,,37,1*4E

$GBGSV,6,4,23,2,,,36,38,,,36,41,,,36,9,,,35,1*73

$GBGSV,6,5,23,7,,,34,40,,,34,5,,,33,10,,,33,1*73

$GBGSV,6,6,23,44,,,33,4,,,33,6,,,31,1*77

$GBRMC,122731.522,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122731.522,0.000,761.960,761.960,696.831,2097152,2097152,2097152*63



2025-07-31 20:27:28:230 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:27:28:441 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:27:28:711 ==>> $GBGGA,122732.502,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,41,60,,,40,3,,,40,1*47

$GBGSV,6,2,24,59,,,40,14,,,40,25,,,40,39,,,39,1*7E

$GBGSV,6,3,24,42,,,39,1,,,38,13,,,37,38,,,37,1*4A

$GBGSV,6,4,24,16,,,37,2,,,36,41,,,36,40,,,35,1*44

$GBGSV,6,5,24,9,,,35,7,,,34,8,,,34,10,,,33,1*42

$GBGSV,6,6,24,44,,,33,6,,,33,4,,,33,5,,,32,1*46

$GBRMC,122732.502,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122732.502,0.000,762.102,762.102,696.960,2097152,2097152,2097152*67



2025-07-31 20:27:29:107 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:27:29:258 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:27:29:264 ==>> #################### 【测试结束】 ####################
2025-07-31 20:27:29:285 ==>> 关闭5V供电
2025-07-31 20:27:29:291 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:27:29:350 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:27:29:742 ==>> $GBGGA,122733.502,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,41,59,,,41,3,,,40,1*4C

$GBGSV,7,2,25,24,,,40,25,,,40,14,,,40,39,,,39,1*74

$GBGSV,7,3,25,42,,,39,1,,,38,13,,,37,38,,,37,1*4A

$GBGSV,7,4,25,16,,,37,2,,,36,40,,,36,41,,,36,1*47

$GBGSV,7,5,25,7,,,35,9,,,35,8,,,34,6,,,34,1*73

$GBGSV,7,6,25,26,,,33,44,,,33,4,,,33,10,,,32,1*40

$GBGSV,7,7,25,5,,,32,1*45

$GBRMC,122733.502,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122733.502,0.000,761.415,761.415,696.332,2097152,2097152,2097152*6B



2025-07-31 20:27:30:287 ==>> 关闭5V供电成功
2025-07-31 20:27:30:292 ==>> 关闭33V供电
2025-07-31 20:27:30:298 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:27:30:349 ==>> 5A A5 02 5A A5 


2025-07-31 20:27:30:454 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:27:30:559 ==>> [D][05:18:13][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:1

2025-07-31 20:27:30:604 ==>> 8:13][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:27:30:709 ==>>                                                                                                                                                                                                                                                                                                                                                                                  44,,,33,4,,,33,1*74

$GBGSV,7,7,25,5,,,32,1*45

$GBRMC,122734.502,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122734.502,0

2025-07-31 20:27:30:739 ==>> .000,763.061,763.061,697.837,2097152,2097152,2097152*63



2025-07-31 20:27:30:829 ==>> [D][05:18:13][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:27:31:292 ==>> 关闭33V供电成功
2025-07-31 20:27:31:297 ==>> 关闭3.7V供电
2025-07-31 20:27:31:301 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:27:31:353 ==>> 6A A6 02 A6 6A 


2025-07-31 20:27:31:443 ==>> Battery OFF
OVER 150


2025-07-31 20:27:32:056 ==>>  

