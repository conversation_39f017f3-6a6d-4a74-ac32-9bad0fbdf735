2025-07-31 20:23:05:175 ==>> MES查站成功:
查站序号:P5100010053132B2验证通过
2025-07-31 20:23:05:180 ==>> 扫码结果:P5100010053132B2
2025-07-31 20:23:05:182 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:23:05:184 ==>> 测试参数版本:2024.10.11
2025-07-31 20:23:05:186 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:23:05:188 ==>> 检测【打开透传】
2025-07-31 20:23:05:191 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:23:05:248 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:23:05:579 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:23:05:592 ==>> 检测【检测接地电压】
2025-07-31 20:23:05:594 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:23:05:647 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:23:05:862 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:23:05:865 ==>> 检测【打开小电池】
2025-07-31 20:23:05:867 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:23:05:951 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:23:06:136 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:23:06:147 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:23:06:150 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:23:06:253 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:23:06:406 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:23:06:409 ==>> 检测【等待设备启动】
2025-07-31 20:23:06:412 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:23:06:635 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:23:06:831 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:23:07:441 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:23:07:534 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:23:07:934 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:23:08:409 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:23:08:537 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:23:08:540 ==>> 检测【产品通信】
2025-07-31 20:23:08:542 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:23:08:716 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:23:08:845 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:23:08:847 ==>> 检测【初始化完成检测】
2025-07-31 20:23:08:849 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:23:09:081 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:23:09:135 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:23:09:138 ==>> 检测【关闭大灯控制1】
2025-07-31 20:23:09:140 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:23:09:326 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:23:09:428 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:23:09:431 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:23:09:433 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:23:09:476 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:23:09:687 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:23:09:964 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:23:09:966 ==>> 检测【关闭仪表供电】
2025-07-31 20:23:09:969 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:23:10:138 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:23:10:258 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:23:10:261 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:23:10:263 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:23:10:504 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:23:10:543 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:23:10:545 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:23:10:547 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:23:10:731 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:23:10:824 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:23:10:827 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:23:10:828 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:23:11:016 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:23:11:095 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:23:11:097 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:23:11:099 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:23:11:121 ==>> 5A

2025-07-31 20:23:11:151 ==>>  A5 01 5A A5 


2025-07-31 20:23:11:241 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:23:11:346 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWER

2025-07-31 20:23:11:364 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:23:11:367 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:23:11:368 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:23:11:376 ==>> ON, 32
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:23:11:451 ==>> 5A A5 03 5A A5 


2025-07-31 20:23:11:511 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:23:11:541 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:23:11:641 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:23:11:645 ==>> 该项需要延时执行
2025-07-31 20:23:12:036 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5011. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5012
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 20:23:12:506 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:23:12:932 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:23:13:448 ==>>                                                                              COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec

2025-07-31 20:23:13:553 ==>> 800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitV

2025-07-31 20:23:13:658 ==>> olt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN ge

2025-07-31 20:23:13:718 ==>> t soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 20:23:13:748 ==>>                                                                                                  

2025-07-31 20:23:14:549 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 20:23:15:367 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:23:15:532 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:23:15:652 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:23:15:655 ==>> 检测【33V输入电压ADC】
2025-07-31 20:23:15:657 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:15:957 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3174  volt:5579 mv
[D][05:17:57][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:57][COMM]adc read left brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read right brake adc:8  volt:10 mv
[D][05:17:57][COMM]adc read throttle adc:5  volt:6 mv
[D][05:17:57][COMM]adc read battery ts volt:13 mv
[D][05:17:57][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:23:16:219 ==>> 【33V输入电压ADC】通过,【32982mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:23:16:222 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:23:16:225 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:23:16:354 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2791mV
Get AD_V6 1986mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:23:16:516 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:16:519 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:23:16:551 ==>> [D][05:17:58][COMM]9707 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:23:16:558 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:16:563 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:23:16:567 ==>> 原始值:【2791】, 乘以分压基数【2】还原值:【5582】
2025-07-31 20:23:16:623 ==>> 【TP68_VCC5V5(ADV5)】通过,【5582mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:23:16:626 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:23:16:663 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1986mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:23:16:667 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:23:16:711 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:23:16:714 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:23:16:913 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2791mV
Get AD_V6 1992mV
Get AD_V7 1090mV
OVER 150
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E22217 10001
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F80C71E22217->0x0008F80C71E2223F 10023
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10023


2025-07-31 20:23:17:031 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:17:049 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:23:17:104 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:17:106 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:23:17:109 ==>> 原始值:【2791】, 乘以分压基数【2】还原值:【5582】
2025-07-31 20:23:17:166 ==>> 【TP68_VCC5V5(ADV5)】通过,【5582mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:23:17:169 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:23:17:225 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:23:17:249 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:23:17:284 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:23:17:286 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:23:17:353 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2790mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:23:17:383 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:23:17:600 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:17:604 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:23:17:652 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:17:655 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:23:17:657 ==>> 原始值:【2790】, 乘以分压基数【2】还原值:【5580】
2025-07-31 20:23:17:795 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10718 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main T

2025-07-31 20:23:17:825 ==>> ask receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:23:17:869 ==>> 【TP68_VCC5V5(ADV5)】通过,【5580mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:23:17:872 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:23:17:924 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:23:17:926 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:23:18:232 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:23:18:236 ==>> 检测【打开WIFI(1)】
2025-07-31 20:23:18:265 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:23:18:268 ==>>                        
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087738003

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539136

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:23:18:414 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:23:18:549 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:23:18:553 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:23:18:555 ==>> 检测【清空消息队列(1)】
2025-07-31 20:23:18:557 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:23:18:744 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:23:18:868 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:23:18:872 ==>> 检测【打开GPS(1)】
2025-07-31 20:23:18:877 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:23:19:225 ==>>                                             [CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOC

2025-07-31 20:23:19:330 ==>> KET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:23:19:394 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:23:19:397 ==>> 检测【打开GSM联网】
2025-07-31 20:23:19:399 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:23:19:435 ==>>                                                                                                                                                                                                 
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_

2025-07-31 20:23:19:466 ==>> SOCKET_IDLE


2025-07-31 20:23:19:780 ==>> [D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:23:19:918 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:23:19:921 ==>> 检测【打开仪表供电1】
2025-07-31 20:23:19:923 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:23:20:146 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:23:20:194 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:23:20:197 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:23:20:199 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:23:20:344 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:23:20:449 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50



2025-07-31 20:23:20:468 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:23:20:471 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:23:20:475 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:23:20:479 ==>> 
$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:23:20:629 ==>> [D][05:18:02][COMM]13732 imu init OK
[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33108]


2025-07-31 20:23:20:739 ==>> 【读取主控ADC采集的仪表电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:23:20:743 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:23:20:746 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:23:20:949 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:23:21:013 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:23:21:016 ==>> 检测【AD_V20电压】
2025-07-31 20:23:21:020 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:23:21:115 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:23:21:146 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:23:21:461 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,60,,,43,25,,,40,33,,,40,39,,,39,1*7B

$GBGSV,3,2,09,40,,,36,59,,,36,24,,,30,13,,,40,1*75

$GBGSV,3,3,09,14,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1563.619,1563.619,50.068,2097152,2097152,2097152*44

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:23:21:536 ==>> 本次取值间隔时间:412ms
2025-07-31 20:23:21:551 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:23:21:926 ==>> 本次取值间隔时间:385ms
2025-07-31 20:23:22:202 ==>> 本次取值间隔时间:271ms
2025-07-31 20:23:22:279 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,60,,,41,33,,,41,14,,,41,25,,,40,1*74

$GBGSV,4,2,13,3,,,40,39,,,39,59,,,38,13,,,37,1*44

$GBGSV,4,3,13,24,,,37,40,,,36,5,,,33,1,,,40,1*70

$GBGSV,4,4,13,41,,,37,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1594.234,1594.234,50.974,2097152,2097152,2097152*40



2025-07-31 20:23:22:369 ==>> 本次取值间隔时间:165ms
2025-07-31 20:23:22:372 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:23:22:477 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:23:22:553 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1660mV
OVER 150


2025-07-31 20:23:22:943 ==>> 本次取值间隔时间:465ms
2025-07-31 20:23:23:178 ==>> 【AD_V20电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:23:181 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:23:23:183 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:23:23:281 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,41,14,,,41,60,,,40,25,,,40,1*70

$GBGSV,5,2,17,3,,,40,39,,,39,59,,,39,24,,,39,1*4A

$GBGSV,5,3,17,1,,,38,13,,,37,38,,,37,40,,,36,1*44

$GBGSV,5,4,17,41,,,35,2,,,35,5,,,33,44,,,32,1*72

$GBGSV,5,5,17,4,,,32,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1546.153,1546.153,49.464,2097152,2097152,2097152*44

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:23:23:386 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:23:23:486 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:23:23:489 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:23:23:507 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:23:23:641 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:23:23:784 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:23:23:788 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:23:23:792 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:23:23:851 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:23:24:079 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:23:24:083 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:23:24:114 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:23:24:344 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,122328.117,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,41,14,,,41,60,,,40,25,,,40,1*7E

$GBGSV,5,2,19,3,,,40,59,,,40,24,,,40,39,,,39,1*44

$GBGSV,5,3,19,1,,,37,13,,,37,38,,,37,40,,,36,1*45

$GBGSV,5,4,19,41,,,35,2,,,34,5,,,33,16,,,33,1*7B

$GBGSV,5,5,19,44,,,32,4,,,32,34,,,31,1*4F

$GBRMC,122328.117,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122328.117,0.000,1523.072,1523.072,48.748,2097152,2097152,2097152*59



2025-07-31 20:23:24:640 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:23:24:643 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:23:24:646 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:23:24:705 ==>> $GBGGA,122328.517,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,14,,,41,60,,,40,25,,,40,1*77

$GBGSV,5,2,20,3,,,40,59,,,40,24,,,40,39,,,39,1*4E

$GBGSV,5,3,20,1,,,37,13,,,37,38,,,36,40,,,36,1*4E

$GBGSV,5,4,20,41,,,35,2,,,34,5,,,33,16,,,33,1*71

$GBGSV,5,5,20,44,,,32,4,,,32,34,,,31,42,,,38,1*48

$GBRMC,122328.517,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122328.517,0.000,1523.075,1523.075,48.752,2097152,2097152,2097152*56



2025-07-31 20:23:24:810 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display

2025-07-31 20:23:24:840 ==>>  set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:23:24:946 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:23:24:949 ==>> 检测【AD_V21电压】
2025-07-31 20:23:24:951 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:23:25:053 ==>> 1A A1 20 00 00 
Get AD_V21 1061mV
OVER 150


2025-07-31 20:23:25:406 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:23:25:421 ==>> 本次取值间隔时间:473ms
2025-07-31 20:23:25:442 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:23:25:541 ==>> 1A A1 20 00 00 
Get AD_V21 1656mV
OVER 150


2025-07-31 20:23:25:736 ==>> $GBGGA,122329.517,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,41,60,,,40,25,,,40,1*73

$GBGSV,7,2,26,3,,,40,59,,,40,24,,,40,42,,,40,1*48

$GBGSV,7,3,26,39,,,39,1,,,37,13,,,37,40,,,37,1*45

$GBGSV,7,4,26,38,,,36,6,,,36,9,,,36,41,,,35,1*73

$GBGSV,7,5,26,16,,,35,7,,,35,2,,,34,5,,,33,1*40

$GBGSV,7,6,26,44,,,32,4,,,32,10,,,32,34,,,31,1*42

$GBGSV,7,7,26,8,,,31,36,,,37,1*49

$GBRMC,122329.517,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122329.517,0.000,1510.765,1510.765,48.355,2097152,2097152,2097152*54



2025-07-31 20:23:25:781 ==>> 本次取值间隔时间:337ms
2025-07-31 20:23:25:818 ==>> 【AD_V21电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:25:821 ==>> 检测【关闭仪表供电2】
2025-07-31 20:23:25:825 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:23:26:040 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:23:26:089 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:23:26:091 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:23:26:094 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:23:26:235 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:23:26:358 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:23:26:363 ==>> 检测【打开AccKey2供电】
2025-07-31 20:23:26:370 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:23:26:521 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:23:26:637 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:23:26:640 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:23:26:643 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:26:731 ==>> $GBGGA,122330.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,25,60,,,40,25,,,40,59,,,40,42,,,39,1*71

$GBGSV,7,3,25,39,,,39,1,,,37,13,,,37,40,,,37,1*46

$GBGSV,7,4,25,38,,,37,6,,,36,9,,,36,41,,,36,1*72

$GBGSV,7,5,25,16,,,36,7,,,35,2,,,35,5,,,33,1*41

$GBGSV,7,6,25,44,,,32,4,,,32,10,,,32,8,,,32,1*7D

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,122330.517,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122330.517,0.000,1520.712,1520.712,48.670,2097152,2097152,2097152*5E



2025-07-31 20:23:26:958 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3175  volt:5581 mv
[D][05:18:08][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:08][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:08][COMM]adc read battery ts volt:14 mv
[D][05:18:08][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:08][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:23:27:191 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33361mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:23:27:195 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:23:27:198 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:23:27:339 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:23:27:414 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:23:27:487 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:23:27:494 ==>> 该项需要延时执行
2025-07-31 20:23:27:759 ==>> $GBGGA,122331.517,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,24,,,41,3,,,40,1*45

$GBGSV,7,2,25,60,,,40,25,,,40,59,,,40,42,,,39,1*71

$GBGSV,7,3,25,39,,,39,1,,,38,13,,,37,40,,,37,1*49

$GBGSV,7,4,25,38,,,36,6,,,36,9,,,36,16,,,36,1*71

$GBGSV,7,5,25,41,,,35,7,,,35,2,,,35,5,,,33,1*40

$GBGSV,7,6,25,8,,,33,44,,,32,4,,,32,10,,,32,1*7C

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,122331.517,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122331.517,0.000,758.115,758.115,693.315,2097152,2097152,2097152*69



2025-07-31 20:23:28:759 ==>> $GBGGA,122332.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,3,,,40,1*47

$GBGSV,7,2,27,60,,,40,59,,,40,42,,,40,25,,,40,1*7D

$GBGSV,7,3,27,39,,,39,1,,,38,13,,,37,40,,,37,1*4B

$GBGSV,7,4,27,38,,,36,9,,,36,16,,,36,6,,,36,1*73

$GBGSV,7,5,27,41,,,36,2,,,35,7,,,35,8,,,34,1*4B

$GBGSV,7,6,27,5,,,33,44,,,33,10,,,32,26,,,32,1*42

$GBGSV,7,7,27,4,,,32,34,,,31,45,,,39,1*48

$GBRMC,122332.517,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122332.517,0.000,757.605,757.605,692.849,2097152,2097152,2097152*69



2025-07-31 20:23:29:430 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:23:29:750 ==>> $GBGGA,122333.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,60,,,41,24,,,41,25,,,41,1*70

$GBGSV,7,2,26,14,,,41,3,,,40,59,,,40,42,,,40,1*4A

$GBGSV,7,3,26,39,,,39,13,,,37,40,,,37,1,,,37,1*45

$GBGSV,7,4,26,38,,,36,9,,,36,16,,,36,6,,,36,1*72

$GBGSV,7,5,26,41,,,36,7,,,35,2,,,35,8,,,34,1*4A

$GBGSV,7,6,26,26,,,33,5,,,33,44,,,33,10,,,32,1*42

$GBGSV,7,7,26,4,,,32,34,,,31,1*42

$GBRMC,122333.517,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122333.517,0.000,759.198,759.198,694.306,2097152,2097152,2097152*6E



2025-07-31 20:23:30:494 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:23:30:499 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:23:30:503 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:30:801 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3180  volt:5589 mv
[D][05:18:12][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:12][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:12][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:12][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:12][COMM]adc read battery ts volt:13 mv
[D][05:18:12][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
$GBGGA,122334.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,60,,,41,14,,,41,3,,,40,1*46

$GBGSV,7,2,26,59,,,40,24,,,40,25,,,40,39,,,39,1*7E

$GBGSV,7,3,26,42,,,39,13,,,37,40,,,37,1,,,37,1*49

$GBGSV,7,4,26,38,,,36,9,,,36,16,,,36,6,,,36,1*72

$GBGSV,7,5,26,41,,,36,2,,,35,7,,,35,26,,,34,1*76

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,12233

2025-07-31 20:23:30:846 ==>> 4.517,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122334.517,0.000,758.391,758.391,693.567,2097152,2097152,2097152*6F

[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:23:31:032 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【25mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:23:31:035 ==>> 检测【打开AccKey1供电】
2025-07-31 20:23:31:040 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:23:31:231 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:23:31:318 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:23:31:337 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:23:31:339 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:23:31:444 ==>> [D][05:18:13][COMM]read battery soc:255
1A A1 00 40 00 
Get AD_V14 2687mV
OVER 150


2025-07-31 20:23:31:580 ==>> 原始值:【2687】, 乘以分压基数【2】还原值:【5374】
2025-07-31 20:23:31:602 ==>> 【读取AccKey1电压(ADV14)前】通过,【5374mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:23:31:605 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:23:31:607 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:31:735 ==>> $GBGGA,122335.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,60,,,40,1*73

$GBGSV,7,2,26,3,,,40,59,,,40,42,,,40,25,,,40,1*49

$GBGSV,7,3,26,39,,,39,13,,,37,40,,,37,1,,,37,1*45

$GBGSV,7,4,26,38,,,36,9,,,36,16,,,36,6,,,36,1*72

$GBGSV,7,5,26,2,,,35,7,,,35,41,,,35,26,,,34,1*75

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122335.517,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122335.517,0.000,758.394,758.394,693.570,2097152,2097152,2097152*68



2025-07-31 20:23:31:960 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:18:13][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:13][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:13][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:13][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:13][COMM]adc read battery ts volt:11 mv
[D][05:18:13][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:13][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:23:32:144 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5575mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:23:32:147 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:23:32:150 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:23:32:329 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:23:32:517 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:23:32:521 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:23:32:526 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:23:32:651 ==>> 1A A1 00 40 00 
Get AD_V14 2685mV
OVER 150


2025-07-31 20:23:32:756 ==>> $GBGGA,122336.517,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,60,,,40,1*73

$GBGSV,7,2,26,3,,,40,59,,,40,42,,,40,25,,,40,1*49

$GBGSV,7,3,26,39,,,39,13,,,37,1,,,37,38,,,36,1*4B

$GBGSV,7,4,26,40,,,36,9,,,36,16,,,36,6,,,36,1*7D

$GBGSV,7,5,26,2,,,35,7,,,35,41,,,35,26,,,34,1*75

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122336.517,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122336.517,0.000,757.598,757.598,692.842,2097152,2097152,2097152*66



2025-07-31 20:23:32:771 ==>> 原始值:【2685】, 乘以分压基数【2】还原值:【5370】
2025-07-31 20:23:32:814 ==>> 【读取AccKey1电压(ADV14)后】通过,【5370mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:23:32:821 ==>> 检测【打开WIFI(2)】
2025-07-31 20:23:32:824 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:23:33:059 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:23:33:199 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:23:33:203 ==>> 检测【转刹把供电】
2025-07-31 20:23:33:206 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:23:33:426 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:15][COMM]read battery soc:255


2025-07-31 20:23:33:500 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:23:33:505 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:23:33:509 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:23:33:610 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:23:33:809 ==>> $GBGGA,122337.517,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,60,,,40,1*73

$GBGSV,7,2,26,3,,,40,59,,,40,25,,,40,39,,,39,1*4B

$GBGSV,7,3,26,42,,,39,13,,,37,1,,,37,38,,,36,1*47

$GBGSV,7,4,26,40,,,36,9,,,36,16,,,36,6,,,36,1*7D

$GBGSV,7,5,26,41,,,36,2,,,35,7,,,35,26,,,34,1*76

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122337.517,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122337.517,0.000,757.595,757.595,692.839,2097152,2097152,2097152*6B

+WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,CC057790A621,-61
+WIFISCAN:4,2,44A1917CAD80,-77
+WIFISCAN:4,3,CC057790A6E1,-79

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 20:23:33:884 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:23:34:544 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:23:34:650 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:23:34:771 ==>> [D][05:18:16][GNSS]recv submsg id[3]
$GBGGA,122338.517,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,26,59,,,40,42,,,40,14,,,40,25,,,40,1*7F

$GBGSV,7,3,26,39,,,39,13,,,37,1,,,37,38,,,36,1*4B

$GBGSV,7,4,26,40,,,36,16,,,36,6,,,36,41,,,36,1*41

$GBGSV,7,5,26,2,,,35,7,,,35,9,,,35,26,,,34,1*49

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122338.517,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122338.517,0.000,756.800,756.800,692.112,2097152,2097152,2097152*64

[W][05:18:16][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2425mV
OVER 150


2025-07-31 20:23:34:801 ==>> 原始值:【2425】, 乘以分压基数【2】还原值:【4850】
2025-07-31 20:23:34:830 ==>> 【读取AD_V15电压(前)】通过,【4850mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:23:34:836 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:23:34:841 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:23:34:936 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:23:35:012 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:23:35:041 ==>> 1A A1 01 00 00 
Get AD_V16 2457mV
OVER 150


2025-07-31 20:23:35:102 ==>> 原始值:【2457】, 乘以分压基数【2】还原值:【4914】
2025-07-31 20:23:35:126 ==>> 【读取AD_V16电压(前)】通过,【4914mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:23:35:130 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:23:35:132 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:35:469 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3174  volt:5579 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:17][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:17][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:17][COMM]adc read battery ts volt:14 mv
[D][05:18:17][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3137  volt:5514 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2385  volt:3843 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
                                         

2025-07-31 20:23:35:654 ==>> 【转刹把供电电压(主控ADC)】通过,【5514mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:23:35:658 ==>> 检测【转刹把供电电压】
2025-07-31 20:23:35:660 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:35:759 ==>> $GBGGA,122339.517,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,24,,,41,14,,,41,1*47

$GBGSV,7,2,26,60,,,40,59,,,40,25,,,40,39,,,39,1*7E

$GBGSV,7,3,26,42,,,39,13,,,37,40,,,37,1,,,37,1*49

$GBGSV,7,4,26,38,,,36,9,,,36,16,,,36,6,,,36,1*72

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,4,,,33,1*4D

$GBGSV,7,7,26,10,,,32,34,,,32,1*74

$GBRMC,122339.517,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122339.517,0.000,760.776,760.776,695.748,2097152,2097152,2097152*6B



2025-07-31 20:23:35:984 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3176  volt:5582 mv
[D][05:18:17][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:17][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:17][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:17][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:17][COMM]adc read battery ts volt:12 mv
[D][05:18:17][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3134  volt:5508 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2385  volt:3843 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:23:36:262 ==>> 【转刹把供电电压】通过,【5508mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:23:36:267 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:23:36:270 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:23:36:436 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:23:36:593 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:23:36:596 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:23:36:599 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:23:36:695 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:23:36:770 ==>> $GBGGA,122340.517,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,25,,,41,1*73

$GBGSV,7,2,26,60,,,40,3,,,40,59,,,40,42,,,40,1*48

$GBGSV,7,3,26,39,,,39,13,,,37,38,,,37,40,,,37,1*7F

$GBGSV,7,4,26,1,,,37,9,,,36,16,,,36,6,,,36,1*49

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,34,44,,,33,4,,,33,10,,,32,1*78

$GBGSV,7,7,26,5,,,32,34,,,32,1*40

$GBRMC,122340.517,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122340.517,0.000,761.576,761.576,696.480,2097152,2097152,2097152*61

[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:23:36:894 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:23:36:898 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:23:36:905 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:23:36:995 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:23:37:010 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:23:37:055 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:23:37:144 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:23:37:150 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:23:37:163 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:23:37:253 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:23:37:443 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:23:37:450 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:23:37:455 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:23:37:480 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:23:37:553 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:23:37:744 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:23:37:748 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:23:37:754 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:23:37:760 ==>> $GBGGA,122341.517,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,59,,,41,24,,,41,14,,,41,1*78

$GBGSV,7,2,26,25,,,41,60,,,40,3,,,40,42,,,40,1*42

$GBGSV,7,3,26,39,,,39,1,,,38,13,,,37,38,,,37,1*45

$GBGSV,7,4,26,40,,,37,16,,,37,9,,,36,6,,,36,1*7D

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,35,44,,,34,5,,,33,10,,,32,1*7F

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122341.517,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122341.517,0.000,765.554,765.554,700.117,2097152,2097152,2097152*65



2025-07-31 20:23:37:839 ==>> 3A A3 05 01 A3 


2025-07-31 20:23:37:944 ==>> ON_OUT5
OVER 150


2025-07-31 20:23:38:057 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:23:38:063 ==>> 检测【左刹电压测试1】
2025-07-31 20:23:38:098 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:38:365 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:20][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:20][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:20][COMM]adc read right brake adc:1728  volt:2278 mv
[D][05:18:20][COMM]adc read throttle adc:1730  volt:2280 mv
[D][05:18:20][COMM]adc read battery ts volt:10 mv
[D][05:18:20][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:20][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:23:38:624 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:38:743 ==>> $GBGGA,122342.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,25,,,41,1*73

$GBGSV,7,2,26,60,,,40,3,,,40,59,,,40,42,,,40,1*48

$GBGSV,7,3,26,39,,,39,1,,,38,13,,,37,38,,,37,1*45

$GBGSV,7,4,26,40,,,37,16,,,37,9,,,36,6,,,36,1*7D

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,35,44,,,34,5,,,33,4,,,33,1*4B

$GBGSV,7,7,26,10,,,32,34,,,32,1*74

$GBRMC,122342.517,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122342.517,0.000,765.548,765.548,700.112,2097152,2097152,2097152*63



2025-07-31 20:23:38:931 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:20][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:20][COMM]adc read left brake adc:1721  volt:2268 mv
[D][05:18:20][COMM]adc read right brake adc:1733  volt:2284 mv
[D][05:18:20][COMM]adc read throttle adc:1731  volt:2282 mv
[D][05:18:20][COMM]adc read battery ts volt:15 mv
[D][05:18:20][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:20][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 

2025-07-31 20:23:38:954 ==>> mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:23:39:161 ==>> 【左刹电压测试1】通过,【2268】符合目标值【2250】至【2500】要求!
2025-07-31 20:23:39:166 ==>> 检测【右刹电压测试1】
2025-07-31 20:23:39:191 ==>> 【右刹电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 20:23:39:198 ==>> 检测【转把电压测试1】
2025-07-31 20:23:39:218 ==>> 【转把电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 20:23:39:223 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:23:39:229 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:23:39:345 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:23:39:450 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:23:39:512 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:23:39:517 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:23:39:523 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:23:39:645 ==>> 3A A3 04 00 A3 


2025-07-31 20:23:39:750 ==>> $GBGGA,122343.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,25,,,41,1*73

$GBGSV,7,2,26,60,,,40,3,,,40,59,,,40,42,,,40,1*48

$GBGSV,7,3,26,39,,,39,1,,,38,13,,,37,38,,,37,1*45

$GBGSV,7,4,26,40,,,37,9,,,36,16,,,36,6,,,36,1*7C

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,34,44,,,34,5,,,33,10,,,32,1*7E

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122343.517,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122343.517,0.000,763.166,763.166,697.934,2097152,2097152,2097152*61

OFF_OUT4
OVER 150


2025-07-31 20:23:39:795 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:23:39:804 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:23:39:809 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:23:39:945 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:23:40:084 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:23:40:088 ==>> 检测【左刹电压测试2】
2025-07-31 20:23:40:092 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:23:40:357 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:22][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:22][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:22][COMM]adc read battery ts volt:7 mv
[D][05:18:22][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:23:40:623 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:23:40:627 ==>> 检测【右刹电压测试2】
2025-07-31 20:23:40:642 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 20:23:40:646 ==>> 检测【转把电压测试2】
2025-07-31 20:23:40:662 ==>> 【转把电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 20:23:40:666 ==>> 检测【晶振检测】
2025-07-31 20:23:40:672 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:23:40:755 ==>> $GBGGA,122344.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,25,,,41,1*73

$GBGSV,7,2,26,60,,,40,3,,,40,59,,,40,42,,,40,1*48

$GBGSV,7,3,26,39,,,39,1,,,38,13,,,37,38,,,37,1*45

$GBGSV,7,4,26,40,,,37,9,,,36,16,,,36,6,,,36,1*7C

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,34,44,,,34,5,,,33,4,,,33,1*4A

$GBGSV,7,7,26,10,,,32,34,,,32,1*74

$GBRMC,122344.517,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122344.517,0.000,763.959,763.959,698.658,2097152,2097152,2097152*6C



2025-07-31 20:23:40:830 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 20:23:40:939 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:23:40:944 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:23:40:948 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:23:41:055 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1670mV
Get AD_V4 1653mV
Get AD_V5 2790mV
Get AD_V6 1988mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:23:41:211 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:23:41:215 ==>> 检测【检测BootVer】
2025-07-31 20:23:41:218 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:41:635 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539136
[D][05:18:23][FCTY]HardwareID  = 867222087738003
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:23][FCTY]Bat         = 3924 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 33, adc = 1305
[D][05:18:23][FCTY]Acckey1 vol = 5577 mv, Acckey2 vol = 75 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3780 mv
[D][05:18:23][F

2025-07-31 20:23:41:665 ==>> CTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][COMM]read battery soc:255


2025-07-31 20:23:41:750 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:23:41:754 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:23:41:758 ==>> 检测【检测固件版本】
2025-07-31 20:23:41:770 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:23:41:775 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:23:41:780 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:23:41:784 ==>> 检测【检测蓝牙版本】
2025-07-31 20:23:41:800 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:23:41:804 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:23:41:808 ==>> 检测【检测MoBikeId】
2025-07-31 20:23:41:830 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:23:41:835 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:23:41:840 ==>> 检测【检测蓝牙地址】
2025-07-31 20:23:41:852 ==>> 取到目标值:D066EFBAE324
2025-07-31 20:23:41:855 ==>> 【检测蓝牙地址】通过,【D066EFBAE324】符合目标值【】要求!
2025-07-31 20:23:41:859 ==>> 提取到蓝牙地址:D066EFBAE324
2025-07-31 20:23:41:866 ==>> 检测【BOARD_ID】
2025-07-31 20:23:41:882 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:23:41:885 ==>> 检测【检测充电电压】
2025-07-31 20:23:41:920 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:23:41:925 ==>> 检测【检测VBUS电压1】
2025-07-31 20:23:41:950 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:23:41:954 ==>> 检测【检测充电电流】
2025-07-31 20:23:41:969 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:23:41:976 ==>> 检测【检测IMEI】
2025-07-31 20:23:42:001 ==>> 取到目标值:867222087738003
2025-07-31 20:23:42:009 ==>> 【检测IMEI】通过,【867222087738003】符合目标值【】要求!
2025-07-31 20:23:42:017 ==>> 提取到IMEI:867222087738003
2025-07-31 20:23:42:025 ==>> 检测【检测IMSI】
2025-07-31 20:23:42:047 ==>> 取到目标值:460130071539136
2025-07-31 20:23:42:050 ==>> 【检测IMSI】通过,【460130071539136】符合目标值【】要求!
2025-07-31 20:23:42:053 ==>> 提取到IMSI:460130071539136
2025-07-31 20:23:42:057 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:23:42:064 ==>> 取到目标值:460130071539136
2025-07-31 20:23:42:092 ==>> 【校验网络运营商(移动)】通过,【460130071539136】符合目标值【】要求!
2025-07-31 20:23:42:096 ==>> 检测【打开CAN通信】
2025-07-31 20:23:42:102 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:23:42:151 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:23:42:348 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:23:42:352 ==>> 检测【检测CAN通信】
2025-07-31 20:23:42:355 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:23:42:443 ==>> can send success


2025-07-31 20:23:42:488 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:23:42:533 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35697
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:23:42:608 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:23:42:627 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:23:42:631 ==>> 检测【关闭CAN通信】
2025-07-31 20:23:42:637 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:23:42:668 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:23:42:758 ==>> $GBGGA,122346.517,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,25,,,41,1*73

$GBGSV,7,2,26,60,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,26,42,,,39,13,,,37,38,,,37,40,,,37,1*73

$GBGSV,7,4,26,1,,,37,9,,,36,16,,,36,6,,,36,1*49

$GBGSV,7,5,26,41,,,36,2,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,26,8,,,34,5,,,33,44,,,33,10,,,32,1*79

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122346.517,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122346.517,0.000,760.779,760.779,695.751,2097152,2097152,2097152*6B

[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:23:42:907 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:23:42:912 ==>> 检测【打印IMU STATE】
2025-07-31 20:23:42:916 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:23:43:151 ==>> [D][05:18:25][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:23:43:184 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:23:43:190 ==>> 检测【六轴自检】
2025-07-31 20:23:43:197 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:23:43:331 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:23:43:482 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:23:43:815 ==>> $GBGGA,122347.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,14,,,41,60,,,40,1*73

$GBGSV,7,2,26,3,,,40,59,,,40,25,,,40,39,,,39,1*4B

$GBGSV,7,3,26,42,,,39,38,,,37,40,,,37,1,,,37,1*40

$GBGSV,7,4,26,13,,,36,16,,,36,6,,,36,41,,,36,1*47

$GBGSV,7,5,26,2,,,35,26,,,35,7,,,35,9,,,35,1*48

$GBGSV,7,6,26,8,,,34,44,,,33,10,,,32,5,,,32,1*78

$GBGSV,7,7,26,4,,,32,34,,,32,1*41

$GBRMC,122347.517,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122347.517,0.000,757.597,757.597,692.841,2097152,2097152,2097152*63



2025-07-31 20:23:45:635 ==>> $GBGGA,122344.522,2301.2574293,N,11421.9413472,E,1,18,0.81,71.843,M,-1.770,M,,*59

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,41,3,61,191,40,24,54,7,41,1*43

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,36,39,52,5,39,1*44

$GBGSV,7,3,26,42,48,162,40,1,48,126,38,9,48,318,36,2,46,238,35,1*4B

$GBGSV,7,4,26,13,45,220,36,60,41,238,40,8,41,207,34,25,40,275,40,1*41

$GBGSV,7,5,26,7,36,175,35,40,32,160,36,4,32,112,32,38,31,192,36,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,32,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,33,34,10,157,32,1*73

$GBRMC,122344.522,A,2301.2574293,N,11421.9413472,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[D][05:18:26][GNSS]HD8040 GPS
[D][05:18:26][GNSS]GPS diff_sec 124009518, report 0x42 frame
$GBGST,122344.522,1.302,0.236,0.208,0.317,2.086,3.901,8.304*72

[D][05:18:26][COMM]Main Task receive event:131
[D][05:18:26][COMM]index:0,power_mode:0xFF
[D][05:18:26][COMM]index:1,sound_mode:0xFF
[D][05:18:26][COMM]index:2,gsensor_mode:0xFF
[D][05:18:26][COMM]index:3,report_freq_mode:0xFF
[D][05:18:26][COMM]index:4,report_period:0xFF
[D][05:18:26][COMM]index:5,normal_reset_mode:0x

2025-07-31 20:23:45:741 ==>> FF
[D][05:18:26][COMM]index:6,normal_reset_period:0xFF
[D][05:18:26][COMM]index:7,spock_over_speed:0xFF
[D][05:18:26][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:26][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:26][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:26][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:26][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:26][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:26][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:26][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:26][COMM]index:16,imu_config_params:0xFF
[D][05:18:26][COMM]index:17,long_connect_params:0xFF
[D][05:18:26][COMM]index:18,detain_mark:0xFF
[D][05:18:26][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:26][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:26][COMM]index:21,mc_mode:0xFF
[D][05:18:26][COMM]index:22,S_mode:0xFF
[D][05:18:26][COMM]index:23,overweight:0xFF
[D][05:18:26][COMM]index:24,standstill_mode:0xFF
[D][05:18:26][COMM]index:25,night_mode:0xFF
[D][05:18:26][COMM]index:26,experiment1:0xFF
[D][05:18:26][COMM]index:27,experiment2:0xFF
[D][05:18:26][COMM]index:28,experiment3:0xFF
[D][05:18:26][COMM]index:29,experiment4:0xFF
[D

2025-07-31 20:23:45:845 ==>> ][05:18:26][COMM]index:30,night_mode_start:0xFF
[D][05:18:26][COMM]index:31,night_mode_end:0xFF
[D][05:18:26][COMM]index:33,park_report_minutes:0xFF
[D][05:18:26][COMM]index:34,park_report_mode:0xFF
[D][05:18:26][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:26][COMM]index:38,charge_battery_para: FF
[D][05:18:26][COMM]index:39,multirider_mode:0xFF
[D][05:18:26][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:26][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:26][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:26][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:26][COMM]index:44,riding_duration_config:0xFF
[D][05:18:26][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:26][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:26][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:26][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:26][COMM]index:49,mc_load_startup:0xFF
[D][05:18:26][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:26][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:26][COMM]index:52,traffic_mode:0xFF
[D][05:18:26][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:26][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:26][COMM]index:5

2025-07-31 20:23:45:950 ==>> 5,wheel_alarm_play_switch:255
[D][05:18:26][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:26][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:26][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:26][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:26][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:26][COMM]index:63,experiment5:0xFF
[D][05:18:26][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:26][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:26][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:26][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:26][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:26][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:26][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:26][COMM]index:72,experiment6:0xFF
[D][05:18:26][COMM]index:73,experiment7:0xFF
[D][05:18:26][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:26][COMM]index:75,zero_value_from_server:-1
[D][05:18:26][COMM]index:76,multirider_threshold:255
[D][05:18:26][COMM]index:77,experiment8:255
[D][05:18:26][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:26][COMM]index:79,temp_park_tail_light_twinkle_duratio

2025-07-31 20:23:46:056 ==>> n:255
[D][05:18:26][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:26][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:26][COMM]index:83,loc_report_interval:255
[D][05:18:26][COMM]index:84,multirider_threshold_p2:255
[D][05:18:26][COMM]index:85,multirider_strategy:255
[D][05:18:26][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:26][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:26][COMM]index:90,weight_param:0xFF
[D][05:18:26][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:26][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:26][COMM]index:95,current_limit:0xFF
[D][05:18:26][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:26][COMM]index:100,location_mode:0xFF

[W][05:18:26][PROT]remove success[1629955106],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:26][COMM]Main Task receive event:131 finished processing
[D][05:18:26][PROT]index:0 1629955106
[D][05:18:26][PROT]is_send:0
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:1
[D][05:18:26][PROT]

2025-07-31 20:23:46:160 ==>> send_path:0x2
[D][05:18:26][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]sending traceid [9999999999900005]
[D][05:18:26][PROT]Send_TO_M2M [1629955106]
[D][05:18:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:26][SAL ]sock send credit cnt[6]
[D][05:18:26][SAL ]sock send ind credit cnt[6]
[D][05:18:26][M2M ]m2m send data len[294]
[D][05:18:26][SAL ]Cellular task submsg id[10]
[D][05:18:26][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e08] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:27][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B5EEA16BB4AE5CF306CEC1926FB

2025-07-31 20:23:46:251 ==>> A5A3E2A7B4D3926CDE87F2CD9CEE0C3EA641F6D3045E0DF357A514F4B73F5297CFD9AFAFB7861B1948906D3ABD3B43F95EB3597D6F541FD5D975032FFB3463493DFCB84101939911220063E95AB328574F2B3157378520D217218A0BE764F206351EDBD06AC898C2678001B1E20ED093C0D8F0FF8A3
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
$GBGGA,122345.022,2301.2575209,N,11421.9415035,E,1,18,0.81,72.136,M,-1.770,M,,*56

[D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38402 imu

2025-07-31 20:23:46:357 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:23:46:402 ==>>                                                                                                                                                                                                              

2025-07-31 20:23:47:385 ==>> $GBGGA,122347.000,2301.2577818,N,11421.9412353,E,1,18,0.81,72.517,M,-1.770,M,,*5F

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,37,39,52,5,39,1*45

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,40,1*40

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,36,1*71

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,33,41,19,322,36,1*4A

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,41,24,54,7,42,39,52,5,40,42,48,162,39,5*77

$GBGSV,3,2,10,25,40,275,39,40,32,160,34,38,31,192,35,41,19,322,31,5*77

$GBGSV,3,3,10,44,13,106,30,34,10,157,31,5*72

$GBRMC,122347.000,A,2301.2577818,N,11421.9412353,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,122347.000,2.759,0.221,0.200,0.294,2.129,2.449,4.605*75



2025-07-31 20:23:47:535 ==>> [D][05:18:29][COMM]read battery soc:255
[D][05:18:29][COMM]msg 0601 loss. last_tick:35678. cur_tick:40685. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40685


2025-07-31 20:23:48:394 ==>> $GBGGA,122348.000,2301.2578874,N,11421.9411716,E,1,18,0.81,72.682,M,-1.770,M,,*5C

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,37,39,52,5,39,1*45

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,41,1*41

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,36,1*71

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,33,41,19,322,36,1*4A

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,42,24,54,7,43,39,52,5,40,42,48,162,40,5*7B

$GBGSV,3,2,10,25,40,275,39,40,32,160,35,38,31,192,35,41,19,322,31,5*76

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122348.000,A,2301.2578874,N,11421.9411716,E,0.003,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,122348.000,2.796,0.235,0.212,0.311,2.106,2.350,4.252*77



2025-07-31 20:23:49:385 ==>> $GBGGA,122349.000,2301.2579436,N,11421.9411504,E,1,18,0.81,72.771,M,-1.770,M,,*5A

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,37,39,52,5,39,1*45

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,39,8,41,207,34,25,40,275,40,1*4E

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,32,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,42,24,54,7,43,39,52,5,40,42,48,162,41,5*7A

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,35,41,19,322,31,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122349.000,A,2301.2579436,N,11421.9411504,E,0.003,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,122349.000,2.905,0.252,0.226,0.331,2.139,2.334,4.036*78



2025-07-31 20:23:49:490 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 20:23:50:393 ==>> $GBGGA,122350.000,2301.2579871,N,11421.9411549,E,1,18,0.81,72.789,M,-1.770,M,,*53

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,36,39,52,5,39,1*44

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,40,1*40

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,32,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,35,41,19,322,31,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122350.000,A,2301.2579871,N,11421.9411549,E,0.000,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,122350.000,2.892,0.208,0.192,0.276,2.112,2.276,3.832*7B

[D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]CLEAN:0


2025-07-31 20:23:51:394 ==>> $GBGGA,122351.000,2301.2579980,N,11421.9411403,E,1,18,0.81,73.016,M,-1.770,M,,*52

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,36,39,52,5,39,1*44

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,40,1*40

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,36,1*71

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,32,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,36,41,19,322,31,5*7B

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122351.000,A,2301.2579980,N,11421.9411403,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,122351.000,3.078,0.252,0.226,0.330,2.203,2.339,3.764*72



2025-07-31 20:23:51:499 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 20:23:52:392 ==>> $GBGGA,122352.000,2301.2579837,N,11421.9411294,E,1,18,0.81,73.155,M,-1.770,M,,*52

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,36,39,52,5,39,1*44

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,41,1*41

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,36,1*71

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,33,41,19,322,36,1*4A

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,35,41,19,322,31,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122352.000,A,2301.2579837,N,11421.9411294,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,122352.000,3.231,0.267,0.239,0.349,2.277,2.394,3.722*7E



2025-07-31 20:23:53:382 ==>> $GBGGA,122353.000,2301.2579947,N,11421.9411502,E,1,18,0.81,73.225,M,-1.770,M,,*59

$GBGSA,A,3,14,33,24,06,16,39,42,09,13,08,25,07,1.49,0.81,1.25,4*06

$GBGSA,A,3,40,38,10,41,44,34,,,,,,,1.49,0.81,1.25,4*07

$GBGSV,7,1,26,14,74,199,41,33,67,296,42,3,61,191,40,24,54,7,41,1*40

$GBGSV,7,2,26,6,53,341,36,59,52,129,40,16,52,345,36,39,52,5,39,1*44

$GBGSV,7,3,26,42,48,162,40,1,48,126,37,9,48,318,36,2,46,238,35,1*44

$GBGSV,7,4,26,13,45,220,37,60,41,238,40,8,41,207,34,25,40,275,40,1*40

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,33,41,19,322,36,1*4A

$GBGSV,7,7,26,44,13,106,33,34,10,157,32,1*73

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,35,41,19,322,31,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122353.000,A,2301.2579947,N,11421.9411502,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122353.000,3.121,0.229,0.208,0.302,2.209,2.314,3.580*71



2025-07-31 20:23:53:487 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:23:53:517 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:23:53:732 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:23:54:410 ==>> $GBGGA,122354.000,2301.2580060,N,11421.9411756,E,1,23,0.68,73.344,M,-1.770,M,,*5E

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.31,0.68,1.12,4*0C

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,44,34,,1.31,0.68,1.12,4*07

$GBGSV,7,1,26,14,74,198,41,33,67,296,42,3,62,190,40,24,54,7,41,1*43

$GBGSV,7,2,26,6,53,341,36,16,52,345,37,39,52,5,39,59,51,128,41,1*46

$GBGSV,7,3,26,42,48,162,40,9,48,318,36,2,48,240,35,1,46,124,37,1*49

$GBGSV,7,4,26,13,45,220,37,60,43,241,41,8,41,207,34,25,40,275,41,1*4C

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,33,41,19,322,36,1*4A

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,35,41,19,322,31,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122354.000,A,2301.2580060,N,11421.9411756,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,122354.000,3.019,0.212,0.202,0.288,2.145,2.240,3.455*7F



2025-07-31 20:23:55:443 ==>> $GBGGA,122355.000,2301.2580149,N,11421.9411680,E,1,23,0.68,73.379,M,-1.770,M,,*51

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.31,0.68,1.12,4*0C

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,44,34,,1.31,0.68,1.12,4*07

$GBGSV,7,1,26,14,74,198,41,33,67,296,42,3,62,190,40,24,54,7,41,1*43

$GBGSV,7,2,26,6,53,341,36,16,52,345,37,39,52,5,39,59,51,128,41,1*46

$GBGSV,7,3,26,42,48,162,40,9,48,318,36,2,48,240,35,1,46,124,37,1*49

$GBGSV,7,4,26,13,45,220,37,60,43,241,41,8,41,207,35,25,40,275,41,1*4D

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,32,26,23,231,35,5,22,257,32,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,34,34,10,157,32,1*74

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,36,41,19,322,31,5*7B

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122355.000,A,2301.2580149,N,11421.9411680,E,0.000,0.00,310725,,,A,S*34

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6
$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,122355.000,3.078,0.206,0.197,0.280,2.172,2.257,3.412*7A

[D][0

2025-07-31 20:23:55:488 ==>> 5:18:37][COMM]Main Task receive event:142
[D][05:18:37][COMM]###### 48530 imu self test OK ######
[D][05:18:37][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-21,-2,4064]
[D][05:18:37][COMM]Main Task receive event:142 finished processing


2025-07-31 20:23:55:533 ==>>                                          

2025-07-31 20:23:55:611 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:23:55:616 ==>> 检测【打印IMU STATE2】
2025-07-31 20:23:55:620 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:23:55:838 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:37][COMM]YAW data: 32763[32763]
[D][05:18:37][COMM]pitch:-66 roll:0
[D][05:18:37][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:23:55:887 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:23:55:892 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:23:55:899 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:23:55:944 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:23:56:048 ==>> [D][05:18:38][FCTY]get_ext_48v_vol retry i = 0,volt = 18
[D][05:18:3

2025-07-31 20:23:56:108 ==>> 8][FCTY]get_ext_48v_vol retry i = 1,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 2,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 3,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 4,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 5,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 6,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 7,volt = 18
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 8,volt = 18


2025-07-31 20:23:56:158 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:23:56:163 ==>> 检测【检测VBUS电压2】
2025-07-31 20:23:56:169 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:56:532 ==>> $GBGGA,122356.000,2301.2580174,N,11421.9411737,E,1,23,0.68,73.462,M,-1.770,M,,*5C

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.31,0.68,1.12,4*0C

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,44,34,,1.31,0.68,1.12,4*07

$GBGSV,7,1,26,14,74,198,41,33,67,296,42,3,62,190,40,24,54,7,41,1*43

$GBGSV,7,2,26,6,53,341,36,16,52,345,37,39,52,5,39,59,51,128,41,1*46

$GBGSV,7,3,26,42,48,162,40,9,48,318,36,2,48,240,35,1,46,124,37,1*49

$GBGSV,7,4,26,13,45,220,37,60,43,241,41,8,41,207,35,25,40,275,41,1*4D

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,33,26,23,231,35,5,22,257,33,41,19,322,36,1*4B

$GBGSV,7,7,26,44,13,106,33,34,10,157,32,1*73

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,36,41,19,322,31,5*7B

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122356.000,A,2301.2580174,N,11421.9411737,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,122356.000,3.034,0.236,0.223,0.320,2.142,2.220,3.332*73

[W][05:18:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][0

2025-07-31 20:23:56:638 ==>> 5:18:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:38][FCTY]DeviceID    = 460130071539136
[D][05:18:38][FCTY]HardwareID  = 867222087738003
[D][05:18:38][FCTY]MoBikeID    = 9999999999
[D][05:18:38][FCTY]LockID      = FFFFFFFFFF
[D][05:18:38][FCTY]BLEFWVersion= 105
[D][05:18:38][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:38][FCTY]Bat         = 3944 mv
[D][05:18:38][FCTY]Current     = 150 ma
[D][05:18:38][FCTY]VBUS        = 9700 mv
[D][05:18:38][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:38][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:38][FCTY]Acckey1 vol = 5589 mv, Acckey2 vol = 101 mv
[D][05:18:38][FCTY]Bike Type flag is invalied
[D][05:18:38][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:38][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:38][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:38][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:38][FCTY]Bat1         = 3780 mv
[D][05:18:38][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 

2025-07-31 20:23:56:669 ==>> ==========
[D][05:18:38][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:23:56:685 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:57:016 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][05:18:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:38][FCTY]DeviceID    = 460130071539136
[D][05:18:38][FCTY]HardwareID  = 867222087738003
[D][05:18:38][FCTY]MoBikeID    = 9999999999
[D][05:18:38][FCTY]LockID      = FFFFFFFFFF
[D][05:18:38][FCTY]BLEFWVersion= 105
[D][05:18:38][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:38][FCTY]Bat         = 3944 mv
[D][05:18:38][FCTY]Current     = 150 ma
[D][05:18:38][FCTY]VBUS        = 9700 mv
[D][05:18:38][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:38][FCTY]Ext battery vol = 4, adc = 178
[D][05:18:38][FCTY]Acckey1 vol = 5574 mv, Acckey2 vol = 0 mv
[D][05:18:38][FCTY]Bike Type flag is invalied
[D][05:18:38][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:39][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:39][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:39][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:39][FCTY]Bat1         = 3780 mv
[D][05:18:39][FCTY]=========

2025-07-31 20:23:57:046 ==>> =========== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:23:57:225 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:57:383 ==>> $GBGGA,122357.000,2301.2580163,N,11421.9411739,E,1,23,0.68,73.506,M,-1.770,M,,*56

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.31,0.68,1.12,4*0C

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,44,34,,1.31,0.68,1.12,4*07

$GBGSV,7,1,26,14,74,198,41,33,67,296,42,3,62,190,40,24,54,7,41,1*43

$GBGSV,7,2,26,6,53,341,36,16,52,345,37,39,52,5,39,59,51,128,40,1*47

$GBGSV,7,3,26,42,48,162,40,9,48,318,36,2,48,240,35,1,46,124,37,1*49

$GBGSV,7,4,26,13,45,220,37,60,43,241,40,8,41,207,35,25,40,275,41,1*4C

$GBGSV,7,5,26,7,36,175,35,40,32,160,37,4,32,112,32,38,31,192,37,1*70

$GBGSV,7,6,26,10,28,187,33,26,23,231,35,5,22,257,33,41,19,322,37,1*4A

$GBGSV,7,7,26,44,13,106,33,34,10,157,32,1*73

$GBGSV,3,1,10,33,67,296,43,24,54,7,43,39,52,5,40,42,48,162,41,5*7B

$GBGSV,3,2,10,25,40,275,40,40,32,160,35,38,31,192,36,41,19,322,32,5*78

$GBGSV,3,3,10,44,13,106,30,34,10,157,32,5*71

$GBRMC,122357.000,A,2301.2580163,N,11421.9411739,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,122357.000,3.171,0.224,0.211,0.305,2.215,2.285,3.340*7C



2025-07-31 20:23:57:670 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:39][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========
[D][05:18:39][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:39][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:39][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:39][FCTY]DeviceID    = 460130071539136
[D][05:18:39][FCTY]HardwareID  = 867222087738003
[D][05:18:39][FCTY]MoBikeID    = 9999999999
[D][05:18:39][FCTY]LockID      = FFFFFFFFFF
[D][05:18:39][FCTY]BLEFWVersion= 105
[D][05:18:39][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:39][FCTY]Bat         = 3844 mv
[D][05:18:39][FCTY]Current     = 0 ma
[D][05:18:39][FCTY]VBUS        = 9700 mv
[D][05:18:39][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:39][FCTY]Ext battery vol = 3, adc = 149
[D][05:18:39][FCTY]Acckey1 vol = 5586 mv, Acckey2 vol = 0 mv
[D][05:18:39][FCTY]Bike Type flag is invalied
[D][05:18:39][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:39][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:39][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:39][FCTY]CAT1_GNSS_VERSION = V3465b5b1


2025-07-31 20:23:57:714 ==>> 
[D][05:18:39][FCTY]Bat1         = 3780 mv
[D][05:18:39][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:23:57:762 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:58:451 ==>> [D][05:18:40][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:40][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:40][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:40][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========
[D][05:18:40][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:40][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:40][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:40][FCTY]DeviceID    = 460130071539136
[D][05:18:40][FCTY]HardwareID  = 867222087738003
[D][05:18:40][FCTY]MoBikeID    = 9999999999
[D][05:18:40][FCTY]LockID      = FFFFFFFFFF
[D][05:18:40][FCTY]BLEFWVersion= 105
[D][05:18:40][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:40][FCTY]Bat         = 3844 mv
[D][05:18:40][FCTY]Current     = 0 ma
[D][05:18:40][FCTY]VBUS        = 9700 mv
[D][05:18:40][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:40][FCTY]Ext battery vol = 3, adc = 132
[D][05:18:40][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 50 mv
[D][05:18:40][FCTY]Bike Type flag is invalied
[D][05:18:40][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:40

2025-07-31 20:23:58:555 ==>> ][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:40][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:40][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:40][FCTY]Bat1         = 3780 mv
[D][05:18:40][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========
[D][05:18:40][COMM]Main Task receive event:65
[D][05:18:40][COMM]main task tmp_sleep_event = 80
[D][05:18:40][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:40][COMM]Main Task receive event:65 finished processing
[D][05:18:40][COMM]Main Task receive event:60
[D][05:18:40][COMM]smart_helmet_vol=255,255
[D][05:18:40][COMM]BAT CAN get state1 Fail 204
[D][05:18:40][COMM]BAT CAN get soc Fail, 204
[W][05:18:40][GNSS]stop locating
[D][05:18:40][GNSS]stop event:8
[D][05:18:40][GNSS]GPS stop. ret=0
[D][05:18:40][GNSS]all continue location stop
[D][05:18:40][COMM]report elecbike
[D][05:18:40][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[D][05:18:40][CAT1]gsm read msg sub id: 24
[W][05:18:40][PROT]remove success[1629955120],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:40][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:40]

2025-07-31 20:23:58:571 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:23:58:662 ==>> [PROT]index:0
[D][05:18:40][PROT]is_send:1
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:3
[D][05:18:40][PROT]send_path:0x3
[D][05:18:40][PROT]msg_type:0x5d03
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]Sending traceid[9999999999900006]
[D][05:18:40][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:40][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:40][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:40][PROT]index:0 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:3
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]=================================

2025-07-31 20:23:58:766 ==>> ==========================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[W][05:18:40][PROT]add success [1629955120],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:40][COMM]Main Task receive event:60 finished processing
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 24, ret: 6
[D][05:18:40][CAT1]sub id: 24, ret: 6

[D][05:18:40][CAT1]gsm read msg sub id: 15
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B3159CC

2025-07-31 20:23:58:841 ==>> AB7A138F8ED10BA93B58B46D5D1EDF746816B70BCD22F13647AB6BE934DE0E20CEA8D6826805B50B23C9C25396EB5F3FF7A9A81579E68F6B60DA7BF15D0038B2223234937629AF8043567AA74B2FF8A
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]


2025-07-31 20:23:59:112 ==>>                                                                                                                                            [W][05:18:41][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:41][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========
[D][05:18:41][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:41][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:41][FCTY]DeviceID    = 460130071539136
[D][05:18:41][FCTY]HardwareID  = 867222087738003
[D][05:18:41][FCTY]MoBikeID    = 9999999999
[D][05:18:41][FCTY]LockID      = FFFFFFFFFF
[D][05:18:41][FCTY]BLEFWVersion= 105
[D][05:18:41][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:18:41][FCTY]Bat         = 3844 mv
[D][05:18:41][FCTY]Current     = 0 ma
[D][05:18:41][FCTY]VBUS        = 5000 mv
[D][05:18:41][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:41][FCTY]Ext battery vol = 2, adc = 101
[D][05:18:41][FCTY]Acckey1 vol = 5574 mv, Acckey2 vol = 0 mv
[D][05:18:41][FCTY]Bike Type flag is invalied
[D][05:18:41][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:41][FCTY]CAT1_KER

2025-07-31 20:23:59:157 ==>> NEL_RTK = 1.2.4
[D][05:18:41][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:41][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:41][FCTY]Bat1         = 3780 mv
[D][05:18:41][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:23:59:356 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:23:59:363 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:23:59:384 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:23:59:442 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:23:59:547 ==>> [D][05:18:41][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
[D][05:18:41][COMM]read battery soc:255


2025-07-31 20:23:59:641 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:23:59:647 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:23:59:655 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:23:59:742 ==>> 5A A5 04 5A A5 


2025-07-31 20:23:59:847 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:23:59:911 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:23:59:919 ==>> 检测【打开WIFI(3)】
2025-07-31 20:23:59:941 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:24:00:168 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:24:00:445 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:24:00:452 ==>> 检测【扩展芯片hw】
2025-07-31 20:24:00:475 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:24:00:635 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:42][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:24:00:733 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:24:00:742 ==>> 检测【扩展芯片boot】
2025-07-31 20:24:00:756 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:24:00:761 ==>> 检测【扩展芯片sw】
2025-07-31 20:24:00:774 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:24:00:780 ==>> 检测【检测音频FLASH】
2025-07-31 20:24:00:789 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:24:00:907 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:24:00:996 ==>> +WIFISCAN:4,0,CC057790A620,-58
+WIFISCAN:4,1,CC057790A621,-58
+WIFISCAN:4,2,CC057790A5C1,-78
+WIFISCAN:4,3,F86FB0660A82,-83

[D][05:18:43][CAT1]wifi scan report total[4]


2025-07-31 20:24:01:101 ==>> [D][05:18:43][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:43][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:0------------
[D][05:18:43][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:24:01:829 ==>>                                                              
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:1------------
[D][05:18:43][COMM]more than the number of battery plugs
[D][05:18:43][COMM]VBUS is 1
[D][05:18:43][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:43][COMM]file:B50 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:43][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:43][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:43][COMM]Bat auth off fail, error:-1
[D][05:18:43][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:1------------
[D][05:18:43][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:1------------
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]file:B50 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[9

2025-07-31 20:24:01:934 ==>> 20].cmd file 'B50'
[D][05:18:43][COMM]read file, len:10800, num:3
[D][05:18:43][COMM]Main Task receive event:65
[D][05:18:43][COMM]main task tmp_sleep_event = 80
[D][05:18:43][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:43][COMM]Main Task receive event:65 finished processing
[D][05:18:43][COMM]Main Task receive event:66
[D][05:18:43][COMM]Try to Auto Lock Bat
[D][05:18:43][COMM]Main Task receive event:66 finished processing
[D][05:18:43][COMM]Main Task receive event:60
[D][05:18:43][COMM]smart_helmet_vol=255,255
[D][05:18:43][COMM]BAT CAN get state1 Fail 204
[D][05:18:43][COMM]BAT CAN get soc Fail, 204
[D][05:18:43][COMM]get soc error
[E][05:18:43][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:43][COMM]report elecbike
[D][05:18:43][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[D][05:18:43][COMM]Receive Bat Lock cmd 0
[D][05:18:43][COMM]VBUS is 1
[D][05:18:43][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:43][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:43][COMM]--->crc16:0xb8a
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel pa

2025-07-31 20:24:02:039 ==>> rse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:43][PROT]remove success[1629955123],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]index:1
[D][05:18:43][PROT]is_send:1
[D][05:18:43][PROT]sequence_num:6
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:3
[D][05:18:43][PROT]send_path:0x3
[D][05:18:43][PROT]msg_type:0x5d03
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]Sending traceid[9999999999900007]
[D][05:18:43][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:43][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:43][PROT]ble is not inited or not connected 

2025-07-31 20:24:02:146 ==>> or cccd not enabled
[W][05:18:43][PROT]add success [1629955123],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:43][COMM]Main Task receive event:60 finished processing
[D][05:18:43][COMM]Main Task receive event:61
[D][05:18:43][COMM][D301]:type:3, trace id:280
[D][05:18:43][COMM]id[], hw[000
[D][05:18:43][COMM]get mcMaincircuitVolt error
[D][05:18:43][COMM]get mcSubcircuitVolt error
[D][05:18:43][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:43][COMM]BAT CAN get state1 Fail 204
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:43][COMM]BAT CAN get soc Fail, 204
[D][05:18:43][COMM]get bat work state err
[W][05:18:43][PROT]remove success[1629955123],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:43][PROT]add success [1629955123],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:43][COMM]Main Task receive event:61 finished processing
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:43][M2M ]m2m_task: control_queue type:[M2M_GSM

2025-07-31 20:24:02:249 ==>> _POWER_ON]
[D][05:18:43][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_proces

2025-07-31 20:24:02:324 ==>> s].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][GNSS]recv submsg id[3]


2025-07-31 20:24:03:547 ==>> [D][05:18:45][PROT]CLEAN,SEND:0
[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:6
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:3
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]sending traceid [9999999999900007]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[198]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE300844194C9B5

2025-07-31 20:24:03:652 ==>> 4F6ADAC49D303A3B5439D94D9FDA59420BC92C7C8BFE611D815C75C08301B43C8F2BFCB95E2BEBD79B18D2930A532ED2DACE8F072D4A624D878030681845AE6E451B4C6CE73EE11B95CBC7F
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]
                                         

2025-07-31 20:24:04:222 ==>> [D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:24:04:985 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:24:05:045 ==>>                                                ]flash test ok


2025-07-31 20:24:05:318 ==>> [D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:47][COMM]58396 imu init OK
[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:47][COMM]accel parse set 0
[D][05:18:47][COMM][Audio].l:[1012].open hexlog save
[D][05:18:47][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:24:05:574 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:24:05:847 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:24:05:852 ==>> 检测【打开喇叭声音】
2025-07-31 20:24:05:861 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:24:06:570 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:48][COMM]read file, len:15228, num:4
[D][05:18:48][COMM]--->crc16:0x419c
[D][05:18:48][COMM]read file success
[W][05:18:48][COMM][Audio].l:[936].close hexlog save
[D][05:18:48][COMM]accel parse set 1
[D][05:18:48][COMM][Audio]mon:9,05:18:48
[D][05:18:48][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_start].

2025-07-31 20:24:06:649 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:24:06:660 ==>> 检测【打开大灯控制】
2025-07-31 20:24:06:682 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:24:06:694 ==>> l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:48][COMM]59408 imu init OK
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:48][COMM]f:[ec800m_audio

2025-07-31 20:24:06:781 ==>> _play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:24:06:856 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:24:06:920 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:24:06:929 ==>> 检测【关闭仪表供电3】
2025-07-31 20:24:06:950 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:24:07:115 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:49][COMM]set POWER 0


2025-07-31 20:24:07:193 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:24:07:200 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:24:07:220 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:24:07:326 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:24:07:469 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:24:07:475 ==>> 检测【读大灯电压】
2025-07-31 20:24:07:485 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:24:07:643 ==>> [D][05:18:49][COMM]read battery soc:255
[W][05:18:49][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:49][COMM]arm_hub read adc[5],val[33062]


2025-07-31 20:24:07:753 ==>> 【读大灯电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:24:07:759 ==>> 检测【关闭大灯控制2】
2025-07-31 20:24:07:768 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:24:07:904 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:24:08:028 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:24:08:041 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:24:08:061 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:24:08:235 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:50][COMM]arm_hub read adc[5],val[115]


2025-07-31 20:24:08:306 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:24:08:316 ==>> 检测【打开WIFI(4)】
2025-07-31 20:24:08:342 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:24:08:703 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:50][PROT]CLEAN,SEND:1
[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]exec over: func id: 12, ret: 6
[D][05:18:50][PROT]index:1 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:6
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900007]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[198]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], 

2025-07-31 20:24:08:747 ==>> len[198], data[0x20052de0] format[0]
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 20:24:08:978 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:24:08:989 ==>> 检测【EC800M模组版本】
2025-07-31 20:24:09:011 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:24:09:265 ==>> [D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BED3D3BBEBBE593DEC398FD92DFBA253F7502717257C7439370F60977CEBC11DFA434B7D93931AFA122718B61F56488E3024C32007E4F6C89B8D7F57F09F9508E6EA9B4473C170C839962ECB28E460C5D29B4F
[D][05:18:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
[W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:51][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:51][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:24:09:595 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:24:09:601 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:24:09:607 ==>> 检测【配置蓝牙地址】
2025-07-31 20:24:09:626 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:24:09:809 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D066EFBAE324>】
2025-07-31 20:24:09:821 ==>> [W][05:18:51][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:24:10:069 ==>> recv ble 1
recv ble 2
ble set mac ok :d0,66,ef,ba,e3,24
enable filters ret : 0[D][05:18:52][COMM]63224 imu init OK
[D][05:18:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:24:10:348 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:24:10:356 ==>> 检测【BLETEST】
2025-07-31 20:24:10:363 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:24:10:449 ==>> 4A A4 01 A4 4A 


2025-07-31 20:24:10:554 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D066EFBAE324*RSSI:-20*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D066EFBAE32499999OVER 150


2025-07-31 20:24:11:072 ==>> [D][05:18:53][COMM]64235 imu init OK
[D][05:18:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:24:11:380 ==>> 【BLETEST】通过,【-20dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:24:11:394 ==>> 该项需要延时执行
2025-07-31 20:24:11:694 ==>> [D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:53][COMM]read battery soc:255
[D][05:18:53][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:53][COMM]accel parse set 0
[D][05:18:53][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:24:12:073 ==>> [D][05:18:54][COMM]65246 imu init OK


2025-07-31 20:24:13:299 ==>> +WIFISCAN:4,0,CC057790A621,-56
+WIFISCAN:4,1,CC057790A5C1,-77
+WIFISCAN:4,2,CC057790A6E1,-78
+WIFISCAN:4,3,F86FB0660A82,-83

[D][05:18:55][CAT1]wifi scan report total[4]


2025-07-31 20:24:13:602 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:24:13:782 ==>> [D][05:18:55][GNSS]recv submsg id[3]


2025-07-31 20:24:14:472 ==>> [D][05:18:56][PROT]CLEAN,SEND:1
[D][05:18:56][PROT]index:1 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:6
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900007]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88BE0A1A09FFC950D4A957A97F7DD664AE7FAC64C3BE1CEDE52444C4ACAF54A5E4CF

2025-07-31 20:24:14:547 ==>> 758D5369DF70462D8992C87D48751EDDC21A2BF33B1589BE06036106040CABF1FBA0B6315DA5940451A5642C38CFC34943C1
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 20:24:15:612 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:24:17:603 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:24:19:715 ==>> [D][05:19:01][PROT]CLEAN,SEND:1
[D][05:19:01][PROT]CLEAN:1
[D][05:19:01][PROT]index:0 1629955141
[D][05:19:01][PROT]is_send:0
[D][05:19:01][PROT]sequence_num:5
[D][05:19:01][PROT]retry_timeout:0
[D][05:19:01][PROT]retry_times:2
[D][05:19:01][PROT]send_path:0x2
[D][05:19:01][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:01][PROT]===========================================================
[W][05:19:01][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955141]
[D][05:19:01][PROT]===========================================================
[D][05:19:01][PROT]sending traceid [9999999999900006]
[D][05:19:01][PROT]Send_TO_M2M [1629955141]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:01][SAL ]sock send credit cnt[6]
[D][05:19:01][SAL ]sock send ind credit cnt[6]
[D][05:19:01][M2M ]m2m send data len[198]
[D][05:19:01][SAL ]Cellular task submsg id[10]
[D][05:19:01][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:01][CAT1]gsm read msg sub id: 15
[D][05:19:01][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:01][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B3D149CE49AB637E0734025C16EBAD01542A65F8F961C806BDEA55DD35ACED6E7A24678AD9DCD189B

2025-07-31 20:24:19:790 ==>> 2F597A1B1E2E6EAD3F12D8AEB5EE032C8DC2CB9FE11B2825E48C9E2D3913A8589130B64486BB26F61CA88
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:01][PROT]M2M Send ok [1629955141]
[D][05:19:01][COMM]read battery soc:255


2025-07-31 20:24:21:385 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:24:21:391 ==>> 检测【检测WiFi结果】
2025-07-31 20:24:21:400 ==>> WiFi信号:【CC057790A620】,信号值:-61
2025-07-31 20:24:21:406 ==>> WiFi信号:【CC057790A621】,信号值:-61
2025-07-31 20:24:21:431 ==>> WiFi信号:【44A1917CAD80】,信号值:-77
2025-07-31 20:24:21:437 ==>> WiFi信号:【CC057790A6E1】,信号值:-79
2025-07-31 20:24:21:462 ==>> WiFi信号:【CC057790A5C1】,信号值:-78
2025-07-31 20:24:21:469 ==>> WiFi信号:【F86FB0660A82】,信号值:-83
2025-07-31 20:24:21:479 ==>> WiFi数量【6】, 最大信号值:-61
2025-07-31 20:24:21:495 ==>> 检测【检测GPS结果】
2025-07-31 20:24:21:505 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 20:24:21:525 ==>> 
北斗星号:【14】,信号值:【41】
北斗星号:【33】,信号值:【41】
北斗星号:【3】,信号值:【40】
北斗星号:【24】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【59】,信号值:【40】
北斗星号:【16】,信号值:【36】
北斗星号:【39】,信号值:【39】
北斗星号:【42】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【9】,信号值:【36】
北斗星号:【2】,信号值:【35】
北斗星号:【13】,信号值:【36】
北斗星号:【60】,信号值:【40】
北斗星号:【25】,信号值:【40】
北斗星号:【7】,信号值:【35】
北斗星号:【40】,信号值:【36】
北斗星号:【38】,信号值:【36】
北斗星号:【26】,信号值:【35】
北斗星号:【41】,信号值:【36】

2025-07-31 20:24:21:536 ==>> 检测【CSQ强度】
2025-07-31 20:24:21:554 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:24:21:673 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][COMM]read battery soc:255
[D][05:19:03][CAT1]<<< 
+CSQ: 31,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:24:21:941 ==>> 【CSQ强度】通过,【31】符合目标值【18】至【31】要求!
2025-07-31 20:24:21:947 ==>> 检测【关闭GSM联网】
2025-07-31 20:24:21:968 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:24:22:134 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:04][COMM]GSM test
[D][05:19:04][COMM]GSM test disable


2025-07-31 20:24:22:214 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:24:22:220 ==>> 检测【4G联网测试】
2025-07-31 20:24:22:230 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:24:22:406 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 20:24:23:289 ==>> [D][05:19:04][COMM]Main Task receive event:14
[D][05:19:04][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955144, allstateRepSeconds = 0
[D][05:19:04][COMM]index:0,power_mode:0xFF
[D][05:19:04][COMM]index:1,sound_mode:0xFF
[D][05:19:04][COMM]index:2,gsensor_mode:0xFF
[D][05:19:04][COMM]index:3,report_freq_mode:0xFF
[D][05:19:04][COMM]index:4,report_period:0xFF
[D][05:19:04][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:04][COMM]index:6,normal_reset_period:0xFF
[D][05:19:04][COMM]index:7,spock_over_speed:0xFF
[D][05:19:04][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:04][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:04][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:04][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:04][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:04][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:04][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:04][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:04][COMM]index:16,imu_config_params:0xFF
[D][05:19:04][COMM]index:17,long_connect_params:0xFF
[D][05:19:04][COMM]index:18,detain_mark:0xFF
[D][05:19:04][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:04][COMM]index:20,lock_pos_report_int

2025-07-31 20:24:23:394 ==>> erval:0xFF
[D][05:19:04][COMM]index:21,mc_mode:0xFF
[D][05:19:04][COMM]index:22,S_mode:0xFF
[D][05:19:04][COMM]index:23,overweight:0xFF
[D][05:19:04][COMM]index:24,standstill_mode:0xFF
[D][05:19:04][COMM]index:25,night_mode:0xFF
[D][05:19:04][COMM]index:26,experiment1:0xFF
[D][05:19:04][COMM]index:27,experiment2:0xFF
[D][05:19:04][COMM]index:28,experiment3:0xFF
[D][05:19:04][COMM]index:29,experiment4:0xFF
[D][05:19:04][COMM]index:30,night_mode_start:0xFF
[D][05:19:04][COMM]index:31,night_mode_end:0xFF
[D][05:19:04][COMM]index:33,park_report_minutes:0xFF
[D][05:19:04][COMM]index:34,park_report_mode:0xFF
[D][05:19:04][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:04][COMM]index:38,charge_battery_para: FF
[D][05:19:04][COMM]index:39,multirider_mode:0xFF
[D][05:19:04][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:04][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:04][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:04][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:04][COMM]index:44,riding_duration_config:0xFF
[D][05:19:04][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:04][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:04][COMM]index:

2025-07-31 20:24:23:499 ==>> 47,bat_info_rep_cfg:0xFF
[D][05:19:04][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:04][COMM]index:49,mc_load_startup:0xFF
[D][05:19:04][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:04][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:04][COMM]index:52,traffic_mode:0xFF
[D][05:19:04][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:04][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:04][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:04][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:04][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:04][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:04][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:04][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:04][COMM]index:63,experiment5:0xFF
[D][05:19:04][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:04][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:04][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:04][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:04][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:04][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:04][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:04][

2025-07-31 20:24:23:604 ==>> COMM]index:72,experiment6:0xFF
[D][05:19:04][COMM]index:73,experiment7:0xFF
[D][05:19:04][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:04][COMM]index:75,zero_value_from_server:-1
[D][05:19:04][COMM]index:76,multirider_threshold:255
[D][05:19:04][COMM]index:77,experiment8:255
[D][05:19:04][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:04][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:04][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:04][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:04][COMM]index:83,loc_report_interval:255
[D][05:19:04][COMM]index:84,multirider_threshold_p2:255
[D][05:19:04][COMM]index:85,multirider_strategy:255
[D][05:19:04][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:04][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:04][COMM]index:90,weight_param:0xFF
[D][05:19:04][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:04][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:04][COMM]index:95,current_limit:0xFF
[D][05:19:04][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:04][COMM]index:100,location_mode:0xFF

[W][05:19:04][PROT]remove succe

2025-07-31 20:24:23:709 ==>> ss[1629955144],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:04][PROT]add success [1629955144],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:04][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:04][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:04][PROT]index:0 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:8
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:1
[D][05:19:04][PROT]send_path:0x2
[D][05:19:04][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:04][PROT]===========================================================
[W][05:19:04][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955144]
[D][05:19:04][PROT]===========================================================
[D][05:19:04][PROT]sending traceid [9999999999900009]
[D][05:19:04][PROT]Send_TO_M2M [1629955144]
[D][05:19:04][CAT1]gsm read msg sub id: 13
[D][05:19:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:04][SAL ]sock send credit cnt[6]
[D][05:19:04][SAL ]sock send ind credit cnt[6]
[D][05:19:04][M2M ]m2m send data len[294]
[D][05:19:04][SAL ]Cellular task submsg id[10]
[D][05:19:0

2025-07-31 20:24:23:814 ==>> 4][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]<<< 
+CSQ: 31,99

OK

[D][05:19:04][CAT1]exec over: func id: 13, ret: 21
[D][05:19:04][M2M ]get csq[31]
[D][05:19:04][CAT1]gsm read msg sub id: 15
[D][05:19:04][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:04][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B1F6FFFFD4928F24EC48CECE84800D14F068600B7DDD0DF166D468AF1C083BBE746B2A43D77254FF9DD78D4784C7DF1F71945431639E737B766D8FF999D5032B19C048D66793A7DCC5DC05449793AF95674A3771C57D69625CECE1296F7C4CD47B76059F6E8F3A58F56A03D4BA4C224F10FFB08C4B988C3244EC31869FC9DCD5E35A34
[D][05:19:04][CAT1]<<< 
SEND OK

[D][05:19:04][CAT1]exec over: func id: 15, ret: 11
[D][05:19:04][CAT1]sub id: 15, ret: 11

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:04][M2M ]g_m2m_is_idle become true
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:04][PROT]M2M Send ok [1629955144]
>>>>>RESEND ALLSTATE<<<<<
[D][05:19:05][HS

2025-07-31 20:24:23:919 ==>> DK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:19:05][PROT]remove success[1629955145],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:05][PROT]add success [1629955145],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:05][COMM]------>period, report file manifest
[D][05:19:05][COMM]Main Task receive event:14 finished processing
[D][05:19:05][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:05][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:05][CAT1]gsm read msg sub id: 21
[D][05:19:05][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]cell info report total[0]
[D][05:19:05][CAT1]exec over: func id: 21, ret: 6
                                         

2025-07-31 20:24:24:259 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:24:24:270 ==>> 检测【关闭GPS】
2025-07-31 20:24:24:281 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:24:24:447 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:06][GNSS]stop locating
[D][05:19:06][GNSS]all continue location stop
[W][05:19:06][GNSS]stop locating
[D][05:19:06][GNSS]all sing location stop


2025-07-31 20:24:24:547 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:24:24:553 ==>> 检测【清空消息队列2】
2025-07-31 20:24:24:564 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:24:24:741 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:06][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:24:24:844 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:24:24:851 ==>> 检测【轮动检测】
2025-07-31 20:24:24:862 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:24:24:952 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:24:25:027 ==>> [D][05:19:07][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:24:25:348 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:24:25:453 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:24:25:625 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:24:25:638 ==>> 检测【关闭小电池】
2025-07-31 20:24:25:659 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:24:25:670 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 20:24:25:741 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:24:25:899 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:24:25:909 ==>> 检测【进入休眠模式】
2025-07-31 20:24:25:921 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:24:26:182 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:08][COMM]Main Task receive event:28
[D][05:19:08][COMM]main task tmp_sleep_event = 8
[D][05:19:08][COMM]prepare to sleep
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:24:26:990 ==>> [D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][M2M ]tcpclient close[4]
[D][05:19:09][SAL ]Cellular task submsg id[12]
[D][05:19:09][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:09][CAT1]gsm read msg sub id: 9
[D][05:19:09][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 9, ret: 6
[D][05:19:09][CAT1]sub id: 9, ret: 6

[D][05:19:09][SAL ]Cellular task submsg id[68]
[D][05:19:09][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:09][SAL ]socket close ind. id[4]
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok
[D][05:19:09][CAT1]pdpdeact urc len[22]


2025-07-31 20:24:27:296 ==>> [E][05:19:09][COMM]1x1 rx timeout
[D][05:19:09][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:24:27:813 ==>> [D][05:19:09][COMM]read battery soc:255
[E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[W][05:19:09][COMM]CAN STOP!
[D][05:19:09][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:09][COMM]------------ready to Power off Acckey 1------------
[D][05:19:09][COMM]------------ready to Power off Acckey 2------------
[D][05:19:09][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:09][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1302
[D][05:19:09][COMM]bat sleep fail, reason:-1
[D][05:19:09][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:09][COMM]accel parse set 0
[D][05:19:09][COMM]imu rest ok. 80865
[D][05:19:09][COMM]imu sleep 0
[W][05:19:09][COMM]now sleep


2025-07-31 20:24:27:982 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:24:27:989 ==>> 检测【检测33V休眠电流】
2025-07-31 20:24:28:000 ==>> 开始33V电流采样
2025-07-31 20:24:28:028 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:28:083 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:24:29:084 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:24:29:130 ==>> Current33V:????:16.09

2025-07-31 20:24:29:598 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:29:605 ==>> 【检测33V休眠电流】通过,【16.09uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:24:29:617 ==>> 该项需要延时执行
2025-07-31 20:24:31:619 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:24:31:630 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:24:31:657 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:24:31:757 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1671mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 2022mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:24:32:695 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:24:32:706 ==>> 检测【打开小电池2】
2025-07-31 20:24:32:717 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:24:32:754 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:24:33:018 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:24:33:024 ==>> 该项需要延时执行
2025-07-31 20:24:33:525 ==>> 此处延时了:【500】毫秒
2025-07-31 20:24:33:560 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:24:33:578 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:24:33:646 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:24:34:078 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:24:34:089 ==>> 该项需要延时执行
2025-07-31 20:24:34:353 ==>> [D][05:19:16][COMM]------------ready to Power on Acckey 1------------
[D][05:19:16][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:16][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:16][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:16][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:16][COMM]----- get Acckey 1 and value:1------------
[W][05:19:16][COMM]CAN START!
[D][05:19:16][CAT1]gsm read msg sub id: 12
[D][05:19:16][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:16][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 87325
[D][05:19:16][COMM][Audio]exec status ready.
[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]exec over: func id: 12, ret: 6
[D][05:19:16][COMM]im

2025-07-31 20:24:34:398 ==>> u wakeup ok. 87339
[D][05:19:16][COMM]imu wakeup 1
[W][05:19:16][COMM]wake up system, wakeupEvt=0x80
[D][05:19:16][COMM]frm_can_weigth_power_set 1
[D][05:19:16][COMM]Clear Sleep Block Evt
[D][05:19:16][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:16][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:24:34:592 ==>> 此处延时了:【500】毫秒
2025-07-31 20:24:34:604 ==>> 检测【进入休眠模式2】
2025-07-31 20:24:34:626 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:24:34:639 ==>> [D][05:19:16][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[E][05:19:16][COMM]1x1 rx timeout
[D][05:19:16][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:24:34:697 ==>>                                                                                                                                           87820. period:50
[D][05:19:16][COMM]msg 

2025-07-31 20:24:34:757 ==>> 02A5 loss. last_tick:87310. cur_tick:87820. period:50
[D][05:19:16][COMM]msg 02A6 loss. last_tick:87310. cur_tick:87820. period:50
[D][05:19:16][COMM]msg 02A7 loss. last_tick:87310. cur_tick:87821. period:50
[D][05:19:16][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 87821
[D][05:19:16][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 87822
[W][05:19:16][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:24:35:062 ==>> [E][05:19:17][COMM]1x1 rx timeout
[E][05:19:17][COMM]1x1 tp timeout
[E][05:19:17][COMM]1x1 error -3.
[D][05:19:17][COMM]Main Task receive event:28 finished processing
[D][05:19:17][COMM]Main Task receive event:28
[D][05:19:17][COMM]prepare to sleep
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 6
[D][05:19:17][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:24:35:367 ==>> [D][05:19:17][COMM]msg 0220 loss. last_tick:87310. cur_tick:88316. period:100
[D][05:19:17][COMM]msg 0221 loss. last_tick:87310. cur_tick:88316. period:100
[D][05:19:17][COMM]msg 0224 loss. last_tick:87310. cur_tick:88317. period:100
[D][05:19:17][COMM]msg 0260 loss. last_tick:87310. cur_tick:88317. period:100
[D][05:19:17][COMM]msg 0280 loss. last_tick:87310. cur_tick:88317. period:100
[D][05:19:17][COMM]msg 02C0 loss. last_tick:87310. cur_tick:88318. period:100
[D][05:19:17][COMM]msg 02C1 loss. last_tick:87310. cur_tick:88318. period:100
[D][05:19:17][COMM]msg 02C2 loss. last_tick:87310. cur_tick:88318. period:100
[D][05:19:17][COMM]msg 02E0 loss. last_tick:87310. cur_tick:88319. period:100
[D][05:19:17][COMM]msg 02E1 loss. last_tick:87310. cur_tick:88319. period:100
[D][05:19:17][COMM]msg 02E2 loss. last_tick:87310. cur_tick:88320. period:100
[D][05:19:17][COMM]msg 0300 loss. last_tick:87310. cur_tick:88320. period:100
[D][05:19:17][COMM]msg 0301 loss. last_tick:87310. cur_tick:88320. period:100
[D][05:19:17][COMM]bat msg 0240 loss. last_tick:87310. cur_tick:88321. period:100. j,i:1 54
[D][05:19:17][COMM]bat msg 0241 loss. last_tick:87310. cur_tick:88321. period:100. j,i:2 

2025-07-31 20:24:35:472 ==>> 55
[D][05:19:17][COMM]bat msg 0242 loss. last_tick:87310. cur_tick:88321. period:100. j,i:3 56
[D][05:19:17][COMM]bat msg 0244 loss. last_tick:87310. cur_tick:88322. period:100. j,i:5 58
[D][05:19:17][COMM]bat msg 024E loss. last_tick:87310. cur_tick:88322. period:100. j,i:15 68
[D][05:19:17][COMM]bat msg 024F loss. last_tick:87310. cur_tick:88322. period:100. j,i:16 69
[D][05:19:17][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 88323
[D][05:19:17][COMM]CAN message bat fault change: 0x00000000->0x0001802E 88323
[D][05:19:17][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 88324
                                                                              

2025-07-31 20:24:35:654 ==>> [D][05:19:17][COMM]msg 0222 loss. last_tick:87310. cur_tick:88818. period:150
[D][05:19:17][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 88819


2025-07-31 20:24:35:744 ==>>             ][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:17][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:17][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:24:35:954 ==>> [E][05:19:17][COMM]1x1 rx timeout
[E][05:19:17][COMM]1x1 tp timeout
[E][05:19:17][COMM]1x1 error -3.
[W][05:19:17][COMM]CAN STOP!
[D][05:19:17][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:17][COMM]------------ready to Power off Acckey 1------------
[D][05:19:17][COMM]------------ready to Power off Acckey 2------------
[D][05:19:17][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:17][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 102
[D][05:19:17][COMM]bat sleep fail, reason:-1
[D][05:19:17][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:17][COMM]accel parse set 0
[D][05:19:17][COMM]imu rest ok. 89007
[D][05:19:18][COMM]imu sleep 0
[W][05:19:18][COMM]now sleep


2025-07-31 20:24:36:157 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:24:36:164 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:24:36:176 ==>> 开始小电池电流采样
2025-07-31 20:24:36:203 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:36:258 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:24:37:272 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:24:37:317 ==>> CurrentBattery:ƽ��:70.53

2025-07-31 20:24:37:785 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:37:792 ==>> 【检测小电池休眠电流】通过,【70.53uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:24:37:799 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:24:37:821 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:24:37:845 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:24:38:087 ==>> [D][05:19:20][COMM]------------ready to Power on Acckey 1------------
[D][05:19:20][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:20][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:20][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:20][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:20][COMM]----- get Acckey 1 and value:1------------
[W][05:19:20][COMM]CAN START!
[D][05:19:20][CAT1]gsm read msg sub id: 12
[D][05:19:20][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:20][COMM]CAN message bat fault change: 0x0001802E->0x00000000 91102
[D][05:19:20][COMM][Audio]exec status ready.
[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 12, ret: 6
[D][05:19:20][COMM]imu wakeup ok. 91116
[D][05:19:20][COMM]imu wakeup 1
[W][05:19:20][COMM]wake up system, wakeupEvt=0x80
[D][05:19:20][COMM]frm_can_weigth_power_set 1
[D][05:19:20][COMM]Clear Sleep Block Evt
[D][05:19:20][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:20][COMM]1x1 frm_can_tp_send ok
[D][05:19:20][COMM]read battery soc:0


2025-07-31 20:24:38:100 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:24:38:117 ==>> 该项需要延时执行
2025-07-31 20:24:38:347 ==>> [E][05:19:20][COMM]1x1 rx timeout
[D][05:19:20][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:24:38:452 ==>> [D][05:19:20][COMM]msg 02A0 loss. last_tick:91083. cur_tick:91596. period:50
[D][05:19:20][COMM]msg 02A4 loss. last

2025-07-31 20:24:38:512 ==>> _tick:91083. cur_tick:91596. period:50
[D][05:19:20][COMM]msg 02A5 loss. last_tick:91083. cur_tick:91597. period:50
[D][05:19:20][COMM]msg 02A6 loss. last_tick:91083. cur_tick:91597. period:50
[D][05:19:20][COMM]msg 02A7 loss. last_tick:91083. cur_tick:91597. period:50
[D][05:19:20][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 91598
[D][05:19:20][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 91598


2025-07-31 20:24:38:602 ==>> 此处延时了:【500】毫秒
2025-07-31 20:24:38:616 ==>> 检测【检测唤醒】
2025-07-31 20:24:38:641 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:24:38:664 ==>> [D][05:19:20][COMM]IMU: [18,0,-1076] ret=21 AWAKE!


2025-07-31 20:24:39:357 ==>> [E][05:19:20][COMM]1x1 rx timeout
[E][05:19:20][COMM]1x1 tp timeout
[E][05:19:20][COMM]1x1 error -3.
[D][05:19:20][COMM]Main Task receive event:28 finished processing
[D][05:19:20][COMM]Main Task receive event:65
[D][05:19:20][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:20][COMM]Main Task receive event:65 finished processing
[D][05:19:20][COMM]Main Task receive event:60
[D][05:19:20][COMM]smart_helmet_vol=255,255
[D][05:19:20][COMM]report elecbike
[D][05:19:20][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:20][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:20][PROT]remove success[1629955160],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:20][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:20][PROT]index:0
[D][05:19:20][PROT]is_send:1
[D][05:19:20][PROT]sequence_num:10
[D][05:19:20][PROT]retry_timeout:0
[D][05:19:20][PROT]retry_times:3
[D][05:19:20][PROT]send_path:0x3
[D][05:19:20][PROT]msg_type:0x5d03
[D][05:19:20][PROT]===========================================================
[W][05:19:20][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955160]
[D][05:19:20][PROT]===========================================================
[D][05:19:20][PROT]Sending traceid[999999999990000B]
[D][05:19:20][BLE ]BLE_WRN [ble_service_get_c

2025-07-31 20:24:39:462 ==>> urrent_send_enabled:28] ble is not connect

[D][05:19:20][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:19:20][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[W][05:19:20][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:20][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:20][SAL ]open socket ind id[4], rst[0]
[D][05:19:20][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:20][SAL ]Cellular task submsg id[8]
[D][05:19:20][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:20][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:19:20][PROT]add success [1629955160],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:20][COMM]Main Task receive event:60 finished processing
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:20][CAT1]gsm read msg sub id: 8
[D][05:19:20][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:20][CAT1]<<< 
+CGATT: 0

OK

[W][05:19:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 

2025-07-31 20:24:39:567 ==>> ==========
[D][05:19:20][FCTY]==========Modules-nRF5340 ==========
[D][05:19:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:20][FCTY]DeviceID    = 460130071539136
[D][05:19:20][FCTY]HardwareID  = 867222087738003
[D][05:19:20][FCTY]MoBikeID    = 9999999999
[D][05:19:20][FCTY]LockID      = FFFFFFFFFF
[D][05:19:20][FCTY]BLEFWVersion= 105
[D][05:19:20][FCTY]BLEMacAddr   = D066EFBAE324
[D][05:19:20][FCTY]Bat         = 3924 mv
[D][05:19:20][FCTY]Current     = 0 ma
[D][05:19:20][FCTY]VBUS        = 2600 mv
[D][05:19:20][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:20][COMM]IMU: [0,-3,-1009] ret=32 AWAKE!
[D][05:19:21][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:21][FCTY]Ext battery vol = 33, adc = 1311
[D][05:19:21][FCTY]Acckey1 vol = 5574 mv, Acckey2 vol = 202 mv
[D][05:19:21][FCTY]Bike Type flag is invalied
[D][05:19:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:21][FCTY]CAT1_GNSS_VE

2025-07-31 20:24:39:663 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:24:39:673 ==>> 检测【关机】
2025-07-31 20:24:39:688 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:24:39:719 ==>> RSION = V3465b5b1
[D][05:19:21][FCTY]Bat1         = 3780 mv
[D][05:19:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:21][FCTY]==========Modules-nRF5340 ==========
[D][05:19:21][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:21][CAT1]<<< 
+CME ERROR: 100

[D][05:19:21][COMM]msg 0220 loss. last_tick:91083. cur_tick:92092. period:100
[D][05:19:21][COMM]msg 0221 loss. last_tick:91083. cur_tick:92093. period:100
[D][05:19:21][COMM]msg 0224 loss. last_tick:91083. cur_tick:92093. period:100
[D][05:19:21][COMM]msg 0260 loss. last_tick:91083. cur_tick:92093. period:100
[D][05:19:21][COMM]msg 0280 loss. last_tick:91083. cur_tick:92094. period:100
[D][05:19:21][COMM]msg 02C0 loss. last_tick:91083. cur_tick:92094. period:100
[D][05:19:21][COMM]msg 02C1 loss. last_tick:91083. cur_tick:92094. period:100
[D][05:19:21][COMM]msg 02C2 loss. last_tick:91083. cur_tick:92095. period:100
[D][05:19:21][COMM]msg 02E0 loss. last_tick:91083. cur_tick:92095. period:100
[D][05:19:21][COMM]msg 02E1 loss. last_tick:91083. cur_tick:92096. period:100
[D][05:19:21][COMM]msg 02E2 loss. last_tick:91083. cur_tick:92096. period:100
[D][05:19:21][COMM]msg 0300 loss. last

2025-07-31 20:24:39:777 ==>> _tick:91083. cur_tick:92096. period:100
[D][05:19:21][COMM]msg 0301 loss. last_tick:91083. cur_tick:92097. period:100
[D][05:19:21][COMM]bat msg 0240 loss. last_tick:91083. cur_tick:92097. period:100. j,i:1 54
[D][05:19:21][COMM]bat msg 0241 loss. last_tick:91083. cur_tick:92097. period:100. j,i:2 55
[D][05:19:21][COMM]bat msg 0242 loss. last_tick:91083. cur_tick:92098. period:100. j,i:3 56
[D][05:19:21][COMM]bat msg 0244 loss. last_tick:91083. cur_tick:92098. period:100. j,i:5 58
[D][05:19:21][COMM]bat msg 024E loss. last_tick:91083. cur_tick:92098. period:100. j,i:15 68
[D][05:19:21][COMM]bat msg 024F loss. last_tick:91083. cur_tick:92099. period:100. j,i:16 69
[D][05:19:21][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 92099
[D][05:19:21][COMM]CAN message bat fault change: 0x00000000->0x0001802E 92100
[D][05:19:21][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 92100


2025-07-31 20:24:39:882 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:24:39:987 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:24:40:092 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:24:40:197 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1][PROT]Sending traceid[999999999990000C]
[D][05:19:21][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:21][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:21][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:21][C

2025-07-31 20:24:40:302 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:24:40:407 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:24:40:512 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:24:40:617 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  index:6, len:560
[D][05:19:22][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:22][COMM]read battery soc:255
[D][05:19:22][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:22][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:22][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:24:40:647 ==>>                               

2025-07-31 20:24:40:692 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:24:40:874 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:22][COMM]arm_hub_enable: hub power: 0
[D][05:19:22][HSDK]hexlog index save 0 7424 3 @ 0 : 0
[D][05:19:22][HSDK]write save hexlog index [0]
[D][05:19:22][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:22][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:24:41:307 ==>> [W][05:19:23][COMM]Power Off


2025-07-31 20:24:41:499 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:24:41:511 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:24:41:517 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:24:41:553 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:24:41:658 ==>> [D][05:19:23][FCTY]get_ext_48v_vol retry i = 0,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 1,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 2,volt = 18
[D][05:19:23][FCT

2025-07-31 20:24:41:703 ==>> Y]get_ext_48v_vol retry i = 3,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 4,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 5,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 6,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 7,volt = 18
[D][05:19:23][FCTY]get_ext_48v_vol retry i = 8,volt = 18


2025-07-31 20:24:41:784 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:24:41:796 ==>> 检测【检测小电池关机电流】
2025-07-31 20:24:41:803 ==>> 开始小电池电流采样
2025-07-31 20:24:41:814 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:41:898 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:24:42:907 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:24:42:969 ==>> CurrentBattery:ƽ��:69.28

2025-07-31 20:24:43:417 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:24:43:424 ==>> 【检测小电池关机电流】通过,【69.28uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:24:43:721 ==>> MES过站成功
2025-07-31 20:24:43:729 ==>> #################### 【测试结束】 ####################
2025-07-31 20:24:43:754 ==>> 关闭5V供电
2025-07-31 20:24:43:769 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:24:43:849 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:24:44:762 ==>> 关闭5V供电成功
2025-07-31 20:24:44:775 ==>> 关闭33V供电
2025-07-31 20:24:44:799 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:24:44:854 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:24:45:770 ==>> 关闭33V供电成功
2025-07-31 20:24:45:782 ==>> 关闭3.7V供电
2025-07-31 20:24:45:805 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:24:45:846 ==>> 6A A6 02 A6 6A 


2025-07-31 20:24:45:951 ==>> Battery OFF
OVER 150


2025-07-31 20:24:46:744 ==>>  

