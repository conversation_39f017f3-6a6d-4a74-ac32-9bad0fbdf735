2025-07-31 21:05:37:082 ==>> MES查站成功:
查站序号:P5100010053134CD验证通过
2025-07-31 21:05:37:098 ==>> 扫码结果:P5100010053134CD
2025-07-31 21:05:37:100 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:05:37:101 ==>> 测试参数版本:2024.10.11
2025-07-31 21:05:37:103 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:05:37:105 ==>> 检测【打开透传】
2025-07-31 21:05:37:107 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:05:37:172 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:05:37:512 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:05:37:528 ==>> 检测【检测接地电压】
2025-07-31 21:05:37:531 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:05:37:666 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:05:37:820 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:05:37:822 ==>> 检测【打开小电池】
2025-07-31 21:05:37:825 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:05:37:969 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:05:38:109 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:05:38:111 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:05:38:113 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:05:38:166 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:05:38:396 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:05:38:402 ==>> 检测【等待设备启动】
2025-07-31 21:05:38:408 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:05:38:705 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:05:38:886 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:05:39:429 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:05:39:536 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:05:39:611 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:05:40:010 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:05:40:470 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:05:40:485 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:05:40:764 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:05:40:770 ==>> 检测【产品通信】
2025-07-31 21:05:40:774 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:05:40:944 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:05:41:052 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:05:41:058 ==>> 检测【初始化完成检测】
2025-07-31 21:05:41:065 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:05:41:110 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:05:41:290 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:05:41:336 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:05:41:342 ==>> 检测【关闭大灯控制1】
2025-07-31 21:05:41:346 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:05:41:533 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:05:41:631 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:05:41:635 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:05:41:641 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:05:41:648 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][0

2025-07-31 21:05:41:683 ==>> 5:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:05:41:788 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:05:41:926 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:05:41:932 ==>> 检测【关闭仪表供电】
2025-07-31 21:05:41:936 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:05:42:155 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:05:42:221 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:05:42:224 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:05:42:226 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:05:42:427 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:05:42:514 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:05:42:517 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:05:42:520 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:05:42:532 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:42:637 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:05:42:847 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:05:42:849 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:05:42:852 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:05:43:034 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:05:43:168 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:05:43:174 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:05:43:179 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:05:43:263 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:05:43:353 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27


2025-07-31 21:05:43:428 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 21:05:43:489 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:05:43:493 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:05:43:495 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:05:43:533 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:43:563 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:05:44:081 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message f

2025-07-31 21:05:44:111 ==>> ault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 21:05:44:570 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:44:784 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:05:44:787 ==>> 该项需要延时执行
2025-07-31 21:05:44:955 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:05:45:429 ==>>                                                                                                                            -
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_

2025-07-31 21:05:45:535 ==>> process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id

2025-07-31 21:05:45:640 ==>> [], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] 

2025-07-31 21:05:45:732 ==>> ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
                                                                                                                                                                                  

2025-07-31 21:05:46:597 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:47:431 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:05:47:596 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:48:606 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:05:48:790 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:05:48:792 ==>> 检测【33V输入电压ADC】
2025-07-31 21:05:48:795 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:05:49:080 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10016
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10016
[W][05:17:59][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:59][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:17:59][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:59][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:59][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:59][COMM]adc read throttle adc:14  volt:18 mv
[D][05:17:59][COMM]adc read battery ts volt:16 mv
[D][05:17:59][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:17:59][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:17:59][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:59][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:17:59][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:59][COMM]arm_hub adc re

2025-07-31 21:05:49:110 ==>> ad board id adc:3358  volt:2705 mv
[D][05:17:59][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:05:49:215 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:05:49:341 ==>> 【33V输入电压ADC】通过,【32678mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:05:49:344 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:05:49:345 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:05:49:427 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 21:05:49:472 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:05:49:629 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:49:641 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:05:49:667 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:49:670 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:05:49:674 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 21:05:49:701 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:05:49:704 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:05:49:735 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:05:49:738 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:05:49:779 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:05:49:783 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:05:49:865 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,

2025-07-31 21:05:49:925 ==>> 0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1993mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:05:50:076 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:50:079 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:05:50:113 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:50:117 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:05:50:119 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 21:05:50:149 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:05:50:152 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:05:50:184 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:05:50:188 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:05:50:222 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:05:50:225 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:05:50:286 ==>>                                                                                                                                                                                                                                                                            [D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087753143

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539191

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1659mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1994mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:05:50:523 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:50:525 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:05:50:559 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:50:563 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:05:50:567 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 21:05:50:596 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:05:50:599 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:05:50:608 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:05:50:632 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:05:50:639 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:05:50:673 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:05:50:676 ==>> 检测【打开WIFI(1)】
2025-07-31 21:05:50:677 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:05:50:853 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:05:50:957 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:05:50:962 ==>> 检测【清空消息队列(1)】
2025-07-31 21:05:50:976 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:05:51:168 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:01][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:05:51:242 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:05:51:245 ==>> 检测【打开GPS(1)】
2025-07-31 21:05:51:247 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:05:51:459 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][COMM]read battery soc:255


2025-07-31 21:05:51:532 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:05:51:535 ==>> 检测【打开GSM联网】
2025-07-31 21:05:51:539 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:05:51:759 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:05:51:818 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:05:51:821 ==>> 检测【打开仪表供电1】
2025-07-31 21:05:51:823 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:05:52:065 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:05:52:108 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:05:52:111 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:05:52:127 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:05:52:260 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:05:52:396 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:05:52:401 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:05:52:404 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:05:52:549 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33317]


2025-07-31 21:05:52:609 ==>> [D][05:18:02][COMM]13729 imu init OK


2025-07-31 21:05:52:699 ==>> 【读取主控ADC采集的仪表电压】通过,【33317mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:05:52:702 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:05:52:704 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:05:52:714 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>>

2025-07-31 21:05:52:744 ==>>  AT+QIACT=1



2025-07-31 21:05:52:849 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:05:52:992 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:05:52:995 ==>> 检测【AD_V20电压】
2025-07-31 21:05:52:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:05:53:106 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:05:53:166 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 21:05:53:211 ==>> 本次取值间隔时间:96ms
2025-07-31 21:05:53:239 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:05:53:346 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:05:53:421 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 21:05:53:466 ==>> 1A A1 10 00 00 
Get AD_V20 2mV
OVER 150


2025-07-31 21:05:53:618 ==>> 本次取值间隔时间:268ms
2025-07-31 21:05:53:646 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:05:53:752 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:05:53:947 ==>> 1A A1 10 00 00 
Get AD_V20 1646mV
OVER 150
                                                                                                                                         0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F3838323436333407

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][COMM]init key as 
[D][05:1

2025-07-31 21:05:54:052 ==>> 8:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.47.13.70"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 


2025-07-31 21:05:54:082 ==>> 本次取值间隔时间:315ms
2025-07-31 21:05:54:110 ==>> 【AD_V20电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:54:115 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:05:54:119 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:05:54:157 ==>> OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
                                                    3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:05:54:400 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:05:54:403 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:05:54:407 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:05:54:600 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:05:54:688 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:05:54:692 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:05:54:697 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:05:54:765 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:05:54:930 ==>> [D][05:18:05][COMM]IMU: [8,-4,-985] ret=28 AWAKE!


2025-07-31 21:05:54:978 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:05:54:982 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:05:54:986 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:05:55:249 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:05:55:354 ==>>                                                                                                                                                            

2025-07-31 21:05:55:429 ==>>                                              ,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1583.711,1583.711,50.666,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 21:05:55:522 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:05:55:526 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:05:55:529 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:05:55:535 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 21:05:55:624 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:05:55:729 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneli

2025-07-31 21:05:55:759 ==>> ne display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:05:55:807 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:05:55:810 ==>> 检测【AD_V21电压】
2025-07-31 21:05:55:815 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:05:55:864 ==>> 1A A1 20 00 00 
Get AD_V21 1037mV
OVER 150


2025-07-31 21:05:56:015 ==>> 本次取值间隔时间:195ms
2025-07-31 21:05:56:063 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:05:56:170 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 21:05:56:365 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,41,24,,,40,60,,,40,25,,,39,1*7E

$GBGSV,4,2,14,59,,,38,39,,,37,14,,,35,16,,,35,1*7E

$GBGSV,4,3,14,41,,,34,1,,,34,44,,,32,5,,,29,1*7F

$GBGSV,4,4,14,40,,,37,3,,,36,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1499.442,1499.442,48.001,2097152,2097152,2097152*42



2025-07-31 21:05:56:410 ==>> 本次取值间隔时间:337ms
2025-07-31 21:05:56:446 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:05:56:456 ==>> 检测【关闭仪表供电2】
2025-07-31 21:05:56:464 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:05:56:653 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:05:56:734 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:05:56:737 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:05:56:759 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:05:56:954 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:05:57:034 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:05:57:040 ==>> 检测【打开AccKey2供电】
2025-07-31 21:05:57:046 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:05:57:254 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:05:57:332 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:05:57:336 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:05:57:340 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:05:57:359 ==>>                                                                              16,33,,,41,24,,,39,60,,,39,25,,,39,1*7C

$GBGSV,4,2,16,3,,,39,59,,,38,39,,,38,40,,,37,1*48

$GBGSV,4,3,16,41,,,36,14,,,35,16,,,35,1,,,34,1*42

$GBGSV,4,4,16,44,,,32,2,,,32,4,,,30,5,,,30,1*42

$GBRMC,,V,,,,,,,280725,0.0,E,N,V*5A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1487.344,1487.344,47.609,2097152,2097152,2097152*43



2025-07-31 21:05:57:599 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:07][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:18:07][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:07][COMM]adc read right brake adc:17  volt:22 mv
[D][05:18:07][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:07][COMM]adc read battery ts volt:21 mv
[D][05:18:07][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:07][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2480  volt:3996 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:6  volt:139 mv
[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:05:57:874 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33285mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:05:57:878 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:05:57:880 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:05:58:023 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:05:58:177 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:05:58:181 ==>> 该项需要延时执行
2025-07-31 21:05:58:400 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,41,24,,,39,25,,,39,3,,,39,1*47

$GBGSV,5,2,19,60,,,38,59,,,38,39,,,38,40,,,37,1*72

$GBGSV,5,3,19,41,,,37,14,,,35,16,,,35,1,,,34,1*4D

$GBGSV,5,4,19,23,,,34,2,,,33,34,,,33,44,,,32,1*4D

$GBGSV,5,5,19,4,,,30,5,,,30,13,,,36,1*78

$GBRMC,,V,,,,,,,310725,0.1,E,N,V*53

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1478.698,1478.698,47.324,2097152,2097152,2097152*49



2025-07-31 21:05:59:417 ==>> $GBGGA,130603.207,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,3,,,39,60,,,39,59,,,39,1*47

$GBGSV,6,2,22,24,,,39,25,,,39,39,,,38,40,,,37,1*72

$GBGSV,6,3,22,41,,,37,16,,,35,1,,,35,13,,,35,1*40

$GBGSV,6,4,22,14,,,35,9,,,34,2,,,33,23,,,33,1*7A

$GBGSV,6,5,22,6,,,32,42,,,32,44,,,32,34,,,32,1*42

$GBGSV,6,6,22,4,,,31,5,,,30,1*76

$GBRMC,130603.207,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130603.207,0.000,730.765,730.765,668.305,2097152,2097152,2097152*6D



2025-07-31 21:05:59:522 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:05:59:717 ==>> $GBGGA,130603.507,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,39,60,,,39,59,,,39,1*41

$GBGSV,6,2,24,24,,,39,25,,,39,39,,,38,40,,,37,1*74

$GBGSV,6,3,24,41,,,37,16,,,36,13,,,35,1,,,35,1*45

$GBGSV,6,4,24,14,,,35,7,,,34,9,,,34,2,,,33,1*4D

$GBGSV,6,5,24,38,,,33,23,,,33,6,,,32,44,,,32,1*4F

$GBGSV,6,6,24,42,,,32,34,,,32,5,,,31,4,,,31,1*70

$GBRMC,130603.507,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130603.507,0.000,729.347,729.347,667.008,2097152,2097152,2097152*6B



2025-07-31 21:06:00:734 ==>> $GBGGA,130604.507,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,40,60,,,39,59,,,39,1*4F

$GBGSV,6,2,24,24,,,39,25,,,39,39,,,38,40,,,37,1*74

$GBGSV,6,3,24,41,,,37,16,,,36,14,,,36,7,,,35,1*47

$GBGSV,6,4,24,1,,,35,13,,,34,9,,,34,42,,,34,1*7F

$GBGSV,6,5,24,2,,,33,38,,,33,6,,,33,23,,,33,1*7D

$GBGSV,6,6,24,44,,,32,34,,,32,4,,,31,5,,,30,1*77

$GBRMC,130604.507,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130604.507,0.000,732.797,732.797,670.162,2097152,2097152,2097152*67



2025-07-31 21:06:01:182 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:06:01:186 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:06:01:190 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:01:475 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:11][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:11][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:11][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:11][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:11][COMM]adc read battery ts volt:20 mv
[D][05:18:11][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:11][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2418  volt:3896 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:06:01:550 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:06:01:655 ==>> $GBGGA,130605.507,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,39,59,,,39,24,,,39,1*41

$GBGSV,6,2,24,25,,,39,60,,,38,39,,,

2025-07-31 21:06:01:715 ==>> 38,40,,,37,1*75

$GBGSV,6,3,24,41,,,37,16,,,36,14,,,36,7,,,35,1*47

$GBGSV,6,4,24,13,,,34,1,,,34,9,,,34,42,,,34,1*7E

$GBGSV,6,5,24,38,,,33,6,,,33,23,,,33,2,,,32,1*7C

$GBGSV,6,6,24,44,,,32,34,,,32,5,,,31,4,,,31,1*76

$GBRMC,130605.507,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130605.507,0.000,730.205,730.205,667.791,2097152,2097152,2097152*6A



2025-07-31 21:06:01:734 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【177mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:06:01:737 ==>> 检测【打开AccKey1供电】
2025-07-31 21:06:01:741 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:06:01:958 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:06:02:026 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:06:02:029 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:06:02:033 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:06:02:171 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150


2025-07-31 21:06:02:291 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 21:06:02:328 ==>> 【读取AccKey1电压(ADV14)前】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:06:02:331 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:06:02:342 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:02:772 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:12][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:12][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:12][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:12][COMM]adc read battery ts volt:13 mv
[D][05:18:12][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:12][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2458  volt:3960 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,130606.507,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,60,,,39,59,,,39,1*4F

$GBGSV,7,2,25,24,,,39,25,,,39,39,,,38,40,,,37,1*74

$GBGSV,7,3,25,41,,,37,16,,,36,14,,,36,7,,,35,1*47

$GBGSV,7,4,25,1,,,35,42,,,35,13,,,34,9,,,34,1*7E

$GBGSV,7,5,25,2,,,33,38,,,33,44,,,33,6,,,33,1*7C

$GBGSV,7,6,25,34,,,33,23,,,33,5,,,31,4,,,31,1*77

$GBGSV,7,7,25,10,,,29,1*7B

$GBRMC,1

2025-07-31 21:06:02:802 ==>> 30606.507,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130606.507,0.000,730.799,730.799,668.336,2097152,2097152,2097152*6F



2025-07-31 21:06:02:876 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:06:02:879 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:06:02:882 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:06:03:031 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:06:03:168 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:06:03:173 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:06:03:178 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:06:03:271 ==>> 1A A1 00 40 00 
Get AD_V14 2665mV
OVER 150


2025-07-31 21:06:03:422 ==>> 原始值:【2665】, 乘以分压基数【2】还原值:【5330】
2025-07-31 21:06:03:462 ==>> 【读取AccKey1电压(ADV14)后】通过,【5330mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:06:03:466 ==>> 检测【打开WIFI(2)】
2025-07-31 21:06:03:470 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:06:03:545 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:06:03:771 ==>> $GBGGA,130607.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,39,24,,,39,25,,,39,1*49

$GBGSV,7,2,26,60,,,38,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,7,,,35,1,,,35,16,,,35,1*70

$GBGSV,7,4,26,42,,,35,14,,,35,9,,,34,6,,,34,1*7D

$GBGSV,7,5,26,2,,,33,13,,,33,38,,,33,44,,,33,1*4B

$GBGSV,7,6,26,34,,,33,23,,,33,5,,,31,4,,,31,1*74

$GBGSV,7,7,26,10,,,30,8,,,29,1*43

$GBRMC,130607.507,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130607.507,0.000,722.586,722.586,660.825,2097152,2097152,2097152*6F

[W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:06:04:017 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:06:04:023 ==>> 检测【转刹把供电】
2025-07-31 21:06:04:035 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:06:04:228 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:06:04:307 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:06:04:310 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:06:04:315 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:06:04:411 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:06:04:471 ==>> +WIFISCAN:4,0,44A1917CAD80,-76
+WIFISCAN:4,1,CC057790A7C1,-78
+WIFISCAN:4,2,44A1917CAD81,-78
+WIFISCAN:4,3,CC057790A7C0,-78

[D][05:18:14][CAT1]wifi scan report total[4]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2405mV
OVER 150


2025-07-31 21:06:04:576 ==>> 原始值:【2405】, 乘以分压基数【2】还原值:【4810】
2025-07-31 21:06:04:611 ==>> 【读取AD_V15电压(前)】通过,【4810mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:06:04:615 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:06:04:619 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:06:04:726 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:06:04:816 ==>> $GBGGA,130608.507,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,24,,,39,1*46

$GBGSV,7,2,26,25,,,39,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,16,,,36,7,,,35,1,,,35,1*73

$GBGSV,7,4,26,42,,,35,14,,,35,13,,,34,9,,,34,1*49

$GBGSV,7,5,26,6,,,34,2,,,33,38,,,33,44,,,33,1*78

$GBGSV,7,6,26,34,,,33,23,,,33,4,,,32,10,,,31,1*43

$GBGSV,7,7,26,5,,,31,8,,,30,1*7E

$GBRMC,130608.507,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130608.507,0.000,728.151,728.151,665.914,2097152,2097152,2097152*66

[D][05:18:14][GNSS]recv submsg id[3]
[D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2434mV
OVER 150


2025-07-31 21:06:04:891 ==>> [D][05:18:14][COMM]IMU: [3,-4,-1031] ret=22 AWAKE!


2025-07-31 21:06:04:896 ==>> 原始值:【2434】, 乘以分压基数【2】还原值:【4868】
2025-07-31 21:06:04:934 ==>> 【读取AD_V16电压(前)】通过,【4868mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:06:04:937 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:06:04:941 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:05:283 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:15][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:15][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:15 mv
[D][05:18:15][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3092  volt:5435 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:06:05:485 ==>> 【转刹把供电电压(主控ADC)】通过,【5435mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:06:05:489 ==>> 检测【转刹把供电电压】
2025-07-31 21:06:05:492 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:05:541 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:06:05:826 ==>> $GBGGA,130609.507,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,24,,,39,1*46

$GBGSV,7,2,26,25,,,39,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,7,,,35,1,,,35,16,,,35,1*70

$GBGSV,7,4,26,42,,,35,14,,,35,13,,,34,9,,,34,1*49

$GBGSV,7,5,26,6,,,34,2,,,33,38,,,33,44,,,33,1*78

$GBGSV,7,6,26,34,,,33,23,,,33,10,,,32,4,,,32,1*40

$GBGSV,7,7,26,5,,,31,8,,,30,1*7E

$GBRMC,130609.507,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130609.507,0.000,728.148,728.148,665.911,2097152,2097152,2097152*62

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:15][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:15][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:15][COMM]adc read battery ts volt:16 mv
[D][05:18:15][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc re

2025-07-31 21:06:05:871 ==>> ad vbat adc:2407  volt:3878 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:06:06:061 ==>> 【转刹把供电电压】通过,【5417mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:06:06:066 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:06:06:071 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:06:06:253 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:06:06:346 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:06:06:352 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:06:06:356 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:06:06:450 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:06:06:557 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:06:06:572 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:06:06:665 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:06:06:782 ==>> $GBGGA,130610.507,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,39,60,,,39,24,,,39,1*48

$GBGSV,7,2,26,25,,,39,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,7,,,35,1,,,35,16,,,35,1*70

$GBGSV,7,4,26,42,,,35,14,,,35,9,,,34,6,,,34,1*7D

$GBGSV,7,5,26,2,,,33,13,,,33,38,,,33,44,,,33,1*4B

$GBGSV,7,6,26,34,,,33,23,,,33,10,,,32,4,,,32,1*40

$GBGSV,7,7,26,8,,,31,5,,,31,1*7F

$GBRMC,130610.507,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130610.507,0.000,727.348,727.348,665.179,2097152,2097152,2097152*6C

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:06:06:811 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:06:06:815 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:06:06:818 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:06:06:917 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:06:06:962 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:06:07:055 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:06:07:059 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:06:07:063 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:06:07:161 ==>> 3A A3 03 01 A3 


2025-07-31 21:06:07:266 ==>> ON_OUT3
OVER 150


2025-07-31 21:06:07:356 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:06:07:360 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:06:07:362 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:06:07:463 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:06:07:568 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:06:07:658 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:06:07:662 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:06:07:667 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:06:07:675 ==>> $GBGGA,130611.507,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,39,60,,,39,59,,,39,1*42

$GBGSV,7,2,26,24,,,39,25,,,39,39,,,38,40,,,37,1*77

$GBGSV,7,3,26,41,,,37,7,,,35,1,,,35,16,,,35,1*70

$GBGSV,7,4,26,42,,,35,14,,,35,13,,,34,38,,,34,1*7B

$GBGSV,7,5,26,9,,,34,6,,,34,2,,,33,44,,,33,1*4D

$GBGSV,7,6,26,10,,,32,34,,,32,23,,,32,8,,,

2025-07-31 21:06:07:718 ==>> 31,1*4F

$GBGSV,7,7,26,4,,,31,5,,,30,1*72

$GBRMC,130611.507,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130611.507,0.000,726.561,726.561,664.460,2097152,2097152,2097152*61

3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:06:07:947 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:06:07:953 ==>> 检测【左刹电压测试1】
2025-07-31 21:06:07:958 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:08:282 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1726  volt:2275 mv
[D][05:18:18][COMM]adc read right brake adc:1725  volt:2274 mv
[D][05:18:18][COMM]adc read throttle adc:1731  volt:2282 mv
[D][05:18:18][COMM]adc read battery ts volt:13 mv
[D][05:18:18][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:18][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2416  volt:3892 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:06:08:499 ==>> 【左刹电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 21:06:08:503 ==>> 检测【右刹电压测试1】
2025-07-31 21:06:08:541 ==>> 【右刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 21:06:08:548 ==>> 检测【转把电压测试1】
2025-07-31 21:06:08:577 ==>> 【转把电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 21:06:08:583 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:06:08:588 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:06:08:711 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150
$GBGGA,130612.507,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,59,,,39,1*4C

$GBGSV,7,2,26,24,,,39,25,,,39,39,,,38,40,,,37,1*77

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,42,,,35,13,,,34,38,,,34,1*4F

$GBGSV,7,5,26,9,,,34,6,,,34,2,,,33,44,,,33,1*4D

$GBGSV,7,6,26,34,,,33,23,,,33,10,,,32,5,,,31,1*42

$GBGSV,7,7,26,8,,,31,4,,,31,1*7E

$GBRMC,130612.507,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130612.507,0.000,731.332,731.332,668.822,2097152,2097152,2097152*64



2025-07-31 21:06:08:868 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:06:08:872 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:06:08:875 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:06:08:966 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:06:09:195 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:06:09:201 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:06:09:206 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:06:09:259 ==>> 3A A3 05 00 A3 


2025-07-31 21:06:09:364 ==>> OFF_OUT5
OVER 150


2025-07-31 21:06:09:518 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:06:09:525 ==>> 检测【左刹电压测试2】
2025-07-31 21:06:09:533 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:09:559 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:06:09:844 ==>> $GBGGA,130613.507,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,24,,,39,1*46

$GBGSV,7,2,26,25,,,39,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,42,,,35,13,,,34,38,,,34,1*4F

$GBGSV,7,5,26,9,,,34,6,,,34,2,,,33,10,,,33,1*4C

$GBGSV,7,6,26,44,,,33,34,,,33,23,,,33,4,,,32,1*40

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,130613.507,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130613.507,0.000,732.121,732.121,669.543,2097152,2097152,2097152*6E

[D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:19][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:19][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:19][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:19][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:19][COMM]adc read battery ts volt:19 mv
[D][05:18:19][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:19][COMM]adc read throttle brake in adc:6  volt

2025-07-31 21:06:09:889 ==>> :10 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2476  volt:3989 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:06:10:128 ==>> 【左刹电压测试2】通过,【17】符合目标值【0】至【50】要求!
2025-07-31 21:06:10:132 ==>> 检测【右刹电压测试2】
2025-07-31 21:06:10:180 ==>> 【右刹电压测试2】通过,【14】符合目标值【0】至【50】要求!
2025-07-31 21:06:10:184 ==>> 检测【转把电压测试2】
2025-07-31 21:06:10:232 ==>> 【转把电压测试2】通过,【19】符合目标值【0】至【50】要求!
2025-07-31 21:06:10:236 ==>> 检测【晶振检测】
2025-07-31 21:06:10:239 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:06:10:447 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 21:06:10:576 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:06:10:580 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:06:10:586 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:06:10:733 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1659mV
Get AD_V4 1650mV
Get AD_V5 2768mV
Get AD_V6 1994mV
Get AD_V7 1096mV
OVER 150
$GBGGA,130614.507,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,24,,,39,1*46

$GBGSV,7,2,26,25,,,39,59,,,38,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,42,,,35,13,,,34,38,,,34,1*4F

$GBGSV,7,5,26,9,,,34,6,,,34,2,,,33,10,,,33,1*4C

$GBGSV,7,6,26,34,,,33,23,,,33,44,,,32,8,,,31,1*4E

$GBGSV,7,7,26,5,,,31,4,,,31,1*73

$GBRMC,130614.507,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130614.507,0.000,730.534,730.534,668.093,2097152,2097152,2097152*60



2025-07-31 21:06:10:870 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:10:874 ==>> 检测【检测BootVer】
2025-07-31 21:06:10:880 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:06:11:245 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539191
[D][05:18:21][FCTY]HardwareID  = 867222087753143
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = CD8B7BF806B6
[D][05:18:21][FCTY]Bat         = 3924 mv
[D][05:18:21][FCTY]Current     = 150 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1293
[D][05:18:21][FCTY]Acckey1 vol = 5514 mv, Acckey2 vol = 50 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4


2025-07-31 21:06:11:290 ==>> 
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3820 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:06:11:418 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:06:11:422 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:06:11:425 ==>> 检测【检测固件版本】
2025-07-31 21:06:11:465 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:06:11:471 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:06:11:478 ==>> 检测【检测蓝牙版本】
2025-07-31 21:06:11:501 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:06:11:504 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:06:11:509 ==>> 检测【检测MoBikeId】
2025-07-31 21:06:11:538 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:06:11:542 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:06:11:549 ==>> 检测【检测蓝牙地址】
2025-07-31 21:06:11:555 ==>> 取到目标值:CD8B7BF806B6
2025-07-31 21:06:11:561 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:06:11:575 ==>> 【检测蓝牙地址】通过,【CD8B7BF806B6】符合目标值【】要求!
2025-07-31 21:06:11:579 ==>> 提取到蓝牙地址:CD8B7BF806B6
2025-07-31 21:06:11:585 ==>> 检测【BOARD_ID】
2025-07-31 21:06:11:612 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:06:11:619 ==>> 检测【检测充电电压】
2025-07-31 21:06:11:648 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:06:11:653 ==>> 检测【检测VBUS电压1】
2025-07-31 21:06:11:665 ==>> $GBGGA,130615.507,,,,,0,00,,,M,,M,,*68


2025-07-31 21:06:11:683 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:06:11:690 ==>> 检测【检测充电电流】
2025-07-31 21:06:11:727 ==>> 【检测充电电流】通过,【150ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:06:11:735 ==>> 检测【检测IMEI】
2025-07-31 21:06:11:741 ==>> 取到目标值:867222087753143
2025-07-31 21:06:11:746 ==>> 
$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,59,,,39,1*4C

$GBGSV,7,2,26,24,,,39,25,,,39,39,,,38,40,,,37,1*77

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,42,,,35,13,,,34,38,,,34,1*4F

$GBGSV,7,5,26,9,,,34,6,,,34,2,,,33,10,,,33,1*4C

$GBGSV,7,6,26,44,,,33,34,,,33,23,,,33,5,,,31,1*42

$GBGSV,7,7,26,8,,,31,4,,,31,1*7E

$GBRMC,130615.507,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130615.507,0.000,732.126,732.126,669.548,2097152,2097152,2097152*63



2025-07-31 21:06:11:766 ==>> 【检测IMEI】通过,【867222087753143】符合目标值【】要求!
2025-07-31 21:06:11:770 ==>> 提取到IMEI:867222087753143
2025-07-31 21:06:11:790 ==>> 检测【检测IMSI】
2025-07-31 21:06:11:796 ==>> 取到目标值:460130071539191
2025-07-31 21:06:11:803 ==>> 【检测IMSI】通过,【460130071539191】符合目标值【】要求!
2025-07-31 21:06:11:807 ==>> 提取到IMSI:460130071539191
2025-07-31 21:06:11:810 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:06:11:830 ==>> 取到目标值:460130071539191
2025-07-31 21:06:11:837 ==>> 【校验网络运营商(移动)】通过,【460130071539191】符合目标值【】要求!
2025-07-31 21:06:11:840 ==>> 检测【打开CAN通信】
2025-07-31 21:06:11:843 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:06:11:966 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:06:12:164 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:06:12:168 ==>> 检测【检测CAN通信】
2025-07-31 21:06:12:174 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:06:12:269 ==>> can send success


2025-07-31 21:06:12:314 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:06:12:359 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33453
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:06:12:434 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:06:12:476 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:06:12:480 ==>> 检测【关闭CAN通信】
2025-07-31 21:06:12:486 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:06:12:494 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:06:12:569 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:06:12:674 ==>> $GBGGA,130616.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,60,,,39,3,,,39,59,,,39,1*42

$GBGSV,7,2,26,24,,,39,

2025-07-31 21:06:12:734 ==>> 25,,,39,39,,,38,40,,,37,1*77

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,42,,,35,2,,,34,13,,,34,1*76

$GBGSV,7,5,26,9,,,34,6,,,34,10,,,33,38,,,33,1*75

$GBGSV,7,6,26,34,,,33,23,,,33,44,,,32,4,,,32,1*41

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,130616.507,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130616.507,0.000,731.326,731.326,668.817,2097152,2097152,2097152*66



2025-07-31 21:06:12:835 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:06:12:843 ==>> 检测【打印IMU STATE】
2025-07-31 21:06:12:857 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:06:13:068 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:06:13:133 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:06:13:137 ==>> 检测【六轴自检】
2025-07-31 21:06:13:141 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:06:13:358 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:06:13:584 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:06:13:839 ==>> $GBGGA,130617.507,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,39,60,,,39,1*45

$GBGSV,7,2,26,59,,,39,25,,,39,39,,,38,40,,,37,1*7D

$GBGSV,7,3,26,41,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,26,1,,,35,6,,,35,42,,,35,2,,,34,1*43

$GBGSV,7,5,26,13,,,34,38,,,34,9,,,34,10,,,33,1*46

$GBGSV,7,6,26,44,,,33,34,,,33,23,,,33,4,,,32,1*40

$GBGSV,7,7,26,8,,,31,5,,,31,1*7F

$GBRMC,130617.507,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130617.507,0.000,735.308,735.308,672.458,2097152,2097152,2097152*6B



2025-07-31 21:06:15:574 ==>> $GBGGA,130614.513,2301.2572852,N,11421.9416478,E,1,08,1.17,80.783,M,-1.770,M,,*54

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.45,4*06

$GBGSV,7,1,26,33,66,243,41,3,61,190,39,6,55,44,34,39,53,14,38,1*71

$GBGSV,7,2,26,59,52,129,38,16,52,353,36,1,48,126,35,7,47,193,35,1*7A

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,33,4,32,112,32,1*76

$GBGSV,7,5,26,41,32,313,37,5,22,257,31,44,19,88,33,13,19,208,34,1*79

$GBGSV,7,6,26,38,17,199,33,8,16,202,31,42,6,322,35,14,,,36,1*47

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBRMC,130614.513,A,2301.2572852,N,11421.9416478,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:18:24][GNSS]HD8040 GPS
[D][05:18:24][GNSS]GPS diff_sec 124012070, report 0x42 frame
$GBGST,130614.513,0.952,0.151,0.150,0.238,2.086,2.431,9.150*70

[D][05:18:24][COMM]Main Task receive event:131
[D][05:18:24][COMM]index:0,power_mode:0xFF
[D][05:18:24][COMM]index:1,sound_mode:0xFF
[D][05:18:24][COMM]index:2,gsensor_mode:0xFF
[D][05:18:24][COMM]index:3,report_freq_mode:0xFF
[D][05:18:24][COMM]index:4,report_period:0xFF
[D][05:18:24][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:24][COMM]index:6,normal_reset_period:0xFF
[D][05:18:24][COM

2025-07-31 21:06:15:680 ==>> M]index:7,spock_over_speed:0xFF
[D][05:18:24][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:24][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:24][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:24][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:24][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:24][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:24][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:24][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:24][COMM]index:16,imu_config_params:0xFF
[D][05:18:24][COMM]index:17,long_connect_params:0xFF
[D][05:18:24][COMM]index:18,detain_mark:0xFF
[D][05:18:24][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:24][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:24][COMM]index:21,mc_mode:0xFF
[D][05:18:24][COMM]index:22,S_mode:0xFF
[D][05:18:24][COMM]index:23,overweight:0xFF
[D][05:18:24][COMM]index:24,standstill_mode:0xFF
[D][05:18:24][COMM]index:25,night_mode:0xFF
[D][05:18:24][COMM]index:26,experiment1:0xFF
[D][05:18:24][COMM]index:27,experiment2:0xFF
[D][05:18:24][COMM]index:28,experiment3:0xFF
[D][05:18:24][COMM]index:29,experiment4:0xFF
[D][05:18:24][COMM]index:30,night_mode_start:0xFF
[D][05:18:24][COMM]i

2025-07-31 21:06:15:784 ==>> ndex:31,night_mode_end:0xFF
[D][05:18:24][COMM]index:33,park_report_minutes:0xFF
[D][05:18:24][COMM]index:34,park_report_mode:0xFF
[D][05:18:24][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:24][COMM]index:38,charge_battery_para: FF
[D][05:18:24][COMM]index:39,multirider_mode:0xFF
[D][05:18:24][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:24][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:24][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:24][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:24][COMM]index:44,riding_duration_config:0xFF
[D][05:18:24][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:24][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:24][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:24][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:24][COMM]index:49,mc_load_startup:0xFF
[D][05:18:24][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:24][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:24][COMM]index:52,traffic_mode:0xFF
[D][05:18:24][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:24][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:24][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:24][COMM]index:57,traffic_sens

2025-07-31 21:06:15:889 ==>> _cycle:0xFF
[D][05:18:24][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:24][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:24][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:24][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:24][COMM]index:63,experiment5:0xFF
[D][05:18:24][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:24][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:24][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:24][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:24][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:24][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:24][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:24][COMM]index:72,experiment6:0xFF
[D][05:18:24][COMM]index:73,experiment7:0xFF
[D][05:18:24][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:24][COMM]index:75,zero_value_from_server:-1
[D][05:18:24][COMM]index:76,multirider_threshold:255
[D][05:18:24][COMM]index:77,experiment8:255
[D][05:18:24][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:24][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:24][COMM]index:80,temp_park_reminder_timeout_duration:255


2025-07-31 21:06:15:994 ==>> 
[D][05:18:24][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:24][COMM]index:83,loc_report_interval:255
[D][05:18:24][COMM]index:84,multirider_threshold_p2:255
[D][05:18:24][COMM]index:85,multirider_strategy:255
[D][05:18:24][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:24][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:24][COMM]index:90,weight_param:0xFF
[D][05:18:24][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:24][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:24][COMM]index:95,current_limit:0xFF
[D][05:18:24][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:24][COMM]index:100,location_mode:0xFF

[W][05:18:24][PROT]remove success[1629955104],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:24][PROT]add success [1629955104],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:24][COMM]Main Task receive event:131 finished processing
[D][05:18:24][PROT]index:0 1629955104
[D][05:18:24][PROT]is_send:0
[D][05:18:24][PROT]sequence_num:4
[D][05:18:24][PROT]retry_timeout:0
[D][05:18:24][PROT]retry_times:1
[D][05:18:24][PROT]send_path:0x2
[D][05:18:24][PROT]min_index:0, type:0x4205, priori

2025-07-31 21:06:16:100 ==>> ty:0
[D][05:18:24][PROT]===========================================================
[W][05:18:24][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955104]
[D][05:18:24][PROT]===========================================================
[D][05:18:24][PROT]sending traceid [9999999999900005]
[D][05:18:24][PROT]Send_TO_M2M [1629955104]
[D][05:18:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:24][SAL ]sock send credit cnt[6]
[D][05:18:24][SAL ]sock send ind credit cnt[6]
[D][05:18:24][M2M ]m2m send data len[294]
[D][05:18:24][SAL ]Cellular task submsg id[10]
[D][05:18:24][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e08] format[0]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6
[D][05:18:25][CAT1]gsm read msg sub id: 15
[D][05:18:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:25][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B5CB4E679D2C0ED75D6953F2E21AC0E619C2B34DD4D91658EA3F294D4FAD4AF6A2C2F35B214BF09248F785087B32EEF057996478D97942724AA011

2025-07-31 21:06:16:191 ==>> 66C593EF33A8FC29569D62907C1C0E42A54F9093F24E2E96A5BB460A8D8712E4B286A8D6C38BE373D030ECEAD9581082440B0326B6017A06F4761B022B43C0091D9D0C8738A69B26
$GBGGA,130615.013,2301.2580662,N,11421.9414862,E,1,08,1.17,78.654,M,-1.770,M,,*59

[D][05:18:25][CAT1]<<< 
SEND OK

[D][05:18:25][CAT1]exec over: func id: 15, ret: 11
[D][05:18:25][CAT1]sub id: 15, ret: 11

[D][05:18:25][SAL ]Cellular task submsg id[68]
[D][05:18:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:25][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:25][M2M ]g_m2m_is_idle become true
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:25][PROT]M2M Send ok [1629955105]
$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.45,4*06

$GBGSV,7,1,26,33,66,243,41,3,61,190,39,6,55,44,34,39,53,14,38,1*71

$GBGSV,7,2,26,59,52,129,39,16,52,

2025-07-31 21:06:16:297 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 21:06:16:341 ==>>                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:06:17:329 ==>> $GBGGA,130617.000,2301.2582534,N,11421.9412656,E,1,08,1.17,77.245,M,-1.770,M,,*5F

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.45,4*06

$GBGSV,7,1,26,33,66,242,42,3,61,190,39,6,55,44,34,39,53,14,38,1*73

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,33,25,45,299,39,24,43,26,39,40,42,161,37,1*7D

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,32,4,32,112,32,1*77

$GBGSV,7,5,26,41,32,313,37,5,22,257,31,44,19,88,33,13,19,208,34,1*79

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130617.000,A,2301.2582534,N,11421.9412656,E,0.000,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,130617.000,1.348,0.285,0.281,0.434,1.294,1.394,4.427*7A

                                                                                                    

2025-07-31 21:06:17:359 ==>>                                                                       

2025-07-31 21:06:17:587 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:06:18:326 ==>> $GBGGA,130618.000,2301.2583006,N,11421.9412034,E,1,08,1.17,77.065,M,-1.770,M,,*57

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.45,4*06

$GBGSV,7,1,26,33,66,242,41,3,61,190,39,6,55,44,34,39,53,14,38,1*70

$GBGSV,7,2,26,59,52,129,38,16,52,353,36,1,48,126,35,7,47,193,35,1*7A

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,32,4,32,112,32,1*77

$GBGSV,7,5,26,41,32,313,37,5,22,257,31,44,19,88,32,13,19,208,34,1*78

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130618.000,A,2301.2583006,N,11421.9412034,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130618.000,1.075,0.224,0.221,0.340,1.003,1.095,3.862*79



2025-07-31 21:06:19:326 ==>> $GBGGA,130619.000,2301.2583542,N,11421.9411842,E,1,08,1.17,77.076,M,-1.770,M,,*5B

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.45,4*06

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,59,52,129,38,16,52,353,36,1,48,126,35,7,47,193,35,1*7A

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,33,41,32,313,38,1*4E

$GBGSV,7,5,26,4,32,112,31,5,22,257,31,44,19,88,33,13,19,208,34,1*4D

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130619.000,A,2301.2583542,N,11421.9411842,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130619.000,0.989,0.200,0.198,0.303,0.875,0.957,3.484*79



2025-07-31 21:06:19:610 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 21:06:20:340 ==>> $GBGGA,130620.000,2301.2584222,N,11421.9412003,E,1,08,1.17,77.039,M,-1.770,M,,*52

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,59,52,129,38,16,52,353,36,1,48,126,34,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,32,41,32,313,37,1*40

$GBGSV,7,5,26,4,32,112,32,5,22,257,31,44,19,88,33,13,19,208,34,1*4E

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,41,5*78

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130620.000,A,2301.2584222,N,11421.9412003,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,130620.000,0.859,0.198,0.195,0.297,0.715,0.791,3.162*7C

[D][05:18:30][PROT]CLEAN,SEND:0
[D][05:18:30][PROT]CLEAN:0


2025-07-31 21:06:21:330 ==>> $GBGGA,130621.000,2301.2584636,N,11421.9412020,E,1,08,1.17,77.030,M,-1.770,M,,*5A

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,59,52,129,39,16,52,353,35,1,48,126,35,7,47,193,35,1*78

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,40,40,42,161,37,1*74

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,32,41,32,313,37,1*40

$GBGSV,7,5,26,4,32,112,32,5,22,257,31,44,19,88,32,13,19,208,34,1*4F

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,41,5*78

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130621.000,A,2301.2584636,N,11421.9412020,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130621.000,1.108,0.208,0.205,0.311,0.911,0.972,3.084*7E



2025-07-31 21:06:21:602 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 21:06:22:343 ==>> $GBGGA,130622.000,2301.2584937,N,11421.9412283,E,1,08,1.17,77.103,M,-1.770,M,,*5D

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,42,3,61,190,40,6,55,44,34,39,53,14,38,1*7D

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,40,24,43,26,40,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,32,13,33,218,34,1*4F

$GBGSV,7,5,26,41,32,313,38,4,32,112,32,5,22,257,31,44,19,88,33,1*47

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130622.000,A,2301.2584937,N,11421.9412283,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130622.000,1.318,0.209,0.206,0.313,1.066,1.117,3.029*7B



2025-07-31 21:06:23:318 ==>> $GBGGA,130623.000,2301.2585027,N,11421.9412581,E,1,08,1.17,77.134,M,-1.770,M,,*54

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,40,42,161,37,1*7A

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,33,13,33,218,34,1*4E

$GBGSV,7,5,26,41,32,313,38,4,32,112,31,5,22,257,31,44,19,88,32,1*45

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130623.000,A,2301.2585027,N,11421.9412581,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,130623.000,1.442,0.244,0.241,0.369,1.149,1.194,2.963*74



2025-07-31 21:06:23:453 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:06:23:669 ==>> [D][05:18:33][COMM]read battery soc:255
[W][05:18:33][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:06:24:317 ==>> $GBGGA,130624.000,2301.2585269,N,11421.9412652,E,1,08,1.17,77.213,M,-1.770,M,,*50

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,40,40,42,161,37,1*74

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,33,13,33,218,34,1*4E

$GBGSV,7,5,26,41,32,313,38,4,32,112,31,5,22,257,31,44,19,88,33,1*44

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,40,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130624.000,A,2301.2585269,N,11421.9412652,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,130624.000,1.276,0.226,0.223,0.342,1.002,1.046,2.768*7E



2025-07-31 21:06:25:322 ==>> [D][05:18:35][CAT1]<<< 
OK

[D][05:18:35][CAT1]exec over: func id: 12, ret: 6
$GBGGA,130625.000,2301.2585434,N,11421.9412597,E,1,08,1.17,77.280,M,-1.770,M,,*5F

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,39,6,55,44,34,39,53,14,38,1*70

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,40,40,42,161,37,1*74

$GBGSV,7,4,26,60,41,238,39,9,39,323,34,10,36,201,33,13,33,218,34,1*4E

$GBGSV,7,5,26,41,32,313,38,4,32,112,31,5,22,257,31,44,19,88,33,1*44

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,41,24,43,26,41,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130625.000,A,2301.2585434,N,11421.9412597,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,130625.000,1.605,0.208,0.205,0.311,1.252,1.289,2.857*74



2025-07-31 21:06:25:382 ==>>                                                                                                                                                                                                                                                

2025-07-31 21:06:25:609 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 21:06:26:327 ==>> $GBGGA,130626.000,2301.2585608,N,11421.9412602,E,1,08,1.17,77.338,M,-1.770,M,,*5C

$GBGSA,A,3,33,39,16,25,24,40,41,44,,,,,2.72,1.17,2.46,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,39,6,55,44,34,39,53,14,38,1*70

$GBGSV,7,2,26,59,52,129,39,16,52,353,36,1,48,126,35,7,47,193,35,1*7B

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,40,40,42,161,37,1*74

$GBGSV,7,4,26,60,41,238,39,9,39,323,35,10,36,201,33,13,33,218,34,1*4F

$GBGSV,7,5,26,41,32,313,38,4,32,112,32,5,22,257,31,44,19,88,33,1*47

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,41,5*78

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130626.000,A,2301.2585608,N,11421.9412602,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,130626.000,1.229,0.208,0.205,0.314,0.942,0.980,2.571*79



2025-07-31 21:06:27:342 ==>> $GBGGA,130627.000,2301.2585668,N,11421.9412389,E,1,10,0.96,77.341,M,-1.770,M,,*52

$GBGSA,A,3,33,39,16,59,25,24,60,40,41,44,,,2.45,0.96,2.25,4*06

$GBGSV,7,1,26,33,66,242,41,3,61,190,40,6,55,44,34,39,53,14,38,1*7E

$GBGSV,7,2,26,16,52,353,36,59,50,129,39,1,48,126,35,7,47,193,35,1*79

$GBGSV,7,3,26,2,45,238,34,25,45,299,39,24,43,26,39,60,43,240,39,1*77

$GBGSV,7,4,26,40,42,161,37,9,39,323,34,10,36,201,33,13,33,218,34,1*4E

$GBGSV,7,5,26,41,32,313,38,4,32,112,32,5,22,257,31,44,19,88,33,1*47

$GBGSV,7,6,26,38,17,199,34,8,16,202,31,42,6,322,35,14,,,36,1*40

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,41,5*78

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130627.000,A,2301.2585668,N,11421.9412389,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130627.000,1.106,0.180,0.180,0.279,0.825,0.863,2.423*7B



2025-07-31 21:06:27:627 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:06:28:348 ==>> $GBGGA,130628.000,2301.2585704,N,11421.9412302,E,1,10,0.96,77.218,M,-1.770,M,,*58

$GBGSA,A,3,33,39,16,59,25,24,60,40,41,44,,,2.45,0.96,2.26,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,39,6,55,44,34,14,54,188,36,1*42

$GBGSV,7,2,26,39,53,14,38,16,52,353,36,59,50,129,39,1,48,126,35,1*72

$GBGSV,7,3,26,7,47,193,35,2,45,238,34,25,45,299,39,24,43,26,40,1*4D

$GBGSV,7,4,26,60,43,240,39,40,42,161,37,9,39,323,34,10,36,201,33,1*4D

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,5,22,257,31,1*71

$GBGSV,7,6,26,44,19,88,33,38,17,199,34,8,16,202,31,42,6,322,35,1*48

$GBGSV,7,7,26,34,,,33,23,,,33,1*74

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,40,24,43,26,41,5*78

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130628.000,A,2301.2585704,N,11421.9412302,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130628.000,1.081,0.181,0.181,0.281,0.796,0.831,2.340*7F



2025-07-31 21:06:29:345 ==>> $GBGGA,130629.000,2301.2585687,N,11421.9412371,E,1,11,0.92,77.150,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,39,16,59,25,24,60,40,41,44,,2.40,0.92,2.22,4*05

$GBGSV,7,1,26,33,66,242,41,3,61,190,39,6,55,44,34,14,54,188,36,1*42

$GBGSV,7,2,26,39,53,14,38,16,52,353,36,59,50,129,38,1,48,126,35,1*73

$GBGSV,7,3,26,7,47,193,35,2,45,238,34,25,45,299,39,24,43,26,40,1*4D

$GBGSV,7,4,26,60,43,240,39,40,42,161,37,9,39,323,34,10,36,201,33,1*4D

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,5,22,257,31,1*71

$GBGSV,7,6,26,34,21,144,33,44,19,88,33,38,17,199,34,8,16,202,31,1*78

$GBGSV,7,7,26,42,6,322,35,23,,,33,1*76

$GBGSV,2,1,07,33,66,242,43,39,53,14,40,25,45,299,41,24,43,26,41,5*79

$GBGSV,2,2,07,40,42,161,38,41,32,313,38,44,19,88,35,5*7A

$GBRMC,130629.000,A,2301.2585687,N,11421.9412371,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,130629.000,1.244,0.207,0.203,0.322,0.933,0.964,2.380*75



2025-07-31 21:06:29:630 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 21:06:30:365 ==>> $GBGGA,130630.000,2301.2585495,N,11421.9412560,E,1,18,0.72,77.077,M,-1.770,M,,*51

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,24,60,1.76,0.72,1.61,4*05

$GBGSA,A,3,40,13,41,42,34,44,,,,,,,1.76,0.72,1.61,4*09

$GBGSV,7,1,26,33,66,242,41,3,62,190,39,14,54,188,36,39,53,14,38,1*72

$GBGSV,7,2,26,6,52,350,34,16,52,353,36,59,50,129,39,7,47,193,35,1*77

$GBGSV,7,3,26,9,47,327,34,1,46,125,35,2,45,238,34,25,45,300,39,1*4C

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,201,33,1*4A

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,31,42,29,166,35,1*4F

$GBGSV,7,6,26,5,22,257,30,34,21,144,33,44,19,88,33,38,17,199,34,1*73

$GBGSV,7,7,26,8,16,202,31,23,8,262,33,1*70

$GBGSV,2,1,08,33,66,242,43,39,53,14,40,25,45,300,41,24,43,26,41,5*77

$GBGSV,2,2,08,40,42,161,38,41,32,313,38,34,21,144,33,44,19,88,35,5*40

$GBRMC,130630.000,A,2301.2585495,N,11421.9412560,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130630.000,1.995,0.283,0.264,0.424,1.501,1.526,2.751*79



2025-07-31 21:06:31:389 ==>> $GBGGA,130631.000,2301.2585299,N,11421.9412673,E,1,21,0.63,77.001,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.32,0.63,1.16,4*04

$GBGSA,A,3,60,40,13,41,42,38,34,44,23,,,,1.32,0.63,1.16,4*05

$GBGSV,7,1,26,33,66,242,41,3,62,190,39,14,54,188,36,39,53,14,38,1*72

$GBGSV,7,2,26,6,52,350,34,16,52,353,36,59,50,129,39,9,47,327,34,1*75

$GBGSV,7,3,26,1,46,125,35,2,45,238,34,25,45,300,39,7,45,178,35,1*49

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,201,33,1*4A

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,42,29,166,35,1*4C

$GBGSV,7,6,26,5,22,257,31,38,21,192,34,34,21,144,33,44,19,88,32,1*7D

$GBGSV,7,7,26,8,16,202,32,23,8,262,33,1*73

$GBGSV,3,1,10,33,66,242,43,39,53,14,41,25,45,300,40,24,43,26,40,5*7E

$GBGSV,3,2,10,40,42,161,38,41,32,313,38,42,29,166,35,34,21,144,33,5*7C

$GBGSV,3,3,10,44,19,88,35,23,8,262,32,5*73

$GBRMC,130631.000,A,2301.2585299,N,11421.9412673,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130631.000,3.432,0.180,0.176,0.262,2.359,2.378,3.413*7C



2025-07-31 21:06:31:631 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:06:32:391 ==>> $GBGGA,130632.000,2301.2585076,N,11421.9412748,E,1,21,0.63,76.898,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.32,0.63,1.16,4*04

$GBGSA,A,3,60,40,13,41,42,38,34,44,23,,,,1.32,0.63,1.16,4*05

$GBGSV,7,1,26,33,66,242,42,3,62,190,40,14,54,188,36,39,53,14,38,1*7F

$GBGSV,7,2,26,6,52,350,34,16,52,353,36,59,50,129,39,9,47,327,35,1*74

$GBGSV,7,3,26,1,46,125,35,2,45,238,34,25,45,300,39,7,45,178,35,1*49

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,201,33,1*4A

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,42,29,166,35,1*4C

$GBGSV,7,6,26,5,22,257,31,38,21,192,34,34,21,144,33,44,19,88,33,1*7C

$GBGSV,7,7,26,8,16,202,32,23,8,262,33,1*73

$GBGSV,3,1,11,33,66,242,43,39,53,14,41,25,45,300,41,24,43,26,41,5*7F

$GBGSV,3,2,11,40,42,161,38,41,32,313,38,42,29,166,35,38,21,192,31,5*78

$GBGSV,3,3,11,34,21,144,33,44,19,88,35,23,8,262,32,5*47

$GBRMC,130632.000,A,2301.2585076,N,11421.9412748,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,130632.000,3.552,0.226,0.219,0.324,2.419,2.438,3.428*76



2025-07-31 21:06:33:390 ==>> $GBGGA,130633.000,2301.2584861,N,11421.9412772,E,1,22,0.62,76.790,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.30,0.62,1.15,4*00

$GBGSA,A,3,24,60,40,13,41,42,38,34,44,23,,,1.30,0.62,1.15,4*03

$GBGSV,7,1,26,33,66,242,42,3,62,190,40,14,53,188,36,39,53,14,39,1*79

$GBGSV,7,2,26,6,52,350,34,16,52,353,36,59,50,129,39,2,47,239,34,1*70

$GBGSV,7,3,26,9,47,327,35,1,46,125,35,25,45,300,40,7,45,178,36,1*43

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,201,33,1*4A

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,42,29,166,36,1*4F

$GBGSV,7,6,26,5,22,257,31,38,21,192,34,34,21,144,33,44,19,88,33,1*7C

$GBGSV,7,7,26,8,16,202,32,23,8,262,33,1*73

$GBGSV,3,1,11,33,66,242,43,39,53,14,41,25,45,300,41,24,43,26,41,5*7F

$GBGSV,3,2,11,40,42,161,38,41,32,313,38,42,29,166,36,38,21,192,31,5*7B

$GBGSV,3,3,11,34,21,144,33,44,19,88,35,23,8,262,32,5*47

$GBRMC,130633.000,A,2301.2584861,N,11421.9412772,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,130633.000,3.485,0.235,0.229,0.338,2.380,2.399,3.359*7A



2025-07-31 21:06:33:649 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 21:06:33:769 ==>> 未匹配到【六轴自检】数据,请核对检查!
2025-07-31 21:06:33:776 ==>> #################### 【测试结束】 ####################
2025-07-31 21:06:33:952 ==>> 关闭5V供电
2025-07-31 21:06:33:960 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:06:34:071 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:06:34:390 ==>> $GBGGA,130634.000,2301.2584637,N,11421.9412818,E,1,23,0.61,76.683,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.30,0.61,1.15,4*03

$GBGSA,A,3,24,60,40,10,13,41,42,38,34,44,23,,1.30,0.61,1.15,4*01

$GBGSV,7,1,26,33,66,242,42,3,62,190,40,14,53,188,36,39,53,14,38,1*78

$GBGSV,7,2,26,6,52,350,34,16,52,353,36,59,50,129,39,2,47,239,34,1*70

$GBGSV,7,3,26,9,47,327,35,1,46,125,35,25,45,300,40,7,45,178,36,1*43

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,190,33,1*41

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,42,29,166,36,1*4F

$GBGSV,7,6,26,5,22,257,31,38,21,192,34,34,21,144,33,44,19,88,33,1*7C

$GBGSV,7,7,26,8,16,202,31,23,8,262,33,1*70

$GBGSV,3,1,11,33,66,242,43,39,53,14,41,25,45,300,41,24,43,26,41,5*7F

$GBGSV,3,2,11,40,42,161,38,41,32,313,38,42,29,166,36,38,21,192,31,5*7B

$GBGSV,3,3,11,34,21,144,33,44,19,88,35,23,8,262,32,5*47

$GBRMC,130634.000,A,2301.2584637,N,11421.9412818,E,0.000,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,130634.000,3.563,0.218,0.212,0.313,2.418,2.438,3.362*78



2025-07-31 21:06:34:958 ==>> 关闭5V供电成功
2025-07-31 21:06:34:966 ==>> 关闭33V供电
2025-07-31 21:06:34:974 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:06:35:069 ==>> 5A A5 02 5A A5 


2025-07-31 21:06:35:159 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:06:35:431 ==>> $GBGGA,130635.000,2301.2584477,N,11421.9412897,E,1,23,0.61,76.615,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.30,0.61,1.15,4*03

$GBGSA,A,3,24,60,40,10,13,41,42,38,34,44,23,,1.30,0.61,1.15,4*01

$GBGSV,7,1,26,33,66,242,42,3,62,190,40,14,53,188,36,39,53,14,38,1*78

$GBGSV,7,2,26,6,52,350,35,16,52,353,36,59,50,129,39,2,47,239,34,1*71

$GBGSV,7,3,26,9,47,327,35,1,46,125,35,25,45,300,40,7,45,178,36,1*43

$GBGSV,7,4,26,24,43,26,40,60,43,240,39,40,42,161,37,10,36,190,33,1*41

$GBGSV,7,5,26,13,33,218,34,41,32,313,38,4,32,112,32,42,29,166,36,1*4F

$GBGSV,7,6,26,5,22,257,31,38,21,192,34,34,21,144,33,44,19,88,33,1*7C

$GBGSV,7,7,26,8,16,202,31,23,8,262,34,1*77

$GBGSV,3,1,11,33,66,242,43,39,53,14,40,25,45,300,41,24,43,26,41,5*7E

$GBGSV,3,2,11,40,42,161,38,41,32,313,38,42,29,166,36,38,21,192,31,5*7B

$GBGSV,3,3,11,34,21,144,33,44,19,88,35,23,8,262,32,5*47

$GBRMC,130635.000,A,2301.2584477,N,11421.9412897,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,130635.000,3.481,0.196,0.191,0.283,2.372,2.392,3.295*74

[D][05:18:45][FCTY]get_ext_48v_vol retry i = 0,volt = 16
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 1,volt = 16
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][

2025-07-31 21:06:35:506 ==>> 05:18:45][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:45][FCTY]get_ext_48v_vol retry i = 8,volt = 11
 

2025-07-31 21:06:35:964 ==>> 关闭33V供电成功
2025-07-31 21:06:35:972 ==>> 关闭3.7V供电
2025-07-31 21:06:35:983 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:06:36:074 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


