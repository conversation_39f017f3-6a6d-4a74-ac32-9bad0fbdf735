2025-07-31 23:33:15:009 ==>> MES查站成功:
查站序号:P5100010053135F0验证通过
2025-07-31 23:33:15:032 ==>> 扫码结果:P5100010053135F0
2025-07-31 23:33:15:035 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:33:15:037 ==>> 测试参数版本:2024.10.11
2025-07-31 23:33:15:040 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:33:15:043 ==>> 检测【打开透传】
2025-07-31 23:33:15:045 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:33:15:128 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:33:15:387 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:33:15:392 ==>> 检测【检测接地电压】
2025-07-31 23:33:15:394 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:33:15:528 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 23:33:15:667 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:33:15:669 ==>> 检测【打开小电池】
2025-07-31 23:33:15:672 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:33:15:726 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:33:15:939 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:33:15:942 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:33:15:945 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:33:16:026 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 23:33:16:211 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:33:16:214 ==>> 检测【等待设备启动】
2025-07-31 23:33:16:217 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:33:16:487 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:33:16:682 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:33:17:235 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:33:17:312 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:33:17:387 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:33:17:795 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:33:18:268 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:33:18:271 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:33:18:542 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:33:18:544 ==>> 检测【产品通信】
2025-07-31 23:33:18:545 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:33:18:694 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 23:33:18:817 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:33:18:819 ==>> 检测【初始化完成检测】
2025-07-31 23:33:18:822 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:33:18:892 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:33:19:057 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:33:19:100 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:33:19:107 ==>> 检测【关闭大灯控制1】
2025-07-31 23:33:19:111 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:33:19:313 ==>> [D][05:17:51][COMM]2626 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:19:379 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:33:19:385 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:33:19:390 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:33:19:570 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:33:19:648 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:33:19:653 ==>> 检测【关闭仪表供电】
2025-07-31 23:33:19:655 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:33:19:826 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:33:19:920 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:33:19:922 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:33:19:923 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:33:20:084 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:33:20:197 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:33:20:199 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:33:20:202 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:33:20:342 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:20:417 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:33:20:480 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:33:20:483 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:33:20:484 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:33:20:718 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:33:20:776 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:33:20:779 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:33:20:780 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:33:20:823 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:33:20:913 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 23:33:21:068 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:33:21:071 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:33:21:074 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:33:21:124 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:33:21:334 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:21:363 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:33:21:366 ==>> 该项需要延时执行
2025-07-31 23:33:21:864 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault 

2025-07-31 23:33:21:894 ==>> change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 23:33:22:351 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:22:456 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54

2025-07-31 23:33:22:501 ==>> ][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:33:22:988 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not re

2025-07-31 23:33:23:095 ==>> ady
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54]

2025-07-31 23:33:23:199 ==>> [COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited

2025-07-31 23:33:23:259 ==>>  or not connected or cccd not enabled
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:33:23:349 ==>> [D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:23:424 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:33:24:372 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:24:909 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:33:25:368 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:33:25:371 ==>> 检测【33V输入电压ADC】
2025-07-31 23:33:25:374 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:33:25:376 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:25:639 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:17:57][COMM]adc read out 24v adc:1322  volt:33437 mv
[D][05:17:57][COMM]adc read left brake adc:17  volt:22 mv
[D][05:17:57][COMM]adc read right brake adc:17  volt:22 mv
[D][05:17:57][COMM]adc read throttle adc:18  volt:23 mv
[D][05:17:57][COMM]adc read battery ts volt:21 mv
[D][05:17:57][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2418  volt:3896 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:33:25:925 ==>> 【33V输入电压ADC】通过,【33032mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:33:25:928 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:33:25:931 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:33:26:033 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 1988mV
Get AD_V7 1099mV
OVER 150


2025-07-31 23:33:26:230 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:26:233 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:33:26:398 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:33:26:478 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:26:481 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:33:26:484 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 23:33:26:527 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:33:26:531 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:33:26:585 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:33:26:588 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:33:26:634 ==>> 【TP1_VCC12V(ADV7)】通过,【1099mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:33:26:637 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:33:26:775 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021
1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1650mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1096mV
OVER 150


2025-07-31 23:33:26:925 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:33:26:952 ==>> 【TP7_VCC3V3(ADV2)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:26:954 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:33:26:991 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:26:995 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:33:26:998 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 23:33:27:075 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:33:27:092 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:33:27:096 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:33:27:145 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:33:27:148 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:33:27:180 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:33:27:183 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:33:27:241 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2770mV
Get AD_V6 1995mV
Get AD_V7 1097mV
OVER 150


2025-07-31 23:33:27:482 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:27:485 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:33:27:510 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:27:514 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:33:27:519 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 23:33:27:528 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:33:27:531 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:33:27:547 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:33:27:551 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:33:27:571 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:33:27:575 ==>> 检测【打开WIFI(1)】
2025-07-31 23:33:27:579 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:33:27:605 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D

2025-07-31 23:33:27:650 ==>> ][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 23:33:28:073 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087589240

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539552

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:33:28:106 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:33:28:109 ==>> 检测【清空消息队列(1)】
2025-07-31 23:33:28:113 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:33:28:331 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:33:28:379 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:33:28:382 ==>> 检测【打开GPS(1)】
2025-07-31 23:33:28:384 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:33:28:392 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:33:28:496 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18

2025-07-31 23:33:28:526 ==>> :00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 23:33:28:649 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:33:28:654 ==>> 检测【打开GSM联网】
2025-07-31 23:33:28:678 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:33:28:813 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:33:28:923 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:33:28:926 ==>> 检测【打开仪表供电1】
2025-07-31 23:33:28:928 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:33:29:025 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:33:29:130 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:33:29:193 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:33:29:196 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:33:29:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:33:29:419 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:33:29:473 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:33:29:477 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:33:29:481 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:33:29:615 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33618]


2025-07-31 23:33:29:754 ==>> 【读取主控ADC采集的仪表电压】通过,【33618mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:33:29:757 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:33:29:766 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:29:932 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:30:069 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:33:30:071 ==>> 检测【AD_V20电压】
2025-07-31 23:33:30:075 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:30:178 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:30:253 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:33:30:389 ==>> [D][05:18:02][COMM]13727 imu init OK


2025-07-31 23:33:30:555 ==>> 本次取值间隔时间:373ms
2025-07-31 23:33:30:578 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:30:692 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:30:737 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:30:827 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:33:30:857 ==>> 本次取值间隔时间:160ms
2025-07-31 23:33:30:880 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:30:932 ==>>                                                                                                [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket s

2025-07-31 23:33:30:977 ==>> ize[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:33:30:992 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:31:082 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 23:33:31:172 ==>>                                                                                                                                                                                                                                                                                                                      [D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.119.47.31"

OK

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:33:31:487 ==>> 本次取值间隔时间:487ms
2025-07-31 23:33:31:505 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:31:608 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:31:638 ==>> [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:33:31:819 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:31:834 ==>> 本次取值间隔时间:212ms
2025-07-31 23:33:32:081 ==>> 本次取值间隔时间:243ms
2025-07-31 23:33:32:159 ==>> 本次取值间隔时间:69ms
2025-07-31 23:33:32:317 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:33:32:578 ==>> 本次取值间隔时间:406ms
2025-07-31 23:33:32:582 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:32:683 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:32:713 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:33:32:818 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:33:32:983 ==>> 本次取值间隔时间:294ms
2025-07-31 23:33:32:998 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:05][COMM]read battery soc:255


2025-07-31 23:33:33:001 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:33:103 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:33:193 ==>>                                              00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,39,,,39,40,,,39,25,,,38,59,,,38,1*75

$GBGSV,2,2,05,34,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1583.598,1583.598,50.554,2097152,2097152,2097152*4E

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 23:33:33:283 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:33:389 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:33:33:403 ==>> 本次取值间隔时间:291ms
2025-07-31 23:33:33:583 ==>> 本次取值间隔时间:168ms
2025-07-31 23:33:33:781 ==>> 本次取值间隔时间:185ms
2025-07-31 23:33:34:117 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,40,39,,,39,25,,,39,60,,,39,1*76

$GBGSV,3,2,12,43,,,39,59,,,38,34,,,38,11,,,38,1*79

$GBGSV,3,3,12,23,,,38,7,,,37,2,,,30,3,,,39,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1564.092,1564.092,50.019,2097152,2097152,2097152*42



2025-07-31 23:33:34:177 ==>> 本次取值间隔时间:383ms
2025-07-31 23:33:34:181 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:33:34:283 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:33:34:288 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:33:34:433 ==>> 1A A1 10 00 00 
Get AD_V20 1638mV
OVER 150


2025-07-31 23:33:34:598 ==>> 本次取值间隔时间:307ms
2025-07-31 23:33:34:619 ==>> 【AD_V20电压】通过,【1638mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:33:34:621 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:33:34:625 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:33:34:733 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:33:34:912 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:33:34:915 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:33:34:919 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:33:35:008 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 23:33:35:113 ==>>       ,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,41,39,,,39,25,,,39,60,,,39,1*74

$GBGSV,4,2,16,43,,,39,3,,,38,59,,,38,34,,,38,1*49

$GBGSV,4,3,16,11,,,38,23,,,38,7,,,38,41,,,37,1*4A

$GBGSV,4,4,16,1,,,35,2,,,31,5,,,30,4,,,25,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000

2025-07-31 23:33:35:158 ==>> ,K,N*20

$GBGST,,0.000,1510.699,1510.699,48.389,2097152,2097152,2097152*41

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:35:948 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:33:36:191 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,41,39,,,40,25,,,39,60,,,39,1*74

$GBGSV,5,2,19,43,,,39,3,,,39,59,,,38,34,,,38,1*46

$GBGSV,5,3,19,11,,,38,23,,,38,7,,,38,41,,,37,1*44

$GBGSV,5,4,19,1,,,35,16,,,35,32,,,34,24,,,32,1*48

$GBGSV,5,5,19,2,,,31,5,,,29,4,,,27,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1499.094,1499.094,48.006,2097152,2097152,2097152*45

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:36:984 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:33:37:014 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 23:33:37:119 ==>> $GBGGA,153341.017,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,39,,,40,25,,,39,43,,,39,1*7E

$GBGSV,6,2,22,3,,,39,34,,,39,60,,

2025-07-31 23:33:37:179 ==>> ,38,59,,,38,1*4C

$GBGSV,6,3,22,11,,,38,23,,,38,7,,,38,41,,,37,1*4F

$GBGSV,6,4,22,16,,,37,1,,,35,6,,,35,32,,,33,1*71

$GBGSV,6,5,22,10,,,33,24,,,33,9,,,33,2,,,31,1*7B

$GBGSV,6,6,22,5,,,29,4,,,28,1*76

$GBRMC,153341.017,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153341.017,0.000,1490.651,1490.651,47.723,2097152,2097152,2097152*53



2025-07-31 23:33:37:284 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:33:37:680 ==>> $GBGGA,153341.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,39,,,40,25,,,39,43,,,39,1*7E

$GBGSV,6,2,22,3,,,39,11,,,39,34,,,38,60,,,38,1*40

$GBGSV,6,3,22,59,,,38,23,,,38,7,,,38,41,,,37,1*43

$GBGSV,6,4,22,16,,,37,1,,,35,6,,,35,10,,,34,1*76

$GBGSV,6,5,22,32,,,33,9,,,33,24,,,32,2,,,31,1*7A

$GBGSV,6,6,22,5,,,29,4,,,28,1*76

$GBRMC,153341.517,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153341.517,0.000,1490.651,1490.651,47.724,2097152,2097152,2097152*51



2025-07-31 23:33:38:020 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 23:33:38:024 ==>> #################### 【测试结束】 ####################
2025-07-31 23:33:38:066 ==>> 关闭5V供电
2025-07-31 23:33:38:071 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:33:38:130 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:33:38:673 ==>> $GBGGA,153342.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,25,,,39,43,,,39,1*7F

$GBGSV,6,2,23,3,,,39,7,,,39,11,,,38,34,,,38,1*70

$GBGSV,6,3,23,60,,,38,59,,,38,23,,,38,41,,,37,1*73

$GBGSV,6,4,23,16,,,37,1,,,35,6,,,35,10,,,35,1*76

$GBGSV,6,5,23,32,,,33,9,,,33,24,,,32,12,,,32,1*49

$GBGSV,6,6,23,2,,,31,5,,,30,4,,,28,1*4F

$GBRMC,153342.517,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153342.517,0.000,1487.124,1487.124,47.608,2097152,2097152,2097152*5D



2025-07-31 23:33:39:008 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 23:33:39:068 ==>> 关闭5V供电成功
2025-07-31 23:33:39:074 ==>> 关闭33V供电
2025-07-31 23:33:39:079 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:33:39:113 ==>> 5A A5 02 5A A5 


2025-07-31 23:33:39:218 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:33:39:384 ==>> [D][05:18:11][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:33:39:702 ==>> $GBGGA,153343.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,39,43,,,39,1*78

$GBGSV,7,2,25,3,,,39,11,,,39,34,,,39,60,,,39,1*46

$GBGSV,7,3,25,59,,,39,7,,,38,23,,,38,41,,,37,1*44

$GBGSV,7,4,25,16,,,37,10,,,36,1,,,35,6,,,35,1*72

$GBGSV,7,5,25,32,,,33,9,,,33,24,,,32,12,,,32,1*4E

$GBGSV,7,6,25,2,,,31,5,,,30,4,,,28,38,,,53,1*45

$GBGSV,7,7,25,13,,,38,1*78

$GBRMC,153343.517,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153343.517,0.000,1494.339,1494.339,47.842,2097152,2097152,2097152*5C

[D][05:18:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:33:40:083 ==>> 关闭33V供电成功
2025-07-31 23:33:40:093 ==>> 关闭3.7V供电
2025-07-31 23:33:40:115 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:33:40:234 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


