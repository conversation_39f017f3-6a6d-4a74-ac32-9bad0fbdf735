2025-07-31 22:47:11:737 ==>> MES查站成功:
查站序号:P510001005312CA5验证通过
2025-07-31 22:47:11:743 ==>> 扫码结果:P510001005312CA5
2025-07-31 22:47:11:747 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:47:11:750 ==>> 测试参数版本:2024.10.11
2025-07-31 22:47:11:752 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:47:11:755 ==>> 检测【打开透传】
2025-07-31 22:47:11:757 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:47:11:815 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:47:12:099 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:47:12:122 ==>> 检测【检测接地电压】
2025-07-31 22:47:12:127 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:47:12:211 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:47:12:395 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:47:12:399 ==>> 检测【打开小电池】
2025-07-31 22:47:12:402 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:47:12:514 ==>> 6A A6 01 A6 6A 


2025-07-31 22:47:12:604 ==>> Battery ON
OVER 150


2025-07-31 22:47:12:667 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:47:12:669 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:47:12:671 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:47:12:815 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:47:12:941 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:47:12:943 ==>> 检测【等待设备启动】
2025-07-31 22:47:12:945 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:47:13:397 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:47:13:594 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:47:13:977 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:47:14:285 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:47:14:663 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:47:15:017 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:47:15:152 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:47:15:318 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:47:15:321 ==>> 检测【产品通信】
2025-07-31 22:47:15:323 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:47:15:472 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:47:15:660 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:47:15:663 ==>> 检测【初始化完成检测】
2025-07-31 22:47:15:666 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:47:15:849 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:47:16:003 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:47:16:005 ==>> 检测【关闭大灯控制1】
2025-07-31 22:47:16:007 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:47:16:363 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:47:16:558 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:47:16:561 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:47:16:563 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:47:16:699 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:47:16:865 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:47:16:884 ==>> 检测【关闭仪表供电】
2025-07-31 22:47:16:886 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:47:17:108 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:47:17:141 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:47:17:143 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:47:17:145 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:47:17:273 ==>> [D][05:17:52][COMM]3636 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:47:17:426 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:47:17:428 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:47:17:430 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:47:17:593 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:47:17:712 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:47:17:715 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:47:17:718 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:47:17:894 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:47:17:988 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:47:17:991 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:47:17:992 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:47:18:104 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:47:18:258 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:47:18:261 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:47:18:263 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:47:18:269 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 19
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:47:18:314 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:47:18:533 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:47:18:536 ==>> 该项需要延时执行
2025-07-31 22:47:18:768 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5003. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5004. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5004. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5005. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5006. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5006. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C7

2025-07-31 22:47:18:798 ==>> 1E22217 5007
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5007


2025-07-31 22:47:19:254 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:47:19:791 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:47:20:347 ==>>                                                                                                                                    5:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Aud

2025-07-31 22:47:20:452 ==>> io].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1

2025-07-31 22:47:20:557 ==>> 
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT C

2025-07-31 22:47:20:647 ==>> AN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 22:47:21:290 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:47:22:302 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:47:22:542 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:47:22:546 ==>> 检测【33V输入电压ADC】
2025-07-31 22:47:22:549 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:22:817 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:17:58][COMM]adc read out 24v adc:1303  volt:32956 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:58][COMM]adc read battery ts volt:6 mv
[D][05:17:58][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2396  volt:3860 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:47:23:079 ==>> 【33V输入电压ADC】通过,【32779mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:47:23:084 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:47:23:088 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:47:23:212 ==>> 1A A1 00 00 FC 
Get AD_V2 1639mV
Get AD_V3 1665mV
Get AD_V4 0mV
Get AD_V5 2762mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 22:47:23:317 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:47:23:360 ==>> 【TP7_VCC3V3(ADV2)】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:23:364 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:47:23:391 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:23:394 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:47:23:396 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 22:47:23:410 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:47:23:414 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:47:23:442 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:47:23:445 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:47:23:473 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:47:23:475 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:47:23:662 ==>> 1A A1 00 00 FC 
Get AD_V2 1639mV
Get AD_V3 1665mV
Get AD_V4 0mV
Get AD_V5 2761mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10013
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10014


2025-07-31 22:47:23:761 ==>> 【TP7_VCC3V3(ADV2)】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:23:767 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:47:23:780 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:23:787 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:47:23:790 ==>> 原始值:【2761】, 乘以分压基数【2】还原值:【5522】
2025-07-31 22:47:23:802 ==>> 【TP68_VCC5V5(ADV5)】通过,【5522mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:47:23:804 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:47:23:820 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:47:23:823 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:47:23:843 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:47:23:845 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:47:23:921 ==>> 1A A1 00 00 FC 
Get AD_V2 1641mV
Get AD_V3 1665mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:47:24:011 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:47:24:124 ==>> 【TP7_VCC3V3(ADV2)】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:24:127 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:47:24:143 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:24:145 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:47:24:150 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 22:47:24:161 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:47:24:163 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:47:24:180 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:47:24:182 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:47:24:202 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:47:24:204 ==>> 检测【打开WIFI(1)】
2025-07-31 22:47:24:222 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:47:24:538 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,2

2025-07-31 22:47:24:583 ==>> 3,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:47:24:736 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:47:24:740 ==>> 检测【清空消息队列(1)】
2025-07-31 22:47:24:766 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:47:24:996 ==>>                                                  ]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087518447

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290229

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:47:25:270 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:47:25:273 ==>> 检测【打开GPS(1)】
2025-07-31 22:47:25:275 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:47:25:313 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:47:25:510 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:47:25:544 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:47:25:547 ==>> 检测【打开GSM联网】
2025-07-31 22:47:25:550 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:47:25:705 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:47:25:813 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:47:25:816 ==>> 检测【打开仪表供电1】
2025-07-31 22:47:25:817 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:47:26:012 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:47:26:096 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:47:26:099 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:47:26:117 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:47:26:303 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:47:26:430 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:47:26:433 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:47:26:436 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:47:26:592 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33062]


2025-07-31 22:47:26:724 ==>> 【读取主控ADC采集的仪表电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:47:26:745 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:47:26:748 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:47:26:899 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:47:27:038 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:47:27:042 ==>> 检测【AD_V20电压】
2025-07-31 22:47:27:045 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:47:27:139 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:47:27:214 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:47:27:304 ==>> [D][05:18:02][COMM]13728 imu init OK


2025-07-31 22:47:27:349 ==>> 本次取值间隔时间:204ms
2025-07-31 22:47:27:396 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:47:27:499 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:47:27:634 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:47:27:739 ==>> 本次取值间隔时间:237ms
2025-07-31 22:47:27:791 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:47:27:905 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:47:28:010 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:47:28:160 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:47:28:190 ==>> 本次取值间隔时间:284ms
2025-07-31 22:47:28:241 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:47:28:250 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 22:47:28:355 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:47:28:445 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150


2025-07-31 22:47:28:490 ==>> 本次取值间隔时间:134ms
2025-07-31 22:47:28:538 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:28:543 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:47:28:546 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:47:28:610 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:47:28:816 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:47:28:820 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:47:28:839 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:47:29:045 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 27,

2025-07-31 22:47:29:090 ==>> 99

OK

[D][05:18:04][COMM]Main Task receive event:4
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:04][GNSS]rtk_id: 303E383D3535373F383A363F33333007



2025-07-31 22:47:29:195 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:47:29:585 ==>> [D][05:18:04][COMM]S->M yaw:INVALID
[D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:47:29:842 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:47:30:007 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:47:30:116 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:47:30:119 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:47:30:122 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:47:30:217 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:47:30:322 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:05][COMM]read battery soc:255
[D]

2025-07-31 22:47:30:352 ==>> [05:18:05][COMM]M->S yaw:INVALID


2025-07-31 22:47:30:386 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:47:30:389 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:47:30:391 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:47:30:607 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:47:30:666 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:47:30:670 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:47:30:672 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:47:30:953 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:47:31:162 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,34,,,42,40,,,42,59,,,42,41,,,42,1*7D

$GBGSV,3,2,12,39,,,41,33,,,38,25,,,41,12,,,39,1*7B

$GBGSV,3,3,12,24,,,39,60,,,39,16,,,38,11,,,36,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1706.619,1706.619,54.512,2097152,2097152,2097152*48

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 22:47:31:195 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:47:31:199 ==>> 检测【AD_V21电压】
2025-07-31 22:47:31:202 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:47:31:327 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
1A A1 20 00 00 
Get AD_V21 1652mV
OVER 150


2025-07-31 22:47:31:524 ==>> 本次取值间隔时间:319ms
2025-07-31 22:47:31:549 ==>> 【AD_V21电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:31:552 ==>> 检测【关闭仪表供电2】
2025-07-31 22:47:31:554 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:47:31:725 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:47:31:820 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:47:31:823 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:47:31:825 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:47:32:115 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,34,,,41,40,,,41,59,,,41,41,,,41,1*7E

$GBGSV,5,2,17,25,,,41,39,,,40,60,,,40,11,,,39,1*73

$GBGSV,5,3,17,7,,,39,24,,,38,16,,,38,33,,,38,1*41

$GBGSV,5,4,17,10,,,38,6,,,36,12,,,34,3,,,44,1*7F

$GBGSV,5,5,17,2,,,37,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.825,1616.825,51.674,2097152,2097152,2097152*4E



2025-07-31 22:47:32:283 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 22:47:32:355 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:47:32:358 ==>> 检测【打开AccKey2供电】
2025-07-31 22:47:32:363 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:47:32:513 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
[D][05:18:07][COMM]S->M yaw:INVALID


2025-07-31 22:47:32:656 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:47:32:659 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:47:32:680 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:32:922 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:08][COMM]adc read out 24v adc:1301  volt:32906 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:08][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:08][COMM]adc read battery ts volt:10 mv
[D][05:18:08][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2396  volt:3860 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:47:33:117 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,34,,,41,40,,,41,59,,,41,25,,,41,1*78

$GBGSV,6,2,23,3,,,40,41,,,40,39,,,40,60,,,40,1*49

$GBGSV,6,3,23,7,,,39,11,,,38,24,,,38,16,,,38,1*45

$GBGSV,6,4,23,33,,,38,1,,,38,10,,,37,6,,,37,1*73

$GBGSV,6,5,23,43,,,37,2,,,35,12,,,35,5,,,34,1*74

$GBGSV,6,6,23,4,,,33,32,,,31,23,,,43,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1567.868,1567.868,50.147,2097152,2097152,2097152*48



2025-07-31 22:47:33:194 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32906mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:47:33:197 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:47:33:201 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:47:33:377 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:47:33:478 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:47:33:481 ==>> 该项需要延时执行
2025-07-31 22:47:33:544 ==>> [D][05:18:08][COMM]M->S yaw:INVALID


2025-07-31 22:47:34:131 ==>> $GBGGA,144737.961,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,34,,,41,40,,,41,59,,,41,25,,,41,1*7F

$GBGSV,6,2,24,3,,,40,41,,,40,39,,,40,60,,,40,1*4E

$GBGSV,6,3,24,7,,,39,11,,,38,24,,,38,16,,,38,1*42

$GBGSV,6,4,24,33,,,38,1,,,38,43,,,38,10,,,37,1*4A

$GBGSV,6,5,24,6,,,37,9,,,37,23,,,36,2,,,35,1*4C

$GBGSV,6,6,24,12,,,35,5,,,34,4,,,33,32,,,32,1*73

$GBRMC,144737.961,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144737.961,0.000,1566.760,1566.760,50.103,2097152,2097152,2097152*5A



2025-07-31 22:47:34:297 ==>> [D][05:18:09][COMM]S->M yaw:INVALID
[D][05:18:09][COMM]read battery soc:255


2025-07-31 22:47:34:741 ==>> $GBGGA,144738.561,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,42,34,,,41,25,,,41,1*7F

$GBGSV,6,2,24,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,6,3,24,7,,,40,11,,,38,24,,,38,16,,,38,1*4C

$GBGSV,6,4,24,33,,,38,1,,,38,43,,,38,10,,,37,1*4A

$GBGSV,6,5,24,6,,,37,9,,,37,23,,,37,12,,,36,1*7F

$GBGSV,6,6,24,2,,,35,5,,,34,4,,,33,32,,,33,1*43

$GBRMC,144738.561,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144738.561,0.000,1580.582,1580.582,50.547,2097152,2097152,2097152*5D



2025-07-31 22:47:35:710 ==>> $GBGGA,144739.541,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,16,,,39,33,,,39,11,,,38,1*4A

$GBGSV,7,4,25,24,,,38,1,,,38,43,,,38,10,,,37,1*4C

$GBGSV,7,5,25,6,,,37,23,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,35,5,,,34,44,,,34,4,,,33,1*45

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144739.541,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144739.541,0.000,1573.744,1573.744,50.332,2097152,2097152,2097152*5A



2025-07-31 22:47:36:285 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:47:36:467 ==>> [D][05:18:11][COMM]M->S yaw:INVALID


2025-07-31 22:47:36:482 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:47:36:486 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:47:36:491 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:36:829 ==>> $GBGGA,144740.521,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,41,34,,,41,25,,,41,1*7F

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,16,,,39,33,,,39,11,,,38,1*4A

$GBGSV,7,4,25,24,,,38,1,,,38,43,,,38,10,,,37,1*4C

$GBGSV,7,5,25,6,,,37,23,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,36,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144740.521,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144740.521,0.000,1573.740,1573.740,50.327,2097152,2097152,2097152*56

[W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:4 mv
[D][05:18:12][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2397  v

2025-07-31 22:47:36:874 ==>> olt:3862 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:47:37:041 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:47:37:044 ==>> 检测【打开AccKey1供电】
2025-07-31 22:47:37:047 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:47:37:191 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:47:37:326 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:47:37:330 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:47:37:358 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:47:37:415 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150


2025-07-31 22:47:37:582 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 22:47:37:600 ==>> 【读取AccKey1电压(ADV14)前】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:47:37:614 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:47:37:617 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:37:673 ==>> $GBGGA,144741.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,16,,,39,43,,,39,33,,,38,1*4D

$GBGSV,7,4,25,11,,,38,24,,,38,1,,,38,10,,,38,1*44

$GBGSV,7,5,25,6,,,37,23,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,35,5,,,34,44,,,34,4,,,33,1*45

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144741.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144741.501,0.000,1575.402,1575.402,50.384,2097152,2097152,2097152*5C



2025-07-31 22:47:37:704 ==>>                                      

2025-07-31 22:47:37:913 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:6 mv
[D][05:18:13][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:47:38:128 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:47:38:131 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:47:38:136 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:47:38:319 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 
[D][05:18:13][COMM]read battery soc:255


2025-07-31 22:47:38:421 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:47:38:457 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:47:38:460 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:47:38:517 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150


2025-07-31 22:47:38:622 ==>> $GBGGA,144742.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,16,,,

2025-07-31 22:47:38:682 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 22:47:38:686 ==>> 39,43,,,39,33,,,39,1*4C

$GBGSV,7,4,25,11,,,39,24,,,38,1,,,38,10,,,38,1*45

$GBGSV,7,5,25,6,,,37,23,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,35,5,,,34,44,,,34,4,,,33,1*45

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144742.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144742.501,0.000,1578.720,1578.720,50.491,2097152,2097152,2097152*5C



2025-07-31 22:47:38:760 ==>> 【读取AccKey1电压(ADV14)后】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:47:38:763 ==>> 检测【打开WIFI(2)】
2025-07-31 22:47:38:768 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:47:38:789 ==>> [D][05:18:14][COMM]M->S yaw:INVALID


2025-07-31 22:47:38:938 ==>> [D][05:18:14][COMM]S->M yaw:INVALID
[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:47:39:097 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:47:39:101 ==>> 检测【转刹把供电】
2025-07-31 22:47:39:124 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:47:39:302 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:47:39:423 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:47:39:429 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:47:39:455 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:47:39:528 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:47:39:678 ==>> 1A A1 00 80 00 
Get AD_V15 2403mV
OVER 150
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,144743.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,16,,,39,43,,,39,33,,,39,1*4C

$GBGSV,7,4,25,11,,,39,24,,,38,1,,,38,10,,,38,1*45

$GBGSV,7,5,25,6,,,37,23,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,36,5,,,35,44,,,34,32,,,34,1*75

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,144743.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144743.501,0.000,1583.686,1583.686,50.641,2097152,2097152,2097152*52



2025-07-31 22:47:39:693 ==>> 原始值:【2403】, 乘以分压基数【2】还原值:【4806】
2025-07-31 22:47:39:722 ==>> 【读取AD_V15电压(前)】通过,【4806mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:47:39:725 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:47:39:729 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:47:39:829 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:47:39:904 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2431mV
OVER 150


2025-07-31 22:47:39:994 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 22:47:40:020 ==>> 【读取AD_V16电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:47:40:023 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:47:40:026 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:40:329 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:15][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:6 mv
[D][05:18:15][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 22:47:40:568 ==>> 【转刹把供电电压(主控ADC)】通过,【5414mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:47:40:571 ==>> 检测【转刹把供电电压】
2025-07-31 22:47:40:574 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:40:680 ==>> $GBGGA,144744.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,43,,,39,33,,,39,11,,,39,1*4B

$GBGSV,7,4,25,16,,,38,24,,,38,1,,,38,10,,,37,1*4C

$GBGSV,7,5,25,23,,,37,6,,,36,9,,,36,12,,,36,1*7F

$GBGSV,7,6,25,2,,,36,5,,,34,44,,,34,32,,,34,1*74

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,144744.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144744.501,0.000,1577.057,1577.057,50.434,2097152,2097152,2097152*55



2025-07-31 22:47:40:920 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:16][COMM]adc read battery ts volt:2 mv
[D][05:18:16][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:16][COMM]M->S yaw:INVALID


2025-07-31 22:47:41:126 ==>> 【转刹把供电电压】通过,【5417mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:47:41:131 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:47:41:136 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:47:41:274 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:47:41:398 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:47:41:404 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:47:41:409 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:47:41:498 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:47:41:603 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:47:41:612 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:47:41:678 ==>> $GBGGA,144745.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,42,59,,,41,25,,,41,1*7F

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,43,,,39,11,,,39,16,,,39,1*4C

$GBGSV,7,4,25,33,,,38,24,,,38,1,,,38,10,,,37,1*4B

$GBGSV,7,5,25,23,,,37,6,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,35,5,,,34,44,,,34,32,,,33,1*70

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,144745.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144745.501,0.000,1577.064,1577.064,50.441,2097152,2097152,2097152*56

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:47:41:708 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:47:41:783 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:47:41:813 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:47:41:831 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:47:41:835 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:47:41:840 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:47:41:933 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:47:42:008 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:47:42:056 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:47:42:063 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:47:42:069 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:47:42:113 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:47:42:308 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:47:42:341 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:47:42:347 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:47:42:371 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:47:42:413 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:47:42:625 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:47:42:630 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:47:42:640 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:47:42:714 ==>> $GBGGA,144746.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,59,,,41,25,,,41,1*7C

$GBGSV,7,2,25,41,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,25,7,,,40,43,,,39,11,,,39,16,,,39,1*4C

$GBGSV,7,4,25,33,,,38,24,,,38,1,,,38,10,,,37,1*4B

$GBGSV,7,5,25,23,,,37,6,,,37,9,,,36,12,,,36,1*7E

$GBGSV,7,6,25,2,,,35,5,,,34,44,,,34,32,,,33,1*70

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,144746.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144746.501,0.000,1575.403,1575.403,50.385,2097152,2097152,2097152*5A

3A A3 05 01 A3 


2025-07-31 22:47:42:804 ==>> ON_OUT5
OVER 150


2025-07-31 22:47:42:899 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:47:42:903 ==>> 检测【左刹电压测试1】
2025-07-31 22:47:42:909 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:43:214 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:18][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:18][COMM]adc read left brake adc:1713  volt:2258 mv
[D][05:18:18][COMM]adc read right brake adc:1719  volt:2266 mv
[D][05:18:18][COMM]adc read throttle adc:1718  volt:2264 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:47:43:428 ==>> 【左刹电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 22:47:43:432 ==>> 检测【右刹电压测试1】
2025-07-31 22:47:43:450 ==>> 【右刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 22:47:43:453 ==>> 检测【转把电压测试1】
2025-07-31 22:47:43:468 ==>> 【转把电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 22:47:43:472 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:47:43:502 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:47:43:685 ==>> 3A A3 03 00 A3 
$GBGGA,144747.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,41,60,,,40,1*4C

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,24,,,38,1,,,38,23,,,38,1*46

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,32,,,33,1*73

$GBGSV,7,7,26,4,,,33,14,,,29,1*48

$GBRMC,144747.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144747.501,0.000,1565.862,1565.862,50.105,2097152,2097152,2097152*51

OFF_OUT3
OVER 

2025-07-31 22:47:43:700 ==>> 150


2025-07-31 22:47:43:748 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:47:43:752 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:47:43:757 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:47:43:805 ==>> 3A A3 04 00 A3 


2025-07-31 22:47:43:910 ==>> OFF_OUT4
OVER 150


2025-07-31 22:47:44:018 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:47:44:022 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:47:44:027 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:47:44:105 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:47:44:290 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:47:44:295 ==>> 检测【左刹电压测试2】
2025-07-31 22:47:44:299 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:47:44:331 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:47:44:728 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3122  volt:5487 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:19][COMM]adc read battery ts volt:3 mv
[D][05:18:19][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:19][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,144748.501,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,41,59,,,41,25,,,41,1*7E

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,41,60,,,40,1*4C

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,38,24,,,38,1,,,38,23,,,38,1*47

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,

2025-07-31 22:47:44:773 ==>> 26,2,,,35,5,,,34,44,,,34,32,,,33,1*73

$GBGSV,7,7,26,4,,,33,14,,,30,1*40

$GBRMC,144748.501,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144748.501,0.000,1567.454,1567.454,50.154,2097152,2097152,2097152*5A



2025-07-31 22:47:44:824 ==>> 【左刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:47:44:830 ==>> 检测【右刹电压测试2】
2025-07-31 22:47:44:850 ==>> 【右刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:47:44:854 ==>> 检测【转把电压测试2】
2025-07-31 22:47:44:879 ==>> 【转把电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:47:44:885 ==>> 检测【晶振检测】
2025-07-31 22:47:44:898 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:47:45:093 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]
[D][05:18:20][COMM]S->M yaw:INVALID


2025-07-31 22:47:45:158 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:47:45:162 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:47:45:168 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:47:45:198 ==>> 1A A1 00 00 FC 
Get AD_V2 1639mV
Get AD_V3 1664mV
Get AD_V4 1647mV
Get AD_V5 2762mV
Get AD_V6 1989mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:47:45:428 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:47:45:432 ==>> 检测【检测BootVer】
2025-07-31 22:47:45:438 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:47:45:835 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130020290229
[D][05:18:20][FCTY]HardwareID  = 867222087518447
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
$GBGGA,144749.501,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,59,,,41,25,,,41,1*7C

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,40,60,,,40,1*4D

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,24,,,38,1,,,38,23,,,38,1*46

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,36,5,,,34,44,,,34,32,,,33,1*70

$GBGSV,7,7,26,4,,,33,14,,,30,1*40

$GBRMC,144749.501,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,14

2025-07-31 22:47:45:925 ==>> 4749.501,0.000,1569.045,1569.045,50.201,2097152,2097152,2097152*58

[D][05:18:21][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1295
[D][05:18:21][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3802 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:47:45:964 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:47:45:968 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:47:45:978 ==>> 检测【检测固件版本】
2025-07-31 22:47:45:983 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:47:45:987 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:47:45:991 ==>> 检测【检测蓝牙版本】
2025-07-31 22:47:46:004 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:47:46:010 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:47:46:025 ==>> 检测【检测MoBikeId】
2025-07-31 22:47:46:028 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:47:46:046 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:47:46:050 ==>> 检测【检测蓝牙地址】
2025-07-31 22:47:46:053 ==>> 取到目标值:FE16D5F6D559
2025-07-31 22:47:46:056 ==>> 【检测蓝牙地址】通过,【FE16D5F6D559】符合目标值【】要求!
2025-07-31 22:47:46:060 ==>> 提取到蓝牙地址:FE16D5F6D559
2025-07-31 22:47:46:070 ==>> 检测【BOARD_ID】
2025-07-31 22:47:46:074 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:47:46:091 ==>> 检测【检测充电电压】
2025-07-31 22:47:46:128 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:47:46:132 ==>> 检测【检测VBUS电压1】
2025-07-31 22:47:46:151 ==>> [D][05:18:21][COMM]M->S yaw:INVALID


2025-07-31 22:47:46:178 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:47:46:185 ==>> 检测【检测充电电流】
2025-07-31 22:47:46:227 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:47:46:233 ==>> 检测【检测IMEI】
2025-07-31 22:47:46:236 ==>> 取到目标值:867222087518447
2025-07-31 22:47:46:252 ==>> 【检测IMEI】通过,【867222087518447】符合目标值【】要求!
2025-07-31 22:47:46:259 ==>> 提取到IMEI:867222087518447
2025-07-31 22:47:46:264 ==>> 检测【检测IMSI】
2025-07-31 22:47:46:279 ==>> 取到目标值:460130020290229
2025-07-31 22:47:46:283 ==>> 【检测IMSI】通过,【460130020290229】符合目标值【】要求!
2025-07-31 22:47:46:303 ==>> 提取到IMSI:460130020290229
2025-07-31 22:47:46:312 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:47:46:323 ==>> 取到目标值:460130020290229
2025-07-31 22:47:46:331 ==>> 【校验网络运营商(移动)】通过,【460130020290229】符合目标值【】要求!
2025-07-31 22:47:46:352 ==>> 检测【打开CAN通信】
2025-07-31 22:47:46:358 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:47:46:363 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:47:46:409 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:47:46:579 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:47:46:585 ==>> 检测【检测CAN通信】
2025-07-31 22:47:46:609 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:47:46:682 ==>> $GBGGA,144750.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,40,60,,,40,1*4D

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,24,,,38,1,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,32,,,33,1*73

$GBGSV,7,7,26,4,,,33,14,,,30,1*40

$GBRMC,144750.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144750.501,0.000,1565.855,1565.855,50.099,2097152,2097152,2097152*53



2025-07-31 22:47:46:712 ==>> can send success


2025-07-31 22:47:46:757 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:47:46:802 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33198
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:47:46:851 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:47:46:859 ==>> 检测【关闭CAN通信】
2025-07-31 22:47:46:883 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:47:46:888 ==>> [D][05:18:22][COMM]S->M yaw:INVALID
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:47:46:911 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:47:47:142 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:47:47:146 ==>> 检测【打印IMU STATE】
2025-07-31 22:47:47:150 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:47:47:298 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:47:47:441 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:47:47:445 ==>> 检测【六轴自检】
2025-07-31 22:47:47:449 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:47:47:711 ==>> $GBGGA,144751.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,40,60,,,40,1*4D

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,24,,,38,1,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,38,6,,,37,9,,,36,12,,,36,1*72

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,34,1*41

$GBGSV,7,7,26,32,,,33,14,,,30,1*75

$GBRMC,144751.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144751.501,0.000,1569.041,1569.041,50.197,2097152,2097152,2097152*5D

[W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:47:48:353 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:47:48:674 ==>> $GBGGA,144752.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,42,59,,,41,25,,,41,1*7D

$GBGSV,7,2,26,41,,,41,39,,,41,3,,,40,60,,,40,1*4D

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,24,,,38,1,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,26,32,,,33,14,,,30,1*75

$GBRMC,144752.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144752.501,0.000,1569.049,1569.049,50.205,2097152,2097152,2097152*56



2025-07-31 22:47:49:312 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:47:49:679 ==>> $GBGGA,144753.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,26,41,,,41,39,,,41,60,,,41,3,,,40,1*4C

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,1,,,39,33,,,38,24,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144753.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144753.501,0.000,1569.041,1569.041,50.197,2097152,2097152,2097152*5F

[D][05:18:25][COMM]M->S yaw:INVALID


2025-07-31 22:47:50:344 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 22:47:50:691 ==>> $GBGGA,144754.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,42,41,,,42,59,,,41,1*7C

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,1,,,38,33,,,38,24,,,38,10,,,38,1*47

$GBGSV,7,5,26,23,,,38,6,,,37,9,,,36,12,,,36,1*72

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144754.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144754.501,0.000,1573.831,1573.831,50.357,2097152,2097152,2097152*56



2025-07-31 22:47:50:965 ==>> [D][05:18:26][COMM]S->M yaw:INVALID


2025-07-31 22:47:51:672 ==>> $GBGGA,144755.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,42,41,,,42,59,,,41,1*7C

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,1,,,38,24,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,38,6,,,37,9,,,36,12,,,36,1*72

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,34,1*41

$GBGSV,7,7,26,32,,,34,14,,,31,1*73

$GBRMC,144755.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144755.501,0.000,1577.013,1577.013,50.452,2097152,2097152,2097152*55



2025-07-31 22:47:51:777 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33182. cur_tick:38195. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F8

2025-07-31 22:47:51:807 ==>> 0C71E2223F 38196


2025-07-31 22:47:52:112 ==>> [D][05:18:27][COMM]M->S yaw:INVALID


2025-07-31 22:47:52:368 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 22:47:52:687 ==>> $GBGGA,144756.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,42,34,,,41,41,,,41,1*7E

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,38,1,,,38,24,,,38,10,,,38,1*47

$GBGSV,7,5,26,23,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,34,1*41

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144756.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144756.501,0.000,1569.038,1569.038,50.195,2097152,2097152,2097152*58

[D][05:18:28][COMM]S->M yaw:INVALID


2025-07-31 22:47:53:715 ==>> $GBGGA,144757.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,42,59,,,41,41,,,41,1*7F

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,39,1,,,38,24,,,38,10,,,38,1*46

$GBGSV,7,5,26,23,,,38,6,,,37,9,,,36,12,,,36,1*72

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,34,1*41

$GBGSV,7,7,26,32,,,34,14,,,31,1*73

$GBRMC,144757.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144757.501,0.000,1577.012,1577.012,50.451,2097152,2097152,2097152*54

[D][05:18:29][COMM]M->S yaw:INVALID


2025-07-31 22:47:54:365 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 22:47:54:672 ==>> $GBGGA,144758.501,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,59,,,41,41,,,41,1*7E

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,38,1,,,38,24,,,38,23,,,38,1*47

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,34,1*41

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144758.501,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144758.501,0.000,1570.635,1570.635,50.247,2097152,2097152,2097152*5A



2025-07-31 22:47:55:680 ==>> $GBGGA,144759.501,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,41,,,41,1*7D

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,41,60,,,41,1*4F

$GBGSV,7,3,26,7,,,40,43,,,39,11,,,39,16,,,39,1*4F

$GBGSV,7,4,26,33,,,38,1,,,38,24,,,38,23,,,38,1*47

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144759.501,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144759.501,0.000,1569.042,1569.042,50.199,2097152,2097152,2097152*5B



2025-07-31 22:47:56:373 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 22:47:56:673 ==>> $GBGGA,144800.501,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,41,,,41,1*7D

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,11,,,39,16,,,39,43,,,38,1*4E

$GBGSV,7,4,26,33,,,38,1,,,38,24,,,38,23,,,38,1*47

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,5,,,34,44,,,34,4,,,33,1*46

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144800.501,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144800.501,0.000,1564.255,1564.255,50.042,2097152,2097152,2097152*5F



2025-07-31 22:47:56:973 ==>> [D][05:18:32][COMM]S->M yaw:INVALID


2025-07-31 22:47:57:702 ==>> $GBGGA,144801.501,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,59,,,41,41,,,41,1*7D

$GBGSV,7,2,26,25,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,11,,,39,16,,,39,43,,,39,1*4F

$GBGSV,7,4,26,1,,,39,33,,,38,24,,,38,23,,,38,1*46

$GBGSV,7,5,26,10,,,37,6,,,37,9,,,36,12,,,36,1*7D

$GBGSV,7,6,26,2,,,35,44,,,34,5,,,33,4,,,33,1*41

$GBGSV,7,7,26,32,,,33,14,,,31,1*74

$GBRMC,144801.501,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144801.501,0.000,781.466,781.466,714.667,2097152,2097152,2097152*68



2025-07-31 22:47:57:762 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:47:58:005 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:47:58:384 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:47:58:704 ==>> $GBGGA,144802.501,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,43,34,,,42,39,,,41,59,,,41,1*71

$GBGSV,7,2,27,25,,,41,41,,,41,7,,,40,3,,,40,1*70

$GBGSV,7,3,27,60,,,40,56,,,40,16,,,39,1,,,39,1*44

$GBGSV,7,4,27,11,,,39,33,,,39,43,,,39,24,,,38,1*70

$GBGSV,7,5,27,23,,,38,10,,,37,6,,,37,9,,,36,1*70

$GBGSV,7,6,27,12,,,36,2,,,35,44,,,34,4,,,34,1*74

$GBGSV,7,7,27,32,,,34,5,,,33,14,,,31,1*47

$GBRMC,144802.501,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144802.501,0.000,787.004,787.004,719.732,2097152,2097152,2097152*67



2025-07-31 22:47:59:532 ==>> [D][05:18:34][CAT1]<<< 
OK

[D][05:18:34][CAT1]exec over: func id: 12, ret: 6
[D][05:18:34][COMM]M->S yaw:INVALID


2025-07-31 22:47:59:774 ==>> $GBGGA,144803.501,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,34,,,42,3,,,41,39,,,41,1*4F

$GBGSV,7,2,27,59,,,41,56,,,41,25,,,41,41,,,41,1*7B

$GBGSV,7,3,27,7,,,40,60,,,40,16,,,39,1,,,39,1*70

$GBGSV,7,4,27,11,,,39,33,,,39,43,,,39,10,,,38,1*77

$GBGSV,7,5,27,24,,,38,23,,,38,6,,,37,9,,,36,1*78

$GBGSV,7,6,27,12,,,36,2,,,35,44,,,35,5,,,34,1*74

$GBGSV,7,7,27,32,,,34,4,,,33,14,,,31,1*46

$GBRMC,144803.501,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144803.501,0.000,789.301,789.301,721.832,2097152,2097152,2097152*62

[D][05:18:35][COMM]Main Task receive event:142
[D][05:18:35][COMM]###### 46103 imu self test OK ######
[D][05:18:35][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-15,-11,4039]
[D][05:18:35][COMM]Main Task receive event:142 finished processing


2025-07-31 22:47:59:879 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:47:59:883 ==>> 检测【打印IMU STATE2】
2025-07-31 22:47:59:890 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:48:00:109 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:35][COMM]YAW data: 32763[32763]
[D][05:18:35][COMM]pitch:-66 roll:0
[D][05:18:35][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:48:00:198 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:48:00:215 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:48:00:219 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:48:00:305 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:48:00:476 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:48:00:485 ==>> 检测【检测VBUS电压2】
2025-07-31 22:48:00:491 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:48:00:516 ==>> [D][05:18:35][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:35][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:18:35][COMM]S->M yaw:INVALID


2025-07-31 22:48:00:894 ==>> $GBGGA,144804.501,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,39,,,42,34,,,42,3,,,41,1*4C

$GBGSV,7,2,27,60,,,41,59,,,41,56,,,41,25,,,41,1*78

$GBGSV,7,3,27,41,,,41,7,,,40,16,,,39,1,,,39,1*72

$GBGSV,7,4,27,11,,,39,33,,,39,43,,,39,24,,,38,1*70

$GBGSV,7,5,27,23,,,38,10,,,37,6,,,37,9,,,36,1*70

$GBGSV,7,6,27,12,,,36,2,,,35,44,,,35,5,,,34,1*74

$GBGSV,7,7,27,32,,,34,4,,,33,14,,,31,1*46

$GBRMC,144804.501,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144804.501,0.000,790.071,790.071,722.536,2097152,2097152,2097152*6F

[W][05:18:36][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:36][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:36][FCTY]==========Modules-nRF5340 ==========
[D][05:18:36][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:36][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:36][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:36][FCTY]DeviceID    = 460130020290229
[D][05:18:36][FCTY]HardwareID  = 867222087518447
[D][05:18:36][FCTY]MoBikeID    = 9999999999
[D][05:18:36][FCTY]LockID      = FFFFFFFFFF
[D][05:18:36][FCTY]BLEFWVersion= 105
[D][05:18:36][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:36][FCTY]Bat         = 3944 mv
[D][

2025-07-31 22:48:00:984 ==>> 05:18:36][FCTY]Current     = 100 ma
[D][05:18:36][FCTY]VBUS        = 10100 mv
[D][05:18:36][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:36][FCTY]Ext battery vol = 7, adc = 315
[D][05:18:36][FCTY]Acckey1 vol = 5487 mv, Acckey2 vol = 0 mv
[D][05:18:36][FCTY]Bike Type flag is invalied
[D][05:18:36][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:36][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:36][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:36][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:36][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:36][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:36][FCTY]Bat1         = 3802 mv
[D][05:18:36][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:36][FCTY]==========Modules-nRF5340 ==========
[D][05:18:36][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 22:48:01:257 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:48:01:587 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:36][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:36][FCTY]==========Modules-nRF5340 ==========
[D][05:18:36][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:36][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:36][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:36][FCTY]DeviceID    = 460130020290229
[D][05:18:36][FCTY]HardwareID  = 867222087518447
[D][05:18:36][FCTY]MoBikeID    = 9999999999
[D][05:18:36][FCTY]LockID      = FFFFFFFFFF
[D][05:18:36][FCTY]BLEFWVersion= 105
[D][05:18:36][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:36][FCTY]Bat         = 3944 mv
[D][05:18:36][FCTY]Current     = 100 ma
[D][05:18:36][FCTY]VBUS        = 10100 mv
[D][05:18:36][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:36][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:36][FCTY]Acckey1 vol = 5494 mv, Acckey2 vol = 25 mv
[D][05:18:36][FCTY]Bike Type flag is invalied
[D][05:18:36][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:36][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:36][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:36][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:36][FCTY]CAT1_GNSS_PL

2025-07-31 22:48:01:632 ==>> ATFORM = C4
[D][05:18:36][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:36][FCTY]Bat1         = 3802 mv
[D][05:18:36][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:36][FCTY]==========Modules-nRF5340 ==========
[D][05:18:36][COMM]M->S yaw:INVALID


2025-07-31 22:48:01:722 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 22:48:01:786 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:48:01:827 ==>> [D][05:18:37][COMM]S->M yaw:INVALID


2025-07-31 22:48:02:190 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:37][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========
[D][05:18:37][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:37][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:37][FCTY]DeviceID    = 460130020290229
[D][05:18:37][FCTY]HardwareID  = 867222087518447
[D][05:18:37][FCTY]MoBikeID    = 9999999999
[D][05:18:37][FCTY]LockID      = FFFFFFFFFF
[D][05:18:37][FCTY]BLEFWVersion= 105
[D][05:18:37][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:37][FCTY]Bat         = 3824 mv
[D][05:18:37][FCTY]Current     = 0 ma
[D][05:18:37][FCTY]VBUS        = 10100 mv
[D][05:18:37][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:37][FCTY]Ext battery vol = 3, adc = 127
[D][05:18:37][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 0 mv
[D][05:18:37][FCTY]Bike Type flag is invalied
[D][05:18:37][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:37][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:37][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:37][FCTY]C

2025-07-31 22:48:02:235 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:37][FCTY]Bat1         = 3802 mv
[D][05:18:37][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:48:02:327 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:48:02:845 ==>>                                                                                                                                       [D][05:18:37][COMM]Main Task receive event:65
[D][05:18:37][COMM]main task tmp_sleep_event = 80
[D][05:18:37][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:37][COMM]Main Task receive event:65 finished processing
[D][05:18:37][COMM]Main Task receive event:60
[D][05:18:37][COMM]smart_helmet_vol=255,255
[D][05:18:37][COMM]BAT CAN get state1 Fail 204
[D][05:18:37][COMM]BAT CAN get soc Fail, 204
[D][05:18:37][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:37][GNSS]stop locating
[D][05:18:37][GNSS]stop event:8
[D][05:18:37][GNSS]GPS stop. ret=0
[D][05:18:37][GNSS]all continue location stop
[D][05:18:37][COMM]report elecbike
[W][05:18:37][PROT]remove success[1629955117],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:37][COMM]Main Task receive event:60 finished processing
[D][05:18:37][PROT]min_index:0, type:0x5D03, priority:3
[D

2025-07-31 22:48:02:951 ==>> ][05:18:37][PROT]index:0
[D][05:18:37][PROT]is_send:1
[D][05:18:37][PROT]sequence_num:4
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x3
[D][05:18:37][PROT]msg_type:0x5d03
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]Sending traceid[9999999999900005]
[D][05:18:37][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:37][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:37][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:37][PROT]index:0 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:4
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]====================

2025-07-31 22:48:03:055 ==>> =======================================
[D][05:18:37][PROT]sending traceid [9999999999900005]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][CAT1]gsm read msg sub id: 24
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 24, ret: 6
[D][05:18:37][CAT1]sub id: 24, ret: 6

[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[W][05:18:37][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:37][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========
[D][05:18:37][F

2025-07-31 22:48:03:160 ==>> CTY]BootVersion = SA_BOOT_V109
[D][05:18:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:37][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:37][FCTY]DeviceID    = 460130020290229
[D][05:18:37][FCTY]HardwareID  = 867222087518447
[D][05:18:37][FCTY]MoBikeID    = 9999999999
[D][05:18:37][FCTY]LockID      = FFFFFFFFFF
[D][05:18:37][CAT1]Send Data To Server[198][198] ... ->:
0063B98F113311331133113311331B88B5E0EAA543B1F92B3AB8F98C453D5711DE805A2D7D05753EB9866289D350484A3317EA6C79BE1C914863343F75B8F69E4FD0FC55FD7D15D65EFD3DED9FABF80A7C599D67FC768D4601A7A35A293618B2C44190
[D][05:18:37][FCTY]BLEFWVersion= 105
[D][05:18:37][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:37][FCTY]Bat         = 3824 mv
[D][05:18:37][FCTY]Current     = 0 ma
[D][05:18:37][FCTY]VBUS        = 5000 mv
[D][05:18:37][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:37][FCTY]Ext battery vol = 2, adc = 118
[D][05:18:37][FCTY]Acckey1 vol = 5487 mv, Acckey2 vol = 0 mv
[D][05:18:37][FCTY]Bike Type flag is invalied
[D][05:18:37][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:37][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][0

2025-07-31 22:48:03:250 ==>> 5:18:37][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:37][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:37][FCTY]Bat1         = 3802 mv
[D][05:18:37][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 22:48:03:377 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:48:03:445 ==>> [D][05:18:38][GNSS]recv submsg id[1]
[D][05:18:38][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:38][GNSS]location stop evt done evt


2025-07-31 22:48:03:779 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][05:18:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:38][FCTY]DeviceID    = 460130020290229
[D][05:18:38][FCTY]HardwareID  = 867222087518447
[D][05:18:38][FCTY]MoBikeID    = 9999999999
[D][05:18:38][FCTY]LockID      = FFFFFFFFFF
[D][05:18:38][FCTY]BLEFWVersion= 105
[D][05:18:38][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:18:38][FCTY]Bat         = 3844 mv
[D][05:18:38][FCTY]Current     = 0 ma
[D][05:18:38][FCTY]VBUS        = 5000 mv
[D][05:18:39][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:39][FCTY]Ext battery vol = 2, adc = 96
[D][05:18:39][FCTY]Acckey1 vol = 5489 mv, Acckey2 vol = 0 mv
[D][05:18:39][FCTY]Bike Type flag is invalied
[D][05:18:39][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:39][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:39][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:39][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:39][FCTY]Bat1         = 3802 mv
[D][05:18:39][FCTY]==========

2025-07-31 22:48:03:809 ==>> ========== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:48:03:937 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:48:03:943 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:48:03:950 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:48:04:005 ==>> 5A A5 01 5A A5 


2025-07-31 22:48:04:110 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:48:04:215 ==>> [D][05:18:39][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30
[D][05:18:39][COMM]read battery soc:255


2025-07-31 22:48:04:242 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:48:04:248 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:48:04:253 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:48:04:305 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:48:04:541 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:48:04:549 ==>> 检测【打开WIFI(3)】
2025-07-31 22:48:04:580 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:48:04:728 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:48:04:851 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:48:04:857 ==>> 检测【扩展芯片hw】
2025-07-31 22:48:04:864 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:48:05:001 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:40][COMM]M->S yaw:INVALID
[D][05:18:40][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:48:05:130 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:48:05:135 ==>> 检测【扩展芯片boot】
2025-07-31 22:48:05:154 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:48:05:159 ==>> 检测【扩展芯片sw】
2025-07-31 22:48:05:184 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:48:05:190 ==>> 检测【检测音频FLASH】
2025-07-31 22:48:05:216 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:48:05:372 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:48:05:772 ==>> [D][05:18:41][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:41][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:41][COMM]----- get Acckey 1 and value:1------------
[D][05:18:41][COMM]----- get Acckey 2 and value:0------------
[D][05:18:41][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:48:06:468 ==>>                                                                                                                                                        get Acckey 2 and value:1------------
[D][05:18:41][COMM]more than the number of battery plugs
[D][05:18:41][COMM]VBUS is 1
[D][05:18:41][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:41][COMM]file:B50 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:41][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:41][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:18:41][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:41][COMM]Bat auth off fail, error:-1
[D][05:18:41][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:41][COMM]----- get Acckey 1 and value:1------------
[D][05:18:41][COMM]----- get Acckey 2 and value:1------------
[D][05:18:41][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:41][COMM]----- get Acckey 1 and value:1------------
[D][05:18:41][COMM]----- get Acckey 2 and value:1------------
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:41][COMM]file:B50 exist
[D]

2025-07-31 22:48:06:573 ==>> [05:18:41][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:41][COMM]read file, len:10800, num:3
[D][05:18:41][COMM]Main Task receive event:65
[D][05:18:41][COMM]main task tmp_sleep_event = 80
[D][05:18:41][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:41][COMM]Main Task receive event:65 finished processing
[D][05:18:41][COMM]Main Task receive event:66
[D][05:18:41][COMM]Try to Auto Lock Bat
[D][05:18:41][COMM]Main Task receive event:66 finished processing
[D][05:18:41][COMM]Main Task receive event:60
[D][05:18:41][COMM]smart_helmet_vol=255,255
[D][05:18:41][COMM]BAT CAN get state1 Fail 204
[D][05:18:41][COMM]BAT CAN get soc Fail, 204
[D][05:18:41][COMM]get soc error
[E][05:18:41][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:41][COMM]report elecbike
[W][05:18:41][PROT]remove success[1629955121],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:41][PROT]add success [1629955121],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:41][COMM]Main Task receive event:60 finished processing
[D][05:18:41][PROT]min_index:1, t

2025-07-31 22:48:06:678 ==>> ype:0x5D03, priority:4
[D][05:18:41][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:41][PROT]index:1
[D][05:18:41][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:41][PROT]is_send:1
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:3
[D][05:18:41][PROT]send_path:0x3
[D][05:18:41][PROT]msg_type:0x5d03
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]Sending traceid[9999999999900006]
[D][05:18:41][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:41][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:41][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:41][COMM]Receive Bat Lock cmd 0
[D][05:18:41][COMM]VBUS is 1
[D][05:18:41][COMM]Main Task receive event:61
[D][05:18:41][COMM][D301]:type:3, trace id:280
[D][05:18:41][COMM]id[], hw[000
[D][05:18:41][COMM]get mcMaincircuitVolt error
[D][05:18:41][COMM]get mcSubcircuitVolt error
[D][05:18:41][COM

2025-07-31 22:48:06:783 ==>> M]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:41][COMM]BAT CAN get state1 Fail 204
[D][05:18:41][COMM]BAT CAN get soc Fail, 204
[D][05:18:41][COMM]get bat work state err
[W][05:18:41][PROT]remove success[1629955121],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:41][PROT]add success [1629955121],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:41][COMM]Main Task receive event:61 finished processing
[D][05:18:41][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:41][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:41][COMM]--->crc16:0xb8a
[D][05:18:41][COMM]read file success
[W][05:18:41][COMM][Audio].l:[936].close hexlog save
[D][05:18:41][COMM]accel parse set 1
[D][05:18:41][COMM][Audio]mon:9,05:18:41
[D][05:18:41][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_start].l

2025-07-31 22:48:06:888 ==>> :[691].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D]

2025-07-31 22:48:06:963 ==>> [05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]read battery soc:255
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 22:48:07:932 ==>> [D][05:18:43][PROT]CLEAN,SEND:0
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:3
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][198] ... ->:
0063B98D113311331133113311331B88B350C8AF735BFD722077A0F7E352E47EF28A3C16

2025-07-31 22:48:08:008 ==>> 8D6B58C21BC23A853DB799C33D9C2B1EC68099E040A2878F62A5EF28D4B4E02DE834691DA7C5D59C9E0FD38740F9C05D5BAC69A9E2C6E7295DDEB282C5681C
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 22:48:08:234 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 22:48:08:900 ==>> [D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:48:09:236 ==>> [D][05:18:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:48:09:495 ==>> [D][05:18:44][COMM]crc 108B
[D][05:18:44][COMM]flash test ok


2025-07-31 22:48:10:002 ==>> [D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:45][COMM]56309 imu init OK
[D][05:18:45][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:45][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:45][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:45][COMM]accel parse set 0
[D][05:18:45][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:48:10:229 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:48:10:248 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:48:10:254 ==>> 检测【打开喇叭声音】
2025-07-31 22:48:10:274 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:48:10:918 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:45][COMM]file:A20 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:45][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:45][COMM]file:A20 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:45][COMM]read file, len:15228, num:4
[D][05:18:45][COMM]--->crc16:0x419c
[D][05:18:45][COMM]read file success
[W][05:18:45][COMM][Audio].l:[936].close hexlog save
[D][05:18:45][COMM]accel parse set 1
[D][05:18:45][COMM][Audio]mon:9,05:18:45
[D][05:18:45][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:45][COMM]

2025-07-31 22:48:11:023 ==>> f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:45][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l

2025-07-31 22:48:11:054 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:48:11:060 ==>> 检测【打开大灯控制】
2025-07-31 22:48:11:069 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:48:11:128 ==>> :[975].hexsend, index:5, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
                                      

2025-07-31 22:48:11:188 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:48:11:341 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:48:11:347 ==>> 检测【关闭仪表供电3】
2025-07-31 22:48:11:352 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:48:11:492 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:46][COMM]set POWER 0


2025-07-31 22:48:11:626 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:48:11:632 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:48:11:649 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:48:11:769 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:48:11:912 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:48:11:919 ==>> 检测【读大灯电压】
2025-07-31 22:48:11:928 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:48:12:092 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:47][COMM]arm_hub read adc[5],val[32969]


2025-07-31 22:48:12:186 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:48:12:195 ==>> 检测【关闭大灯控制2】
2025-07-31 22:48:12:217 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:48:12:233 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:48:12:393 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:48:12:456 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:48:12:463 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:48:12:468 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:48:12:591 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:47][COMM]arm_hub read adc[5],val[115]


2025-07-31 22:48:12:731 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:48:12:737 ==>> 检测【打开WIFI(4)】
2025-07-31 22:48:12:746 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:48:13:254 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:48][CAT1]gsm read msg sub id: 12
[D][05:18:48][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]index:1 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:5
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900006]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail 

2025-07-31 22:48:13:359 ==>> EXCEPTION i[1] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:500

[D][05:18:48][CAT1]<<< 
+MT ERROR:500

[D][05:18:48][CAT1]exec over: func id: 12, ret: 17
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Da

2025-07-31 22:48:13:366 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:48:13:372 ==>> 检测【EC800M模组版本】
2025-07-31 22:48:13:391 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:48:13:450 ==>> ta To Server[198][201] ... ->:
0063B98C113311331133113311331B88B30F94CB1AD6BD9E016133C987B45A65C4AFF24A217C78D2BC01CEFE79462894A9A95C03162ACD6FACCE4A7011ABF3E1B04444866E76217A51E3178B74F363843B120DCEC954C8EDA662C2F726BB39ACBCE567
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
                                                            

2025-07-31 22:48:13:554 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:48][CAT1]gsm read msg sub id: 12

2025-07-31 22:48:13:599 ==>> 
[D][05:18:48][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:48][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:48][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:48:13:648 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:48:13:655 ==>> 检测【配置蓝牙地址】
2025-07-31 22:48:13:691 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:48:13:796 ==>> [W][05:18:49][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:48:13:856 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FE16D5F6D559>】
2025-07-31 22:48:14:008 ==>> recv ble 1
recv ble 2
ble set mac ok :fe,16,d5,f6,d5,59
enable filters ret : 0

2025-07-31 22:48:14:131 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:48:14:138 ==>> 检测【BLETEST】
2025-07-31 22:48:14:143 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:48:14:204 ==>> 4A A4 01 A4 4A 


2025-07-31 22:48:14:249 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:48:14:429 ==>> recv ble 1
recv ble 2
<BSJ*MAC:FE16D5F6D559*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FE16D5F6D55999999OVER 150
[D][05:18:49][COMM]60844 imu init OK
[D][05:18:49][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:48:14:845 ==>> +WIFISCAN:3,0,F88C21BCF57D,-40
+WIFISCAN:3,1,CC057790A621,-60
+WIFISCAN:3,2,CC057790A620,-62

[D][05:18:50][CAT1]wifi scan report total[3]


2025-07-31 22:48:15:163 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:48:15:169 ==>> 该项需要延时执行
2025-07-31 22:48:15:515 ==>> [D][05:18:50][COMM]61855 imu init OK
[D][05:18:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:50][GNSS]recv submsg id[3]


2025-07-31 22:48:16:033 ==>> [D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:51][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:51][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:51][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:51][COMM]accel parse set 0
[D][05:18:51][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:48:16:244 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 22:48:16:457 ==>> [D][05:18:51][COMM]62868 imu init OK


2025-07-31 22:48:17:915 ==>> [D][05:18:53][COMM]S->M yaw:INVALID


2025-07-31 22:48:18:476 ==>> [D][05:18:53][PROT]CLEAN,SEND:1
[D][05:18:53][PROT]index:1 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:5
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900006]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][COMM]read battery soc:255
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B345821FAA3C64EBD67F62DDC8B528CE701D64C240998643

2025-07-31 22:48:18:551 ==>> 5F9F636567B680499CFAC9445B9BB308D88DCBD388C01CA0602F2C4E24D0E25576F87A6119C5D140ACD4F4A6FB18EFAA65171AA04D40E845A0A7A8
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 22:48:18:951 ==>> [D][05:18:54][COMM]M->S yaw:INVALID


2025-07-31 22:48:20:254 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:48:22:074 ==>> [D][05:18:57][COMM]S->M yaw:INVALID


2025-07-31 22:48:22:270 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 22:48:23:103 ==>> [D][05:18:58][COMM]M->S yaw:INVALID


2025-07-31 22:48:23:331 ==>> [D][05:18:58][COMM]S->M yaw:INVALID


2025-07-31 22:48:23:712 ==>> [D][05:18:58][PROT]CLEAN,SEND:1
[D][05:18:58][PROT]CLEAN:1
[D][05:18:58][PROT]index:0 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:4
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:2
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:58][PROT]===========================================================
[D][05:18:58][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:58][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][PROT]sending traceid [9999999999900005]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[198]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Sen

2025-07-31 22:48:23:787 ==>> d Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5FF8C0B496B8C37AAE52196FC88D41F68B8E609193BCEF9D48008377BA876D1E6C0DFD53145B8B22F9255D9E28D19C6650624EC87B3813710A5C7E434EF5DD7E1FE314C2089484C23EFC1A4F3ADBB6B52BE9B
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 22:48:24:264 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 22:48:24:369 ==>> [D][05:18:59][COMM]M->S yaw:INVALID


2025-07-31 22:48:25:167 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:48:25:174 ==>> 检测【检测WiFi结果】
2025-07-31 22:48:25:200 ==>> WiFi信号:【F88C21BCF57D】,信号值:-40
2025-07-31 22:48:25:206 ==>> WiFi信号:【CC057790A621】,信号值:-60
2025-07-31 22:48:25:230 ==>> WiFi信号:【CC057790A620】,信号值:-62
2025-07-31 22:48:25:239 ==>> WiFi数量【3】, 最大信号值:-40
2025-07-31 22:48:25:249 ==>> 检测【检测GPS结果】
2025-07-31 22:48:25:268 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:48:25:398 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:00][GNSS]stop locating
[D][05:19:00][GNSS]all continue location stop
[W][05:19:00][GNSS]stop locating
[D][05:19:00][GNSS]all sing location stop


2025-07-31 22:48:26:183 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:48:26:194 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:26:218 ==>> 定位已等待【1】秒.
2025-07-31 22:48:26:274 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 22:48:26:639 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:19:01][COMM]Open GPS Module...
[D][05:19:01][COMM]LOC_MODEL_CONT
[D][05:19:01][GNSS]start event:8
[D][05:19:01][GNSS]GPS start. ret=0
[W][05:19:01][GNSS]start cont locating
[D][05:19:01][CAT1]gsm read msg sub id: 23
[D][05:19:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:19:01][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:19:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:48:27:194 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:27:204 ==>> 定位已等待【2】秒.
2025-07-31 22:48:27:336 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:48:28:008 ==>> [D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:48:28:204 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:28:214 ==>> 定位已等待【3】秒.
2025-07-31 22:48:28:238 ==>>                                                ,,,,,,,,,,,4*14

$GBGSV,2,1,07,40,,,42,34,,,41,39,,,41,60,,,41,1*7E

$GBGSV,2,2,07,41,,,39,59,,,38,23,,,34,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1634.620,1634.620,52.269,2097152,2097152,2097152*45

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:19:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:19:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 23, ret: 6
[D][05:19:03][CAT1]sub id: 23, ret: 6



2025-07-31 22:48:28:309 ==>> [D][05:19:03][COMM]read battery soc:255
[D][05:19:03][COMM]S->M yaw:INVALID


2025-07-31 22:48:28:569 ==>> [D][05:19:03][GNSS]recv submsg id[1]
[D][05:19:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:48:28:930 ==>> [D][05:19:04][PROT]CLEAN,SEND:0
[D][05:19:04][PROT]index:0 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:4
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:1
[D][05:19:04][PROT]send_path:0x2
[D][05:19:04][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:04][PROT]===========================================================
[W][05:19:04][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955144]
[D][05:19:04][PROT]===========================================================
[D][05:19:04][PROT]sending traceid [9999999999900005]
[D][05:19:04][PROT]Send_TO_M2M [1629955144]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:04][SAL ]sock send credit cnt[6]
[D][05:19:04][SAL ]sock send ind credit cnt[6]
[D][05:19:04][M2M ]m2m send data len[198]
[D][05:19:04][SAL ]Cellular task submsg id[10]
[D][05:19:04][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:04][CAT1]gsm read msg sub id: 15
[D][05:19:04][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5E4B4713E

2025-07-31 22:48:29:005 ==>> FAD029844A693A76EEA34D4D07664680083DFA74BC4238598B3C6CAE200584F915BA6E6E6155D323541A58A619CF6B6AE5B2FDAE5BB527B8D5CECF6CD63B692AA2E4CBF659799D147D7952C34E7E
[D][05:19:04][CAT1]<<< 
SEND OK

[D][05:19:04][CAT1]exec over: func id: 15, ret: 11
[D][05:19:04][CAT1]sub id: 15, ret: 11

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:04][M2M ]g_m2m_is_idle become true
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:04][PROT]M2M Send ok [1629955144]


2025-07-31 22:48:29:110 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,40,,,42,34,,,41,39,,,41,25,,,41,1*79

$GBGSV,5,2,17,60,,,40,41,,,40,59,,,40,11,,,39,1*76

$GBGSV,5,3,17,7,,,39,16,,,38,24,,,37,23,,,36,1*41

$GBGSV,5,4,17,12,,,36,6,,,36,4,,,33,43,,,17,1*71

$GBGSV,5,5,17,1,,,41,

2025-07-31 22:48:29:140 ==>> 1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1544.465,1544.465,49.547,2097152,2097152,2097152*44



2025-07-31 22:48:29:215 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:29:225 ==>> 定位已等待【4】秒.
2025-07-31 22:48:29:354 ==>> [D][05:19:04][COMM]M->S yaw:INVALID


2025-07-31 22:48:30:175 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,34,,,41,39,,,41,25,,,41,1*7F

$GBGSV,6,2,21,59,,,41,3,,,41,60,,,40,41,,,40,1*4D

$GBGSV,6,3,21,1,,,39,11,,,39,7,,,39,16,,,39,1*71

$GBGSV,6,4,21,2,,,38,24,,,37,23,,,37,43,,,37,1*4A

$GBGSV,6,5,21,12,,,36,6,,,36,44,,,34,5,,,34,1*76

$GBGSV,6,6,21,4,,,33,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1589.217,1589.217,50.821,2097152,2097152,2097152*41



2025-07-31 22:48:30:220 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:30:231 ==>> 定位已等待【5】秒.
2025-07-31 22:48:30:280 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 22:48:31:191 ==>> $GBGGA,144831.017,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,34,,,41,39,,,41,25,,,41,1*7C

$GBGSV,6,2,22,59,,,41,60,,,41,41,,,41,3,,,40,1*4F

$GBGSV,6,3,22,7,,,40,11,,,39,16,,,39,1,,,38,1*7D

$GBGSV,6,4,22,2,,,37,24,,,37,23,,,37,43,,,37,1*46

$GBGSV,6,5,22,6,,,37,12,,,36,44,,,34,5,,,34,1*74

$GBGSV,6,6,22,4,,,33,32,,,30,1*40

$GBRMC,144831.017,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144831.017,0.000,1575.424,1575.424,50.406,2097152,2097152,2097152*5B



2025-07-31 22:48:31:221 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:31:230 ==>> 定位已等待【6】秒.
2025-07-31 22:48:31:699 ==>> $GBGGA,144831.517,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,42,39,,,41,25,,,41,1*79

$GBGSV,6,2,24,59,,,41,41,,,41,60,,,40,3,,,40,1*48

$GBGSV,6,3,24,7,,,40,11,,,39,16,,,39,1,,,38,1*7B

$GBGSV,6,4,24,23,,,38,43,,,38,24,,,37,6,,,37,1*44

$GBGSV,6,5,24,2,,,36,12,,,36,10,,,35,44,,,34,1*42

$GBGSV,6,6,24,5,,,34,9,,,34,4,,,33,32,,,31,1*4B

$GBRMC,144831.517,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144831.517,0.000,1566.785,1566.785,50.128,2097152,2097152,2097152*57



2025-07-31 22:48:32:226 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:32:236 ==>> 定位已等待【7】秒.
2025-07-31 22:48:32:288 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 22:48:32:669 ==>> $GBGGA,144832.517,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,41,,,41,60,,,40,3,,,40,1*48

$GBGSV,6,3,24,7,,,40,11,,,39,16,,,39,1,,,39,1*7A

$GBGSV,6,4,24,23,,,38,43,,,38,24,,,37,6,,,37,1*44

$GBGSV,6,5,24,2,,,36,12,,,36,10,,,36,9,,,35,1*79

$GBGSV,6,6,24,44,,,34,5,,,34,4,,,34,32,,,32,1*76

$GBRMC,144832.517,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144832.517,0.000,1573.680,1573.680,50.335,2097152,2097152,2097152*5A



2025-07-31 22:48:33:241 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:33:251 ==>> 定位已等待【8】秒.
2025-07-31 22:48:33:692 ==>> $GBGGA,144833.517,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,41,,,40,60,,,40,3,,,40,1*49

$GBGSV,6,3,24,7,,,40,16,,,39,11,,,38,1,,,38,1*7A

$GBGSV,6,4,24,43,,,38,23,,,37,24,,,37,6,,,37,1*4B

$GBGSV,6,5,24,2,,,36,12,,,36,10,,,36,9,,,35,1*79

$GBGSV,6,6,24,44,,,34,5,,,34,4,,,34,32,,,32,1*76

$GBRMC,144833.517,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144833.517,0.000,1566.768,1566.768,50.112,2097152,2097152,2097152*5C



2025-07-31 22:48:34:157 ==>> [D][05:19:09][PROT]CLEAN,SEND:0
[D][05:19:09][PROT]CLEAN:0
[D][05:19:09][PROT]index:2 1629955149
[D][05:19:09][PROT]is_send:0
[D][05:19:09][PROT]sequence_num:6
[D][05:19:09][PROT]retry_timeout:0
[D][05:19:09][PROT]retry_times:3
[D][05:19:09][PROT]send_path:0x2
[D][05:19:09][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:09][PROT]===========================================================
[W][05:19:09][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955149]
[D][05:19:09][PROT]===========================================================
[D][05:19:09][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908B1C89C8906980220
[D][05:19:09][PROT]sending traceid [9999999999900007]
[D][05:19:09][PROT]Send_TO_M2M [1629955149]
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:09][SAL ]sock send credit cnt[6]
[D][05:19:09][SAL ]sock send ind credit cnt[6]
[D][05:19:09][M2M ]m2m send data len[134]
[D][05:19:09][SAL ]Cellular task submsg id[10]
[D][05:19:09][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:09][CAT1]gsm read msg sub id: 15
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:09][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:09][CAT1]Send Data To Server[134][137] ... ->:
00

2025-07-31 22:48:34:232 ==>> 43B683113311331133113311331B88BE1D4352C255B3C73182EA8A4310A0A49AC44BD89521AB8F7A1331D34253BD55F477DF5FB26DEA03F90D0257AA07BA8EA9026B
[D][05:19:09][CAT1]<<< 
SEND OK

[D][05:19:09][CAT1]exec over: func id: 15, ret: 11
[D][05:19:09][CAT1]sub id: 15, ret: 11

[D][05:19:09][SAL ]Cellular task submsg id[68]
[D][05:19:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:09][M2M ]g_m2m_is_idle become true
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:09][PROT]M2M Send ok [1629955149]
[D][05:19:09][COMM]S->M yaw:INVALID


2025-07-31 22:48:34:247 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:34:257 ==>> 定位已等待【9】秒.
2025-07-31 22:48:34:307 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 22:48:34:689 ==>> $GBGGA,144834.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,41,,,41,60,,,40,3,,,40,1*48

$GBGSV,6,3,24,7,,,39,16,,,39,11,,,38,1,,,38,1*74

$GBGSV,6,4,24,43,,,38,23,,,38,24,,,37,6,,,37,1*44

$GBGSV,6,5,24,10,,,37,2,,,36,12,,,36,9,,,35,1*78

$GBGSV,6,6,24,44,,,34,5,,,34,4,,,33,32,,,32,1*71

$GBRMC,144834.517,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144834.517,0.000,1568.498,1568.498,50.169,2097152,2097152,2097152*57



2025-07-31 22:48:35:076 ==>> [D][05:19:10][COMM]M->S yaw:INVALID


2025-07-31 22:48:35:256 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:35:264 ==>> 定位已等待【10】秒.
2025-07-31 22:48:35:700 ==>> $GBGGA,144835.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,41,1*48

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,37,1*7D

$GBGSV,7,5,25,6,,,37,10,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,35,44,,,34,5,,,34,4,,,33,1*4E

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144835.517,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144835.517,0.000,1577.065,1577.065,50.441,2097152,2097152,2097152*59



2025-07-31 22:48:36:268 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:36:280 ==>> 定位已等待【11】秒.
2025-07-31 22:48:36:313 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 22:48:36:705 ==>> $GBGGA,144836.517,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,41,1*48

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,39,1*7A

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,38,1*72

$GBGSV,7,5,25,6,,,37,10,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144836.517,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144836.517,0.000,1583.692,1583.692,50.647,2097152,2097152,2097152*5E



2025-07-31 22:48:37:274 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:37:285 ==>> 定位已等待【12】秒.
2025-07-31 22:48:37:703 ==>> $GBGGA,144837.517,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,40,3,,,40,1*48

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,39,1*7A

$GBGSV,7,4,25,43,,,39,23,,,38,33,,,38,24,,,38,1*73

$GBGSV,7,5,25,10,,,38,6,,,37,2,,,36,12,,,36,1*7A

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144837.517,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144837.517,0.000,1583.688,1583.688,50.643,2097152,2097152,2097152*5B



2025-07-31 22:48:37:777 ==>> [D][05:19:13][COMM]S->M yaw:INVALID


2025-07-31 22:48:38:282 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:38:295 ==>> 定位已等待【13】秒.
2025-07-31 22:48:38:328 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 22:48:38:696 ==>> $GBGGA,144838.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,34,,,41,25,,,41,1*79

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,41,1*48

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,38,1*72

$GBGSV,7,5,25,10,,,38,6,,,37,2,,,36,12,,,36,1*7A

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144838.517,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144838.517,0.000,1585.352,1585.352,50.702,2097152,2097152,2097152*50



2025-07-31 22:48:39:291 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:39:307 ==>> 定位已等待【14】秒.
2025-07-31 22:48:39:336 ==>> [D][05:19:14][PROT]CLEAN,SEND:2
[D][05:19:14][COMM]M->S yaw:INVALID
[D][05:19:14][PROT]index:2 1629955154
[D][05:19:14][PROT]is_send:0
[D][05:19:14][PROT]sequence_num:6
[D][05:19:14][PROT]retry_timeout:0
[D][05:19:14][PROT]retry_times:2
[D][05:19:14][PROT]send_path:0x2
[D][05:19:14][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:14][PROT]===========================================================
[W][05:19:14][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955154]
[D][05:19:14][PROT]===========================================================
[D][05:19:14][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908B1C89C8906980220
[D][05:19:14][PROT]sending traceid [9999999999900007]
[D][05:19:14][PROT]Send_TO_M2M [1629955154]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:14][SAL ]sock send credit cnt[6]
[D][05:19:14][SAL ]sock send ind credit cnt[6]
[D][05:19:14][M2M ]m2m send data len[134]
[D][05:19:14][SAL ]Cellular task submsg id[10]
[D][05:19:14][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:14][CAT1]gsm read msg sub id: 15
[D][05:19:14][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:14][CAT1]<<< 
ERROR



2025-07-31 22:48:39:699 ==>> $GBGGA,144839.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,34,,,42,25,,,41,1*7B

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,41,1*48

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,39,1*7A

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,38,1*72

$GBGSV,7,5,25,10,,,38,6,,,37,2,,,36,12,,,36,1*7A

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144839.517,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144839.517,0.000,1590.332,1590.332,50.866,2097152,2097152,2097152*5C



2025-07-31 22:48:40:306 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:40:317 ==>> 定位已等待【15】秒.
2025-07-31 22:48:40:336 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 22:48:40:697 ==>> $GBGGA,144840.517,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,39,,,41,25,,,41,1*7A

$GBGSV,7,2,26,59,,,41,41,,,41,60,,,41,3,,,41,1*4B

$GBGSV,7,3,26,7,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,7,4,26,43,,,38,23,,,38,33,,,38,24,,,37,1*7E

$GBGSV,7,5,26,10,,,37,6,,,37,2,,,36,12,,,36,1*76

$GBGSV,7,6,26,9,,,36,44,,,35,5,,,34,4,,,34,1*48

$GBGSV,7,7,26,32,,,33,20,,,36,1*74

$GBRMC,144840.517,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144840.517,0.000,1583.692,1583.692,50.647,2097152,2097152,2097152*5F



2025-07-31 22:48:41:318 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:41:331 ==>> 定位已等待【16】秒.
2025-07-31 22:48:41:693 ==>> $GBGGA,144841.517,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,43,34,,,41,39,,,41,25,,,41,1*78

$GBGSV,7,2,26,59,,,41,41,,,41,3,,,41,60,,,40,1*4A

$GBGSV,7,3,26,7,,,40,16,,,39,11,,,39,43,,,39,1*4F

$GBGSV,7,4,26,1,,,38,23,,,38,33,,,38,24,,,38,1*47

$GBGSV,7,5,26,10,,,37,6,,,37,2,,,36,12,,,36,1*76

$GBGSV,7,6,26,9,,,36,44,,,35,5,,,34,4,,,34,1*48

$GBGSV,7,7,26,32,,,33,38,,,37,1*7C

$GBRMC,144841.517,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144841.517,0.000,1585.349,1585.349,50.699,2097152,2097152,2097152*5D



2025-07-31 22:48:42:328 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:42:340 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 22:48:42:366 ==>> 定位已等待【17】秒.
2025-07-31 22:48:42:722 ==>> $GBGGA,144842.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,39,1*7A

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,38,1*72

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144842.517,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144842.517,0.000,1582.032,1582.032,50.593,2097152,2097152,2097152*57

[D][05:19:18][COMM]S->M yaw:INVALID


2025-07-31 22:48:43:329 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:43:341 ==>> 定位已等待【18】秒.
2025-07-31 22:48:43:694 ==>> $GBGGA,144843.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,41,1*48

$GBGSV,7,3,25,7,,,40,11,,,39,16,,,38,1,,,38,1*7A

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144843.517,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144843.517,0.000,1578.717,1578.717,50.489,2097152,2097152,2097152*5C



2025-07-31 22:48:44:229 ==>> [D][05:19:19][COMM]M->S yaw:INVALID


2025-07-31 22:48:44:334 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 22:48:44:350 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:44:372 ==>> 定位已等待【19】秒.
2025-07-31 22:48:44:697 ==>> $GBGGA,144844.517,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,38,1,,,38,1*7A

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144844.517,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144844.517,0.000,1577.057,1577.057,50.434,2097152,2097152,2097152*5D



2025-07-31 22:48:45:338 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:45:351 ==>> 定位已等待【20】秒.
2025-07-31 22:48:45:690 ==>> $GBGGA,144845.517,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,3,,,41,60,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,5,,,34,4,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144845.517,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144845.517,0.000,1578.716,1578.716,50.487,2097152,2097152,2097152*54



2025-07-31 22:48:46:351 ==>> [D][05:19:21][COMM]read battery soc:255


2025-07-31 22:48:46:361 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:46:386 ==>> 定位已等待【21】秒.
2025-07-31 22:48:46:699 ==>> $GBGGA,144846.517,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,3,,,41,60,,,41,1*48

$GBGSV,7,3,25,7,,,40,16,,,38,11,,,38,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,23,,,38,33,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,35,44,,,34,5,,,34,4,,,34,1*49

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144846.517,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144846.517,0.000,1575.402,1575.402,50.384,2097152,2097152,2097152*53



2025-07-31 22:48:47:364 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:47:374 ==>> 定位已等待【22】秒.
2025-07-31 22:48:47:700 ==>> $GBGGA,144847.517,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,3,,,41,41,,,40,60,,,40,1*48

$GBGSV,7,3,25,7,,,39,16,,,38,11,,,38,1,,,38,1*75

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,37,24,,,37,1*72

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,35,44,,,34,5,,,34,4,,,34,1*49

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144847.517,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144847.517,0.000,1568.765,1568.765,50.168,2097152,2097152,2097152*52



2025-07-31 22:48:48:359 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 22:48:48:390 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:48:400 ==>> 定位已等待【23】秒.
2025-07-31 22:48:48:695 ==>> $GBGGA,144848.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,60,,,41,3,,,40,41,,,40,1*48

$GBGSV,7,3,25,7,,,39,16,,,38,11,,,38,1,,,38,1*75

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,35,44,,,34,5,,,34,4,,,34,1*49

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144848.517,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144848.517,0.000,1570.422,1570.422,50.220,2097152,2097152,2097152*52



2025-07-31 22:48:49:344 ==>> [D][05:19:24][CAT1]exec over: func id: 15, ret: -93
[D][05:19:24][CAT1]sub id: 15, ret: -93

[D][05:19:24][SAL ]Cellular task submsg id[68]
[D][05:19:24][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:24][SAL ]socket send fail. id[4]
[D][05:19:24][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:24][M2M ]m2m select fd[4]
[D][05:19:24][M2M ]socket[4] Link is disconnected
[D][05:19:24][M2M ]tcpclient close[4]
[D][05:19:24][SAL ]socket[4] has closed
[D][05:19:24][PROT]protocol read data ok
[E][05:19:24][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:24][PROT]M2M Send Fail [1629955164]
[D][05:19:24][PROT]CLEAN,SEND:2
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:24][CAT1]gsm read msg sub id: 10
[D][05:19:24][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:24][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:24][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:48:49:389 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:49:420 ==>> 定位已等待【24】秒.
2025-07-31 22:48:49:806 ==>> [D][05:19:25][CAT1]<<< 
OK

[D][05:19:25][CAT1]exec over: func id: 10, ret: 6
[D][05:19:25][CAT1]sub id: 10, ret: 6

[D][05:19:25][SAL ]Cellular task submsg id[68]
[D][05:19:25][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:25][M2M ]m2m gsm shut done, ret[0]
$GBGGA,144849.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,3,,,41,41,,,41,60,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,38,11,,,38,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,37,24,,,37,1*72

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,35,44,,,34,4,,,34,5,,,33,1*4E

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144849.517,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144849.517,0.000,1570.429,1570.429,50.227,2097152,2097152,2097152*54

[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:25][SAL ]open socket ind id[4], rst[0]
[D][05:19:25][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:25][SAL ]Cellular task submsg id[8]
[D][05:19:25][SAL ]cellular OPEN socket size[144], ms

2025-07-31 22:48:49:851 ==>> g->data[0x20052db0], socket[0]
[D][05:19:25][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:25][CAT1]gsm read msg sub id: 8
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:25][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:25][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:25][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:48:49:956 ==>> [D][05:19:25][CAT1]pdpdeact urc len[22]


2025-07-31 22:48:50:366 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 22:48:50:396 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:50:407 ==>> 定位已等待【25】秒.
2025-07-31 22:48:50:700 ==>> $GBGGA,144850.517,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,3,,,40,41,,,40,60,,,40,1*49

$GBGSV,7,3,25,7,,,39,16,,,39,11,,,38,1,,,38,1*74

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144850.517,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144850.517,0.000,1572.077,1572.077,50.270,2097152,2097152,2097152*5E



2025-07-31 22:48:50:866 ==>> [D][05:19:26][COMM]S->M yaw:INVALID


2025-07-31 22:48:51:048 ==>> [D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:26][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:26][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:26][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:26][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 22:48:51:212 ==>> [D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:48:51:410 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:51:424 ==>> 定位已等待【26】秒.
2025-07-31 22:48:51:733 ==>> [D][05:19:26][CAT1]opened : 0, 0
[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:26][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:26][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:26][M2M ]g_m2m_is_idle become true
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:26][PROT]index:2 1629955166
[D][05:19:26][PROT]is_send:0
[D][05:19:26][PROT]sequence_num:6
[D][05:19:26][PROT]retry_timeout:0
[D][05:19:26][PROT]retry_times:1
[D][05:19:26][PROT]send_path:0x2
[D][05:19:26][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:26][PROT]===========================================================
[W][05:19:26][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955166]
[D][05:19:26][PROT]===========================================================
[D][05:19:26][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908B1C89C8906980220
[D][05:19:26][PROT]sending traceid [9999999999900007]
[D][05:19:26][PROT]Send_TO_M2M [1629955166]
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:26][SAL ]sock send credit cnt[6]
[D][05:19:26][SAL ]sock send ind credit cnt[6]
[D][05:19:26][M2M ]m2m send data len[134

2025-07-31 22:48:51:838 ==>> ]
[D][05:19:26][SAL ]Cellular task submsg id[10]
[D][05:19:26][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:26][CAT1]gsm read msg sub id: 15
[D][05:19:26][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:26][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BEB121BC68F983893C891306D2BA675AB43548542BA66FDD6E8229FAA6D87A9950EF27ECAD7F598041E1829F210BE751320F8C
[D][05:19:26][CAT1]<<< 
SEND OK

[D][05:19:26][CAT1]exec over: func id: 15, ret: 11
[D][05:19:26][CAT1]sub id: 15, ret: 11

[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:26][M2M ]g_m2m_is_idle become true
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:26][PROT]M2M Send ok [1629955166]
$GBGGA,144851.517,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,3,,,40,60,,,40,1*48

$GBGSV,7,3,25,7,,,40,16,,,38,11,,,38,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,3

2025-07-31 22:48:51:883 ==>> 8,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,36,2,,,36,12,,,36,1*74

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144851.517,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144851.517,0.000,1572.081,1572.081,50.274,2097152,2097152,2097152*5B



2025-07-31 22:48:52:378 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 22:48:52:423 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:52:435 ==>> 定位已等待【27】秒.
2025-07-31 22:48:52:708 ==>> [D][05:19:27][COMM]M->S yaw:INVALID
$GBGGA,144852.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,38,1,,,38,1*7A

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144852.517,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144852.517,0.000,1577.057,1577.057,50.434,2097152,2097152,2097152*5A



2025-07-31 22:48:53:436 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:53:448 ==>> 定位已等待【28】秒.
2025-07-31 22:48:54:012 ==>> $GBGGA,144853.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,60,,,41,3,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,38,11,,,38,1,,,38,1*7B

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,37,1*7D

[D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100020. period:10000
[

2025-07-31 22:48:54:117 ==>> D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tic

2025-07-31 22:48:54:222 ==>> k:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100027. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100027. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100028. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100028. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100028. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100029. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100029. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100030. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100030
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100030
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100031
$GBGSV,7,5,25,1

2025-07-31 22:48:54:267 ==>> 0,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144853.517,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144853.517,0.000,1575.399,1575.399,50.380,2097152,2097152,2097152*53



2025-07-31 22:48:54:372 ==>> [D][05:19:29][COMM]read battery soc:255


2025-07-31 22:48:54:447 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:54:469 ==>> 定位已等待【29】秒.
2025-07-31 22:48:54:702 ==>> $GBGGA,144854.517,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,41,,,41,3,,,41,60,,,40,1*49

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,38,1,,,38,1*7A

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,37,1*7D

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,33,1*4D

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144854.517,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144854.517,0.000,1575.402,1575.402,50.384,2097152,2097152,2097152*50



2025-07-31 22:48:55:257 ==>> [D][05:19:30][COMM]S->M yaw:INVALID


2025-07-31 22:48:55:456 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:55:468 ==>> 定位已等待【30】秒.
2025-07-31 22:48:55:702 ==>> $GBGGA,144855.517,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,39,,,41,59,,,41,1*72

$GBGSV,7,2,26,41,,,41,3,,,41,25,,,40,60,,,40,1*40

$GBGSV,7,3,26,7,,,40,16,,,39,1,,,39,11,,,38,1*78

$GBGSV,7,4,26,43,,,38,33,,,38,23,,,38,24,,,37,1*7E

$GBGSV,7,5,26,10,,,37,6,,,37,2,,,36,12,,,36,1*76

$GBGSV,7,6,26,9,,,36,44,,,34,4,,,34,5,,,34,1*49

$GBGSV,7,7,26,32,,,33,45,,,49,1*7F

$GBRMC,144855.517,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144855.517,0.000,1577.056,1577.056,50.432,2097152,2097152,2097152*5B



2025-07-31 22:48:56:310 ==>> [D][05:19:31][COMM]M->S yaw:INVALID


2025-07-31 22:48:56:385 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 22:48:56:460 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:56:472 ==>> 定位已等待【31】秒.
2025-07-31 22:48:56:762 ==>> $GBGGA,144856.517,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,39,,,41,59,,,41,1*72

$GBGSV,7,2,26,41,,,41,3,,,41,25,,,41,60,,,40,1*41

$GBGSV,7,3,26,7,,,40,16,,,39,1,,,38,11,,,38,1*79

$GBGSV,7,4,26,43,,,38,33,,,38,23,,,38,24,,,38,1*71

$GBGSV,7,5,26,10,,,37,6,,,37,2,,,36,12,,,36,1*76

$GBGSV,7,6,26,9,,,36,44,,,34,4,,,34,5,,,34,1*49

$GBGSV,7,7,26,32,,,33,45,,,38,1*79

$GBRMC,144856.517,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144856.517,0.000,1578.715,1578.715,50.486,2097152,2097152,2097152*57

[D][05:19:32][PROT]CLEAN,SEND:2
[D][05:19:32][PROT]CLEAN:2
[D][05:19:32][COMM]S->M yaw:INVALID


2025-07-31 22:48:57:473 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:57:486 ==>> 定位已等待【32】秒.
2025-07-31 22:48:57:713 ==>> $GBGGA,144857.524,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,39,,,41,59,,,41,1*71

$GBGSV,7,2,25,41,,,41,3,,,41,25,,,41,60,,,40,1*42

$GBGSV,7,3,25,7,,,40,16,,,39,1,,,39,11,,,38,1*7B

$GBGSV,7,4,25,43,,,38,33,,,38,23,,,38,24,,,38,1*72

$GBGSV,7,5,25,10,,,37,6,,,37,2,,,36,12,,,36,1*75

$GBGSV,7,6,25,9,,,36,44,,,34,4,,,34,5,,,34,1*4A

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144857.524,V,,,,,,,310725,0.5,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144857.524,0.000,0.079,0.079,0.129,237615,32070,823298*61



2025-07-31 22:48:57:773 ==>>                                      

2025-07-31 22:48:58:418 ==>> [D][05:19:33][COMM]read battery soc:255


2025-07-31 22:48:58:477 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:58:488 ==>> 定位已等待【33】秒.
2025-07-31 22:48:58:702 ==>> $GBGGA,144858.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,41,,,41,25,,,41,7,,,40,3,,,40,1*72

$GBGSV,7,3,25,60,,,40,16,,,39,11,,,39,1,,,38,1*4A

$GBGSV,7,4,25,33,,,38,24,,,38,43,,,38,23,,,38,1*72

$GBGSV,7,5,25,6,,,37,10,,,37,9,,,36,2,,,36,1*4F

$GBGSV,7,6,25,12,,,36,5,,,34,44,,,34,4,,,33,1*77

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144858.504,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144858.504,0.185,21.335,155.940,493.622,237724,32086,823266*53



2025-07-31 22:48:59:483 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:48:59:495 ==>> 定位已等待【34】秒.
2025-07-31 22:49:00:495 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:49:00:517 ==>> 定位已等待【35】秒.
2025-07-31 22:49:00:529 ==>> $GBGGA,144859.504,2301.2561776,N,11421.9432634,E,1,13,0.83,74.570,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,25,34,11,41,12,33,23,1.72,0.83,1.51,4*0D

$GBGSA,A,3,24,,,,,,,,,,,,1.72,0.83,1.51,4*04

$GBGSV,7,1,25,40,68,174,42,7,65,202,40,39,62,39,41,3,60,190,41,1*40

$GBGSV,7,2,25,6,59,7,37,16,59,12,39,59,52,129,41,25,50,1,41,1*7D

$GBGSV,7,3,25,10,50,228,37,1,48,125,38,9,47,333,36,2,46,237,36,1*42

$GBGSV,7,4,25,34,46,98,41,11,42,133,38,60,41,239,40,41,40,258,41,1*4B

$GBGSV,7,5,25,4,32,111,34,12,30,66,36,33,27,197,38,23,26,301,38,1*78

$GBGSV,7,6,25,24,23,67,38,5,21,256,34,44,4,182,34,43,,,38,1*79

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144859.504,A,2301.2561776,N,11421.9432634,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:19:35][GNSS]HD8040 GPS
[D][05:19:35][GNSS]GPS diff_sec 124018164, report 0x42 frame
$GBGST,144859.504,1.088,0.165,0.169,0.258,2.182,1.886,7.085*77

[D][05:19:35][COMM]Main Task receive event:131
[D][05:19:35][COMM]index:0,power_mode:0xFF
[D][05:19:35][COMM]index:1,sound_mode:0xFF
[D][05:19:35][COMM]index:2,gsensor_mode:0xFF
[D][05:19:35][COMM]index:3,report_freq_mode:0xFF
[D][05:19:35][COMM]index:4,report_period:0xFF
[D][05:19:35][COMM]index:5,normal_reset_mo

2025-07-31 22:49:00:600 ==>> de:0xFF
[D][05:19:35][COMM]index:6,normal_reset_period:0xFF
[D][05:19:35][COMM]index:7,spock_over_speed:0xFF
[D][05:19:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:35][COMM]index:16,imu_config_params:0xFF
[D][05:19:35][COMM]index:17,long_connect_params:0xFF
[D][05:19:35][COMM]index:18,detain_mark:0xFF
[D][05:19:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:35][COMM]index:21,mc_mode:0xFF
[D][05:19:35][COMM]index:22,S_mode:0xFF
[D][05:19:35][COMM]index:23,overweight:0xFF
[D][05:19:35][COMM]index:24,standstill_mode:0xFF
[D][05:19:35][COMM]index:25,night_mode:0xFF
[D][05:19:35][COMM]index:26,experiment1:0xFF
[D][05:19:35][COMM]index:27,experiment2:0xFF
[D][05:19:35][COMM]index:28,experiment3:0xFF
[D][05:19:35][COMM]index:29,experiment4:0xF

2025-07-31 22:49:00:706 ==>> F
[D][05:19:35][COMM]index:30,night_mode_start:0xFF
[D][05:19:35][COMM]index:31,night_mode_end:0xFF
[D][05:19:35][COMM]index:33,park_report_minutes:0xFF
[D][05:19:35][COMM]index:34,park_report_mode:0xFF
[D][05:19:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:35][COMM]index:38,charge_battery_para: FF
[D][05:19:35][COMM]index:39,multirider_mode:0xFF
[D][05:19:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:35][COMM]index:44,riding_duration_config:0xFF
[D][05:19:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:35][COMM]index:49,mc_load_startup:0xFF
[D][05:19:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:35][COMM]index:52,traffic_mode:0xFF
[D][05:19:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:35][COMM]index:55,wheel

2025-07-31 22:49:00:812 ==>> _alarm_play_switch:255
[D][05:19:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:35][COMM]index:63,experiment5:0xFF
[D][05:19:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:35][COMM]index:72,experiment6:0xFF
[D][05:19:35][COMM]index:73,experiment7:0xFF
[D][05:19:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:35][COMM]index:75,zero_value_from_server:-1
[D][05:19:35][COMM]index:76,multirider_threshold:255
[D][05:19:35][COMM]index:77,experiment8:255
[D][05:19:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:35

2025-07-31 22:49:00:917 ==>> ][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:35][COMM]index:83,loc_report_interval:255
[D][05:19:35][COMM]index:84,multirider_threshold_p2:255
[D][05:19:35][COMM]index:85,multirider_strategy:255
[D][05:19:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:35][COMM]index:90,weight_param:0xFF
[D][05:19:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:35][COMM]index:95,current_limit:0xFF
[D][05:19:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:35][COMM]index:100,location_mode:0xFF

[W][05:19:35][PROT]remove success[1629955175],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:35][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:35][PROT]add success [1629955175],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:35][COMM]Main Task receive event:131 finished processing
[D][05:19:35][PROT]index:0 1629955175
[D][05:19:35][PROT]is_send:0
[D][05:19:35][PROT]sequence_num:7
[D][05:19:35][PROT]retry_

2025-07-31 22:49:01:022 ==>> timeout:0
[D][05:19:35][PROT]retry_times:1
[D][05:19:35][PROT]send_path:0x2
[D][05:19:35][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:35][PROT]===========================================================
[W][05:19:35][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955175]
[D][05:19:35][PROT]===========================================================
[D][05:19:35][PROT]sending traceid [9999999999900008]
[D][05:19:35][PROT]Send_TO_M2M [1629955175]
[D][05:19:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:35][SAL ]sock send credit cnt[6]
[D][05:19:35][SAL ]sock send ind credit cnt[6]
[D][05:19:35][M2M ]m2m send data len[294]
[D][05:19:35][SAL ]Cellular task submsg id[10]
[D][05:19:35][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:35][CAT1]gsm read msg sub id: 15
[D][05:19:35][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:35][CAT1]<<< 
ERROR

$GBGGA,144900.004,2301.2566341,N,11421.9435389,E,1,15,0.77,75.790,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,0

2025-07-31 22:49:01:113 ==>> 6,16,25,59,34,11,60,41,12,1.68,0.77,1.50,4*07

$GBGSA,A,3,33,23,24,,,,,,,,,,1.68,0.77,1.50,4*04

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,41,3,60,190,41,1*41

$GBGSV,7,2,25,6,59,7,37,16,59,12,38,25,50,1,41,10,50,228,37,1*70

$GBGSV,7,3,25,59,49,130,41,1,48,125,38,9,47,333,36,2,46,237,36,1*4C

$GBGSV,7,4,25,34,46,98,41,11,42,133,38,60,42,239,41,41,40,258,41,1*49

$GBGSV,7,5,25,4,32,111,34,12,30,66,36,33,27,197,38,23,26,301,38,1*78

$GBGSV,7,6,25,24,23,67,38,5,21,256,34,44,4,182,34,43,,,38,1*79

$GBGSV,7,7,25,32,,,33,1*70

$GBGSV,2,1,08,40,68,174,40,39,62,39,41,25,50,1,40,34,46,98,39,5*7D

$GBGSV,2,2,08,41,40,258,41,33,27,197,38,23,26,301,38,24,23,67,36,5*4F

$GBRMC,144900.004,A,2301.2566341,N,11421.9435389,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,144900.004,1.813,0.417,0.439,0.662,1.909,

2025-07-31 22:49:01:339 ==>>                                                                                                                                                                                                                                                                                                      ,59,7,37,16,59,12,39,25,50,1,41,10,50,228,37,1*71

$GBGSV,7,3,25,59,49,130,41,1,48,125,39,9,47,333,36,2,46,237,36,1*4D

$GBGSV,7,4,25,34,46,98,41,11,42,133,39,60,42,239,41,41,40,258,41,1*48

$GBGSV,7,5,25,43,34,168,38,4,32,111,34,12,30,66,36,33,27,197,38,1*70

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,34,44,13,47,34,1*47

$GBGSV,7,7,25,32,,,33,1*70

$GBGSV,2,1,08,40,68,174,41,39,62,39,41,25,50,1,40,34,46,98,40,5*72

$GBGSV,2,2,08,41,40,258,41,33,27,197,38,23,26,301,39,24,23,67,36,5*4E

$GBRMC,144901.000,A,2301.2567777,N,11421.9436337,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,144901.000,1.819,0.161,0.166,0.261,1.695,1.656,4.042*72



2025-07-31 22:49:01:505 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 22:49:01:513 ==>> 
北斗星号:【40】,信号值:【42】
北斗星号:【7】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【6】,信号值:【37】
北斗星号:【16】,信号值:【39】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【10】,信号值:【37】
北斗星号:【1】,信号值:【38】
北斗星号:【9】,信号值:【36】
北斗星号:【2】,信号值:【36】
北斗星号:【34】,信号值:【41】
北斗星号:【11】,信号值:【38】
北斗星号:【60】,信号值:【40】
北斗星号:【41】,信号值:【41】
北斗星号:【12】,信号值:【36】
北斗星号:【33】,信号值:【38】
北斗星号:【23】,信号值:【38】
北斗星号:【24】,信号值:【38】
北斗星号:【43】,信号值:【38】

2025-07-31 22:49:01:521 ==>> 检测【CSQ强度】
2025-07-31 22:49:01:537 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:49:01:689 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:49:02:331 ==>> $GBGGA,144902.000,2301.2569551,N,11421.9436664,E,1,17,0.70,77.904,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,16,25,59,34,11,60,41,43,1.44,0.70,1.25,4*08

$GBGSA,A,3,12,33,23,24,44,,,,,,,,1.44,0.70,1.25,4*0C

$GBGSV,7,1,25,40,68,174,42,7,65,202,40,39,62,39,42,3,60,190,40,1*42

$GBGSV,7,2,25,6,59,7,37,16,59,12,38,25,50,1,41,10,50,228,37,1*70

$GBGSV,7,3,25,59,49,130,41,1,48,125,38,9,47,333,36,2,46,237,36,1*4C

$GBGSV,7,4,25,34,46,98,41,11,42,133,39,60,42,239,40,41,40,258,41,1*49

$GBGSV,7,5,25,43,34,168,38,4,32,111,33,12,30,66,36,33,27,197,38,1*77

$GBGSV,7,6,25,23,26,301,38,24,23,67,37,5,21,256,34,44,13,47,34,1*48

$GBGSV,7,7,25,32,13,311,33,1*41

$GBGSV,3,1,10,40,68,174,41,39,62,39,41,25,50,1,41,34,46,98,40,5*7B

$GBGSV,3,2,10,41,40,258,41,43,34,168,34,33,27,197,38,23,26,301,39,5*7D

$GBGSV,3,3,10,24,23,67,36,44,13,47,34,5*76

$GBRMC,144902.000,A,2301.2569551,N,11421.9436664,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144902.000,1.617,0.181,0.182,0.275,1.439,1.428,3.422*7A



2025-07-31 22:49:02:437 ==>> [D][05:19:37][COMM]read battery soc:255


2025-07-31 22:49:03:377 ==>> $GBGGA,144903.000,2301.2570576,N,11421.9436602,E,1,23,0.60,78.316,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,41,1*43

$GBGSV,7,2,25,6,59,7,37,16,59,12,39,10,53,210,38,9,51,344,36,1*4B

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,36,34,46,98,41,1*77

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,41,41,40,258,41,1*44

$GBGSV,7,5,25,43,34,168,38,4,32,111,34,12,30,66,36,33,27,197,38,1*70

$GBGSV,7,6,25,23,26,301,38,24,23,67,37,5,21,256,34,44,13,47,34,1*48

$GBGSV,7,7,25,32,13,311,33,1*41

$GBGSV,3,1,11,40,68,174,41,39,62,39,41,25,50,1,41,34,46,98,40,5*7A

$GBGSV,3,2,11,41,40,258,41,43,34,168,35,33,27,197,38,23,26,301,39,5*7D

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,32,5*40

$GBRMC,144903.000,A,2301.2570576,N,11421.9436602,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,144903.000,1.773,0.185,0.187,0.277,1.478,1.479,3.147*7C



2025-07-31 22:49:03:574 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:49:03:790 ==>> [W][05:19:39][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:49:04:356 ==>> $GBGGA,144904.000,2301.2571124,N,11421.9436430,E,1,23,0.60,78.702,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,41,1*43

$GBGSV,7,2,25,6,59,7,37,16,59,12,39,10,53,210,38,9,51,344,36,1*4B

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,37,34,46,98,41,1*76

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,40,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,34,44,13,47,35,1*46

$GBGSV,7,7,25,32,13,311,33,1*41

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,98,40,5*79

$GBGSV,3,2,11,41,40,258,42,43,34,168,35,33,27,197,38,23,26,301,39,5*7E

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144904.000,A,2301.2571124,N,11421.9436430,E,0.003,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,144904.000,1.775,0.172,0.174,0.260,1.437,1.443,2.912*74



2025-07-31 22:49:04:446 ==>> [D][05:19:39][COMM]read battery soc:255


2025-07-31 22:49:05:352 ==>> [D][05:19:40][COMM]S->M yaw:INVALID
$GBGGA,144905.000,2301.2571336,N,11421.9436393,E,1,23,0.60,78.904,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,40,1*42

$GBGSV,7,2,25,6,59,7,37,16,59,12,39,10,53,210,38,9,51,344,36,1*4B

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,37,34,46,97,41,1*79

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,40,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,34,44,13,47,35,1*46

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,97,40,5*76

$GBGSV,3,2,11,41,40,258,42,43,34,168,36,33,27,197,38,23,26,301,39,5*7D

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144905.000,A,2301.2571336,N,11421.9436393,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,144905.000,1.861,0.169,0.171,0.259,1.469,1.478,2.790*7D



2025-07-31 22:49:05:640 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:49:05:827 ==>> [W][05:19:41][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:49:06:350 ==>> $GBGGA,144906.000,2301.2571532,N,11421.9436469,E,1,23,0.60,78.941,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,41,1*43

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,42,2,46,237,37,34,46,97,41,1*7A

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,41,41,40,258,41,1*44

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,35,44,13,47,35,1*47

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,97,40,5*76

$GBGSV,3,2,11,41,40,258,41,43,34,168,36,33,27,197,38,23,26,301,39,5*7E

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144906.000,A,2301.2571532,N,11421.9436469,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144906.000,2.070,0.164,0.166,0.251,1.593,1.603,2.787*7A

[D][05:19:41][COMM]M->S yaw:INVALID


2025-07-31 22:49:06:455 ==>> [D][05:19:41][COMM]read battery soc:255


2025-07-31 22:49:06:636 ==>> [D][05:19:42][COMM]S->M yaw:INVALID


2025-07-31 22:49:06:726 ==>> [D][05:19:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:49:07:357 ==>> $GBGGA,144907.000,2301.2571646,N,11421.9436310,E,1,23,0.60,79.018,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,41,3,61,190,41,1*40

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,36,34,46,97,41,1*78

$GBGSV,7,4,25,1,45,125,39,11,42,133,39,60,42,239,41,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,35,44,13,47,34,1*46

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,97,40,5*76

$GBGSV,3,2,11,41,40,258,41,43,34,168,36,33,27,197,38,23,26,301,39,5*7E

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144907.000,A,2301.2571646,N,11421.9436310,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144907.000,2.073,0.143,0.144,0.220,1.581,1.592,2.699*7D



2025-07-31 22:49:07:725 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:49:07:895 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:49:08:355 ==>> $GBGGA,144908.000,2301.2571796,N,11421.9436196,E,1,23,0.60,78.988,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,40,1*42

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,42,2,46,237,36,34,46,97,42,1*78

$GBGSV,7,4,25,1,45,125,39,11,42,133,39,60,42,239,41,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,35,44,13,47,34,1*46

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,41,25,50,1,41,34,46,97,40,5*75

$GBGSV,3,2,11,41,40,258,41,43,34,168,36,33,27,197,38,23,26,301,39,5*7E

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144908.000,A,2301.2571796,N,11421.9436196,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144908.000,2.004,0.166,0.168,0.252,1.524,1.534,2.586*70



2025-07-31 22:49:08:445 ==>> [D][05:19:43][COMM]read battery soc:255


2025-07-31 22:49:08:612 ==>> [D][05:19:44][COMM]M->S yaw:INVALID


2025-07-31 22:49:09:369 ==>> $GBGGA,144909.000,2301.2572074,N,11421.9436314,E,1,23,0.60,78.961,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,43,7,65,202,40,39,62,39,42,3,61,190,40,1*42

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,42,2,46,237,36,34,46,97,41,1*7B

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,41,41,40,258,41,1*44

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,37,5,21,256,35,44,13,47,34,1*49

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,41,25,50,1,41,34,46,97,40,5*75

$GBGSV,3,2,11,41,40,258,42,43,34,168,36,33,27,197,38,23,26,301,39,5*7D

$GBGSV,3,3,11,24,23,67,36,44,13,47,33,32,13,311,33,5*40

$GBRMC,144909.000,A,2301.2572074,N,11421.9436314,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,144909.000,2.054,0.168,0.170,0.255,1.549,1.560,2.557*72

[D][05:19:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:49:09:801 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:49:10:047 ==>> [D][05:19:45][CAT1]exec over: func id: 15, ret: -93
[D][05:19:45][CAT1]sub id: 15, ret: -93

[D][05:19:45][SAL ]Cellular task submsg id[68]
[D][05:19:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:45][SAL ]socket send fail. id[4]
[D][05:19:45][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:45][CAT1]gsm read msg sub id: 12
[D][05:19:45][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:45][M2M ]m2m select fd[4]
[D][05:19:45][M2M ]socket[4] Link is disconnected
[D][05:19:45][M2M ]tcpclient close[4]
[D][05:19:45][SAL ]socket[4] has closed
[D][05:19:45][PROT]protocol read data ok
[E][05:19:45][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:45][PROT]M2M Send Fail [1629955185]
[D][05:19:45][PROT]CLEAN,SEND:0
[D][05:19:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:45][PROT]CLEAN:0
[D][05:19:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:45][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:45][CAT1]exec over: func id: 12, ret: 21
[D][05:19:45][CAT1]gsm read msg sub id: 12
[D][05:19:45][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:45][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:45][CAT1]exec over: func id: 12

2025-07-31 22:49:10:122 ==>> , ret: 21
[D][05:19:45][CAT1]gsm read msg sub id: 12
[D][05:19:45][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:45][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:45][CAT1]exec over: func id: 12, ret: 21
[D][05:19:45][CAT1]gsm read msg sub id: 12
[D][05:19:45][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:45][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:45][CAT1]exec over: func id: 12, ret: 21
[D][05:19:45][CAT1]gsm read msg sub id: 10
[D][05:19:45][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:45][CAT1]<<< 
+CGATT: 1

OK

[W][05:19:45][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:45][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:49:10:201 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 22:49:10:227 ==>> 检测【关闭GSM联网】
2025-07-31 22:49:10:242 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:49:10:471 ==>> $GBGGA,144910.000,2301.2572421,N,11421.9436326,E,1,23,0.60,79.080,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,42,7,65,202,40,39,62,39,42,3,61,190,41,1*42

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,42,2,46,237,37,34,46,97,41,1*7A

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,40,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,39,4,32,111,34,12,30,66,36,33,27,197,38,1*71

$GBGSV,7,6,25,23,26,301,38,24,23,67,37,5,21,256,34,44,13,47,34,1*48

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,97,41,5*77

$GBGSV,3,2,11,41,40,258,42,43,34,168,36,33,27,197,38,23,26,301,39,5*7D

$GBGSV,3,3,11,24,23,67,36,44,13,47,33,32,13,311,33,5*40

$GBRMC,144910.000,A,2301.2572421,N,11421.9436326,E,0.004,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,144910.000,1.922,0.175,0.177,0.264,1.450,1.461,2.431*70

[D][05:19:45][CAT1]<<< 
OK

[D][05:19:45][CAT1]exec over: func id: 10, ret: 6
[D][05:19:45][CAT1]sub id: 10, ret: 6

[D][05:19:45][SAL ]Cellu

2025-07-31 22:49:10:576 ==>> lar task submsg id[68]
[D][05:19:45][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:45][M2M ]m2m gsm shut done, ret[0]
[D][05:19:45][CAT1]gsm read msg sub id: 12
[D][05:19:45][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:45][SAL ]open socket ind id[4], rst[0]
[D][05:19:45][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:45][SAL ]Cellular task submsg id[8]
[D][05:19:45][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:19:45][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:45][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:45][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:45][CAT1]exec over: func id: 12, ret: 21
[D][05:19:45][CAT1]gsm read msg sub id: 8
[D][05:19:45][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:45][CAT1]<<< 
+CGATT: 0

OK

[W][05:19:45][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:45][COMM]GSM test
[D][05:19:45][COMM]GSM test disable
[D][05:19:45][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:45][CAT1]pdpdeact urc len[22]


2025-07-31 22:49:10:606 ==>>                                          

2025-07-31 22:49:10:729 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:49:10:738 ==>> 检测【4G联网测试】
2025-07-31 22:49:10:758 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:49:10:787 ==>> [D][05:19:46][COMM]S->M yaw:INVALID


2025-07-31 22:49:11:534 ==>> [W][05:19:46][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:46][COMM]Main Task receive event:14
[D][05:19:46][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955186, allstateRepSeconds = 0
[D][05:19:46][COMM]index:0,power_mode:0xFF
[D][05:19:46][COMM]index:1,sound_mode:0xFF
[D][05:19:46][COMM]index:2,gsensor_mode:0xFF
[D][05:19:46][COMM]index:3,report_freq_mode:0xFF
[D][05:19:46][COMM]index:4,report_period:0xFF
[D][05:19:46][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:46][COMM]index:6,normal_reset_period:0xFF
[D][05:19:46][COMM]index:7,spock_over_speed:0xFF
[D][05:19:46][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:46][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:46][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:46][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:46][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:46][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:46][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:46][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:46][COMM]index:16,imu_config_params:0xFF
[D][05:19:46][COMM]index:17,long_connect_params:0xFF
[D][05:19:46][COMM]index:18,detain_mark:0xFF
[D][05:19:46][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:46

2025-07-31 22:49:11:639 ==>> ][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:46][COMM]index:21,mc_mode:0xFF
[D][05:19:46][COMM]index:22,S_mode:0xFF
[D][05:19:46][COMM]index:23,overweight:0xFF
[D][05:19:46][COMM]index:24,standstill_mode:0xFF
[D][05:19:46][COMM]index:25,night_mode:0xFF
[D][05:19:46][COMM]index:26,experiment1:0xFF
[D][05:19:46][COMM]index:27,experiment2:0xFF
[D][05:19:46][COMM]index:28,experiment3:0xFF
[D][05:19:46][COMM]index:29,experiment4:0xFF
[D][05:19:46][COMM]index:30,night_mode_start:0xFF
[D][05:19:46][COMM]index:31,night_mode_end:0xFF
[D][05:19:46][COMM]index:33,park_report_minutes:0xFF
[D][05:19:46][COMM]index:34,park_report_mode:0xFF
[D][05:19:46][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:46][COMM]index:38,charge_battery_para: FF
[D][05:19:46][COMM]index:39,multirider_mode:0xFF
[D][05:19:46][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:46][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:46][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:46][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:46][COMM]index:44,riding_duration_config:0xFF
[D][05:19:46][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:46][COMM]index:46,camera_park_type_cfg:

2025-07-31 22:49:11:744 ==>> 0xFF
[D][05:19:46][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:46][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:46][COMM]index:49,mc_load_startup:0xFF
[D][05:19:46][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:46][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:46][COMM]index:52,traffic_mode:0xFF
[D][05:19:46][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:46][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:46][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:46][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:46][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:46][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:46][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:46][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:46][COMM]index:63,experiment5:0xFF
[D][05:19:46][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:46][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:46][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:46][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:46][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:46][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:46][COMM]index:71,camera_park_sel

2025-07-31 22:49:11:849 ==>> f_check_cfg:0xFF
[D][05:19:46][COMM]index:72,experiment6:0xFF
[D][05:19:46][COMM]index:73,experiment7:0xFF
[D][05:19:46][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:46][COMM]index:75,zero_value_from_server:-1
[D][05:19:46][COMM]index:76,multirider_threshold:255
[D][05:19:46][COMM]index:77,experiment8:255
[D][05:19:46][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:46][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:46][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:46][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:46][COMM]index:83,loc_report_interval:255
[D][05:19:46][COMM]index:84,multirider_threshold_p2:255
[D][05:19:46][COMM]index:85,multirider_strategy:255
[D][05:19:46][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:46][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:46][COMM]index:90,weight_param:0xFF
[D][05:19:46][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:46][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:46][COMM]index:95,current_limit:0xFF
[D][05:19:46][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:46][COMM]index:100,location_mode:0xFF

[

2025-07-31 22:49:11:954 ==>> W][05:19:46][PROT]remove success[1629955186],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:46][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:46][PROT]add success [1629955186],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:46][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:46][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,144911.000,2301.2572630,N,11421.9436254,E,1,23,0.60,79.166,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,42,7,65,202,40,39,62,39,42,3,61,190,41,1*42

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,36,34,46,97,41,1*78

$GBGSV,7,4,25,1,45,125,38,11,42,133,38,60,42,239,41,41,40,258,41,1*45

$GBGSV,7,5,25,43,34,168,38,4,32,111,34,12,30,66,36,33,27,197,38,1*70

$GBGSV,7,6,25,23,26,301,38,24,23,67,38,5,21,256,34,44,13,47,34,1*47

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,41,25,50,1,41,34,46,97,40,5*75

$GBGSV,3,2,11,41,40,258,41,43,34,168,36,33,27,197,38,23,26,301,39

2025-07-31 22:49:11:999 ==>> ,5*7E

$GBGSV,3,3,11,24,23,67,36,44,13,47,33,32,13,311,33,5*40

$GBRMC,144911.000,A,2301.2572630,N,11421.9436254,E,0.004,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,144911.000,1.828,0.168,0.170,0.256,1.378,1.389,2.330*7A



2025-07-31 22:49:12:459 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            PROT]index:0 1629955187
[D][05:19:47][PROT]is_send:0
[D][05:19:47][PROT]sequence_num:8
[D][05:19:4

2025-07-31 22:49:12:564 ==>> 7][PROT]retry_timeout:0
[D][05:19:47][PROT]retry_times:1
[D][05:19:47][PROT]send_path:0x2
[D][05:19:47][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:47][PROT]===========================================================
[W][05:19:47][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955187]
[D][05:19:47][PROT]===========================================================
[D][05:19:47][PROT]sending traceid [9999999999900009]
[D][05:19:47][PROT]Send_TO_M2M [1629955187]
[D][05:19:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:47][SAL ]sock send credit cnt[6]
[D][05:19:47][SAL ]sock send ind credit cnt[6]
[D][05:19:47][M2M ]m2m send data len[294]
[D][05:19:47][SAL ]Cellular task submsg id[10]
[D][05:19:47][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:47][CAT1]gsm read msg sub id: 15
[D][05:19:47][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:47][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88B10D5CA4236BAC99091BBD426C6D9B6AD2B60E4B61C01F3931D711678B4E12154E9D23405396BC18EF1C95839B31B43EEDCDC1506AD0DDF4143C082D268F9D6CD00DBDC69D8AA98B99AA46CFA

2025-07-31 22:49:12:669 ==>> 1678B380805E85D0F835D873858529CDD6A41A3397582F7BB880B73EBF78D4EAB891EEBD5A0CD81BF8F02C580F2CF8FDFD1FE34345973
[D][05:19:47][CAT1]<<< 
SEND OK

[D][05:19:47][CAT1]exec over: func id: 15, ret: 11
[D][05:19:47][CAT1]sub id: 15, ret: 11

[D][05:19:47][SAL ]Cellular task submsg id[68]
[D][05:19:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:47][PROT]remove success[1629955187],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:47][PROT]add success [1629955187],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:47][COMM]------>period, report file manifest
[D][05:19:47][COMM]Main Task receive event:14 finished processing
[D][05:19:47][CAT1]gsm read msg sub id: 21
[D][05:19:47][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:47][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:47][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:47][M2M ]g_m2m_is_idle become true
[D][05:19:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:47][PROT]M2M Send ok [1629955187]
[D][05:19:47][CAT1]<<< 
OK

[D][05:19:47][CAT1]cell info report total[0]
[D][05:19:47][CAT1]exec

2025-07-31 22:49:12:774 ==>>  over: func id: 21, ret: 6
$GBGGA,144912.000,2301.2572729,N,11421.9436127,E,1,23,0.60,79.220,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,02,34,1.23,0.60,1.07,4*05

$GBGSA,A,3,01,11,60,41,43,12,33,23,24,44,32,,1.23,0.60,1.07,4*08

$GBGSV,7,1,25,40,68,174,42,7,65,202,40,39,62,39,41,3,61,190,41,1*41

$GBGSV,7,2,25,6,59,8,37,16,59,12,39,10,53,210,38,9,51,344,36,1*44

$GBGSV,7,3,25,25,50,1,41,59,49,130,41,2,46,237,36,34,46,97,41,1*78

$GBGSV,7,4,25,1,45,125,38,11,42,133,39,60,42,239,41,41,40,258,41,1*44

$GBGSV,7,5,25,43,34,168,39,4,32,111,33,12,30,66,36,33,27,197,38,1*76

$GBGSV,7,6,25,23,26,301,38,24,23,67,37,5,21,256,34,44,13,47,34,1*48

$GBGSV,7,7,25,32,13,311,34,1*46

$GBGSV,3,1,11,40,68,174,41,39,62,39,42,25,50,1,41,34,46,97,40,5*76

$GBGSV,3,2,11,41,40,258,41,43,34,168,36,33,27,197,38,23,26,301,39,5*7E

$GBGSV,3,3,11,24,23,67,37,44,13,47,33,32,13,311,33,5*41

$GBRMC,144912.000,A,2301.2572729,N,11421.9436127,E,0.004,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,144912.000,1.919,0.185,0.187,0.280,1.438,1.448,2.348*7C



2025-07-31 22:49:12:790 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:49:12:799 ==>> 检测【关闭GPS】
2025-07-31 22:49:12:820 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:49:12:833 ==>>                                          

2025-07-31 22:49:13:188 ==>> [W][05:19:48][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:48][GNSS]stop locating
[D][05:19:48][GNSS]stop event:8
[D][05:19:48][GNSS]GPS stop. ret=0
[D][05:19:48][GNSS]all continue location stop
[W][05:19:48][GNSS]stop locating
[D][05:19:48][GNSS]all sing location stop
[D][05:19:48][CAT1]gsm read msg sub id: 24
[D][05:19:48][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:48][COMM]M->S yaw:INVALID
[D][05:19:48][CAT1]<<< 
OK

[D][05:19:48][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:48][CAT1]<<< 
OK

[D][05:19:48][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:48][CAT1]<<< 
OK

[D][05:19:48][CAT1]exec over: func id: 24, ret: 6
[D][05:19:48][CAT1]sub id: 24, ret: 6



2025-07-31 22:49:13:327 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:49:13:342 ==>> 检测【清空消息队列2】
2025-07-31 22:49:13:372 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:49:13:495 ==>> [W][05:19:48][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:48][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:49:13:604 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:49:13:616 ==>> 检测【轮动检测】
2025-07-31 22:49:13:632 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:49:13:710 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:49:13:815 ==>> [D][05:19:49][COMM]S->M yaw:INVALID
[D][05:19:49][COMM]Wheel signal detected, lock state = 2, singal = 1
[D][05:19:49][GNSS]recv submsg id[1]
[D][05:19:49][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:49][GNSS]location stop evt done evt


2025-07-31 22:49:14:107 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:49:14:216 ==>> 3A A3 01 01 A3 


2025-07-31 22:49:14:306 ==>> ON_OUT1
OVER 150


2025-07-31 22:49:14:391 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:49:14:406 ==>> 检测【关闭小电池】
2025-07-31 22:49:14:431 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:49:14:520 ==>> [D][05:19:49][COMM]read battery soc:255
6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:49:14:675 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:49:14:685 ==>> 检测【进入休眠模式】
2025-07-31 22:49:14:698 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:49:14:875 ==>> [W][05:19:50][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:49:14:980 ==>> [D][05:19:50][COMM]Main Task receive event:28
[D][05:19:50][COMM]main task tmp_sleep_event = 8
[D][05:19:50][COMM]prepare to sleep
[D][05:19:50][CAT1]gsm read msg sub id: 12
[D][05:19:50][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:49:15:427 ==>> [D][05:19:50][COMM]M->S yaw:INVALID


2025-07-31 22:49:15:810 ==>> [D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]exec over: func id: 12, ret: 6
[D][05:19:51][M2M ]tcpclient close[4]
[D][05:19:51][SAL ]Cellular task submsg id[12]
[D][05:19:51][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:51][CAT1]gsm read msg sub id: 9
[D][05:19:51][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]exec over: func id: 9, ret: 6
[D][05:19:51][CAT1]sub id: 9, ret: 6

[D][05:19:51][SAL ]Cellular task submsg id[68]
[D][05:19:51][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:51][SAL ]socket close ind. id[4]
[D][05:19:51][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:51][COMM]1x1 frm_can_tp_send ok
[D][05:19:51][CAT1]pdpdeact urc len[22]


2025-07-31 22:49:16:099 ==>> [E][05:19:51][COMM]1x1 rx timeout
[D][05:19:51][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:49:16:607 ==>> [E][05:19:51][COMM]1x1 rx timeout
[E][05:19:51][COMM]1x1 tp timeout
[E][05:19:51][COMM]1x1 error -3.
[W][05:19:51][COMM]CAN STOP!
[D][05:19:51][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:51][COMM]------------ready to Power off Acckey 1------------
[D][05:19:51][COMM]------------ready to Power off Acckey 2------------
[D][05:19:51][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:51][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1292
[D][05:19:51][COMM]bat sleep fail, reason:-1
[D][05:19:51][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:51][COMM]accel parse set 0
[D][05:19:51][COMM]imu rest ok. 122920
[D][05:19:51][COMM]read battery soc:255
[D][05:19:51][COMM]imu sleep 0
[W][05:19:51][COMM]now sleep


2025-07-31 22:49:16:763 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:49:16:791 ==>> 检测【检测33V休眠电流】
2025-07-31 22:49:16:811 ==>> 开始33V电流采样
2025-07-31 22:49:16:841 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:16:867 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:49:17:873 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:49:17:920 ==>> Current33V:????:16.91

2025-07-31 22:49:18:383 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:18:400 ==>> 【检测33V休眠电流】通过,【16.91uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:49:18:411 ==>> 该项需要延时执行
2025-07-31 22:49:20:398 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:49:20:412 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:49:20:436 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:49:20:522 ==>> 1A A1 00 00 FC 
Get AD_V2 1639mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2745mV
Get AD_V6 2023mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:49:21:458 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:49:21:484 ==>> 检测【打开小电池2】
2025-07-31 22:49:21:493 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:49:21:512 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:49:21:769 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:49:21:779 ==>> 该项需要延时执行
2025-07-31 22:49:22:273 ==>> 此处延时了:【500】毫秒
2025-07-31 22:49:22:286 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:49:22:300 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:49:22:413 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:49:22:553 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:49:22:568 ==>> 该项需要延时执行
2025-07-31 22:49:23:064 ==>> 此处延时了:【500】毫秒
2025-07-31 22:49:23:090 ==>> 检测【进入休眠模式2】
2025-07-31 22:49:23:115 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:49:23:147 ==>> [D][05:19:58][COMM]------------ready to Power on Acckey 1------------
[D][05:19:58][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:58][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:58][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:58][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:58][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:58][COMM]----- get Acckey 1 and value:1------------
[D][05:19:58][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:58][COMM]CAN START!
[D][05:19:58][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 129329
[D][05:19:58][CAT1]gsm read msg sub id: 12
[D][05:19:58][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:58][COMM][Audio]exec status ready.
[D][05:19:58][CAT1]<<< 
OK

[D][05:19:58][CAT1]exec over

2025-07-31 22:49:23:214 ==>> : func id: 12, ret: 6
[D][05:19:58][COMM]imu wakeup ok. 129356
[D][05:19:58][COMM]imu wakeup 1
[W][05:19:58][COMM]wake up system, wakeupEvt=0x80
[D][05:19:58][COMM]frm_can_weigth_power_set 1
[D][05:19:58][COMM]Clear Sleep Block Evt
[D][05:19:58][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:58][COMM]1x1 frm_can_tp_send ok
[D][05:19:58][COMM]IMU: [16,19,-1030] ret=24 AWAKE!
[D][05:19:58][COMM]S->M yaw:INVALID
                                                    

2025-07-31 22:49:23:274 ==>> [W][05:19:58][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:49:23:500 ==>> [E][05:19:58][COMM]1x1 rx timeout
[D][05:19:58][COMM]1x1 frm_can_tp_send ok
[D][05:19:58][COMM]msg 02A0 loss. last_tick:129315. cur_tick:129823. period:50
[D][05:19:58][COMM]msg 02A4 loss. last_tick:129315. cur_tick:129823. period:50
[D][05:19:58][COMM]msg 02A5 loss. last_tick:129315. cur_tick:129824. period:50
[D][05:19:58][COMM]msg 02A6 loss. last_tick:129315. cur_tick:129824. period:50
[D][05:19:58][COMM]msg 02A7 loss. last_tick:129315. cur_tick:129825. period:50
[D][05:19:58][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 129825
[D][05:19:58][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 129825


2025-07-31 22:49:23:838 ==>> [E][05:19:59][COMM]1x1 rx timeout
[E][05:19:59][COMM]1x1 tp timeout
[E][05:19:59][COMM]1x1 error -3.
[D][05:19:59][COMM]Main Task receive event:28 finished processing
[D][05:19:59][COMM]Main Task receive event:28
[D][05:19:59][COMM]prepare to sleep
[D][05:19:59][CAT1]gsm read msg sub id: 12
[D][05:19:59][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:59][CAT1]<<< 
OK

[D][05:19:59][CAT1]exec over: func id: 12, ret: 6
[D][05:19:59][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:59][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:49:24:127 ==>> [D][05:19:59][COMM]msg 0220 loss. last_tick:129315. cur_tick:130320. period:100
[D][05:19:59][COMM]msg 0221 loss. last_tick:129315. cur_tick:130320. period:100
[D][05:19:59][COMM]msg 0224 loss. last_tick:129315. cur_tick:130320. period:100
[D][05:19:59][COMM]msg 0260 loss. last_tick:129315. cur_tick:130321. period:100
[D][05:19:59][COMM]msg 0280 loss. last_tick:129315. cur_tick:130321. period:100
[D][05:19:59][COMM]msg 02C0 loss. last_tick:129315. cur_tick:130322. period:100
[D][05:19:59][COMM]msg 02C1 loss. last_tick:129315. cur_tick:130322. period:100
[D][05:19:59][COMM]msg 02C2 loss. last_tick:129315. cur_tick:130322. period:100
[D][05:19:59][COMM]msg 02E0 loss. last_tick:129315. cur_tick:130323. period:100
[D][05:19:59][COMM]msg 02E1 loss. last_tick:129315. cur_tick:130323. period:100
[D][05:19:59][COMM]msg 02E2 loss. last_tick:129315. cur_tick:130323. period:100
[D][05:19:59][COMM]msg 0300 loss. last_tick:129315. cur_tick:130324. period:100
[D][05:19:59][COMM]msg 0301 loss. last_tick:129315. cur_tick:130324. period:100
[D][05:19:59][COMM]bat msg 0240 loss. last_tick:129315. cur_tick:130325. period:100. j,i:1 54
[D][05:19:59][COMM]bat msg 

2025-07-31 22:49:24:232 ==>> 0241 loss. last_tick:129315. cur_tick:130325. period:100. j,i:2 55
[D][05:19:59][COMM]bat msg 0242 loss. last_tick:129315. cur_tick:130325. period:100. j,i:3 56
[D][05:19:59][COMM]bat msg 0244 loss. last_tick:129315. cur_tick:130326. period:100. j,i:5 58
[D][05:19:59][COMM]bat msg 024E loss. last_tick:129315. cur_tick:130326. period:100. j,i:15 68
[D][05:19:59][COMM]bat msg 024F loss. last_tick:129315. cur_tick:130326. period:100. j,i:16 69
[D][05:19:59][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 130327
[D][05:19:59][COMM]CAN message bat fault change: 0x00000000->0x0001802E 130327
[D][05:19:59][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 130328
                                                                                                                   

2025-07-31 22:49:24:507 ==>> [D][05:19:59][COMM]msg 0222 loss. last_tick:129315. cur_tick:130822. period:150
[D][05:19:59][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 130823
[D][05:19:59][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:59][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:59][COMM]------------ready to Power off Acckey 2------------


2025-07-31 22:49:24:717 ==>> [E][05:19:59][COMM]1x1 rx timeout
[E][05:19:59][COMM]1x1 tp timeout
[E][05:19:59][COMM]1x1 error -3.
[W][05:19:59][COMM]CAN STOP!
[D][05:19:59][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:59][COMM]------------ready to Power off Acckey 1------------
[D][05:19:59][COMM]------------ready to Power off Acckey 2------------
[D][05:19:59][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:59][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 96
[D][05:19:59][COMM]bat sleep fail, reason:-1
[D][05:19:59][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:59][COMM]accel parse set 0
[D][05:19:59][COMM]imu rest ok. 131011
[D][05:20:00][COMM]imu sleep 0
[W][05:20:00][COMM]now sleep


2025-07-31 22:49:24:886 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:49:24:897 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:49:24:905 ==>> 开始小电池电流采样
2025-07-31 22:49:24:913 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:24:990 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:49:25:994 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:49:26:056 ==>> CurrentBattery:ƽ��:69.36

2025-07-31 22:49:26:503 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:26:511 ==>> 【检测小电池休眠电流】通过,【69.36uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:49:26:540 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:49:26:549 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:49:26:610 ==>> 5A A5 01 5A A5 


2025-07-31 22:49:26:715 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:49:26:830 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:49:26:845 ==>> 该项需要延时执行
2025-07-31 22:49:26:956 ==>> [D][05:20:02][COMM]------------ready to Power on Acckey 1------------
[D][05:20:02][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:20:02][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:20:02][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:20:02][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:20:02][COMM]----- get Acckey 1 and value:1------------
[W][05:20:02][COMM]CAN START!
[D][05:20:02][CAT1]gsm read msg sub id: 12
[D][05:20:02][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:20:02][COMM]CAN message bat fault change: 0x0001802E->0x00000000 133209
[D][05:20:02][COMM][Audio]exec status ready.
[D][05:20:02][CAT1]<<< 
OK

[D][05:20:02][CAT1]exec over: func id: 12, ret: 6
[D][05:20:02][COMM]imu wakeup ok. 133223
[D][05:20:02][COMM]imu wakeup 1
[W][05:20:02][COMM]wake up system, wakeupEvt=0x80
[D][05:20:02][COMM]frm_can_weigth_power_set 1
[D][05:20:02][COMM]Clear Sleep Block Evt
[D][05:20:02][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:20:02][COMM]1x1 frm_can_tp_send ok
[D][05:20:02][COMM]read battery soc:0


2025-07-31 22:49:27:215 ==>> [E][05:20:02][COMM]1x1 rx timeout
[D][05:20:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:49:27:320 ==>> [D][05:20:02][COMM]msg 02A0 loss. last_tick:133191. cur_tick:133702. period:50
[D][05:20:02][COMM]msg 02A4 loss. last_tick:133191. cur_tick:133703. period:50
[D][05:20:02][COMM]

2025-07-31 22:49:27:343 ==>> 此处延时了:【500】毫秒
2025-07-31 22:49:27:363 ==>> 检测【检测唤醒】
2025-07-31 22:49:27:391 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:49:27:416 ==>> msg 02A5 loss. last_tick:133191. cur_tick:133703. period:50
[D][05:20:02][COMM]msg 02A6 loss. last_tick:133191. cur_tick:133704. period:50
[D][05:20:02][COMM]msg 02A7 loss. last_tick:133191. cur_tick:133704. period:50
[D][05:20:02][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 133704
[D][05:20:02][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 133705


2025-07-31 22:49:28:099 ==>> [W][05:20:02][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:20:02][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:02][FCTY]==========Modules-nRF5340 ==========
[D][05:20:02][FCTY]BootVersion = SA_BOOT_V109
[D][05:20:02][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:20:02][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:20:02][FCTY]DeviceID    = 460130020290229
[D][05:20:02][FCTY]HardwareID  = 867222087518447
[D][05:20:02][FCTY]MoBikeID    = 9999999999
[D][05:20:02][FCTY]LockID      = FFFFFFFFFF
[D][05:20:02][FCTY]BLEFWVersion= 105
[D][05:20:02][FCTY]BLEMacAddr   = FE16D5F6D559
[D][05:20:02][FCTY]Bat         = 3844 mv
[D][05:20:02][FCTY]Current     = 0 ma
[D][05:20:02][FCTY]VBUS        = 2600 mv
[D][05:20:02][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:20:02][FCTY]Ext battery vol = 32, adc = 1289
[D][05:20:02][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 0 mv
[D][05:20:02][FCTY]Bike Type flag is invalied
[D][05:20:02][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:20:02][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:20:02][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:20:02][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:20:02][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:20:02][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:20:02][FCTY]Bat1         = 3802 mv
[D][05:20:02][

2025-07-31 22:49:28:154 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:49:28:180 ==>> 检测【关机】
2025-07-31 22:49:28:196 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:49:28:225 ==>> FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:02][FCTY]==========Modules-nRF5340 ==========
[E][05:20:03][COMM]1x1 rx timeout
[E][05:20:03][COMM]1x1 tp timeout
[E][05:20:03][COMM]1x1 error -3.
[D][05:20:03][COMM]Main Task receive event:28 finished processing
[D][05:20:03][COMM]Main Task receive event:65
[D][05:20:03][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:20:03][COMM]Main Task receive event:65 finished processing
[D][05:20:03][COMM]Main Task receive event:60
[D][05:20:03][COMM]smart_helmet_vol=255,255
[D][05:20:03][COMM]report elecbike
[D][05:20:03][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:20:03][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:03][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:20:03][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:20:03][PROT]remove success[1629955203],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:20:03][PROT]min_index:0, type:0x5D03, priority:3
[D][05:20:03][PROT]index:0
[D][05:20:03][PROT]is_send:1
[D][05:20:03][PROT]sequence_num:10
[D][05:20:03][PROT]retry_timeout:0
[D][05:20:03][PROT]retry_times:3
[D][05:20:03][PROT]send_

2025-07-31 22:49:28:309 ==>> path:0x3
[D][05:20:03][PROT]msg_type:0x5d03
[D][05:20:03][PROT]===========================================================
[W][05:20:03][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955203]
[D][05:20:03][PROT]===========================================================
[D][05:20:03][PROT]Sending traceid[999999999990000B]
[D][05:20:03][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:20:03][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:20:03][PROT]ble is not inited or not connected or cccd not enabled
[D][05:20:03][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:20:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:20:03][SAL ]open socket ind id[4], rst[0]
[D][05:20:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:20:03][SAL ]Cellular task submsg id[8]
[D][05:20:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:20:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:20:03][PROT]add success [1629955203],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:20:03][COMM]Main Task receive event:60 finished processing
[D][05:20:03][M2M ]m2

2025-07-31 22:49:28:414 ==>> m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:20:03][CAT1]gsm read msg sub id: 8
[D][05:20:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:20:03][CAT1]<<< 
+CGATT: 0

OK

[D][05:20:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:20:03][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:20:03][CAT1]<<< 
+CME ERROR: 100

[D][05:20:03][COMM]msg 0220 loss. last_tick:133191. cur_tick:134199. period:100
[D][05:20:03][COMM]msg 0221 loss. last_tick:133191. cur_tick:134199. period:100
[D][05:20:03][COMM]msg 0224 loss. last_tick:133191. cur_tick:134200. period:100
[D][05:20:03][COMM]msg 0260 loss. last_tick:133191. cur_tick:134200. period:100
[D][05:20:03][COMM]msg 0280 loss. last_tick:133191. cur_tick:134200. period:100
[D][05:20:03][COMM]msg 02C0 loss. last_tick:133191. cur_tick:134201. period:100
[D][05:20:03][COMM]msg 02C1 loss. last_tick:133191. cur_tick:134201. period:100
[D][05:20:03][COMM]msg 02C2 loss. last_tick:133191. cur_tick:134201. period:100
[D][05:20:03][COMM]msg 02E0 loss. last_tick:133191. cur_tick:134202. period:100
[D][05:20:03][COMM]msg 02E1 loss. last_tick:133191. cur_tick:134202. period:100
[D][05:20:03][COMM]msg 02E2 loss. last_tick:133191. cur_tick:134202. per

2025-07-31 22:49:28:519 ==>> iod:100
[D][05:20:03][COMM]msg 0300 loss. last_tick:133191. cur_tick:134203. period:100
[D][05:20:03][COMM]msg 0301 loss. last_tick:133191. cur_tick:134203. period:100
[D][05:20:03][COMM]bat msg 0240 loss. last_tick:133191. cur_tick:134204. period:100. j,i:1 54
[D][05:20:03][COMM]bat msg 0241 loss. last_tick:133191. cur_tick:134204. period:100. j,i:2 55
[D][05:20:03][COMM]bat msg 0242 loss. last_tick:133191. cur_tick:134204. period:100. j,i:3 56
[D][05:20:03][COMM]bat msg 0244 loss. last_tick:133191. cur_tick:134205. period:100. j,i:5 58
[D][05:20:03][COMM]bat msg 024E loss. last_tick:133191. cur_tick:134205. period:100. j,i:15 68
[D][05:20:03][COMM]bat msg 024F loss. last_tick:133191. cur_tick:134206. period:100. j,i:16 69
[D][05:20:03][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 134206
[D][05:20:03][COMM]CAN message bat fault change: 0x00000000->0x0001802E 134206
[D][05:20:03][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 134207


2025-07-31 22:49:29:183 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:49:29:213 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:49:29:318 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      udio].l:[255]. success, file_name:B50, size:10800
[D][05:20:03][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:20:03][COMM]read file, len:10800, num:3
[D][05:20:03][COMM]Main Task receive event:60
[D][05:20:03][COMM]smart_helmet_vol=255,255
[D][05:20:03][COMM]BAT CAN get state1 Fail 204
[D][05:20:03][COMM]BAT CAN get soc Fail, 204
[D][05:20:03][COMM]BAT CAN get state2 fail 204

2025-07-31 22:49:29:423 ==>> 
[D][05:20:03][COMM]get soh error
[E][05:20:03][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:20:03][COMM]report elecbike
[W][05:20:03][PROT]remove success[1629955203],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:20:03][PROT]add success [1629955203],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:20:03][COMM]Main Task receive event:60 finished processing
[D][05:20:03][COMM]Main Task receive event:61
[D][05:20:03][COMM][D301]:type:3, trace id:280
[D][05:20:03][PROT]min_index:1, type:0x5D03, priority:4
[D][05:20:03][PROT]index:1
[D][05:20:03][PROT]is_send:1
[D][05:20:03][PROT]sequence_num:11
[D][05:20:03][PROT]retry_timeout:0
[D][05:20:03][PROT]retry_times:3
[D][05:20:03][PROT]send_path:0x3
[D][05:20:03][PROT]msg_type:0x5d03
[D][05:20:03][PROT]===========================================================
[W][05:20:03][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955203]
[D][05:20:03][PROT]===========================================================
[D][05:20:03][PROT]Sending traceid[999999999990000C]
[D][05:20:03][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:20:03][BLE ]BLE_WRN [frm_ble_get_current

2025-07-31 22:49:29:528 ==>> _framer:357] ble is not connect

[W][05:20:03][PROT]ble is not inited or not connected or cccd not enabled
[D][05:20:03][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:03][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:20:03][COMM]id[], hw[000
[D][05:20:03][COMM]get mcMaincircuitVolt error
[D][05:20:03][COMM]get mcSubcircuitVolt error
[D][05:20:03][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:20:03][COMM]BAT CAN get state1 Fail 204
[D][05:20:03][COMM]BAT CAN get soc Fail, 204
[D][05:20:03][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:20:03][COMM]BAT CAN get state2 fail 204
[D][05:20:03][COMM]get bat work mode err
[W][05:20:03][PROT]remove success[1629955203],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:20:03][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[D][05:20:03][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:03][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:20:03][PROT]add success [1629955203],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:20:03][COMM]--->crc16:

2025-07-31 22:49:29:633 ==>> 0xb8a
[D][05:20:03][COMM]read file success
[W][05:20:03][COMM][Audio].l:[936].close hexlog save
[D][05:20:03][COMM]accel parse set 1
[D][05:20:03][COMM][Audio]mon:9,05:20:03
[D][05:20:03][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:20:03][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:20:03][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:20:03][COMM]Main Task receive event:61 finished processing
[D][05:20:03][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:20:03][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:20:03][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:20:03][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:03][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:20:03][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:20:03][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:20:03][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:20:03][COMM

2025-07-31 22:49:29:738 ==>> ]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:20:03][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:20:03][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:20:04][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:04][COMM]arm_hub_enable: hub power: 0
[D][05:20:04][HSDK]hexlog index save 0 4096 35 @ 0 : 0
[D][05:20:04][HSDK]write save hexlog index [0]
[D][05:20:04][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:04][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048


2025-07-31 22:49:29:798 ==>> 
[D][05:20:04][COMM]read battery soc:255
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:04][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:20:04][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 22:49:29:903 ==>>                               [W][05:20:05][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:05][COMM]arm_hub_enable: hub power: 0
[D][05:20:05][HSDK]hexlog index save 0 4096 35 @ 0 : 0
[D][05:20:05][HSD

2025-07-31 22:49:29:948 ==>> K]write save hexlog index [0]
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:49:30:208 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:49:30:441 ==>> [W][05:20:05][COMM]Power Off
[W][05:20:05][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:05][COMM]arm_hub_enable: hub power: 0
[D][05:20:05][HSDK]hexlog index save 0 4096 35 @ 0 : 0
[D][05:20:05][HSDK]write save hexlog index [0]
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:49:30:496 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:49:30:505 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:49:30:520 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:49:30:606 ==>> [D][05:20:06][COMM]exit wheel stolen mode.
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:49:30:711 ==>> [D][05:20:06][COMM]Main Task receive event:68
[D][05:20:06][COMM]handlerWheelStolen evt type = 2.
[E][05:20:06][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:20:06][GNSS]stop locating
[D][05:20:06][GNSS]all continue location stop
[D][05:20:06][COMM]Main Task receive event:68 finished processing
  

2025-07-31 22:49:30:774 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:49:30:790 ==>> 检测【检测小电池关机电流】
2025-07-31 22:49:30:811 ==>> 开始小电池电流采样
2025-07-31 22:49:30:839 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:30:876 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:49:31:888 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:49:31:920 ==>> CurrentBattery:ƽ��:71.19

2025-07-31 22:49:32:398 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:49:32:414 ==>> 【检测小电池关机电流】通过,【71.19uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:49:32:811 ==>> MES过站成功
2025-07-31 22:49:32:822 ==>> #################### 【测试结束】 ####################
2025-07-31 22:49:32:840 ==>> 关闭5V供电
2025-07-31 22:49:32:856 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:49:32:907 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:49:33:841 ==>> 关闭5V供电成功
2025-07-31 22:49:33:856 ==>> 关闭33V供电
2025-07-31 22:49:33:885 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:49:33:918 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:49:34:850 ==>> 关闭33V供电成功
2025-07-31 22:49:34:871 ==>> 关闭3.7V供电
2025-07-31 22:49:34:892 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:49:34:916 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:49:35:700 ==>>  

