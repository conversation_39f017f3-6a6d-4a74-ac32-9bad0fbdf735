2025-07-31 20:10:58:747 ==>> MES查站成功:
查站序号:P5100010053126AF验证通过
2025-07-31 20:10:58:766 ==>> 扫码结果:P5100010053126AF
2025-07-31 20:10:58:767 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:10:58:769 ==>> 测试参数版本:2024.10.11
2025-07-31 20:10:58:771 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:10:58:773 ==>> 检测【打开透传】
2025-07-31 20:10:58:775 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:10:58:846 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:10:59:048 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:10:59:054 ==>> 检测【检测接地电压】
2025-07-31 20:10:59:057 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:10:59:148 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:10:59:332 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:10:59:334 ==>> 检测【打开小电池】
2025-07-31 20:10:59:337 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:10:59:449 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:10:59:603 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:10:59:605 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:10:59:608 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:10:59:740 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:10:59:880 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:10:59:882 ==>> 检测【等待设备启动】
2025-07-31 20:10:59:885 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:00:253 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:00:435 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti?

2025-07-31 20:11:00:920 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:01:010 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:01:190 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:01:691 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:01:886 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 20:11:01:946 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:02:369 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:02:564 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 20:11:02:972 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:03:077 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:03:258 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 20:11:03:749 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:03:944 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 20:11:04:004 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:04:454 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:04:620 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 20:11:05:031 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:05:140 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:05:322 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 20:11:05:815 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:05:998 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:06:073 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:06:518 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:06:684 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:07:115 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:07:192 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:07:389 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:07:876 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:08:058 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 20:11:08:148 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:08:581 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:08:763 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:09:194 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:09:256 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:09:439 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:09:962 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:10:127 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:10:233 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:11:10:237 ==>> #################### 【测试结束】 ####################
2025-07-31 20:11:10:256 ==>> 关闭5V供电
2025-07-31 20:11:10:259 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:11:10:341 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:11:10:629 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:10:826 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:11:256 ==>> 关闭5V供电成功
2025-07-31 20:11:11:260 ==>> 关闭33V供电
2025-07-31 20:11:11:264 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:11:11:333 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0
5A A5 02 5A A5 


2025-07-31 20:11:11:438 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:11:11:498 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:11:12:021 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:12:187 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 20:11:12:262 ==>> 关闭33V供电成功
2025-07-31 20:11:12:264 ==>> 关闭3.7V供电
2025-07-31 20:11:12:267 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:11:12:337 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:11:12:567 ==>>  

