2025-07-31 23:32:10:022 ==>> MES查站成功:
查站序号:P51000100531383A验证通过
2025-07-31 23:32:10:036 ==>> 扫码结果:P51000100531383A
2025-07-31 23:32:10:037 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:32:10:039 ==>> 测试参数版本:2024.10.11
2025-07-31 23:32:10:041 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:32:10:042 ==>> 检测【打开透传】
2025-07-31 23:32:10:044 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:32:10:122 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:32:10:377 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:32:10:386 ==>> 检测【检测接地电压】
2025-07-31 23:32:10:388 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:32:10:531 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 23:32:10:669 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:32:10:671 ==>> 检测【打开小电池】
2025-07-31 23:32:10:674 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:32:10:729 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:32:10:945 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:32:10:947 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:32:10:949 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:32:11:035 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:32:11:218 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:32:11:221 ==>> 检测【等待设备启动】
2025-07-31 23:32:11:224 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:11:562 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:11:729 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 23:32:12:246 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:12:250 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:12:428 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 23:32:12:949 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:13:131 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 23:32:13:281 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:13:645 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:13:827 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 23:32:14:315 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:14:345 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:14:527 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 23:32:15:037 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:15:233 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti 

2025-07-31 23:32:15:353 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:15:751 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:15:931 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 23:32:16:385 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:16:446 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:16:628 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 23:32:17:142 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:17:337 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 23:32:17:412 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:17:838 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:18:034 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 23:32:18:451 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:18:544 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:18:739 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: e

2025-07-31 23:32:19:249 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:32:19:429 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:32:19:474 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:20:102 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:32:20:147 ==>>                               , GPS Will Not Open


2025-07-31 23:32:20:511 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:32:20:542 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:32:21:018 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:32:21:051 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:32:21:053 ==>> 检测【产品通信】
2025-07-31 23:32:21:055 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:32:21:215 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:32:21:323 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:32:21:327 ==>> 检测【初始化完成检测】
2025-07-31 23:32:21:351 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:32:21:562 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:32:21:652 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:32:21:744 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:32:21:746 ==>> 检测【关闭大灯控制1】
2025-07-31 23:32:21:748 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:32:21:896 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:32:22:019 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:32:22:022 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:32:22:023 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:32:22:077 ==>> [D][05:17:51][COMM]2629 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:22:287 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:32:22:551 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:32:22:564 ==>> 检测【关闭仪表供电】
2025-07-31 23:32:22:565 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:32:22:717 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:32:22:825 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:32:22:827 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:32:22:830 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:32:22:993 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:32:23:098 ==>> [D][05:17:52][COMM]3641 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:23:106 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:32:23:108 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:32:23:109 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:32:23:294 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:32:23:387 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:32:23:390 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:32:23:391 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:32:23:593 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:32:23:664 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:32:23:667 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:32:23:669 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:32:23:728 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:32:23:818 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30


2025-07-31 23:32:23:878 ==>>                              ry soc:255


2025-07-31 23:32:23:942 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:32:23:944 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:32:23:947 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:32:24:030 ==>> 5A A5 03 5A A5 


2025-07-31 23:32:24:135 ==>> [D][05:17:53][COMM]4653 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:32:24:214 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:32:24:216 ==>> 该项需要延时执行
2025-07-31 23:32:24:626 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5007. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5008. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0

2025-07-31 23:32:24:656 ==>> 008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010


2025-07-31 23:32:25:110 ==>> [D][05:17:54][COMM]5664 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:25:410 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:32:25:890 ==>>                                                                                                                           --
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COM

2025-07-31 23:32:25:995 ==>> M]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17

2025-07-31 23:32:26:100 ==>> :55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CA

2025-07-31 23:32:26:160 ==>> N get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 23:32:26:190 ==>>                                                                                                                                           

2025-07-31 23:32:27:134 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7687 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:27:883 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:32:28:157 ==>> [D][05:17:57][COMM]8698 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:28:217 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:32:28:220 ==>> 检测【33V输入电压ADC】
2025-07-31 23:32:28:234 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:32:28:539 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3166  volt:5565 mv
[D][05:17:57][COMM]adc read out 24v adc:1328  volt:33589 mv
[D][05:17:57][COMM]adc read left brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:12  volt:15 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1310  volt:33133 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2396  volt:3860 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:32:28:753 ==>> 【33V输入电压ADC】通过,【33133mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:32:28:755 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:32:28:758 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:32:28:839 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1665mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1984mV
Get AD_V7 1093mV
OVER 150


2025-07-31 23:32:29:021 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:026 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:32:29:040 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:042 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:32:29:045 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 23:32:29:060 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:32:29:062 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:32:29:083 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1984mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:32:29:085 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:32:29:106 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:32:29:108 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:32:29:155 ==>> [D][05:17:58][COMM]9710 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:32:29:245 ==>> 1A A1 00 00 FC 
Get AD_V2 1670mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1988mV
Get AD_V7 1092mV
OVER 150


2025-07-31 23:32:29:388 ==>> 【TP7_VCC3V3(ADV2)】通过,【1670mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:391 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:32:29:407 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:409 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:32:29:427 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 23:32:29:429 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:32:29:432 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:32:29:446 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:32:29:449 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:32:29:480 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:32:29:483 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:32:29:503 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10014
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10015


2025-07-31 23:32:29:534 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1986mV
Get AD_V7 1092mV
OVER 150


2025-07-31 23:32:29:770 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:772 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:32:29:790 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:29:792 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:32:29:795 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 23:32:29:808 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:32:29:810 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:32:29:826 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1986mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:32:29:833 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:32:29:849 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:32:29:851 ==>> 检测【打开WIFI(1)】
2025-07-31 23:32:29:853 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:32:29:895 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:32:29:985 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:32:30:123 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:32:30:126 ==>> 检测【清空消息队列(1)】
2025-07-31 23:32:30:139 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:32:30:414 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10722 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to:

2025-07-31 23:32:30:459 ==>>  M2M_GSM_INIT_ACK
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:32:30:660 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:32:30:664 ==>> 检测【打开GPS(1)】
2025-07-31 23:32:30:668 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:32:30:906 ==>>                                                                                                                                                                                                                                                                                                                            5:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087618809

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539528

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:

2025-07-31 23:32:30:951 ==>> 18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:32:31:026 ==>>                                                                                                                           [D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:32:31:176 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:32:31:209 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:32:31:213 ==>> 检测【打开GSM联网】
2025-07-31 23:32:31:216 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:32:31:421 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 23:32:31:498 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:32:31:501 ==>> 检测【打开仪表供电1】
2025-07-31 23:32:31:503 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:32:31:894 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0

2025-07-31 23:32:31:984 ==>> x0, power:1
[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.59.18.247"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6
                                         

2025-07-31 23:32:32:061 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:32:32:065 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:32:32:068 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:32:32:089 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular tas

2025-07-31 23:32:32:134 ==>> k submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:32:32:405 ==>>                                                                                                                                                                                                                                                                                                               FG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:32:32:621 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:32:32:625 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:32:32:629 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:32:32:818 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33224]


2025-07-31 23:32:32:900 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:32:32:906 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:32:32:911 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:32:33:139 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:32:33:169 ==>>                                       

2025-07-31 23:32:33:172 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:32:33:176 ==>> 检测【AD_V20电压】
2025-07-31 23:32:33:178 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:32:33:274 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:32:33:334 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:32:33:730 ==>> 本次取值间隔时间:441ms
2025-07-31 23:32:33:748 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:32:33:775 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:32:33:850 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:32:34:016 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,59,,,41,40,,,40,34,,,38,39,,,38,1*76

$GBGSV,2,2,07,25,,,38,60,,,38,11,,,36,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1627.154,1627.154,51.970,2097152,2097152,2097152*45

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id:

2025-07-31 23:32:34:046 ==>>  23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 23:32:34:196 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:32:34:256 ==>> 本次取值间隔时间:404ms
2025-07-31 23:32:34:275 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:32:34:377 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:32:34:438 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:32:34:743 ==>> 本次取值间隔时间:356ms
2025-07-31 23:32:34:761 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:32:34:866 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:32:34:957 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,40,59,,,39,25,,,39,39,,,39,1*7C

$GBGSV,3,2,12,60,,,39,11,,,39,34,,,38,23,,,38,1*74

$GBGSV,3,3,12,3,,,37,7,,,37,43,,,38,2,,,37,1*4B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.043,1596.043,50.959,2097152,2097152,2097152*4F

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150


2025-07-31 23:32:34:987 ==>> 本次取值间隔时间:115ms
2025-07-31 23:32:35:005 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:32:35:007 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:32:35:010 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:32:35:123 ==>> 3A A3 02 00 A3 


2025-07-31 23:32:35:228 ==>> OFF_OUT2
OVER 150


2025-07-31 23:32:35:281 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:32:35:284 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:32:35:288 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:32:35:531 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:32:35:946 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,40,,,40,59,,,39,25,,,39,39,,,39,1*7C

$GBGSV,4,2,15,60,,,39,11,,,39,34,,,38,23,,,38,1*74

$GBGSV,4,3,15,3,,,38,7,,,38,43,,,37,2,,,34,1*47

$GBGSV,4,4,15,1,,,34,32,,,34,41,,,36,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1557.602,1557.602,49.784,2097152,2097152,2097152*49

[D][05:18:05][COMM]read battery soc:255


2025-07-31 23:32:36:322 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:32:36:553 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:32:36:928 ==>> $GBGGA,153240.803,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,41,39,,,40,25,,,39,60,,,39,1*7A

$GBGSV,4,2,16,59,,,38,11,,,38,34,,,38,23,,,38,1*7D

$GBGSV,4,3,16,3,,,38,7,,,38,43,,,38,16,,,36,1*7C

$GBGSV,4,4,16,32,,,35,41,,,34,1,,,34,2,,,33,1*70

$GBRMC,153240.803,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153240.803,0.000,1546.879,1546.879,49.452,2097152,2097152,2097152*55



2025-07-31 23:32:37:346 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:32:37:668 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,153241.503,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,41,39,,,39,25,,,39,59,,,39,1*70

$GBGSV,5,2,19,11,,,39,34,,,39,3,,,39,60,,,38,1*4A

$GBGSV,5,3,19,23,,,38,7,,,38,43,,,38,16,,,37,1*41

$GBGSV,5,4,19,32,,,35,41,,,35,10,,,35,1,,,34,1*4A

$GBGSV,5,5,19,2,,,33,4,,,38,6,,,36,1*40

$GBRMC,153241.503,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153241.503,0.000,1550.991,1550.991,49.581,2097152,2097152,2097152*56



2025-07-31 23:32:37:926 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 23:32:38:384 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 23:32:38:392 ==>> #################### 【测试结束】 ####################
2025-07-31 23:32:38:407 ==>> 关闭5V供电
2025-07-31 23:32:38:411 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:32:38:534 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:32:38:639 ==>> $GBGGA,153242.503,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,40,39,,,39,25,,,39,11,,,39,1*76

$GBGSV,6,2,22,34,,,39,3,,,39,59,,,38,60,,,38,1*4C

$GBGSV,6,3,22,23,,,38,7,,,38,43,,,38,16,,,37,1*4A

$GBGSV,6,4,22,32,,,35,41,,,35,10,,,35,6,,,34,1*46

$GBGSV,6,5,22,1,,,34,2,,,33,9,,,33,24,,,32,1*4F

$GBGSV,6,6,22,4,,,30,12,,,29,1*49

$GBRMC,153242.503,V,,,,,,,,0.0,E,N,V*4B

$

2025-07-31 23:32:38:670 ==>> GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153242.503,0.000,1492.513,1492.513,47.762,2097152,2097152,2097152*54



2025-07-31 23:32:39:411 ==>> 关闭5V供电成功
2025-07-31 23:32:39:416 ==>> 关闭33V供电
2025-07-31 23:32:39:420 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:32:39:533 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:32:39:743 ==>> $GBGGA,153243.503,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,40,39,,,39,25,,,39,3,,,39,1*44

$GBGSV,6,2,23,11,,,38,34,,,38,59,,,38,60,,,38,1*7E

$GBGSV,6,3,23,23,,,38,7,,,38,43,,,38,16,,,37,1*4B

$GBGSV,6,4,23,41,,,36,10,,,36,32,,,35,6,,,34,1*47

$GBGSV,6,5,23,1,,,34,9,,,33,2,,,32,24,,,32,1*4F

$GBGSV,6,6,23,12,,,32,4,,,30,28,,,38,1*43

$GBRMC,153243.503,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153243.503,0.000,1496.267,1496.267,47.868,2097152,2097152,2097152*50

[D][05:18:09][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 8,volt = 13


2025-07-31 23:32:39:938 ==>> [D][05:18:09][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:32:40:424 ==>> 关闭33V供电成功
2025-07-31 23:32:40:429 ==>> 关闭3.7V供电
2025-07-31 23:32:40:434 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:32:40:533 ==>> 6A A6 02 A6 6A 


2025-07-31 23:32:40:638 ==>> $GBGGA,153244.503,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,40,39,,,39,3,,,39,25,,,38,1*44

$GBGSV,6,2,22,11,,,38,34,,,38,59,,,38,60,,,38,1*7F

$GBGSV,6,3,22,23,,,38,7,,,38,43,,,38,16,,,37,1*4A

$GBGSV,6,4,22,41,,,36,10,,,36,32,,,35,6,,,35,1*47

$GBGSV,6,5,22,1,,,34,9,,,33,24,,,33,2,,,32,1*4F

$GBGSV,6,6,22,12,,,32,4,,,30,1*43

$GBRMC,153244.503,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153244.503,0.000,1498.146,1498.146,47.921,2097152,2097152,2097152*5B

Battery OFF
OVER 150


