2025-07-31 18:08:28:174 ==>> MES查站成功:
查站序号:P5100010053133A8验证通过
2025-07-31 18:08:28:183 ==>> 扫码结果:P5100010053133A8
2025-07-31 18:08:28:185 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:08:28:187 ==>> 测试参数版本:2024.10.11
2025-07-31 18:08:28:189 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:08:28:191 ==>> 检测【打开透传】
2025-07-31 18:08:28:193 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:08:28:263 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:08:28:539 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:08:28:544 ==>> 检测【检测接地电压】
2025-07-31 18:08:28:545 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:08:28:655 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:08:28:836 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:08:28:839 ==>> 检测【打开小电池】
2025-07-31 18:08:28:842 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:08:28:956 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:08:29:112 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:08:29:114 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:08:29:118 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:08:29:263 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:08:29:388 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:08:29:390 ==>> 检测【等待设备启动】
2025-07-31 18:08:29:392 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:08:29:696 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:08:29:878 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:08:30:419 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:08:30:570 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:08:30:966 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:08:31:430 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:08:31:551 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:08:31:554 ==>> 检测【产品通信】
2025-07-31 18:08:31:556 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:08:31:723 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:08:31:861 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:08:31:863 ==>> 检测【初始化完成检测】
2025-07-31 18:08:31:866 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:08:32:135 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:08:32:418 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:08:32:421 ==>> 检测【关闭大灯控制1】
2025-07-31 18:08:32:423 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:08:32:673 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:08:32:947 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:08:32:949 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:08:32:950 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:08:33:159 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:08:33:226 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:08:33:229 ==>> 检测【关闭仪表供电】
2025-07-31 18:08:33:250 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:08:33:448 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:08:33:496 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:08:33:499 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:08:33:501 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:08:33:523 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:08:33:628 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:08:33:772 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:08:33:775 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:08:33:777 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:08:33:919 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:08:34:046 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:08:34:048 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:08:34:051 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:08:34:225 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:08:34:321 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:08:34:324 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:08:34:346 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:08:34:453 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:08:34:544 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 14
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:08:34:605 ==>>                                   c:255


2025-07-31 18:08:34:618 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:08:34:621 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:08:34:623 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:08:34:757 ==>> 5A A5 03 5A A5 


2025-07-31 18:08:34:862 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 18:08:34:924 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:08:34:928 ==>> 该项需要延时执行
2025-07-31 18:08:35:074 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5018. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5018. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5019. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5019. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5019. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5020. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5020. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5021. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5021. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5022. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5022. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault

2025-07-31 18:08:35:104 ==>>  change: 0x0000E00C71E22217->0x0008F00C71E22217 5022
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5023


2025-07-31 18:08:35:550 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:08:36:119 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:08:36:702 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][CO

2025-07-31 18:08:36:807 ==>> MM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id

2025-07-31 18:08:36:912 ==>> [], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][

2025-07-31 18:08:37:002 ==>> 05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255
                                        

2025-07-31 18:08:37:590 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:08:38:633 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 18:08:38:926 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:08:38:929 ==>> 检测【33V输入电压ADC】
2025-07-31 18:08:38:932 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:08:39:266 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:17:58][COMM]adc read out 24v adc:1302  volt:32931 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:2 mv
[D][05:17:58][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2417  volt:3894 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:08:39:465 ==>> 【33V输入电压ADC】通过,【32627mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:08:39:468 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:08:39:470 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:08:39:573 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1652mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 18:08:39:602 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:08:39:740 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:39:744 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:08:39:761 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:39:763 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:08:39:764 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 18:08:39:780 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:08:39:800 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:08:39:802 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:08:39:804 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:08:39:823 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:08:39:825 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:08:39:968 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10010
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10010
1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1652mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1991mV
Get AD_V7 1092mV
OVER 150


2025-07-31 18:08:40:119 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:40:121 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:08:40:148 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:40:151 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:08:40:152 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 18:08:40:172 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:08:40:174 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:08:40:199 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:08:40:201 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:08:40:231 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:08:40:233 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:08:40:367 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1652mV
Get AD_V4 1mV
Get AD_V5 2773mV
Get AD_V6 1987mV
Get AD_V7 1092mV
OVER 150


2025-07-31 18:08:40:524 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:40:527 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:08:40:551 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:40:567 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:08:40:570 ==>> 原始值:【2773】, 乘以分压基数【2】还原值:【5546】
2025-07-31 18:08:40:576 ==>> 【TP68_VCC5V5(ADV5)】通过,【5546mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:08:40:579 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:08:40:598 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:08:40:603 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:08:40:632 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:08:40:634 ==>> 检测【打开WIFI(1)】
2025-07-31 18:08:40:636 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:08:40:826 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,

2025-07-31 18:08:40:931 ==>> 0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 18:08:41:006 ==>>                                                                                                                                                                                                                                                                                            

2025-07-31 18:08:41:596 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:08:41:656 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:08:41:855 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:08:41:933 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:08:41:962 ==>> 检测【清空消息队列(1)】
2025-07-31 18:08:41:964 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:08:42:146 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:01][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:08:42:210 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:08:42:213 ==>> 检测【打开GPS(1)】
2025-07-31 18:08:42:218 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:08:42:450 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 18:08:42:493 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:08:42:495 ==>> 检测【打开GSM联网】
2025-07-31 18:08:42:499 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:08:42:647 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][COMM]read battery soc:255


2025-07-31 18:08:42:707 ==>>                                                                                             

2025-07-31 18:08:42:770 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:08:42:773 ==>> 检测【打开仪表供电1】
2025-07-31 18:08:42:775 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:08:43:294 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087873586

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130020290638

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK



2025-07-31 18:08:43:324 ==>> 
[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:08:43:574 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:08:43:579 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:08:43:581 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:08:43:757 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:08:43:874 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:08:43:877 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:08:43:880 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:08:44:064 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub read adc[3],val[33641]


2025-07-31 18:08:44:235 ==>> 【读取主控ADC采集的仪表电压】通过,【33641mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:08:44:238 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:08:44:242 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:08:44:447 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:08:44:529 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:08:44:532 ==>> 检测【AD_V20电压】
2025-07-31 18:08:44:536 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:08:44:644 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:08:44:648 ==>> [D][05:18:03][COMM]14730 imu init OK
[D][05:18:03][COMM]read battery soc:255


2025-07-31 18:08:44:749 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:08:44:779 ==>> 本次取值间隔时间:125ms
2025-07-31 18:08:44:814 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:08:44:916 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:08:45:037 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 18:08:45:097 ==>> 本次取值间隔时间:170ms
2025-07-31 18:08:45:278 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ad msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][COMM]init key as 
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:04][COMM]Main Task receive event:4 finished processing
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 24,99

OK

[W][05:18:04][COMM]>>>>>I

2025-07-31 18:08:45:338 ==>> 本次取值间隔时间:230ms
2025-07-31 18:08:45:342 ==>> nput command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:04][CAT1]<<< 
+QIACT: 1,1,1,"10.80.7.146"

OK

[D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:08:45:533 ==>> 本次取值间隔时间:187ms
2025-07-31 18:08:45:537 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 18:08:45:669 ==>> 本次取值间隔时间:130ms
2025-07-31 18:08:45:673 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:08:45:777 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:08:45:914 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1641mV
OVER 150


2025-07-31 18:08:46:130 ==>> 本次取值间隔时间:342ms
2025-07-31 18:08:46:163 ==>> 【AD_V20电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:08:46:167 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:08:46:171 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:08:46:254 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:08:46:446 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:08:46:449 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:08:46:452 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:08:46:545 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:08:46:712 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][COMM]read battery soc:255


2025-07-31 18:08:47:203 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:08:47:429 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,42,38,,,42,42,,,42,26,,,41,1*78

$GBGSV,3,2,11,59,,,40,39,,,39,60,,,39,33,,,37,1*77

$GBGSV,3,3,11,21,,,18,13,,,44,16,,,36,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1566.417,1566.417,50.317,2097152,2097152,2097152*4F

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 18:08:47:475 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:08:47:687 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:08:48:352 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,24,,,42,21,,,42,38,,,41,42,,,41,1*7F

$GBGSV,4,2,15,26,,,41,59,,,40,60,,,40,39,,,39,1*7F

$GBGSV,4,3,15,8,,,39,13,,,37,33,,,37,14,,,37,1*44

$GBGSV,4,4,15,6,,,37,4,,,36,16,,,35,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1614.072,1614.072,51.596,2097152,2097152,2097152*41



2025-07-31 18:08:48:504 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:08:48:723 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:07][COMM]read battery soc:255


2025-07-31 18:08:49:374 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,3,,,42,21,,,41,38,,,41,1*47

$GBGSV,5,2,19,42,,,41,26,,,41,59,,,41,60,,,40,1*70

$GBGSV,5,3,19,39,,,40,8,,,40,13,,,39,33,,,37,1*46

$GBGSV,5,4,19,14,,,37,6,,,37,16,,,37,2,,,36,1*78

$GBGSV,5,5,19,4,,,35,5,,,35,1,,,35,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.120,1608.120,51.418,2097152,2097152,2097152*46



2025-07-31 18:08:49:540 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:08:49:544 ==>> #################### 【测试结束】 ####################
2025-07-31 18:08:49:584 ==>> 关闭5V供电
2025-07-31 18:08:49:589 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:08:49:650 ==>> 5A A5 04 5A A5 


2025-07-31 18:08:49:755 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:08:50:387 ==>> $GBGGA,100854.182,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,3,,,41,21,,,41,38,,,41,1*4E

$GBGSV,5,2,20,42,,,41,26,,,41,60,,,41,59,,,40,1*7A

$GBGSV,5,3,20,39,,,40,8,,,40,13,,,40,16,,,38,1*4A

$GBGSV,5,4,20,33,,,37,14,,,37,6,,,37,1,,,37,1*77

$GBGSV,5,5,20,2,,,36,4,,,34,5,,,34,9,,,41,1*7E

$GBRMC,100854.182,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100854.182,0.000,1610.302,1610.302,51.487,2097152,2097152,2097152*5D



2025-07-31 18:08:50:600 ==>> 关闭5V供电成功
2025-07-31 18:08:50:604 ==>> 关闭33V供电
2025-07-31 18:08:50:608 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:08:50:662 ==>> 5A A5 02 5A A5 


2025-07-31 18:08:50:767 ==>> $GBGGA,100854.582,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,42,3,,,41,21,,,41,1*4D

$GBGSV,5,2,20,42,,,41,26,,,41,13,,,41,60,,,40,1*74

$GBGSV,5,3,20,59,,,40,39,,,40,8,,,40,9,,,39,1*7B

$GBGSV,5,4,20,16,,,38,33,,,37,14,,,37,6,,,37,1*4E

$GBGSV,5,5,20,1,,,37,2,,,36,4,,,34,5,,,34,1*77

$GBRMC,100854.582,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100854.582,0.000,1612.701,1612.701,51.563,2097152,2097152,2097152*52

[D][05:18:09][COMM]read battery soc:255
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:08:50:962 ==>> [D][05:18:09][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:08:51:176 ==>> [D][05:18:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:08:51:607 ==>> 关闭33V供电成功
2025-07-31 18:08:51:612 ==>> 关闭3.7V供电
2025-07-31 18:08:51:626 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:08:51:654 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:08:51:759 ==>> $GBGGA,100855.562,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,42,3,,,41,21,,,41,1*4F

$GBGSV,6,2,21,42,,,41,26,,,41,13,,,41,60,,,41,1*77

$GBGSV,6,3,21,59,,,40,39,,,40,8,,,40,16,,,39,1*47

$GBGSV,6,4,21,9,,,38,33,,,37,14,,,37,6,,,37,1*72

$GBGSV,6,5,21,1,,,37,2,,,36,4,,,34,5,,,34,1*75

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100855.562,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0. 

