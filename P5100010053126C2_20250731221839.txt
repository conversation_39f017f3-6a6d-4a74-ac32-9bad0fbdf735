2025-07-31 22:18:39:622 ==>> MES查站成功:
查站序号:P5100010053126C2验证通过
2025-07-31 22:18:39:627 ==>> 扫码结果:P5100010053126C2
2025-07-31 22:18:39:629 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:18:39:631 ==>> 测试参数版本:2024.10.11
2025-07-31 22:18:39:633 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:18:39:634 ==>> 检测【打开透传】
2025-07-31 22:18:39:636 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:18:39:703 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:18:39:973 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:18:39:982 ==>> 检测【检测接地电压】
2025-07-31 22:18:39:984 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:18:40:099 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:18:40:253 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:18:40:258 ==>> 检测【打开小电池】
2025-07-31 22:18:40:262 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:18:40:400 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:18:40:523 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:18:40:526 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:18:40:529 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:18:40:598 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:18:40:796 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:18:40:799 ==>> 检测【等待设备启动】
2025-07-31 22:18:40:801 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:18:41:189 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:18:41:354 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 22:18:41:822 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:18:41:867 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:18:42:064 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:18:42:701 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:18:42:761 ==>>                                                    

2025-07-31 22:18:42:851 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:18:43:155 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:18:43:633 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:18:43:653 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:18:43:656 ==>> 检测【产品通信】
2025-07-31 22:18:43:657 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:18:43:857 ==>> AT+PWD= 

2025-07-31 22:18:44:352 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:18:44:549 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:18:44:700 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:18:45:728 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:18:45:880 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][FCTY]F:[init_weight_calibration_point].L:[17805] ready to read para flash
[D][05:17:49][FCTY]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:17:49][COMM]mc_speed_config init now_set:25
[D][05:17:49][FCTY]not change wear status = 2 254
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05

2025-07-31 22:18:45:985 ==>> :17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]frm CAN read mc pwr mode invalid,val:254
[D][05:17:49][COMM]netcore_v

2025-07-31 22:18:46:004 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:18:46:007 ==>> 检测【初始化完成检测】
2025-07-31 22:18:46:011 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:18:46:090 ==>> er 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA_BOOT_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:17:49][FCTY]Bat         = 0 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][FCTY]TEMP

2025-07-31 22:18:46:195 ==>> = 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 0
[D][05:17:49][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]CAT1_GNSS_VERSION =
[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][COMM][CHG]ext_48v_vol:0, disable charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][FCTY]Bat1         = 3693 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi s

2025-07-31 22:18:46:300 ==>> can.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][CAT1]gsm read msg sub id: 1
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:50][COMM]msg 0220 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0221 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0224 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0260 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][C

2025-07-31 22:18:46:405 ==>> OMM]msg 02E2 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick:0. cur_tick:1019. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:1019. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1020. period:100. j,i:3 56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1020. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1021. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1021. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1021
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1022
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1022
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:18:46:707 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:18:46:812 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:18:46:917 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 22:18:47:022 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            :50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_p

2025-07-31 22:18:47:026 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:18:47:127 ==>> ark_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1626 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50]

2025-07-31 22:18:47:157 ==>> [COMM]SE50 init success!


2025-07-31 22:18:47:262 ==>>                                                                                                                     

2025-07-31 22:18:47:295 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:18:47:298 ==>> 检测【关闭大灯控制1】
2025-07-31 22:18:47:324 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:18:47:352 ==>>                                           [W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:18:47:442 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:18:47:566 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:18:47:568 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:18:47:571 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:18:47:787 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:18:47:836 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:18:47:850 ==>> 检测【关闭仪表供电】
2025-07-31 22:18:47:854 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:18:47:984 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:18:48:108 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:18:48:110 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:18:48:112 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:18:48:209 ==>> [D][05:17:52][COMM]3650 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:18:48:284 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:18:48:384 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:18:48:387 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:18:48:391 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:18:48:584 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:18:48:663 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:18:48:667 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:18:48:669 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:18:48:874 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:18:48:937 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:18:48:941 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:18:48:943 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:18:48:979 ==>> 5A A5 01 5A A5 


2025-07-31 22:18:49:084 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:18:49:189 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4661 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]

2025-07-31 22:18:49:219 ==>> . goto init


2025-07-31 22:18:49:257 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:18:49:259 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:18:49:261 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:18:49:399 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:18:49:677 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:18:49:685 ==>> 该项需要延时执行
2025-07-31 22:18:49:732 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5017. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5017. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5018. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5018. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5019. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5019. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5020. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5020. period:500. j,i:24 77
[D][05:17:54][COMM]CAN messag

2025-07-31 22:18:49:762 ==>> e fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5020
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5021


2025-07-31 22:18:50:221 ==>> [D][05:17:54][COMM]5672 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:18:50:770 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:18:51:307 ==>>                                         _poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17

2025-07-31 22:18:51:412 ==>> :55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05

2025-07-31 22:18:51:517 ==>> :17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PR

2025-07-31 22:18:51:592 ==>> OT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6684 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 22:18:52:239 ==>> [D][05:17:56][COMM]7694 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:18:53:174 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:18:53:250 ==>> [D][05:17:57][COMM]8706 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:18:53:679 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:18:53:683 ==>> 检测【33V输入电压ADC】
2025-07-31 22:18:53:686 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:18:54:011 ==>> [D][05:17:58][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:17:58][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:58][COMM]adc read left brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:6  volt:7 mv
[D][05:17:58][COMM]adc read battery ts volt:10 mv
[D][05:17:58][COMM]adc read in 24v adc:1277  volt:32299 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:18:54:241 ==>> 【33V输入电压ADC】通过,【32299mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:18:54:246 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:18:54:258 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:18:54:271 ==>> [D][05:17:58][COMM]9717 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:18:54:301 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1995mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:18:54:514 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:54:517 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:18:54:533 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:54:536 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:18:54:540 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 22:18:54:551 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:18:54:554 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:18:54:570 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:18:54:573 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:18:54:586 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 22:18:54:593 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:18:54:595 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:18:54:691 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1663mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 1991mV
Get AD_V7 1096mV
OVER 150


2025-07-31 22:18:54:878 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:54:881 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:18:54:898 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:54:903 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:18:54:906 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 22:18:54:916 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:18:54:919 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:18:54:934 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:18:54:938 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:18:54:957 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:18:54:959 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:18:54:993 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:18:55:098 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1994mV
Get AD_V7 1097mV
OVER 150


2025-07-31 22:18:55:242 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:55:244 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:18:55:261 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:55:263 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:18:55:265 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 22:18:55:279 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:18:55:282 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:18:55:305 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:18:55:330 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:18:55:333 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:18:55:362 ==>> 检测【打开WIFI(1)】
2025-07-31 22:18:55:378 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:18:55:481 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10728 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0

2025-07-31 22:18:55:526 ==>> ,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:18:55:932 ==>>                          OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087843662

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541231

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:18:56:141 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:18:56:144 ==>> 检测【清空消息队列(1)】
2025-07-31 22:18:56:149 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:18:56:296 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:18:56:421 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:18:56:424 ==>> 检测【打开GPS(1)】
2025-07-31 22:18:56:426 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:18:56:598 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 22:18:56:691 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:18:56:696 ==>> 检测【打开GSM联网】
2025-07-31 22:18:56:719 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:18:56:899 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 22:18:56:962 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:18:56:966 ==>> 检测【打开仪表供电1】
2025-07-31 22:18:56:970 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:18:57:219 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 22:18:57:547 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:18:57:553 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:18:57:558 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:18:57:687 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:18:57:845 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:18:57:849 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:18:57:854 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:18:57:987 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33201]


2025-07-31 22:18:58:145 ==>> 【读取主控ADC采集的仪表电压】通过,【33201mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:18:58:148 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:18:58:150 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:18:58:308 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13740 imu init OK


2025-07-31 22:18:58:383 ==>>                                [D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][COMM]S->M yaw:INVALID


2025-07-31 22:18:58:424 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:18:58:427 ==>> 检测【AD_V20电压】
2025-07-31 22:18:58:431 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:18:58:536 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:18:58:627 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1

1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 22:18:58:929 ==>> 本次取值间隔时间:385ms
2025-07-31 22:18:58:948 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:18:58:950 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:18:58:952 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:18:58:990 ==>> 3A A3 02 00 A3 


2025-07-31 22:18:59:095 ==>> OFF_OUT2
OVER 150


2025-07-31 22:18:59:240 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:18:59:243 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:18:59:245 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:18:59:577 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F3837333431313507

[D][05:18:03][FCTY]F:[appRTKpara2Fla

2025-07-31 22:18:59:683 ==>> shDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.106.17.119"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GP

2025-07-31 22:18:59:787 ==>> IO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:19:00:034 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:19:00:060 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:19:00:065 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:19:00:070 ==>> [D][05:18:04][COMM]M->S yaw:INVALID


2025-07-31 22:19:00:104 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:19:00:269 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:19:00:339 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:19:00:342 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:19:00:346 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:19:00:494 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:19:00:620 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:19:00:623 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:19:00:626 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:19:00:704 ==>> [D][05:18:05][COMM]S->M yaw:INVALID


2025-07-31 22:19:00:809 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:19:00:902 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:19:00:906 ==>> 检测【AD_V21电压】
2025-07-31 22:19:00:910 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:19:00:917 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:19:01:004 ==>> 1A A1 20 00 00 
Get AD_V21 1025mV
OVER 150


2025-07-31 22:19:01:109 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,40,24,,,39,34,,,39,33,,,37,1*78

$GBGSV,2,2,08,41,,,37,39,,,34,60,,,40,11,,,39,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1561.550,1561.550,49.909,2097152,2097152,2097152*42

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 22:19:01:260 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:19:01:395 ==>> 本次取值间隔时间:479ms
2025-07-31 22:19:01:449 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:19:01:500 ==>> 1A A1 20 00 00 
Get AD_V21 1650mV
OVER 150


2025-07-31 22:19:01:636 ==>> 本次取值间隔时间:182ms
2025-07-31 22:19:01:661 ==>> 【AD_V21电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:19:01:665 ==>> 检测【关闭仪表供电2】
2025-07-31 22:19:01:683 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:19:01:892 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:19:01:951 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:19:01:956 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:19:01:961 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:19:02:072 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,40,,,41,60,,,40,34,,,40,24,,,39,1*7D

$GBGSV,4,2,13,41,,,39,7,,,39,33,,,38,25,,,37,1*48

$GBGSV,4,3,13,39,,,36,11,,,35,10,,,35,3,,,39,1*44

$GBGSV,4,4,13,1,,,36,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1579.137,1579.137,50.471,2097152,2097152,2097152*48



2025-07-31 22:19:02:177 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:19:02:241 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:19:02:246 ==>> 检测【打开AccKey2供电】
2025-07-31 22:19:02:250 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:19:02:375 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:19:02:520 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:19:02:523 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:19:02:528 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:02:814 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:07][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:18:07][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:07][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:07][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:07][COMM]adc read battery ts volt:8 mv
[D][05:18:07][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:03:060 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33083mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:19:03:064 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:19:03:068 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:19:03:100 ==>> [D][05:18:07][COMM]M->S yaw:INVALID
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,41,60,,,40,34,,,40,41,,,40,1*71

$GBGSV,5,2,20,3,,,40,59,,,40,24,,,39,7,,,39,1*7D

$GBGSV,5,3,20,33,,,39,25,,,39,39,,,38,1,,,38,1*4E

$GBGSV,5,4,20,11,,,36,10,,,36,2,,,36,44,,,35,1*45

$GBGSV,5,5,20,43,,,35,4,,,34,5,,,34,32,,,32,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1556.740,1556.740,49.783,2097152,2097152,2097152*4E



2025-07-31 22:19:03:265 ==>> [D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:19:03:350 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:19:03:354 ==>> 该项需要延时执行
2025-07-31 22:19:04:150 ==>> $GBGGA,141907.938,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,34,,,40,41,,,40,3,,,40,1*43

$GBGSV,7,2,25,59,,,40,25,,,40,60,,,39,33,,,39,1*79

$GBGSV,7,3,25,39,,,39,24,,,38,7,,,38,1,,,38,1*7E

$GBGSV,7,4,25,16,,,37,11,,,36,10,,,36,2,,,36,1*47

$GBGSV,7,5,25,43,,,36,23,,,36,44,,,35,6,,,35,1*43

$GBGSV,7,6,25,9,,,35,5,,,34,4,,,33,32,,,32,1*49

$GBGSV,7,7,25,12,,,31,1*70

$GBRMC,141907.938,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141907.938,0.000,1532.289,1532.289,49.010,2097152,2097152,2097152*55



2025-07-31 22:19:04:725 ==>> $GBGGA,141908.538,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,40,3,,,40,59,,,40,1*48

$GBGSV,7,2,25,25,,,40,60,,,40,34,,,39,33,,,39,1*72

$GBGSV,7,3,25,39,,,39,24,,,38,7,,,38,1,,,38,1*7E

$GBGSV,7,4,25,16,,,37,11,,,36,10,,,36,43,,,36,1*72

$GBGSV,7,5,25,23,,,36,2,,,35,44,,,35,6,,,35,1*75

$GBGSV,7,6,25,9,,,35,5,,,34,12,,,34,4,,,33,1*4D

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,141908.538,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141908.538,0.000,1535.597,1535.597,49.107,2097152,2097152,2097152*51



2025-07-31 22:19:05:265 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:19:05:713 ==>> $GBGGA,141909.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,40,41,,,40,3,,,40,59,,,40,1*4A

$GBGSV,7,2,26,25,,,40,60,,,39,34,,,39,33,,,39,1*7F

$GBGSV,7,3,26,39,,,39,24,,,38,7,,,38,1,,,37,1*72

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,43,,,36,1*71

$GBGSV,7,5,26,23,,,36,9,,,36,2,,,35,44,,,35,1*7A

$GBGSV,7,6,26,6,,,35,12,,,35,5,,,34,4,,,33,1*40

$GBGSV,7,7,26,32,,,32,37,,,37,1*72

$GBRMC,141909.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141909.518,0.000,1533.930,1533.930,49.045,2097152,2097152,2097152*55



2025-07-31 22:19:06:313 ==>> [D][05:18:10][COMM]S->M yaw:INVALID


2025-07-31 22:19:06:359 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:19:06:376 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:19:06:381 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:06:786 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:11][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:11][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:11][COMM]adc read battery ts volt:12 mv
[D][05:18:11][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
$GBGGA,141910.518,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,40,3,,,40,59,,,40,1*4B

$GBGSV,7,2,26,25,,,40,60,,,40,34,,,40,33,,,40,1*71

$GBGSV,7,3,26,39,,,39,7,,,39,24,,,38,1,,,38,1*7C

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,43,,,36,1*71

$GBGSV,7,5,26,23,,,36,9,,,36,2,,,36,44,,,36,1*7A

$GBGSV,7,6,26,6,,,35,12,,,35,5,,,34,4,,,33,1*40

$GBGSV,7,7,26,32,,,32,38,,,31,1*7B

$GBRMC,141910.518,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141

2025-07-31 22:19:06:815 ==>> 910.518,0.000,1537.138,1537.138,49.166,2097152,2097152,2097152*5D

[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:06:896 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:19:06:901 ==>> 检测【打开AccKey1供电】
2025-07-31 22:19:06:906 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:19:07:077 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:19:07:172 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:19:07:175 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:19:07:178 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:19:07:260 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:19:07:305 ==>> 1A A1 00 40 00 
Get AD_V14 2647mV
OVER 150


2025-07-31 22:19:07:335 ==>> [D][05:18:11][COMM]M->S yaw:INVALID


2025-07-31 22:19:07:425 ==>> 原始值:【2647】, 乘以分压基数【2】还原值:【5294】
2025-07-31 22:19:07:440 ==>> [D][05:18:11][COMM]S->M yaw:INVALID


2025-07-31 22:19:07:443 ==>> 【读取AccKey1电压(ADV14)前】通过,【5294mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:19:07:447 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:19:07:449 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:07:759 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:12][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:11 mv
[D][05:18:12][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
$GBGGA,141911.518,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,40,3,,,40,59,,,40,1*4B

$GBGSV,7,2,26,25,,,40,34,,,40,33,,,40,60,,,39,1*7F

$GBGSV,7,3,26,39,,,39,7,,,38,24,,,38,1,,,38,1*7D

$GBGSV,7,4,26,16,,,37,11,,,37,10,,,36,43,,,36,1*70

$GBGSV,7,5,26,23,,,36,9,,,36,2,,,36,44,,,36,1*7A

$GBGSV,7,6,26,12,,,36,6,,,35,5,,,34,4,,,33,1*43

$GBGSV,7,7,26,32,,,33,38,,,32,1*79

$GBRMC,141911.5

2025-07-31 22:19:07:804 ==>> 18,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141911.518,0.000,1540.315,1540.315,49.256,2097152,2097152,2097152*5C

[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:07:974 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5508mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:19:07:978 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:19:07:981 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:19:08:186 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:19:08:258 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:19:08:261 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:19:08:264 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:19:08:395 ==>> 1A A1 00 40 00 
Get AD_V14 2647mV
OVER 150


2025-07-31 22:19:08:516 ==>> 原始值:【2647】, 乘以分压基数【2】还原值:【5294】
2025-07-31 22:19:08:541 ==>> 【读取AccKey1电压(ADV14)后】通过,【5294mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:19:08:545 ==>> 检测【打开WIFI(2)】
2025-07-31 22:19:08:562 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:19:08:761 ==>> $GBGGA,141912.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,25,,,41,41,,,40,3,,,40,1*41

$GBGSV,7,2,26,59,,,40,34,,,40,33,,,40,60,,,39,1*74

$GBGSV,7,3,26,39,,,39,7,,,39,24,,,38,1,,,38,1*7C

$GBGSV,7,4,26,16,,,37,11,,,37,10,,,36,43,,,36,1*70

$GBGSV,7,5,26,23,,,36,9,,,36,2,,,36,44,,,36,1*7A

$GBGSV,7,6,26,12,,,36,6,,,35,5,,,34,4,,,33,1*43

$GBGSV,7,7,26,32,,,33,38,,,32,1*79

$GBRMC,141912.518,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141912.518,0.000,1543.507,1543.507,49.361,2097152,2097152,2097152*5A

[W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:19:08:825 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:19:08:830 ==>> 检测【转刹把供电】
2025-07-31 22:19:08:835 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:19:08:988 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:19:09:096 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:19:09:099 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:19:09:102 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:19:09:200 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:19:09:292 ==>> [D][05:18:13][COMM]read battery soc:255
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2403mV
OVER 150


2025-07-31 22:19:09:352 ==>> 原始值:【2403】, 乘以分压基数【2】还原值:【4806】
2025-07-31 22:19:09:370 ==>> 【读取AD_V15电压(前)】通过,【4806mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:19:09:373 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:19:09:385 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:19:09:476 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:19:09:599 ==>> 1A A1 01 00 00 
Get AD_V16 2435mV
OVER 150


2025-07-31 22:19:09:629 ==>> 原始值:【2435】, 乘以分压基数【2】还原值:【4870】
2025-07-31 22:19:09:654 ==>> 【读取AD_V16电压(前)】通过,【4870mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:19:09:659 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:19:09:662 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:09:704 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F88C21BCF57D,-36
+WIFISCAN:4,1,F42A7D1297A3,-66
+WIFISCAN:4,2,CC057790A740,-71
+WIFISCAN:4,3,CC057790A5C1,-80

[D][05:18:14][CAT1]wifi scan report total[4]
$GBGGA,141913.518,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,25,,,40,41,,,40,3,,,40,1*40

$GBGSV,7,2,26,59,,,40,34,,,40,33,,,40,60,,,39,1*74

$GBGSV,7,3,26,39,,,39,7,,,39,24,,,38,1,,,38,1*7C

$GBGSV,7,4,26,16,,,38,11,,,37,10,,,3

2025-07-31 22:19:09:749 ==>> 6,43,,,36,1*7F

$GBGSV,7,5,26,23,,,36,9,,,36,44,,,36,12,,,36,1*4B

$GBGSV,7,6,26,6,,,36,2,,,35,5,,,34,4,,,33,1*72

$GBGSV,7,7,26,32,,,33,38,,,33,1*78

$GBRMC,141913.518,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141913.518,0.000,1545.095,1545.095,49.406,2097152,2097152,2097152*5D



2025-07-31 22:19:09:975 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:14][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:14][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:14][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:14][COMM]adc read battery ts volt:9 mv
[D][05:18:14][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:14][COMM]M->S yaw:INVALID
[D][05:18:14][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:10:192 ==>> 【转刹把供电电压(主控ADC)】通过,【5407mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:19:10:196 ==>> 检测【转刹把供电电压】
2025-07-31 22:19:10:199 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:10:297 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 22:19:10:507 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3141  volt:5521 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:10 mv
[D][05:18:14][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:19:10:703 ==>> $GBGGA,141914.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,25,,,40,41,,,40,3,,,40,1*40

$GBGSV,7,2,26,59,,,40,33,,,40,34,,,39,60,,,39,1*7A

$GBGSV,7,3,26,39,,,39,7,,,39,24,,,38,1,,,38,1*7C

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,23,,,36,1*77

$GBGSV,7,5,26,9,,,36,44,,,36,12,,,36,43,,,35,1*4E

$GBGSV,7,6,26,6,,,35,2,,,35,5,,,33,4,,,32,1*77

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141914.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141914.518,0.000,1530.759,1530.759,48.962,2097152,2097152,2097152*54



2025-07-31 22:19:10:731 ==>> 【转刹把供电电压】通过,【5414mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:19:10:738 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:19:10:766 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:19:10:868 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:19:11:008 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:19:11:013 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:19:11:018 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:19:11:110 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:19:11:156 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:19:11:201 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:19:11:233 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:19:11:239 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:19:11:263 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:19:11:276 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 22:19:11:336 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:19:11:381 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:19:11:459 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:19:11:464 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:19:11:468 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:19:11:591 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:19:11:696 ==>> $GBGGA,141915.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,25,,,40,41,,,40,59,,,40,1*7F

$GBGSV,7,2,26,3,,,39,33,,,39,34,,,39,60,,,39,1*45

$GBGSV,7,3,26,39,,,39,7,,,39,24,,,38,1,,,37,1*73

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,23,,,36,1*77

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,35,43,,,35,1*4D

$GBGSV,7,6,26,6,,,35,2,,,35,5,,,33,4,,,33,1*76

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141915.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141915.518,0.000,1525.970,1525.970,48.803,2097152,2097152,2097152*53



2025-07-31 22:19:11:729 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:19:11:734 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:19:11:742 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:19:11:801 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:19:12:014 ==>> [D][05:18:16][COMM]S->M yaw:INVALID


2025-07-31 22:19:12:018 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:19:12:023 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:19:12:029 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:19:12:089 ==>> 3A A3 05 01 A3 


2025-07-31 22:19:12:194 ==>> ON_OUT5
OVER 150


2025-07-31 22:19:12:288 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:19:12:295 ==>> 检测【左刹电压测试1】
2025-07-31 22:19:12:299 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:12:608 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:16][COMM]adc read right brake adc:1721  volt:2268 mv
[D][05:18:16][COMM]adc read throttle adc:1713  volt:2258 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2396  volt:3860 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:12:713 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:19:12:821 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 22:19:12:825 ==>> 检测【右刹电压测试1】
2025-07-31 22:19:12:842 ==>> 【右刹电压测试1】通过,【2268】符合目标值【2250】至【2500】要求!
2025-07-31 22:19:12:846 ==>> 检测【转把电压测试1】
2025-07-31 22:19:12:860 ==>> 【转把电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 22:19:12:866 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:19:12:882 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:19:13:002 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:19:13:063 ==>> [D][05:18:17][COMM]M->S yaw:INVALID


2025-07-31 22:19:13:140 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:19:13:146 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:19:13:167 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:19:13:197 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:19:13:272 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:19:13:411 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:19:13:415 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:19:13:438 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:19:13:501 ==>> 3A A3 05 00 A3 


2025-07-31 22:19:13:591 ==>> OFF_OUT5
OVER 150


2025-07-31 22:19:13:684 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:19:13:696 ==>> 检测【左刹电压测试2】
2025-07-31 22:19:13:700 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:19:13:703 ==>> $GBGGA,141917.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,41,59,,,40,1*7F

$GBGSV,7,2,26,3,,,40,33,,,40,34,,,40,39,,,40,1*49

$GBGSV,7,3,26,60,,,39,7,,,39,24,,,38,1,,,38,1*70

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,23,,,36,1*77

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,36,43,,,36,1*4D

$GBGSV,7,6,26,6,,,36,2,,,36,5,,,34,4,,,33,1*71

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141917.518,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141917.518,0.000,1545.108,1545.108,49.419,2097152,2097152,2097152*57



2025-07-31 22:19:14:017 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:18][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:18][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:18][COMM]S->M yaw:INVALID
[D][05:18:18][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:19:14:215 ==>> 【左刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 22:19:14:227 ==>> 检测【右刹电压测试2】
2025-07-31 22:19:14:234 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 22:19:14:244 ==>> 检测【转把电压测试2】
2025-07-31 22:19:14:252 ==>> 【转把电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 22:19:14:259 ==>> 检测【晶振检测】
2025-07-31 22:19:14:275 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:19:14:482 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:18][COMM][lf state:1][hf state:1]


2025-07-31 22:19:14:530 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:19:14:534 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:19:14:539 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:19:14:587 ==>> 1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1662mV
Get AD_V4 1647mV
Get AD_V5 2764mV
Get AD_V6 1990mV
Get AD_V7 1096mV
OVER 150


2025-07-31 22:19:14:692 ==>> $GBGGA,141918.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,40,59,,,40,1*7E

$GBGSV,7,2,26,3,,,40,33,,,40,39,,,40,34,,,39,1*47

$GBGSV,7,3,26,60,,,39,7,,,39,24,,,38,1,,,38,1*70

$GBGSV,7,4,26,16,,,37,11,,,36,10,,,36,23,,,36,1*77

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,36,43,,,36,1*4D

$GBGSV,7,6,26,2,,,36,6,,,35,5,,,34,4,,,33,1*72

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141918.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141918.518,0.000,1540.322,1540.322,49.263,2097152,2097152,2097152*53



2025-07-31 22:19:14:799 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:19:14:829 ==>> 检测【检测BootVer】
2025-07-31 22:19:14:846 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:15:161 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:19][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
[D][05:18:19][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:19][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:19][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:19][FCTY]DeviceID    = 460130071541231
[D][05:18:19][FCTY]HardwareID  = 867222087843662
[D][05:18:19][FCTY]MoBikeID    = 9999999999
[D][05:18:19][FCTY]LockID      = FFFFFFFFFF
[D][05:18:19][FCTY]BLEFWVersion= 105
[D][05:18:19][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:19][FCTY]Bat         = 3944 mv
[D][05:18:19][FCTY]Current     = 0 ma
[D][05:18:19][FCTY]VBUS        = 11800 mv
[D][05:18:19][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:19][FCTY]Ext battery vol = 32, adc = 1279
[D][05:18:19][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:19][FCTY]Bike Type flag is invalied
[D][05:18:19][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:19][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:19][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:19][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:1

2025-07-31 22:19:15:191 ==>> 9][FCTY]Bat1         = 3693 mv
[D][05:18:19][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:19:15:296 ==>>                                      [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:19:15:333 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:19:15:337 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:19:15:341 ==>> 检测【检测固件版本】
2025-07-31 22:19:15:352 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:19:15:359 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:19:15:375 ==>> 检测【检测蓝牙版本】
2025-07-31 22:19:15:378 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:19:15:382 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:19:15:397 ==>> 检测【检测MoBikeId】
2025-07-31 22:19:15:419 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:19:15:424 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:19:15:428 ==>> 检测【检测蓝牙地址】
2025-07-31 22:19:15:432 ==>> 取到目标值:D4A9210EA5F2
2025-07-31 22:19:15:447 ==>> 【检测蓝牙地址】通过,【D4A9210EA5F2】符合目标值【】要求!
2025-07-31 22:19:15:461 ==>> 提取到蓝牙地址:D4A9210EA5F2
2025-07-31 22:19:15:465 ==>> 检测【BOARD_ID】
2025-07-31 22:19:15:469 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:19:15:473 ==>> 检测【检测充电电压】
2025-07-31 22:19:15:478 ==>> [D][05:18:19][COMM]S->M yaw:INVALID


2025-07-31 22:19:15:492 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:19:15:509 ==>> 检测【检测VBUS电压1】
2025-07-31 22:19:15:513 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:19:15:517 ==>> 检测【检测充电电流】
2025-07-31 22:19:15:537 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:19:15:541 ==>> 检测【检测IMEI】
2025-07-31 22:19:15:551 ==>> 取到目标值:867222087843662
2025-07-31 22:19:15:560 ==>> 【检测IMEI】通过,【867222087843662】符合目标值【】要求!
2025-07-31 22:19:15:567 ==>> 提取到IMEI:867222087843662
2025-07-31 22:19:15:572 ==>> 检测【检测IMSI】
2025-07-31 22:19:15:576 ==>> 取到目标值:460130071541231
2025-07-31 22:19:15:597 ==>> 【检测IMSI】通过,【460130071541231】符合目标值【】要求!
2025-07-31 22:19:15:602 ==>> 提取到IMSI:460130071541231
2025-07-31 22:19:15:606 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:19:15:615 ==>> 取到目标值:460130071541231
2025-07-31 22:19:15:630 ==>> 【校验网络运营商(移动)】通过,【460130071541231】符合目标值【】要求!
2025-07-31 22:19:15:635 ==>> 检测【打开CAN通信】
2025-07-31 22:19:15:639 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:19:15:718 ==>> $GBGGA,141919.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,41,59,,,40,1*7F

$GBGSV,7,2,26,3,,,40,33,,,40,39,,,40,34,,,40,1*49

$GBGSV,7,3,26,60,,,40,7,,,39,24,,,38,1,,,38,1*7E

$GBGSV,7,4,26,16,,,37,23,,,37,11,,,36,10,,,36,1*76

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,36,43,,,36,1*4D

$GBGSV,7,6,26,2,,,36,6,,,36,5,,,34,4,,,33,1*71

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141919.518,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141919.518,0.000,1548.298,1548.298,49.521,2097152,2097152,2097152*53

[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:19:15:871 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:19:15:876 ==>> 检测【检测CAN通信】
2025-07-31 22:19:15:879 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:19:16:022 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:19:16:082 ==>> [D][05:18:20][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 31535
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:19:16:142 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:19:16:173 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:19:16:180 ==>> 检测【关闭CAN通信】
2025-07-31 22:19:16:185 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:19:16:205 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:19:16:307 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:19:16:445 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:19:16:459 ==>> 检测【打印IMU STATE】
2025-07-31 22:19:16:465 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:19:16:731 ==>> [D][05:18:20][COMM]M->S yaw:INVALID
[W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:0
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,141920.518,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,41,59,,,41,1*7E

$GBGSV,7,2,26,3,,,40,33,,,40,39,,,40,34,,,40,1*49

$GBGSV,7,3,26,60,,,40,7,,,39,24,,,38,16,,,38,1*48

$GBGSV,7,4,26,1,,,37,23,,,36,11,,,36,10,,,36,1*41

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,36,43,,,36,1*4D

$GBGSV,7,6,26,2,,,36,6,,,35,5,,,34,4,,,33,1*72

$GBGSV,7,7,26,32,,,32,38,,,32,1*78

$GBRMC,141920.518,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141920.518,0.000,1546.708,1546.708,49.475,2097152,2097152,2097152*59



2025-07-31 22:19:16:980 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:19:16:984 ==>> 检测【六轴自检】
2025-07-31 22:19:16:988 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:19:17:192 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:21][CAT1]gsm read msg sub id: 12
[D][05:18:21][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:19:17:297 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:19:17:702 ==>> $GBGGA,141921.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,41,59,,,41,1*7E

$GBGSV,7,2,26,3,,,40,33,,,40,39,,,40,34,,,40,1*49

$GBGSV,7,3,26,60,,,40,7,,,39,24,,,38,16,,,38,1*48

$GBGSV,7,4,26,1,,,38,23,,,37,11,,,37,10,,,36,1*4E

$GBGSV,7,5,26,9,,,36,12,,,36,44,,,36,43,,,36,1*4D

$GBGSV,7,6,26,2,,,36,6,,,36,5,,,34,4,,,33,1*71

$GBGSV,7,7,26,38,,,33,32,,,32,1*79

$GBRMC,141921.518,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141921.518,0.000,1554.674,1554.674,49.723,2097152,2097152,2097152*58



2025-07-31 22:19:18:700 ==>> $GBGGA,141922.518,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,59,,,40,1*7E

$GBGSV,7,2,27,3,,,40,33,,,40,39,,,40,34,,,40,1*48

$GBGSV,7,3,27,60,,,39,7,,,39,24,,,38,1,,,38,1*71

$GBGSV,7,4,27,16,,,37,11,,,37,23,,,36,10,,,36,1*77

$GBGSV,7,5,27,9,,,36,12,,,36,44,,,36,43,,,36,1*4C

$GBGSV,7,6,27,6,,,36,2,,,35,5,,,34,4,,,34,1*74

$GBGSV,7,7,27,38,,,33,32,,,32,14,,,31,1*7F

$GBRMC,141922.518,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141922.518,0.000,1538.559,1538.559,49.215,2097152,2097152,2097152*5B



2025-07-31 22:19:18:897 ==>> [D][05:18:23][CAT1]<<< 
OK

[D][05:18:23][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:19:19:002 ==>> [D][05:18:23][COMM]Main Task receive event:142
[D][05:18:23][COMM]###### 34477 imu self test OK ######
[D]

2025-07-31 22:19:19:032 ==>> [05:18:23][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-10,-2,4072]
[D][05:18:23][COMM]Main Task receive event:142 finished processing


2025-07-31 22:19:19:066 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:19:19:093 ==>> 检测【打印IMU STATE2】
2025-07-31 22:19:19:101 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:19:19:318 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
[D][05:18:23][COMM]read battery soc:255


2025-07-31 22:19:19:482 ==>> [D][05:18:23][COMM]S->M yaw:INVALID


2025-07-31 22:19:19:591 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:19:19:596 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:19:19:600 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:19:19:709 ==>> $GBGGA,141923.518,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,59,,,40,1*7E

$GBGSV,7,2,27,3,,,40,33,,,40,39,,,40,34,,,40,1*48

$GBGSV,7,3,27,60,,,39,7,,,39,24,,,38,1,,,38,1*71

$GBGSV,7,4,27,16,,,38,11,,,37,23,,,36,10,,,36,1*78

$GBGSV,7,5,27,9,,,36,12,,,36,44,,,36,43,,,36,1*4C

$GBGSV,7,6,27,6,,,36,2,,,35,5,,,34,4,,,33,1*73

$GBGSV,7,7,27,38,,,32,32,,,32,14,,,32,1*7D

$GBRMC,141923.518,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141923.518,0.000,1538.561,1538.561,49.217,2097152,2097152,2097152*58

5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:19:19:863 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:19:19:871 ==>> 检测【检测VBUS电压2】
2025-07-31 22:19:19:880 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:19:908 ==>> [D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:19:20:254 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071541231
[D][05:18:24][FCTY]HardwareID  = 867222087843662
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:24][FCTY]Bat         = 3944 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:24][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_

2025-07-31 22:19:20:299 ==>> GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3693 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 22:19:20:392 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:20:818 ==>> [D][05:18:24][COMM]M->S yaw:INVALID
[W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541231
[D][05:18:25][FCTY]HardwareID  = 867222087843662
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 50 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 173
[D][05:18:25][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLA

2025-07-31 22:19:20:923 ==>> TFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3693 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,141924.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,25,,,41,41,,,40,59,,,40,1*7F

$GBGSV,7,2,27,3,,,40,33,,,40,60,,,40,39,,,39,1*47

$GBGSV,7,3,27,34,,,39,7,,,39,24,,,38,1,,,38,1*70

$GBGSV,7,4,27,16,,,37,11,,,37,23,,,37,10,,,36,1*76

$GBGSV,7,5,27,9,,,36,12,,,36,44,,,36,43,,,36,1*4C

$GBGSV,7,6,27,6,,,36,2,,,35,5,,,34,4,,,34,1*74

$GBGSV,7,7,27,38,,,32,32,,,32,14,,,32,1*7D

$GBRMC,141924.518,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141924.518,0.000,1537.019,1537.019,49.161,2097152,2097152,2097152*5D

[D][05:18:25][COMM]S->M yaw:INVALID


2025-07-31 22:19:20:956 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:21:273 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][COMM]msg 0601 loss. last_tick:31525. cur_tick:36539. period:500
[D][05:18:25][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 36540
[D][05:18:25][FCTY]DeviceID    = 460130071541231
[D][05:18:25][FCTY]HardwareID  = 867222087843662
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 50 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 3, adc = 151
[D][05:18:25][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 25 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT

2025-07-31 22:19:21:334 ==>> 1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3693 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:19:21:456 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:22:175 ==>> $GBGGA,141925.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,25,,,40,41,,,40,59,,,40,1*7E

$GBGSV,7,2,27,3,,,40,33,,,40,60,,,39,39,,,39,1*49

$GBGSV,7,3,27,34,,,39,7,,,39,24,,,38,1,,,38,1*70

$GBGSV,7,4,27,16,,,37,11,,,37,23,,,37,10,,,36,1*76

$GBGSV,7,5,27,9,,,36,12,,,36,44,,,36,43,,,36,1*4C

$GBGSV,7,6,27,6,,,35,2,,,35,5,,,34,4,,,33,1*70

$GBGSV,7,7,27,38,,,32,32,,,32,14,,,32,1*7D

$GBRMC,141925.518,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,141925.518,0.000,1530.876,1530.876,48.965,2097152,2097152,2097152*51

[D][05:18:26][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:26][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541231
[D][05:18:26][FCTY]HardwareID  = 8672220878

2025-07-31 22:19:22:279 ==>> 43662
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:26][FCTY]Bat         = 3664 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 7100 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 124
[D][05:18:26][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3693 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]Main Task receive event:65
[D][05:18:26][COMM]main task tmp_sleep_event = 80
[D][05:18:26][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:26][COMM]Main Task receive event:65 fini

2025-07-31 22:19:22:384 ==>> shed processing
[D][05:18:26][COMM]Main Task receive event:60
[D][05:18:26][COMM]smart_helmet_vol=255,255
[D][05:18:26][COMM]BAT CAN get state1 Fail 204
[D][05:18:26][COMM]BAT CAN get soc Fail, 204
[W][05:18:26][GNSS]stop locating
[D][05:18:26][GNSS]stop event:8
[D][05:18:26][GNSS]all continue location stop
[W][05:18:26][GNSS]sing locating running
[D][05:18:26][COMM]report elecbike
[W][05:18:26][PROT]remove success[1629955106],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]index:0
[D][05:18:26][PROT]is_send:1
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x3
[D][05:18:26][PROT]msg_type:0x5d03
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]Sending traceid[9999999999900005]
[D][05:18:26][BLE ]BLE_WRN [ble_service_get_curr

2025-07-31 22:19:22:489 ==>> ent_send_enabled:28] ble is not connect

[D][05:18:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:26][PROT]index:0 1629955106
[D][05:18:26][PROT]is_send:0
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x2
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]sending traceid [9999999999900005]
[D][05:18:26][PROT]Send_TO_M2M [1629955106]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:26][SAL ]sock send credit cnt[6]
[D][05:18:26][SAL ]sock send ind credit cnt[6]
[D][05:18:26][M2M ]m2m send data len[198]
[D][05:18:26][SAL ]Cellular task submsg id[10]
[D][05:18:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:26][COMM]Main Task receive event:60 finished processing
[D][05:18:26]

2025-07-31 22:19:22:529 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:19:22:579 ==>> [M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:26][CAT1]gsm read msg sub id: 15
[D][05:18:26][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:26][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5375792A6B6B528CAD736193A6EFCDDE31351A5C330795AB7454065D79D388231227FCB02F62836481A24BC74C0886F042CC85E4915F18A70BBE4B3B1C0765DC206638F41B70DBB7F6886BB7D074B975D356A
[D][05:18:26][CAT1]<<< 
SEND OK

[D][05:18:26][CAT1]exec over: func id: 15, ret: 11
[D][05:18:26][CAT1]sub id: 15, ret: 11

[D][05:18:26][SAL ]Cellular task submsg id[68]
[D][05:18:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:26][M2M ]g_m2m_is_idle become true
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:26][PROT]M2M Send ok [1629955106]


2025-07-31 22:19:22:880 ==>> [D][05:18:27][COMM]M->S yaw:INVALID
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071541231
[D][05:18:27][FCTY]HardwareID  = 867222087843662
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D4A9210EA5F2
[D][05:18:27][FCTY]Bat         = 3664 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 98
[D][05:18:27][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_K

2025-07-31 22:19:22:925 ==>> ERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3693 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:19:23:103 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:19:23:124 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:19:23:131 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:19:23:199 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:19:23:259 ==>> [D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28


2025-07-31 22:19:23:334 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 22:19:23:420 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:19:23:427 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:19:23:442 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:19:23:500 ==>> 5A A5 04 5A A5 


2025-07-31 22:19:23:590 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:19:23:733 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:19:23:744 ==>> 检测【打开WIFI(3)】
2025-07-31 22:19:23:752 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:19:23:895 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 22:19:24:036 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:19:24:061 ==>> 检测【扩展芯片hw】
2025-07-31 22:19:24:068 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:19:24:760 ==>> [D][05:18:29][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:24:865 ==>>      5:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:19:25:061 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:19:25:423 ==>>                           ipheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COM

2025-07-31 22:19:25:528 ==>> M]read file, len:10800, num:3
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:29][COMM]report elecbike
[D][05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[

2025-07-31 22:19:25:633 ==>> D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][PROT]index:1
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:

2025-07-31 22:19:25:738 ==>> 29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]read battery soc:255
                                                                        

2025-07-31 22:19:25:768 ==>>                           

2025-07-31 22:19:25:965 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:19:26:040 ==>>                                                                                                                                                                                                                                                                

2025-07-31 22:19:26:104 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:19:26:109 ==>> 检测【扩展芯片boot】
2025-07-31 22:19:26:131 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:19:26:136 ==>> 检测【扩展芯片sw】
2025-07-31 22:19:26:150 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:19:26:155 ==>> 检测【检测音频FLASH】
2025-07-31 22:19:26:161 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:19:26:364 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:19:26:779 ==>> [D][05:18:31][COMM]42237 imu init OK
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:26:884 ==>> [D][05:18:31][CAT1]SEND RAW data timeout
[D][05:18:31][CAT1]exec over: func id: 12, ret: -52


2025-07-31 22:19:27:154 ==>> [D][05:18:31][PROT]CLEAN,SEND:0
[D][05:18:31][PROT]index:1 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900006]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][COMM]f:[drv_audio_ack_receive].wait ack timeout!![

2025-07-31 22:19:27:199 ==>> 42505]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 22:19:27:364 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 22:19:27:796 ==>> [D][05:18:32][COMM]43248 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:28:088 ==>> [D][05:18:32][COMM]f:[drv_audio_ack_receive].wait ack timeout!![43534]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:32][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:19:28:803 ==>> [D][05:18:33][COMM]44260 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:29:357 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:19:29:827 ==>> [D][05:18:34][COMM]45272 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:30:182 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:30:242 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 22:19:30:816 ==>> [D][05:18:35][COMM]46284 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:31:229 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:19:31:235 ==>> 检测【打开喇叭声音】
2025-07-31 22:19:31:261 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:19:31:396 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]read battery soc:255


2025-07-31 22:19:31:507 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:19:31:512 ==>> 检测【打开大灯控制】
2025-07-31 22:19:31:522 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:19:31:658 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:19:31:785 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:19:31:810 ==>> 检测【关闭仪表供电3】
2025-07-31 22:19:31:821 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:19:31:846 ==>> [D][05:18:36][COMM]47295 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:31:945 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 22:19:32:070 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:19:32:079 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:19:32:102 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:19:32:285 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:19:32:370 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:19:32:376 ==>> 检测【读大灯电压】
2025-07-31 22:19:32:407 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:19:32:450 ==>> [E][05:18:36][GNSS]GPS module no nmea data!
[D][05:18:36][GNSS]GPS reload stop. ret=0
[D][05:18:36][GNSS]GPS reload start. ret=0


2025-07-31 22:19:32:556 ==>> [W][05:18:37][COMM]>>>>>Input comman

2025-07-31 22:19:32:586 ==>> d = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[32992]


2025-07-31 22:19:32:650 ==>> 【读大灯电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:19:32:655 ==>> 检测【关闭大灯控制2】
2025-07-31 22:19:32:663 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:19:32:861 ==>> [D][05:18:37][COMM]48305 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:19:32:928 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:19:32:934 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:19:32:953 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:19:33:090 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[69]


2025-07-31 22:19:33:208 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:19:33:219 ==>> 检测【打开WIFI(4)】
2025-07-31 22:19:33:231 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:19:33:377 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][COMM]read battery soc:255


2025-07-31 22:19:33:532 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:19:33:538 ==>> 检测【EC800M模组版本】
2025-07-31 22:19:33:559 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:33:870 ==>> [D][05:18:38][COMM]49317 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:34:581 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:34:857 ==>> [D][05:18:39][COMM]50328 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:19:35:424 ==>> [D][05:18:39][COMM]read battery soc:255
[W][05:18:39][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:35:622 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:35:855 ==>> [D][05:18:40][COMM]imu error,enter wait


2025-07-31 22:19:36:665 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:37:124 ==>> [D][05:18:41][CAT1]exec over: func id: 15, ret: -93
[D][05:18:41][CAT1]sub id: 15, ret: -93

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:41][SAL ]socket send fail. id[4]
[D][05:18:41][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:41][CAT1]gsm read msg sub id: 24
[D][05:18:41][M2M ]m2m select fd[4]
[D][05:18:41][M2M ]socket[4] Link is disconnected
[D][05:18:41][M2M ]tcpclient close[4]
[D][05:18:41][SAL ]socket[4] has closed
[D][05:18:41][PROT]protocol read data ok
[E][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:41][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:41][PROT]M2M Send Fail [1629955121]
[D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 22:19:37:534 ==>> [D][05:18:41][COMM]f:[drv_audio_ack_receive].wait ack timeout!![52794]
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1032].open hexlog save
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:41][COMM]file:A20 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:41][COMM]read file, len:15228, num:4
[D][05:18:41][COMM]--->crc16:0x419c
[D][05:18:41][COMM]read file success
[W][05:18:41][COMM][Audio].l:[936].close hexlog save
[D][05:18:41][COMM]accel parse set 1
[D][05:18:41][COMM][Audio]mon:9,05:18:41
[D][05:18:41][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:41][COMM]read battery soc:255
[W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:37:700 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:38:456 ==>> [D][05:18:42][COMM]f:[drv_audio_ack_receive].wait ack timeout!![53892]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 22:19:38:747 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:39:023 ==>> [D][05:18:43][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 22:19:39:553 ==>> [D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54920]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:39:778 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:40:176 ==>> [D][05:18:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:40:513 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55950]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:19:40:820 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:41:068 ==>> [D][05:18:45][CAT1]exec over: func id: 24, ret: -181
[D][05:18:45][CAT1]sub id: 24, ret: -181

[D][05:18:45][CAT1]gsm read msg sub id: 23
[D][05:18:45][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 22:19:41:416 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:19:41:506 ==>> [D][05:18:45][GNSS]recv submsg id[1]
[D][05:18:45][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:45][GNSS]stop gps fail


2025-07-31 22:19:41:581 ==>>                                                                   [D][05:18:46][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 22:19:41:856 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:42:120 ==>> [D][05:18:46][CAT1]exec over: func id: 23, ret: -151
[D][05:18:46][CAT1]sub id: 23, ret: -151

[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 22:19:42:536 ==>> [D][05:18:46][GNSS]recv submsg id[1]
[D][05:18:46][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:46][GNSS]start gps fail
[E][05:18:46][GNSS]GPS module no nmea data!
[D][05:18:46][GNSS]GPS reload stop. ret=0
[D][05:18:46][GNSS]GPS reload start. ret=0


2025-07-31 22:19:42:701 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:42:900 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:43:423 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:19:43:604 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:43:941 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:44:972 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:45:141 ==>> [D][05:18:49][CAT1]SEND RAW data timeout
[D][05:18:49][CAT1]exec over: func id: 12, ret: -52
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:49][CAT1]gsm read msg sub id: 12
[D][05:18:49][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:19:45:201 ==>>                                                                    

2025-07-31 22:19:45:411 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:19:46:008 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:47:048 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:47:156 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:47:443 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 22:19:47:703 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:48:087 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:48:164 ==>> [D][05:18:52][CAT1]SEND RAW data timeout
[D][05:18:52][CAT1]exec over: func id: 12, ret: -52
[W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 10
[D][05:18:52][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:19:49:133 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:49:441 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 22:19:50:178 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:50:207 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:51:215 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:51:442 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:19:52:226 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:52:257 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:52:594 ==>> [E][05:18:57][GNSS]GPS module no nmea data!
[D][05:18:57][GNSS]GPS reload stop. ret=0
[D][05:18:57][GNSS]GPS reload start. ret=0


2025-07-31 22:19:52:699 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:52:850 ==>> [D][05:18:57][COMM]f:[drv_audio_ack_receive].wait ack timeout!![68293]
[D][05:18:57][COMM]accel parse set 0
[D][05:18:57][COMM][Audio].l:[1032].open hexlog save
[D][05:18:57][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 22:19:53:297 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:53:435 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 22:19:54:275 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:54:320 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:55:199 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:55:349 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:55:455 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 22:19:56:145 ==>> [D][05:19:00][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:19:56:326 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:56:386 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:57:428 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:57:459 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 22:19:57:701 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:19:58:382 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:19:58:472 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:19:59:456 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 22:19:59:516 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:20:00:208 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:20:00:421 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:20:00:556 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:20:01:454 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 22:20:01:591 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:20:02:468 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:20:02:634 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:20:02:725 ==>> [D][05:19:07][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[E][05:19:07][GNSS]GPS module no nmea data!
[D][05:19:07][GNSS]GPS reload stop. ret=0
[D][05:19:07][GNSS]GPS reload start. ret=0
[D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:20:03:478 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 22:20:03:660 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:20:04:152 ==>> [D][05:19:08][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:20:04:674 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:08][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:20:04:704 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 22:20:04:715 ==>> #################### 【测试结束】 ####################
2025-07-31 22:20:04:810 ==>> 关闭5V供电
2025-07-31 22:20:04:817 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:20:04:901 ==>> 5A A5 04 5A A5 


2025-07-31 22:20:05:005 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:20:05:479 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 22:20:05:825 ==>> 关闭5V供电成功
2025-07-31 22:20:05:835 ==>> 关闭33V供电
2025-07-31 22:20:05:859 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:20:05:902 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:20:06:083 ==>> [D][05:19:10][FCTY]get_ext_48v_vol retry i = 0,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 1,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 2,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 3,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 4,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 5,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 6,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 7,volt = 18
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 8,volt = 18
[D][05:19:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 22:20:06:838 ==>> 关闭33V供电成功
2025-07-31 22:20:06:847 ==>> 关闭3.7V供电
2025-07-31 22:20:06:874 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:20:06:901 ==>> 6A A6 02 A6 6A 


2025-07-31 22:20:06:991 ==>> Battery OFF
OVER 150


2025-07-31 22:20:07:312 ==>>  

