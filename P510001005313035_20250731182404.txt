2025-07-31 18:24:04:558 ==>> MES查站成功:
查站序号:P510001005313035验证通过
2025-07-31 18:24:04:561 ==>> 扫码结果:P510001005313035
2025-07-31 18:24:04:563 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:24:04:564 ==>> 测试参数版本:2024.10.11
2025-07-31 18:24:04:566 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:24:04:567 ==>> 检测【打开透传】
2025-07-31 18:24:04:569 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:24:04:684 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:24:05:078 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:24:05:081 ==>> 检测【检测接地电压】
2025-07-31 18:24:05:084 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:24:05:173 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 18:24:05:358 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:24:05:360 ==>> 检测【打开小电池】
2025-07-31 18:24:05:362 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:24:05:475 ==>> 6A A6 01 A6 6A 


2025-07-31 18:24:05:581 ==>> Battery ON
OVER 150


2025-07-31 18:24:05:632 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:24:05:634 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:24:05:636 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:24:05:777 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:24:05:902 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:24:05:905 ==>> 检测【等待设备启动】
2025-07-31 18:24:05:908 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:24:06:314 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:24:06:496 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:24:06:944 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:24:07:171 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:24:07:215 ==>>                                                    

2025-07-31 18:24:07:610 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:24:07:977 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:24:08:098 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:24:08:282 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:24:08:284 ==>> 检测【产品通信】
2025-07-31 18:24:08:285 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:24:08:461 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:24:08:619 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:24:08:621 ==>> 检测【初始化完成检测】
2025-07-31 18:24:08:624 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:24:08:809 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:24:08:922 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:24:08:924 ==>> 检测【关闭大灯控制1】
2025-07-31 18:24:08:926 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:24:09:036 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:24:09:141 ==>> [D][05:17:51][COMM]2640 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:09:246 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 fl

2025-07-31 18:24:09:291 ==>> ash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:24:09:740 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:24:09:743 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:24:09:744 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:24:09:979 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:24:10:037 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:24:10:040 ==>> 检测【关闭仪表供电】
2025-07-31 18:24:10:042 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:24:10:144 ==>> [D][05:17:52][COMM]3650 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:10:249 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:24:10:323 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:24:10:325 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:24:10:327 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:24:10:444 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:24:10:610 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:24:10:613 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:24:10:615 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:24:10:730 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:24:10:888 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:24:10:892 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:24:10:894 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:11:032 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:24:11:171 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:24:11:173 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:24:11:176 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:24:11:182 ==>> [D][05:17:53][COMM]4662 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 18:24:11:272 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:24:11:362 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28


2025-07-31 18:24:11:437 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 18:24:11:441 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:24:11:445 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:24:11:449 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:24:11:662 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150
                               s. last_tick:0. cur_tick:5010. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5011. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5011. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5012. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5012. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5013. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5013. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5013. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5014. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5014. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5014. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5015. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025

2025-07-31 18:24:11:707 ==>> C loss. last_tick:0. cur_tick:5015. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5015
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5016


2025-07-31 18:24:11:749 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:24:11:751 ==>> 该项需要延时执行
2025-07-31 18:24:12:179 ==>> [D][05:17:54][COMM]5676 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:12:963 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:24:13:461 ==>>                                                                                                                 ------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio 

2025-07-31 18:24:13:566 ==>> file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircu

2025-07-31 18:24:13:671 ==>> itVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][0

2025-07-31 18:24:13:761 ==>> 5:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6688 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                         

2025-07-31 18:24:14:228 ==>> [D][05:17:56][COMM]7698 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 18:24:15:201 ==>> [D][05:17:57][COMM]8710 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:15:442 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:24:15:764 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:24:15:768 ==>> 检测【33V输入电压ADC】
2025-07-31 18:24:15:773 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:16:081 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:17:58][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:58][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:58][COMM]adc read right brake adc:7  volt:9 mv
[D][05:17:58][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:58][COMM]adc read battery ts volt:17 mv
[D][05:17:58][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:58][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:24:16:216 ==>> [D][05:17:58][COMM]9722 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:16:294 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:24:16:297 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:24:16:300 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:24:16:381 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1663mV
Get AD_V4 0mV
Get AD_V5 2757mV
Get AD_V6 1989mV
Get AD_V7 1091mV
OVER 150


2025-07-31 18:24:16:561 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 18:24:16:565 ==>> 【TP7_VCC3V3(ADV2)】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:16:568 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:24:16:583 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:16:585 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:24:16:586 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 18:24:16:601 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:24:16:604 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:24:16:620 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:24:16:624 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:24:16:643 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:24:16:667 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:24:16:786 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1663mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 18:24:16:929 ==>> 【TP7_VCC3V3(ADV2)】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:16:931 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:24:16:947 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:16:950 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:24:16:951 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 18:24:16:964 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:24:16:969 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:24:16:985 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:24:16:987 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:24:17:007 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:24:17:009 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:24:17:090 ==>> 1A A1 00 00 FC 
Get AD_V2 1636mV
Get AD_V3 1664mV
Get AD_V4 3mV
Get AD_V5 2759mV
Get AD_V6 1989mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:24:17:292 ==>> 【TP7_VCC3V3(ADV2)】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:17:294 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:24:17:312 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:17:315 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:24:17:317 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 18:24:17:330 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:24:17:332 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:24:17:348 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:24:17:350 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:24:17:376 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:24:17:379 ==>> 检测【打开WIFI(1)】
2025-07-31 18:24:17:383 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:24:17:480 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10733 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m s

2025-07-31 18:24:17:509 ==>> witch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:24:17:979 ==>>                                                                                                                                                                                                                                   [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087820504

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071544987

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][

2025-07-31 18:24:18:008 ==>> 05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:24:18:162 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:24:18:164 ==>> 检测【清空消息队列(1)】
2025-07-31 18:24:18:166 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:24:18:219 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:24:18:369 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:24:18:434 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:24:18:438 ==>> 检测【打开GPS(1)】
2025-07-31 18:24:18:439 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:24:18:474 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[

2025-07-31 18:24:18:504 ==>> 11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:24:18:669 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 18:24:18:758 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:24:18:761 ==>> 检测【打开GSM联网】
2025-07-31 18:24:18:763 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:24:18:956 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 18:24:19:369 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:24:19:372 ==>> 检测【打开仪表供电1】
2025-07-31 18:24:19:380 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:24:19:470 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20053000], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>

2025-07-31 18:24:19:575 ==>> > AT+GPSCFG?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                         

2025-07-31 18:24:19:665 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:24:19:908 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:24:19:911 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:24:19:915 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:24:20:075 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:24:20:167 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:24:20:209 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:24:20:212 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:24:20:216 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:24:20:219 ==>> [D][05:18:02][COMM]13746 imu init OK


2025-07-31 18:24:20:360 ==>> [D][05:18:02][COMM]S->M yaw:INVALID
[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33386]


2025-07-31 18:24:20:483 ==>> 【读取主控ADC采集的仪表电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:24:20:486 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:24:20:489 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:24:20:666 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:24:20:760 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:24:20:763 ==>> 检测【AD_V20电压】
2025-07-31 18:24:20:765 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:24:20:816 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:24:20:861 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:24:20:982 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:24:21:165 ==>> 本次取值间隔时间:290ms
2025-07-31 18:24:21:180 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:24:21:222 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,43,38,,,41,26,,,40,39,,,40,1*75

$GBGSV,3,2,11,33,,,38,8,,,40,21,,,39,13,,,37,1*4F

$GBGSV,3,3,11,14,,,37,59,,,36,9,,,36,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1674.845,1674.845,53.507,2097152,2097152,2097152*4B

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,

2025-07-31 18:24:21:267 ==>> 1,"10.173.155.125"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:24:21:282 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:24:21:357 ==>> 本次取值间隔时间:72ms
2025-07-31 18:24:21:372 ==>>                                                                                             [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL 

2025-07-31 18:24:21:447 ==>> ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:24:21:477 ==>>                                          

2025-07-31 18:24:21:522 ==>> 本次取值间隔时间:154ms
2025-07-31 18:24:21:541 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:24:21:644 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:24:21:780 ==>> 本次取值间隔时间:125ms
2025-07-31 18:24:21:933 ==>> 本次取值间隔时间:148ms
2025-07-31 18:24:21:993 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,24,,,43,38,,,41,13,,,41,8,,,40,1*43

$GBGSV,4,2,15,26,,,40,39,,,40,59,,,40,60,,,40,1*70

$GBGSV,4,3,15,3,,,40,1,,,39,21,,,38,33,,,38,1*7A

$GBGSV,4,4,15,14,,,37,9,,,34,2,,,36,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1631.634,1631.634,52.149,2097152,2097152,2097152*44



2025-07-31 18:24:22:053 ==>> 本次取值间隔时间:117ms
2025-07-31 18:24:22:303 ==>> 本次取值间隔时间:236ms
2025-07-31 18:24:22:307 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:24:22:411 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:24:22:441 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:24:22:471 ==>> 1A A1 10 00 00 
Get AD_V20 1654mV
OVER 150


2025-07-31 18:24:22:669 ==>> 本次取值间隔时间:249ms
2025-07-31 18:24:22:700 ==>> 【AD_V20电压】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:22:705 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:24:22:709 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:24:22:779 ==>> 3A A3 02 00 A3 


2025-07-31 18:24:22:869 ==>> OFF_OUT2
OVER 150


2025-07-31 18:24:22:974 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,43,42,,,42,38,,,41,13,,,41,1*73

$GBGSV,5,2,18,26,,,41,60,,,41,3,,,41,8,,,40,1*70

$GBGSV,5,3,18,39,,,40,59,,,40,1,,,39,21,,,39,1*4D

$GBGSV,5,4,18,33,,,38,14,,,38,2,,,36,9,,,36,1*70

$GBGSV,5,5,18,4,,,35,5,,,33,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1621.461,1621.461,51.850,2097152,2097152,2097152*46



2025-07-31 18:24:22:983 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:24:23:005 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:24:23:008 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:24:23:187 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:24:23:275 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:24:23:278 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:24:23:281 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:24:23:370 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:24:23:475 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 18:24:23:560 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:24:23:564 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:24:23:567 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:24:23:775 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:24:23:862 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:24:23:865 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:24:23:868 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:24:24:000 ==>> $GBGGA,102427.787,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,42,,,42,38,,,41,13,,,41,1*78

$GBGSV,5,2,20,26,,,41,60,,,41,3,,,41,8,,,40,1*7B

$GBGSV,5,3,20,39,,,40,59,,,40,21,,,40,1,,,38,1*49

$GBGSV,5,4,20,33,,,38,14,,,38,16,,,38,9,,,37,1*41

$GBGSV,5,5,20,2,,,36,6,,,35,4,,,34,5,,,33,1*75

$GBRMC,102427.787,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102427.787,0.000,1610.641,1610.641,51.509,2097152,2097152,2097152*53



2025-07-31 18:24:24:090 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:24:24:160 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:24:24:163 ==>> 检测【AD_V21电压】
2025-07-31 18:24:24:168 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:24:24:271 ==>> 1A A1 20 00 00 
Get AD_V21 1651mV
OVER 150


2025-07-31 18:24:24:362 ==>> 本次取值间隔时间:191ms
2025-07-31 18:24:24:430 ==>> 【AD_V21电压】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:24:433 ==>> 检测【关闭仪表供电2】
2025-07-31 18:24:24:436 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:24:24:664 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:24:24:736 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:24:24:741 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:24:24:744 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:24:24:770 ==>>                                                                                                     ,,,42,13,,,42,38,,,41,1*7B

$GBGSV,5,2,20,26,,,41,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,5,3,20,8,,,40,39,,,40,21,,,40,1,,,39,1*7C

$GBGSV,5,4,20,16,,,39,33,,,38,14,,,38,9,,,37,1*40

$GBGSV,5,5,20,2,,,36,6,,,36,4,,,33,5,,,33,1*71

$GBRMC,102428.587,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102428.587,0.000,1618.936,1618.93

2025-07-31 18:24:24:800 ==>> 6,51.778,2097152,2097152,2097152*5A



2025-07-31 18:24:24:890 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:24:25:044 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:24:25:049 ==>> 检测【打开AccKey2供电】
2025-07-31 18:24:25:052 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:24:25:254 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:24:25:357 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:24:25:361 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:24:25:364 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:25:480 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:24:25:690 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:08][COMM]adc read out 24v adc:1313  volt:33209 mv
[D][05:18:08][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:08][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:08][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:08][COMM]adc read battery ts volt:15 mv
[D][05:18:08][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:08][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:24:25:780 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 18:24:25:923 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33209mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:24:25:926 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:24:25:928 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:24:26:036 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:24:26:238 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:24:26:241 ==>> 该项需要延时执行
2025-07-31 18:24:26:752 ==>> $GBGGA,102430.547,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,13,,,42,38,,,41,1*79

$GBGSV,6,2,21,26,,,41,60,,,41,3,,,41,59,,,40,1*4D

$GBGSV,6,3,21,8,,,40,39,,,40,21,,,40,16,,,39,1*48

$GBGSV,6,4,21,1,,,39,33,,,38,14,,,38,9,,,37,1*74

$GBGSV,6,5,21,6,,,37,2,,,36,4,,,33,5,,,33,1*72

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102430.547,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102430.547,0.000,1605.032,1605.032,51.348,2097152,2097152,2097152*58



2025-07-31 18:24:27:497 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 18:24:27:755 ==>> $GBGGA,102431.527,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,13,,,42,38,,,41,1*79

$GBGSV,6,2,21,26,,,41,60,,,41,3,,,41,59,,,40,1*4D

$GBGSV,6,3,21,8,,,40,39,,,40,21,,,40,16,,,39,1*48

$GBGSV,6,4,21,1,,,38,33,,,38,14,,,38,9,,,38,1*7A

$GBGSV,6,5,21,6,,,37,2,,,36,4,,,33,5,,,33,1*72

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102431.527,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102431.527,0.000,801.017,801.017,732.546,2097152,2097152,2097152*65



2025-07-31 18:24:28:740 ==>> $GBGGA,102432.507,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,60,,,42,42,,,42,1*77

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,9,,,38,1,,,38,14,,,38,33,,,38,1*7A

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102432.507,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102432.507,0.000,803.966,803.966,735.242,2097152,2097152,2097152*60



2025-07-31 18:24:29:244 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:24:29:249 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:24:29:252 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:29:594 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:11][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:11][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:11][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:11][COMM]adc read battery ts volt:11 mv
[D][05:18:11][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:11][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:12][COMM]read battery soc:255


2025-07-31 18:24:29:699 ==>> $GBGGA,102433.507,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,33,,,39,9,,,38,6,,,38,1*7F

$GBGSV,6,5,21,14,,,38,2,,,36,5,,,34,4,,,33,1*49

$G

2025-07-31 18:24:29:745 ==>> BGSV,6,6,21,7,,,32,1*43

$GBRMC,102433.507,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102433.507,0.000,804.952,804.952,736.144,2097152,2097152,2097152*67



2025-07-31 18:24:29:791 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:24:29:796 ==>> 检测【打开AccKey1供电】
2025-07-31 18:24:29:800 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:24:29:970 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:24:30:064 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:24:30:069 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:24:30:093 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:24:30:184 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 18:24:30:320 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 18:24:30:339 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:24:30:342 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:24:30:346 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:30:770 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:13][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:13][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:13][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:13][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:13][COMM]adc read battery ts volt:15 mv
[D][05:18:13][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:13][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,102434.507,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,6,,,38,14,,,38,33,,,38,1*42

$GBGSV,6,5,21,2

2025-07-31 18:24:30:815 ==>> ,,,37,9,,,37,5,,,34,4,,,33,1*7B

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102434.507,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102434.507,0.000,803.966,803.966,735.242,2097152,2097152,2097152*66



2025-07-31 18:24:30:878 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5510mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:24:30:882 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:24:30:884 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:24:31:060 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:24:31:154 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:24:31:158 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:24:31:162 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:24:31:273 ==>> 1A A1 00 40 00 
Get AD_V14 2653mV
OVER 150


2025-07-31 18:24:31:408 ==>> 原始值:【2653】, 乘以分压基数【2】还原值:【5306】
2025-07-31 18:24:31:427 ==>> 【读取AccKey1电压(ADV14)后】通过,【5306mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:24:31:430 ==>> 检测【打开WIFI(2)】
2025-07-31 18:24:31:432 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:24:31:499 ==>> [D][05:18:14][COMM]read battery soc:255


2025-07-31 18:24:31:773 ==>> $GBGGA,102435.507,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,26,,,41,8,,,40,3,,,40,1*75

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,6,,,38,14,,,38,33,,,38,1*42

$GBGSV,6,5,21,9,,,37,2,,,36,5,,,34,4,,,33,1*7A

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102435.507,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102435.507,0.000,801.996,801.996,733.441,2097152,2097152,2097152*64

[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:24:31:982 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:24:31:985 ==>> 检测【转刹把供电】
2025-07-31 18:24:31:988 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:24:32:158 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:24:32:288 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:24:32:292 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:24:32:296 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:24:32:399 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:24:32:506 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
+WIFISCAN:4,0,44A1917CA62F,-69
+WIFISCAN:4,1,CC057790A740,-73
+WIFISCAN:4,2,44A1917CA62B,-76
+WIFISCAN:4,3,646E97BD0450,-86

[D][05:18:14][CAT1]wifi scan report total[4]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2395mV
OVER 150


2025-07-31 18:24:32:551 ==>> 原始值:【2395】, 乘以分压基数【2】还原值:【4790】
2025-07-31 18:24:32:597 ==>> 【读取AD_V15电压(前)】通过,【4790mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:24:32:600 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:24:32:604 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:24:32:705 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:24:32:795 ==>> $GBGGA,102436.507,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,1,,,38,14,,,38,33,,,38,9,,,37,1*75

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,33,1*75

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102436.507,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102436.507,0.000,801.014,801.014,732.543,2097152,2097152,2097152*65

[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2425mV
OVER 150


2025-07-31 18:24:32:870 ==>> 原始值:【2425】, 乘以分压基数【2】还原值:【4850】
2025-07-31 18:24:32:905 ==>> 【读取AD_V16电压(前)】通过,【4850mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:24:32:909 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:24:32:912 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:33:191 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:15][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:15][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:15][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:15][COMM]adc read battery ts volt:14 mv
[D][05:18:15][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3069  volt:5394 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:24:33:251 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 18:24:33:434 ==>> 【转刹把供电电压(主控ADC)】通过,【5394mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:24:33:440 ==>> 检测【转刹把供电电压】
2025-07-31 18:24:33:444 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:33:521 ==>> [D][05:18:16][COMM]read battery soc:255


2025-07-31 18:24:33:838 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:16][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:16][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:16][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:16][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:16][COMM]adc read battery ts volt:7 mv
[D][05:18:16][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3072  volt:5400 mv
$GBGGA,102437.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,13,,,41,60,,,41,1*77

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,14,,,38,33,,,38,9,,,37,1*42

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,33,4,,,33,1*72

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102437.507,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102437.507,0.000,800.030,800.031,731.644,2097152,2097152,2097152*62

[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:144

2025-07-31 18:24:33:868 ==>> 1  volt:33409 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:24:33:961 ==>> 【转刹把供电电压】通过,【5400mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:24:33:965 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:24:33:969 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:34:144 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:24:34:238 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:24:34:242 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:24:34:244 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:34:339 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:24:34:447 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:34:557 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:24:34:663 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:34:753 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:17][COMM]>>>>>Input command = ?<<<<
$GBGGA,102438.507,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,13,,,41,60,,,41,1*77

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,1,,,38,14,,,38,33,,,38,9,,,37,1*75

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,7,,,31,1*40

$GBRMC,102438.507,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102438.507,0.000,800.028,800.028,731.642,2097152,2097152,2097152*6A



2025-07-31 18:24:34:768 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:24:34:858 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 18:24:34:891 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:24:34:895 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:24:34:900 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:24:34:993 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:24:35:040 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:24:35:069 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:24:35:117 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:24:35:121 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:24:35:126 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:24:35:174 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:24:35:396 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:24:35:400 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:24:35:404 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:24:35:477 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:24:35:522 ==>> [D][05:18:18][COMM]read battery soc:255


2025-07-31 18:24:35:676 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:24:35:683 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:24:35:688 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:24:35:717 ==>> $GBGGA,102439.507,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,13,,,41,60,,,41,1*77

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,9,,,38,14,,,38,33,,,38,1*4D

$GBGSV,6,5,21,2,,,37,6,,,37,5,,,34,4,,,34,1*73

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102439.507,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102439.507,0.000,803.959,803.959,735.236,2097152,2097152,2097152*68

3A A3 05 01 A3 


2025-07-31 18:24:35:777 ==>> ON_OUT5
OVER 150


2025-07-31 18:24:35:945 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:24:35:951 ==>> 检测【左刹电压测试1】
2025-07-31 18:24:35:973 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:36:293 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1716  volt:2262 mv
[D][05:18:18][COMM]adc read right brake adc:1722  volt:2270 mv
[D][05:18:18][COMM]adc read throttle adc:1716  volt:2262 mv
[D][05:18:18][COMM]adc read battery ts volt:9 mv
[D][05:18:18][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:24:36:477 ==>> 【左刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 18:24:36:480 ==>> 检测【右刹电压测试1】
2025-07-31 18:24:36:501 ==>> 【右刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 18:24:36:507 ==>> 检测【转把电压测试1】
2025-07-31 18:24:36:523 ==>> 【转把电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 18:24:36:526 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:24:36:529 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:24:36:582 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:24:36:687 ==>> $GBGGA,102440.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,3

2025-07-31 18:24:36:732 ==>> 9,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,33,,,39,1,,,38,6,,,38,14,,,38,1*74

$GBGSV,6,5,21,9,,,37,2,,,36,5,,,34,4,,,33,1*7A

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102440.507,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102440.507,0.000,802.983,802.983,734.344,2097152,2097152,2097152*63



2025-07-31 18:24:36:844 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:24:36:850 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:24:36:855 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:24:36:972 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:24:37:156 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:24:37:160 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:24:37:164 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:24:37:278 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:24:37:456 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:24:37:460 ==>> 检测【左刹电压测试2】
2025-07-31 18:24:37:467 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:24:37:520 ==>> [D][05:18:20][COMM]read battery soc:255


2025-07-31 18:24:37:839 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:20][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:20][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:20][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:20][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:20][COMM]adc read battery ts volt:11 mv
[D][05:18:20][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:20][COMM]adc read throttle brake in adc:8  volt:14 mv
$GBGGA,102441.507,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,41,60,,,41,1*74

$GBGSV,6,2,22,38,,,41,3,,,41,26,,,41,8,,,40,1*77

$GBGSV,6,3,22,39,,,40,59,,,40,21,,,40,1,,,39,1*49

$GBGSV,6,4,22,16,,,39,33,,,39,14,,,38,9,,,37,1*40

$GBGSV,6,5,22,6,,,37,2,,,36,5,,,34,4,,,33,1*76

$GBGSV,6,6,22,7,,,31,20,,,36,1*44

$GBRMC,102441.507,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102441.507,0.000,801.017,801.017,732.546,2097152,2097152,2097152*60

[D][05:18:20][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1440  volt:33

2025-07-31 18:24:37:869 ==>> 386 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:24:38:071 ==>> 【左刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 18:24:38:075 ==>> 检测【右刹电压测试2】
2025-07-31 18:24:38:186 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 18:24:38:193 ==>> 检测【转把电压测试2】
2025-07-31 18:24:38:233 ==>> 【转把电压测试2】通过,【13】符合目标值【0】至【50】要求!
2025-07-31 18:24:38:237 ==>> 检测【晶振检测】
2025-07-31 18:24:38:240 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:24:38:462 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 18:24:38:561 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:24:38:564 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:24:38:568 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:24:38:732 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1664mV
Get AD_V4 1652mV
Get AD_V5 2756mV
Get AD_V6 1988mV
Get AD_V7 1090mV
OVER 150
$GBGGA,102442.507,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,44,42,,,42,13,,,41,60,,,41,1*70

$GBGSV,6,2,21,8,,,41,38,,,41,3,,,41,26,,,41,1*75

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,33,,,39,6,,,38,14,,,38,1*43

$GBGSV,6,5,21,9,,,37,2,,,36,5,,,34,4,,,33,1*7A

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102442.507,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102442.507,0.000,804.956,804.956,736.148,2097152,2097152,2097152*6D



2025-07-31 18:24:38:874 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:24:38:880 ==>> 检测【检测BootVer】
2025-07-31 18:24:38:886 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:24:39:259 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071544987
[D][05:18:21][FCTY]HardwareID  = 867222087820504
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = D1BB8C5D9388
[D][05:18:21][FCTY]Bat         = 3924 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1288
[D][05:18:21][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 151 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[

2025-07-31 18:24:39:304 ==>> D][05:18:21][FCTY]Bat1         = 3809 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:24:39:450 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:24:39:455 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:24:39:459 ==>> 检测【检测固件版本】
2025-07-31 18:24:39:492 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:24:39:499 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:24:39:505 ==>> 检测【检测蓝牙版本】
2025-07-31 18:24:39:523 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:24:39:528 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:24:39:533 ==>> 检测【检测MoBikeId】
2025-07-31 18:24:39:549 ==>> [D][05:18:22][COMM]read battery soc:255


2025-07-31 18:24:39:566 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:24:39:572 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:24:39:579 ==>> 检测【检测蓝牙地址】
2025-07-31 18:24:39:582 ==>> 取到目标值:D1BB8C5D9388
2025-07-31 18:24:39:614 ==>> 【检测蓝牙地址】通过,【D1BB8C5D9388】符合目标值【】要求!
2025-07-31 18:24:39:618 ==>> 提取到蓝牙地址:D1BB8C5D9388
2025-07-31 18:24:39:626 ==>> 检测【BOARD_ID】
2025-07-31 18:24:39:659 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:24:39:662 ==>> 检测【检测充电电压】
2025-07-31 18:24:39:700 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:24:39:704 ==>> 检测【检测VBUS电压1】
2025-07-31 18:24:39:739 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:24:39:746 ==>> 检测【检测充电电流】
2025-07-31 18:24:39:759 ==>> $GBGGA,102443.507,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,1,,,39,1*4A

$GBGSV,6,4,21,16,,,39,33,,,39,14,,,38,9,,,37,1*43

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,33,1*75

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102443.507,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102443.507,0.000,802.984,802.984,734.344,2097152,2097152,2097152*60



2025-07-31 18:24:39:783 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:24:39:791 ==>> 检测【检测IMEI】
2025-07-31 18:24:39:796 ==>> 取到目标值:867222087820504
2025-07-31 18:24:39:825 ==>> 【检测IMEI】通过,【867222087820504】符合目标值【】要求!
2025-07-31 18:24:39:829 ==>> 提取到IMEI:867222087820504
2025-07-31 18:24:39:835 ==>> 检测【检测IMSI】
2025-07-31 18:24:39:849 ==>> 取到目标值:460130071544987
2025-07-31 18:24:39:874 ==>> 【检测IMSI】通过,【460130071544987】符合目标值【】要求!
2025-07-31 18:24:39:892 ==>> 提取到IMSI:460130071544987
2025-07-31 18:24:39:896 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:24:39:901 ==>> 取到目标值:460130071544987
2025-07-31 18:24:39:912 ==>> 【校验网络运营商(移动)】通过,【460130071544987】符合目标值【】要求!
2025-07-31 18:24:39:916 ==>> 检测【打开CAN通信】
2025-07-31 18:24:39:921 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:24:39:981 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:24:40:227 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:24:40:231 ==>> 检测【检测CAN通信】
2025-07-31 18:24:40:237 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:24:40:394 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:24:40:454 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33962
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:24:40:515 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:24:40:530 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:24:40:534 ==>> 检测【关闭CAN通信】
2025-07-31 18:24:40:540 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:24:40:575 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:24:40:680 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
$GBGGA,102444.507,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,

2025-07-31 18:24:40:740 ==>> ,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,33,,,39,1,,,38,14,,,38,9,,,37,1*74

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,33,4,,,33,1*72

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102444.507,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102444.507,0.000,801.018,801.018,732.547,2097152,2097152,2097152*64



2025-07-31 18:24:40:803 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:24:40:811 ==>> 检测【打印IMU STATE】
2025-07-31 18:24:40:817 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:24:40:968 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:24:41:077 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:24:41:082 ==>> 检测【六轴自检】
2025-07-31 18:24:41:086 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:24:41:282 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:24:41:537 ==>> [D][05:18:24][COMM]read battery soc:255


2025-07-31 18:24:41:735 ==>> $GBGGA,102445.507,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,59,,,41,26,,,41,1*41

$GBGSV,6,3,21,8,,,40,39,,,40,21,,,40,16,,,39,1*48

$GBGSV,6,4,21,33,,,39,1,,,38,14,,,38,9,,,37,1*74

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,33,1*75

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102445.507,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102445.507,0.000,802.985,802.985,734.346,2097152,2097152,2097152*64



2025-07-31 18:24:42:739 ==>> $GBGGA,102446.507,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,33,,,39,1,,,38,14,,,38,9,,,37,1*74

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,33,1*75

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102446.507,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102446.507,0.000,801.999,801.999,733.444,2097152,2097152,2097152*65



2025-07-31 18:24:42:983 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:24:43:195 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36680 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-10,-5,4077]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 18:24:43:441 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:24:43:448 ==>> 检测【打印IMU STATE2】
2025-07-31 18:24:43:455 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:24:43:557 ==>> [D][05:18:26][COMM]read battery soc:255


2025-07-31 18:24:43:752 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,102447.507,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,42,,,42,60,,,41,1*74

$GBGSV,6,2,21,38,,,41,3,,,41,26,,,41,8,,,40,1*74

$GBGSV,6,3,21,39,,,40,59,,,40,21,,,40,16,,,39,1*7C

$GBGSV,6,4,21,33,,,39,9,,,38,1,,,38,14,,,38,1*7B

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,33,1*75

$GBGSV,6,6,21,7,,,32,1*43

$GBRMC,102447.507,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102447.507,0.000,802.983,802.983,734.344,2097152,2097152,2097152*64



2025-07-31 18:24:44:023 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:24:44:027 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:24:44:033 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:24:44:083 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:24:44:187 ==>> [D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:26][F

2025-07-31 18:24:44:235 ==>> CTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 18:24:44:314 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:24:44:320 ==>> 检测【检测VBUS电压2】
2025-07-31 18:24:44:338 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:24:44:661 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071544987
[D][05:18:26][FCTY]HardwareID  = 867222087820504
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D1BB8C5D9388
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 9000 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 303
[D][05:18:26][FCTY]Acckey1 vol = 5489 mv, Acckey2 vol = 75 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1

2025-07-31 18:24:44:706 ==>>          = 3809 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:24:44:850 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:24:45:695 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                       130,40,1*7A

$GBGSV,6,4,21,14,48,335,38,1,48,126,38,2,46,239,36,21,42,118,40,1*77

$GBGSV,6,5,21,60,41,238,41,4,32,112,34,33,23,324,39,5,22,258,34,1*7A

$GBGSV,6,6,21,7,17,176,31,1*76

$GBRMC,102444.513,A,2301.2568488,N,11421.9414676,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:27][GNSS]HD8040 GPS
[D][05:18:27][GNSS]GPS diff_sec 124002377, report 0x42 frame
$GBGST,102444.513,0.794,0.173,0.160,0.230,2.554,3.418,8.228*74

[D][05:18:27][COMM]Main Task receive event:131
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][COMM]index:0,power_mode:0xFF
[D][05:18:27][COMM]index:1,sound_mode:0xFF
[D][05:18:27][COMM]index:2,gsensor_mode:0xFF
[D][05:18:27][COMM]index:3,report_freq_mode:0xFF
[D][05:18:27][COMM]index:4

2025-07-31 18:24:45:800 ==>> ,report_period:0xFF
[D][05:18:27][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:27][COMM]index:6,normal_reset_period:0xFF
[D][05:18:27][COMM]index:7,spock_over_speed:0xFF
[D][05:18:27][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:27][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:27][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:27][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:27][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:27][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:27][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:27][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:27][COMM]index:16,imu_config_params:0xFF
[D][05:18:27][COMM]index:17,long_connect_params:0xFF
[D][05:18:27][COMM]index:18,detain_mark:0xFF
[D][05:18:27][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:27][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:27][COMM]index:21,mc_mode:0xFF
[D][05:18:27][COMM]index:22,S_mode:0xFF
[D][05:18:27][COMM]index:23,overweight:0xFF
[D][05:18:27][COMM]index:24,standstill_mode:0xFF
[D][05:18:27][COMM]index:25,night_mode:0xFF
[D][05:18:27][COMM]index:26,experiment1:0xFF
[D][05:18:27][COMM]index:27,experiment2:0xFF
[D][05:18:27][COMM]in

2025-07-31 18:24:45:905 ==>> dex:28,experiment3:0xFF
[D][05:18:27][COMM]index:29,experiment4:0xFF
[D][05:18:27][COMM]index:30,night_mode_start:0xFF
[D][05:18:27][COMM]index:31,night_mode_end:0xFF
[D][05:18:27][COMM]index:33,park_report_minutes:0xFF
[D][05:18:27][COMM]index:34,park_report_mode:0xFF
[D][05:18:27][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:27][COMM]index:38,charge_battery_para: FF
[D][05:18:27][COMM]index:39,multirider_mode:0xFF
[D][05:18:27][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:27][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:27][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:27][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:27][COMM]index:44,riding_duration_config:0xFF
[D][05:18:27][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:27][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:27][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:27][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:27][COMM]index:49,mc_load_startup:0xFF
[D][05:18:27][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:27][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:27][COMM]index:52,traffic_mode:0xFF
[D][05:18:27][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:27][CO

2025-07-31 18:24:46:010 ==>> MM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:27][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:27][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:27][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:27][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:27][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:27][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:27][COMM]index:63,experiment5:0xFF
[D][05:18:27][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:27][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:27][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:27][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:27][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:27][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:27][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:27][COMM]index:72,experiment6:0xFF
[D][05:18:27][COMM]index:73,experiment7:0xFF
[D][05:18:27][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:27][COMM]index:75,zero_value_from_server:-1
[D][05:18:27][COMM]index:76,multirider_threshold:255
[D][05:18:27][COMM]index:77,experiment8:255
[D][05:18:27][COMM]index:78,temp_park_audio_play_duration:255


2025-07-31 18:24:46:115 ==>> 
[D][05:18:27][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:27][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:27][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:27][COMM]index:83,loc_report_interval:255
[D][05:18:27][COMM]index:84,multirider_threshold_p2:255
[D][05:18:27][COMM]index:85,multirider_strategy:255
[D][05:18:27][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:27][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:27][COMM]index:90,weight_param:0xFF
[D][05:18:27][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:27][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:27][COMM]index:95,current_limit:0xFF
[D][05:18:27][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:27][COMM]index:100,location_mode:0xFF

[W][05:18:27][PROT]remove success[1629955107],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:1
[D][05:18

2025-07-31 18:24:46:220 ==>> :27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][COMM]Main Task receive event:131 finished processing
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[294]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052d10] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:27][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B56DE780C73DC392F66B4C25DF77F5DD5C6D702CCEEA05F75E099F49908E02863BDDE104FD7AC2671EF46E9D2977DD4F50C4C7111BAC9AD90E253194BB46B61E45362C6445FA36C09150313E7

2025-07-31 18:24:46:325 ==>> F144AEBE7163868911A1BEC8026A0F726C0F6A4F55547AE35B2C594169BB0F17EAF749DEA5DBCD005526E4ADAF7837F85D2F50004AFD8
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
[D][05:18:27][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE

2025-07-31 18:24:46:430 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:24:46:535 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 18:24:47:427 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:24:47:731 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071544987
[D][05:18:30][FCTY]HardwareID  = 867222087820504
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D1BB8C5D9388
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 0,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 94
[D][05:18:30][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 75 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][

2025-07-31 18:24:47:776 ==>> FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3809 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:24:50:255 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][PROT]CLEAN:0
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B9801133113

2025-07-31 18:24:50:330 ==>> 31133113311331B88B324901E83F06242A0986A844143EA449DF8991F7F73DA5C3C9B4DA0919D24180A098FDF95D598B69641A384414B1AB48639EC57854C4C6EDD4325A62394B5FD83E7BCE9C9944DA91E57E7A5AEFC0C363F0A63
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 18:24:50:367 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:24:50:375 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:24:50:393 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:24:50:481 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:24:50:586 ==>> [D][05:18:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30
[D][05:18:33][COMM]read battery soc:255


2025-07-31 18:24:50:676 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:24:50:681 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:24:50:688 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:24:50:781 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:24:50:972 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:24:50:977 ==>> 检测【打开WIFI(3)】
2025-07-31 18:24:50:984 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:24:51:193 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:24:51:259 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:24:51:267 ==>> 检测【扩展芯片hw】
2025-07-31 18:24:51:284 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:24:51:493 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:24:51:541 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:24:51:546 ==>> 检测【扩展芯片boot】
2025-07-31 18:24:51:560 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:24:51:565 ==>> 检测【扩展芯片sw】
2025-07-31 18:24:51:578 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:24:51:584 ==>> 检测【检测音频FLASH】
2025-07-31 18:24:51:589 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:24:51:763 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:24:51:868 ==>> +WIFISCAN:4,0,CC057790A741,-72
+WIFISCAN:4,1,44A1917CA62F,-74
+WIFISCAN:4,2,CC057790A7C0,-77
+WIFISCAN:4,3,CC057790A7C1,-77

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 18:24:52:142 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:24:52:858 ==>>                       _peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l

2025-07-31 18:24:52:963 ==>> :[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[D][05:18:34][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0

2025-07-31 18:24:53:068 ==>> ,10800,2048,2954

[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][PROT]min_index:0, type:0x5D03, priority:4
[

2025-07-31 18:24:53:173 ==>> D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][PROT]index:0
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][PROT]is_send:1
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
[D][05:18:34][PROT]sequence_num:6
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900007]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_P

2025-07-31 18:24:53:278 ==>> OWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][GNSS]recv submsg id[3]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34

2025-07-31 18:24:53:353 ==>> ][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:35][COMM]read battery soc:255


2025-07-31 18:24:54:592 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 18:24:55:199 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:55:499 ==>>                                  [D][05:18:37][PROT]index:0 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:6
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900007]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]Send Data To Server[198][198] ... ->:
0063B98C113311331133113311331B88BEDCD537E95FD5CB8D141F3CC5554606C9E366674B0B5EBBD5

2025-07-31 18:24:55:574 ==>> 4FF69E8A80590B69219BF1D4A8F4064E7EAD5F384D8D567AA071BB5F27DF147D5656D6D691811B662AC761522C5EFBEF6265D2F09282EB6D35FF
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 18:24:55:876 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 18:24:56:194 ==>> [D][05:18:38][COMM]49717 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:24:56:299 ==>>                                                                   a_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process]

2025-07-31 18:24:56:344 ==>> .audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:24:56:616 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 18:24:56:678 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:24:56:686 ==>> 检测【打开喇叭声音】
2025-07-31 18:24:56:708 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:24:57:380 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[70

2025-07-31 18:24:57:473 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:24:57:482 ==>> 检测【打开大灯控制】
2025-07-31 18:24:57:490 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:24:57:496 ==>> 4].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D

2025-07-31 18:24:57:590 ==>> ][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]50728 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:24:57:680 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:24:57:759 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:24:57:768 ==>> 检测【关闭仪表供电3】
2025-07-31 18:24:57:788 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:24:57:969 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 18:24:58:033 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:24:58:039 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:24:58:063 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:24:58:261 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:24:58:316 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:24:58:323 ==>> 检测【读大灯电压】
2025-07-31 18:24:58:343 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:24:58:456 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[32969]


2025-07-31 18:24:58:595 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:24:58:601 ==>> 检测【关闭大灯控制2】
2025-07-31 18:24:58:608 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:24:58:621 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 18:24:58:726 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:24:58:868 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:24:58:877 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:24:58:897 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:24:59:057 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:24:59:151 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:24:59:156 ==>> 检测【打开WIFI(4)】
2025-07-31 18:24:59:161 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:24:59:393 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:24:59:490 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:24:59:499 ==>> 检测【EC800M模组版本】
2025-07-31 18:24:59:519 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:24:59:667 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 18:24:59:911 ==>> [D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:25:00:022 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:25:00:027 ==>> 检测【配置蓝牙地址】
2025-07-31 18:25:00:035 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:25:00:139 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:25:00:229 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D1BB8C5D9388>】
2025-07-31 18:25:00:292 ==>> +WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,CC057790A741,-72
+WIFISCAN:4,2,CC057790A740,-72
+WIFISCAN:4,3,44A1917CA62F,-74

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 18:25:00:379 ==>> [D][05:18:42][GNSS]recv submsg id[3]
recv ble 1
recv ble 2
ble set mac ok :d1,bb,8c,5d,93,88
enable filters ret : 0

2025-07-31 18:25:00:507 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:25:00:512 ==>> 检测【BLETEST】
2025-07-31 18:25:00:518 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:25:00:576 ==>> 4A A4 01 A4 4A 


2025-07-31 18:25:00:681 ==>> [D][05:18:42][PROT]CLEAN,SEND:0
[D][05:18:42][PROT]index:0 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:6
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:2
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900007]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[

2025-07-31 18:25:00:786 ==>> 198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]Send Data To Server[198][198] ... ->:
0063B981113311331133113311331B88BE0CAA5A35F9A4A7D9E4C1006C8190AB9F942C77115AA74C070ADA14C594A0403C5516F3CE42682E22ADF70B3DD5A63E0A16F1874895A426479DF5DA775293D340CC3F72E7967AEFDB1855B1515A418E962835
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]
[D][05:18:43][COMM]read battery soc:255


2025-07-31 18:25:00:891 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D1BB8C5D9388*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D1BB8C5D938899999[D][05:18:43][COMM]54416 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:25:00:981 ==>> OVER 150


2025-07-31 18:25:01:533 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:25:01:539 ==>> 该项需要延时执行
2025-07-31 18:25:01:924 ==>> [D][05:18:44][COMM]55427 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:25:02:502 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:25:02:607 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 18:25:02:913 ==>> [D][05:18:45][COMM]56438 imu init OK


2025-07-31 18:25:04:643 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 18:25:05:942 ==>> [D][05:18:48][PROT]CLEAN,SEND:0
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:6
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:1
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900007]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]Send Data To Server[198][198] ... ->:
0063B982113311331133113311331B88BE0DE069E035400571BB3995434780D2EC9F30AD193FC0BDFFFD530B94C28E2DBEA02CF9F12B93A204E

2025-07-31 18:25:06:017 ==>> 0A498893DBF3E11C39725128D42B294C804313E27497B19283DED2960E957F07063763B15D6653B73AD
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 18:25:06:639 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 18:25:08:636 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 18:25:10:656 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 18:25:11:177 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]CLEAN:0
[D][05:18:53][PROT]index:1 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:5
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:2
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900006]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311

2025-07-31 18:25:11:252 ==>> 331B88B32C3365EB93C5E03F40E594032D9FC7910CF37AE61F0417F540C01E44DA725B0D340CD1FBF550729FC44C56605AE361F35245B81BF690E05846C14E6BDEDF139D884A907BA9BDE7D6849D387AFF68B201D1FB
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 18:25:11:538 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:25:11:547 ==>> 检测【检测WiFi结果】
2025-07-31 18:25:11:573 ==>> WiFi信号:【44A1917CA62F】,信号值:-69
2025-07-31 18:25:11:577 ==>> WiFi信号:【CC057790A740】,信号值:-73
2025-07-31 18:25:11:586 ==>> WiFi信号:【44A1917CA62B】,信号值:-76
2025-07-31 18:25:11:606 ==>> WiFi信号:【646E97BD0450】,信号值:-86
2025-07-31 18:25:11:625 ==>> WiFi信号:【CC057790A741】,信号值:-72
2025-07-31 18:25:11:634 ==>> WiFi信号:【CC057790A7C0】,信号值:-77
2025-07-31 18:25:11:654 ==>> WiFi信号:【CC057790A7C1】,信号值:-77
2025-07-31 18:25:11:663 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 18:25:11:684 ==>> WiFi数量【8】, 最大信号值:-63
2025-07-31 18:25:11:693 ==>> 检测【检测GPS结果】
2025-07-31 18:25:11:716 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:25:11:774 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 18:25:12:551 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:25:12:560 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:25:12:583 ==>> 定位已等待【1】秒.
2025-07-31 18:25:12:659 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 18:25:12:980 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:25:13:552 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:25:13:561 ==>> 定位已等待【2】秒.
2025-07-31 18:25:13:687 ==>> $GBGGA,102517.028,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,13,77,240,,8,75,192,,42,72,15,,24,67,243,,1*77

$GBGSV,4,2,14,6,62,310,,16,62,316,,39,60,342,,38,59,173,,1*49

$GBGSV,4,3,14,9,57,283,,26,56,29,,14,48,335,,21,42,119,,1*7A

$GBGSV,4,4,14,33,23,324,,7,17,176,,1*46

$GBRMC,102517.028,V,,,,,,,310725,0.1,E,N,V*47

$GBGST,102517.028,0.000,0.005,0.005,0.005,229,382,216*61



2025-07-31 18:25:14:357 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:25:14:553 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:25:14:562 ==>> 定位已等待【3】秒.
2025-07-31 18:25:15:081 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,102514.253,2301.2565927,N,11421.9412844,E,1,12,1.33,76.362,M,-1.770,M,,*58

$GBGSA,A,3,13,42,24,06,16,39,38,09,26,14,21,33,2.47,1.33,2.08,4*0B

$GBGSV,8,1,31,13,77,240,41,8,75,192,,42,72,15,42,36,72,124,,1*7A

$GBGSV,8,2,31,24,67,243,42,6,62,310,37,3,62,191,47,16,62,316,35,1*72

$GBGSV,8,3,31,39,60,342,38,38,59,173,40,9,57,283,37,26,56,29,41,1*74

$GBGSV,8,4,31,59,52,130,44,19,50,65,,14,49,335,37,1,48,126,38,1*75

$GBGSV,8,5,31,2,46,239,36,22,44,338,,21,42,119,41,60,41,238,42,1*49

$GBGSV,8,6,31,46,33,46,,4,32,112,,45,31,200,,33,23,324,34,1*7F

$GBGSV,8,7,31,5,22,258,,7,17,176,,40,14,168,37,35,13,220,,1*7E

$GBGSV,8,8,31,20,11,104,,44,11,270,,10,11,191,,1*4E

$GBRMC,102514.253,A,2301.2565927,N,11421.9412844,E,1.201,129.38,310725,,,A,S*3C

$GBVTG,129.38,T,,M,1.201,N,2.223,K,A*2D

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][GNSS]GPS diff_sec 124002378, report 0x42 frame
$GBGST,102514.253,1.955,1.321,1.294,1.785,2.657,2.882,8.002*72

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:57

2025-07-31 18:25:15:187 ==>> ][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 23, ret: 6
[D][05:18:57][CAT1]sub id: 23, ret: 6

[D][05:18:57][COMM]Main Task receive event:131
[D][05:18:57][COMM]index:0,power_mode:0xFF
[D][05:18:57][COMM]index:1,sound_mode:0xFF
[D][05:18:57][COMM]index:2,gsensor_mode:0xFF
[D][05:18:57][COMM]index:3,report_freq_mode:0xFF
[D][05:18:57][COMM]index:4,report_period:0xFF
[D][05:18:57][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:57][COMM]index:6,normal_reset_period:0xFF
[D][05:18:57][COMM]index:7,spock_over_speed:0xFF
[D][05:18:57][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:57][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:57][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:57][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:57][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:57][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:57][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:57][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:57][COMM]index:16,imu_config_params:0xFF
[D][05:18:57][COMM]index:17,long_connect_params:0xFF
[D][05:18:57][COMM]index:18,detain_mark:0xFF
[D][05:18:57][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:57][COMM]index:20,lock_po

2025-07-31 18:25:15:292 ==>> s_report_interval:0xFF
[D][05:18:57][COMM]index:21,mc_mode:0xFF
[D][05:18:57][COMM]index:22,S_mode:0xFF
[D][05:18:57][COMM]index:23,overweight:0xFF
[D][05:18:57][COMM]index:24,standstill_mode:0xFF
[D][05:18:57][COMM]index:25,night_mode:0xFF
[D][05:18:57][COMM]index:26,experiment1:0xFF
[D][05:18:57][COMM]index:27,experiment2:0xFF
[D][05:18:57][COMM]index:28,experiment3:0xFF
[D][05:18:57][COMM]index:29,experiment4:0xFF
[D][05:18:57][COMM]index:30,night_mode_start:0xFF
[D][05:18:57][COMM]index:31,night_mode_end:0xFF
[D][05:18:57][COMM]index:33,park_report_minutes:0xFF
[D][05:18:57][COMM]index:34,park_report_mode:0xFF
[D][05:18:57][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:57][COMM]index:38,charge_battery_para: FF
[D][05:18:57][COMM]index:39,multirider_mode:0xFF
[D][05:18:57][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:57][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:57][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:57][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:57][COMM]index:44,riding_duration_config:0xFF
[D][05:18:57][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:57][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:57][C

2025-07-31 18:25:15:402 ==>> OMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:57][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:57][COMM]index:49,mc_load_startup:0xFF
[D][05:18:57][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:57][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:57][COMM]index:52,traffic_mode:0xFF
[D][05:18:57][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:57][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:57][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:57][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:57][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:57][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:57][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:57][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:57][COMM]index:63,experiment5:0xFF
[D][05:18:57][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:57][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:57][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:57][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:57][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:57][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:57][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:57][COMM]ind

2025-07-31 18:25:15:503 ==>> ex:72,experiment6:0xFF
[D][05:18:57][COMM]index:73,experiment7:0xFF
[D][05:18:57][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:57][COMM]index:75,zero_value_from_server:-1
[D][05:18:57][COMM]index:76,multirider_threshold:255
[D][05:18:57][COMM]index:77,experiment8:255
[D][05:18:57][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:57][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:57][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:57][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:57][COMM]index:83,loc_report_interval:255
[D][05:18:57][COMM]index:84,multirider_threshold_p2:255
[D][05:18:57][COMM]index:85,multirider_strategy:255
[D][05:18:57][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:57][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:57][COMM]index:90,weight_param:0xFF
[D][05:18:57][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:57][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:57][COMM]index:95,current_limit:0xFF
[D][05:18:57][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:57][COMM]index:100,location_mode:0xFF

[W][05:18:57][PROT]remove success[1

2025-07-31 18:25:15:562 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:25:15:573 ==>> 定位已等待【4】秒.
2025-07-31 18:25:15:609 ==>> 629955137],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:57][PROT]add success [1629955137],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:57][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:57][COMM]Main Task receive event:131 finished processing
[D][05:18:57][COMM]read battery soc:255
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 18:25:15:653 ==>>                                                                                                                                                                                                                                                                                                                           

2025-07-31 18:25:16:477 ==>> [D][05:18:58][PROT]CLEAN,SEND:1
[D][05:18:58][PROT]index:1 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:5
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:1
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][PROT]sending traceid [9999999999900006]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[198]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052d10] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Send Data To Server[198][198] ... ->:
0063B983113311331133113311331B88B388B18F7D4ACCE5FEBB129

2025-07-31 18:25:16:568 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:25:16:580 ==>> 定位已等待【5】秒.
2025-07-31 18:25:16:591 ==>> A2BE0B28D33ADBB16FD505FFBBE4146B7B23BB3E2F76B70B098ED3C5F866A3FF6CC8C7C971FF404455218806A4AFA0FDF51341F46AC08523B64EBA766975A460F375CAB79AC4FE1
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]
$GBGGA,102516.033,2301.2572542,N,11421.9419140,E,1,12,1.33,77.855,M,-1.770,M,,*5F

$GBGSA,A,3,13,42,24,06,16,39,38,09,26,14,21,33,2.47,1.33,2.08,4*0B

$GBGSV,6,1,22,13,77,240,41,8,75,192,,42,72,15,42,24,67,243,43,1*74

$GBGSV,6,2,22,6,62,310,37,3,62,191,41,16,62,316,38,39,60,342,40,1*7C

$GBGSV,6,3,22,38,59,173,41,9,57,283,37,26,56,29,41,59,52,130,39,1*78

$GBGSV,6,4,22,14,49,335,38,1,48,126,38,2,46,239,36,21,42,119,41,1*75

$GBGSV,6,5,22,60,41,238,41,4,32,112,33,33,23,324,36,5,22,258,33,1*76

$GBGSV,6,6,22,7,17,176,,40,14,168,31,1*4B

$GBGSV,2,1,07,42,72,15,43,24,67,243,41,39,60,342,41,38,59,173,41,5*4

2025-07-31 18:25:16:628 ==>> A

$GBGSV,2,2,07,26,56,29,40,21,42,119,40,33,23,324,35,5*77

$GBRMC,102516.033,A,2301.2572542,N,11421.9419140,E,0.001,113.89,310725,,,A,S*35

$GBVTG,113.89,T,,M,0.001,N,0.003,K,A*2F

$GBGST,102516.033,3.063,0.300,0.296,0.417,2.476,2.730,6.352*7D



2025-07-31 18:25:16:673 ==>>                                          

2025-07-31 18:25:17:352 ==>> $GBGGA,102517.013,2301.2573409,N,11421.9418702,E,1,12,1.33,76.958,M,-1.770,M,,*5F

$GBGSA,A,3,13,42,24,06,16,39,38,09,26,14,21,33,2.47,1.33,2.08,4*0B

$GBGSV,6,1,22,13,77,240,41,8,75,192,,42,72,15,42,24,67,243,43,1*74

$GBGSV,6,2,22,6,62,310,37,3,62,191,41,16,62,316,39,39,60,342,40,1*7D

$GBGSV,6,3,22,38,59,173,41,9,57,283,37,26,56,29,41,59,52,130,40,1*76

$GBGSV,6,4,22,14,49,335,38,1,48,126,38,2,46,239,36,21,42,119,40,1*74

$GBGSV,6,5,22,60,41,238,41,4,32,112,33,33,23,324,37,5,22,258,33,1*77

$GBGSV,6,6,22,7,17,176,,40,14,168,31,1*4B

$GBGSV,2,1,07,42,72,15,43,24,67,243,43,39,60,342,41,38,59,173,41,5*48

$GBGSV,2,2,07,26,56,29,41,21,42,119,41,33,23,324,35,5*77

$GBRMC,102517.013,A,2301.2573409,N,11421.9418702,E,0.001,113.89,310725,,,A,S*38

$GBVTG,113.89,T,,M,0.001,N,0.001,K,A*2D

$GBGST,102517.013,2.245,0.225,0.222,0.314,1.920,2.173,5.670*7E



2025-07-31 18:25:17:582 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 18:25:17:587 ==>> 
北斗星号:【13】,信号值:【41】
北斗星号:【42】,信号值:【43】
北斗星号:【24】,信号值:【43】
北斗星号:【6】,信号值:【37】
北斗星号:【3】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【39】,信号值:【41】
北斗星号:【38】,信号值:【41】
北斗星号:【9】,信号值:【37】
北斗星号:【26】,信号值:【41】
北斗星号:【59】,信号值:【40】
北斗星号:【14】,信号值:【38】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【36】
北斗星号:【21】,信号值:【41】
北斗星号:【60】,信号值:【41】
北斗星号:【33】,信号值:【35】

2025-07-31 18:25:17:593 ==>> 检测【CSQ强度】
2025-07-31 18:25:17:616 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:25:17:798 ==>> [D][05:19:00][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:19:00][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:25:17:865 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 18:25:17:871 ==>> 检测【关闭GSM联网】
2025-07-31 18:25:17:876 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:25:18:071 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:00][COMM]GSM test
[D][05:19:00][COMM]GSM test disable


2025-07-31 18:25:18:150 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:25:18:159 ==>> 检测【4G联网测试】
2025-07-31 18:25:18:181 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:25:18:359 ==>> $GBGGA,102518.000,2301.2574188,N,11421.9417889,E,1,13,1.32,76.256,M,-1.770,M,,*5F

$GBGSA,A,3,13,08,42,24,06,16,39,38,09,26,14,21,2.44,1.32,2.06,4*0F

$GBGSA,A,3,33,,,,,,,,,,,,2.44,1.32,2.06,4*0E

$GBGSV,6,1,22,13,77,240,41,8,75,192,40,42,72,15,42,24,67,243,43,1*70

$GBGSV,6,2,22,6,62,310,37,3,62,191,41,16,62,316,39,39,60,342,40,1*7D

$GBGSV,6,3,22,38,59,173,41,9,57,283,37,26,56,29,41,59,52,130,40,1*76

$GBGSV,6,4,22,14,49,335,38,1,48,126,39,2,46,239,36,21,42,119,40,1*75

$GBGSV,6,5,22,60,41,238,41,4,32,112,32,33,23,324,38,5,22,258,33,1*79

$GBGSV,6,6,22,7,17,176,,40,14,168,31,1*4B

$GBGSV,2,1,07,42,72,15,43,24,67,243,43,39,60,342,41,38,59,173,41,5*48

$GBGSV,2,2,07,26,56,29,41,21,42,119,41,33,23,324,35,5*77

$GBRMC,102518.000,A,2301.2574188,N,11421.9417889,E,0.000,113.89,310725,,,A,S*3C

$GBVTG,113.89,T,,M,0.000,N,0.001,K,A*2C

$GBGST,102518.000,1.841,0.217,0.213,0.303,1.593,1.840,5.147*76



2025-07-31 18:25:19:161 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                           [D][05:19:00][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:00][COMM]index:6,normal_reset_period:0xFF
[D][05:19:00][COMM]index:7,spock_over_speed:0xFF
[D][05:19:00][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:00][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:00][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:00][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:00][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:00][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:00][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:00][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:00][COMM]index:16,imu_config_params:0xFF
[D][05:19:00][COMM]index:17,long_connect_params:0xFF
[D][05:19:00][COMM]index:18,detain_mark:0xFF
[D][05:19:00][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:00][COMM]index:20,

2025-07-31 18:25:19:266 ==>> lock_pos_report_interval:0xFF
[D][05:19:00][COMM]index:21,mc_mode:0xFF
[D][05:19:00][COMM]index:22,S_mode:0xFF
[D][05:19:00][COMM]index:23,overweight:0xFF
[D][05:19:00][COMM]index:24,standstill_mode:0xFF
[D][05:19:00][COMM]index:25,night_mode:0xFF
[D][05:19:00][COMM]index:26,experiment1:0xFF
[D][05:19:00][COMM]index:27,experiment2:0xFF
[D][05:19:00][COMM]index:28,experiment3:0xFF
[D][05:19:00][COMM]index:29,experiment4:0xFF
[D][05:19:00][COMM]index:30,night_mode_start:0xFF
[D][05:19:00][COMM]index:31,night_mode_end:0xFF
[D][05:19:00][COMM]index:33,park_report_minutes:0xFF
[D][05:19:00][COMM]index:34,park_report_mode:0xFF
[D][05:19:00][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:00][COMM]index:38,charge_battery_para: FF
[D][05:19:00][COMM]index:39,multirider_mode:0xFF
[D][05:19:00][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:00][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:00][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:00][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:00][COMM]index:44,riding_duration_config:0xFF
[D][05:19:00][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:00][COMM]index:46,camera_park_type_cfg:0xFF
[D][05

2025-07-31 18:25:19:371 ==>> :19:00][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:00][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:00][COMM]index:49,mc_load_startup:0xFF
[D][05:19:00][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:00][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:00][COMM]index:52,traffic_mode:0xFF
[D][05:19:00][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:00][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:00][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:00][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:00][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:00][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:00][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:00][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:00][COMM]index:63,experiment5:0xFF
[D][05:19:00][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:00][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:00][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:00][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:00][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:00][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:00][COMM]index:71,camera_park_self_check_cfg

2025-07-31 18:25:19:477 ==>> :0xFF
[D][05:19:00][COMM]index:72,experiment6:0xFF
[D][05:19:00][COMM]index:73,experiment7:0xFF
[D][05:19:00][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:00][COMM]index:75,zero_value_from_server:-1
[D][05:19:00][COMM]index:76,multirider_threshold:255
[D][05:19:00][COMM]index:77,experiment8:255
[D][05:19:00][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:00][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:00][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:00][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:00][COMM]index:83,loc_report_interval:255
[D][05:19:00][COMM]index:84,multirider_threshold_p2:255
[D][05:19:00][COMM]index:85,multirider_strategy:255
[D][05:19:00][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:00][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:00][COMM]index:90,weight_param:0xFF
[D][05:19:00][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:00][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:00][COMM]index:95,current_limit:0xFF
[D][05:19:00][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:00][COMM]index:100,location_mode:0xFF

[W][05:19:00][PR

2025-07-31 18:25:19:582 ==>> OT]remove success[1629955140],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:00][PROT]add success [1629955140],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:00][PROT]index:0 1629955140
[D][05:19:00][PROT]is_send:0
[D][05:19:00][PROT]sequence_num:9
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:1
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:00][PROT]===========================================================
[W][05:19:00][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][PROT]sending traceid [999999999990000A]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][CAT1]gsm read msg sub id: 13
[D][05:19:00][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:00][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[294]
[D][05:19:00][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:00][SAL ]Cellular task submsg id[10]


2025-07-31 18:25:19:688 ==>> [D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052d28] format[0]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:00][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:00][CAT1]exec over: func id: 13, ret: 21
[D][05:19:00][M2M ]get csq[20]
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:00][CAT1]Send Data To Server[294][294] ... ->:
0093B985113311331133113311331B88BCC1B4F1659CD269EAC87ACD775FA97409074341C592FC6BD78F3E09DEBABD3280ED1B39A11B76938CBEFFE6614FD65F7198A61C1275DB923E1B41B0A1BDD8AF48B04475D8ED7A1885EC5808D90FDF8EC1692C4AE1561CFCBBFB751997C480CCA8FB87832D432AC2E2B51CC395F842AC9C3D756C511F844D3BAAFB79419991412289DF
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:01][PROT]M2M Send ok [1629955141]
[D][05:19:01][COMM]read battery soc:255


2025-07-31 18:25:19:763 ==>> 
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:01][PROT]remove success[1629955141],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:01][PROT]add success [1629955141],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:01][COMM]------>period, report file manifest
[D][05:19:01][COMM]Main Task receive event:14 finished processing
[D][05:19:01][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:01][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:01][CAT1]gsm read msg sub id: 21
[D][05:19:01][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]cell info report total[0]
[D][05:19:01][CAT1]exec over: func id: 21, ret: 6


2025-07-31 18:25:19:868 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 18:25:20:353 ==>> $GBGGA,102520.000,2301.2574721,N,11421.9416595,E,1,13,1.32,75.277,M,-1.770,M,,*50

$GBGSA,A,3,13,08,42,24,06,16,39,38,09,26,14,21,2.44,1.32,2.06,4*0F

$GBGSA,A,3,33,,,,,,,,,,,,2.44,1.32,2.06,4*0E

$GBGSV,6,1,22,13,77,240,41,8,75,192,40,42,72,15,42,24,67,243,43,1*70

$GBGSV,6,2,22,6,62,310,37,3,62,191,41,16,62,316,39,39,60,343,40,1*7C

$GBGSV,6,3,22,38,59,173,41,9,57,283,37,26,56,29,40,59,52,130,40,1*77

$GBGSV,6,4,22,14,49,335,38,1,48,126,38,2,46,239,36,21,42,119,40,1*74

$GBGSV,6,5,22,60,41,238,41,4,32,112,33,33,23,324,37,5,22,258,33,1*77

$GBGSV,6,6,22,7,17,176,,40,14,168,31,1*4B

$GBGSV,2,1,07,42,72,15,43,24,67,243,44,39,60,343,41,38,59,173,41,5*4E

$GBGSV,2,2,07,26,56,29,41,21,42,119,41,33,23,324,35,5*77

$GBRMC,102520.000,A,2301.2574721,N,11421.9416595,E,0.002,113.89,310725,,,A,S*31

$GBVTG,113.89,T,,M,0.002,N,0.004,K,A*2B

$GBGST,102520.000,1.122,0.208,0.204,0.291,0.967,1.212,4.351*7C



2025-07-31 18:25:20:669 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 18:25:20:714 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:25:20:721 ==>> 检测【关闭GPS】
2025-07-31 18:25:20:728 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:25:21:062 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:03][GNSS]stop locating
[D][05:19:03][GNSS]stop event:8
[D][05:19:03][GNSS]GPS stop. ret=0
[D][05:19:03][GNSS]all continue location stop
[D][05:19:03][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:19:03][GNSS]stop locating
[D][05:19:03][GNSS]all sing location stop
[D][05:19:03][CAT1]gsm read msg sub id: 24
[D][05:19:03][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 24, ret: 6
[D][05:19:03][CAT1]sub id: 24, ret: 6



2025-07-31 18:25:21:276 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:25:21:294 ==>> 检测【清空消息队列2】
2025-07-31 18:25:21:305 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:25:21:517 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:03][COMM]Protocol queue cleaned by AT_CMD!
[D][05:19:04][GNSS]recv submsg id[1]
[D][05:19:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:04][GNSS]location stop evt done evt


2025-07-31 18:25:21:577 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:25:21:585 ==>> 检测【轮动检测】
2025-07-31 18:25:21:595 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:25:21:683 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 18:25:21:743 ==>> [D][05:19:04][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:25:22:091 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:25:22:181 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:25:22:387 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:25:22:394 ==>> 检测【关闭小电池】
2025-07-31 18:25:22:400 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:25:22:471 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:25:22:677 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:25:22:683 ==>> 检测【进入休眠模式】
2025-07-31 18:25:22:696 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:25:22:723 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 18:25:22:837 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:25:22:942 ==>> [D][05:19:05][COMM]Main Task receive event:28
[D][05:19:05][COMM]main task tmp_sleep_event = 8
[D][05:19:05][COMM]prepare to sleep
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:25:23:840 ==>> [D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 6
[D][05:19:06][M2M ]tcpclient close[4]
[D][05:19:06][SAL ]Cellular task submsg id[12]
[D][05:19:06][SAL ]cellular CLOSE socket size[4], msg->data[0x20052cf0], socket[0]
[D][05:19:06][CAT1]gsm read msg sub id: 9
[D][05:19:06][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 9, ret: 6
[D][05:19:06][CAT1]sub id: 9, ret: 6

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:06][SAL ]socket close ind. id[4]
[D][05:19:06][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:06][COMM]1x1 frm_can_tp_send ok
[D][05:19:06][CAT1]pdpdeact urc len[22]


2025-07-31 18:25:24:126 ==>> [E][05:19:06][COMM]1x1 rx timeout
[D][05:19:06][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:25:24:634 ==>> [E][05:19:07][COMM]1x1 rx timeout
[E][05:19:07][COMM]1x1 tp timeout
[E][05:19:07][COMM]1x1 error -3.
[W][05:19:07][COMM]CAN STOP!
[D][05:19:07][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:07][COMM]------------ready to Power off Acckey 1------------
[D][05:19:07][COMM]------------ready to Power off Acckey 2------------
[D][05:19:07][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:07][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1287
[D][05:19:07][COMM]bat sleep fail, reason:-1
[D][05:19:07][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:07][COMM]accel parse set 0
[D][05:19:07][COMM]imu rest ok. 78051
[D][05:19:07][COMM]imu sleep 0
[W][05:19:07][COMM]now sleep


2025-07-31 18:25:24:793 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:25:24:800 ==>> 检测【检测33V休眠电流】
2025-07-31 18:25:24:821 ==>> 开始33V电流采样
2025-07-31 18:25:24:832 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:24:908 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:25:25:909 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:25:25:969 ==>> Current33V:????:19.66

2025-07-31 18:25:26:424 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:26:433 ==>> 【检测33V休眠电流】通过,【19.66uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:25:26:441 ==>> 该项需要延时执行
2025-07-31 18:25:28:439 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:25:28:450 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:25:28:476 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:25:28:579 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2747mV
Get AD_V6 2013mV
Get AD_V7 1091mV
OVER 150


2025-07-31 18:25:29:465 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:25:29:472 ==>> 检测【打开小电池2】
2025-07-31 18:25:29:478 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:25:29:568 ==>> 6A A6 01 A6 6A 


2025-07-31 18:25:29:673 ==>> Battery ON
OVER 150


2025-07-31 18:25:29:771 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:25:29:782 ==>> 该项需要延时执行
2025-07-31 18:25:30:284 ==>> 此处延时了:【500】毫秒
2025-07-31 18:25:30:295 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:25:30:316 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:25:30:376 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:25:30:595 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:25:30:601 ==>> 该项需要延时执行
2025-07-31 18:25:31:067 ==>> [D][05:19:13][COMM]------------ready to Power on Acckey 1------------
[D][05:19:13][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:13][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:13][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:13][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:13][COMM]----- get Acckey 1 and value:1------------
[W][05:19:13][COMM]CAN START!
[D][05:19:13][CAT1]gsm read msg sub id: 12
[D][05:19:13][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:13][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 84385
[D][05:19:13][COMM][Audio]exec status ready.
[D][05:19:13][CAT1]<<< 
OK

[D][05:19:13][CAT1]exec over: func id: 12, ret: 6
[D][05:19:13][COMM]imu wakeup ok. 84400
[D][05:19:13][COMM]imu wakeup 1
[

2025-07-31 18:25:31:097 ==>> 此处延时了:【500】毫秒
2025-07-31 18:25:31:109 ==>> 检测【进入休眠模式2】
2025-07-31 18:25:31:132 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:25:31:147 ==>> W][05:19:13][COMM]wake up system, wakeupEvt=0x80
[D][05:19:13][COMM]frm_can_weigth_power_set 1
[D][05:19:13][COMM]Clear Sleep Block Evt
[D][05:19:13][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:25:31:217 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:25:31:307 ==>>                                          :19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:25:31:412 ==>>                           0 loss. last_tick:84370. cur_tick:84879. period:50
[D][05:19:13][COMM]msg 02A4 loss. last_tick:84370. cur_tick:84880. period:50
[D][05:19:13][COMM]msg 02A5 loss. last_tick:84370. cur_tick:84880. period:50
[D][05:19:13][COMM]msg 02A6 loss. last_tick:84370. cur_tick:84880. period:50
[D][05:19:13][COMM]msg 02A7 loss. last_tick:84370. cur_tick:84881. period:50
[D][05:19:13][COMM]CAN message fault change: 0x0000000000000000->0x0000E000002200

2025-07-31 18:25:31:442 ==>> 00 84881
[D][05:19:13][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 84882


2025-07-31 18:25:31:779 ==>> [E][05:19:14][COMM]1x1 rx timeout
[E][05:19:14][COMM]1x1 tp timeout
[E][05:19:14][COMM]1x1 error -3.
[D][05:19:14][COMM]Main Task receive event:28 finished processing
[D][05:19:14][COMM]Main Task receive event:28
[D][05:19:14][COMM]prepare to sleep
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:14][CAT1]<<< 
OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 6
[D][05:19:14][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:14][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:25:32:099 ==>> [D][05:19:14][COMM]msg 0220 loss. last_tick:84370. cur_tick:85376. period:100
[D][05:19:14][COMM]msg 0221 loss. last_tick:84370. cur_tick:85376. period:100
[D][05:19:14][COMM]msg 0224 loss. last_tick:84370. cur_tick:85376. period:100
[D][05:19:14][COMM]msg 0260 loss. last_tick:84370. cur_tick:85377. period:100
[D][05:19:14][COMM]msg 0280 loss. last_tick:84370. cur_tick:85377. period:100
[D][05:19:14][COMM]msg 02C0 loss. last_tick:84370. cur_tick:85378. period:100
[D][05:19:14][COMM]msg 02C1 loss. last_tick:84370. cur_tick:85378. period:100
[D][05:19:14][COMM]msg 02C2 loss. last_tick:84370. cur_tick:85378. period:100
[D][05:19:14][COMM]msg 02E0 loss. last_tick:84370. cur_tick:85379. period:100
[D][05:19:14][COMM]msg 02E1 loss. last_tick:84370. cur_tick:85379. period:100
[D][05:19:14][COMM]msg 02E2 loss. last_tick:84370. cur_tick:85379. period:100
[D][05:19:14][COMM]msg 0300 loss. last_tick:84370. cur_tick:85380. period:100
[D][05:19:14][COMM]msg 0301 loss. last_tick:84370. cur_tick:85380. period:100
[D][05:19:14][COMM]bat msg 0240 loss. last_tick:84370. cur_tick:85380. period:100. j,i:1 54
[D][05:19:14][COMM]bat msg 0241 loss. last_tick:84370. cur_tick:85381. period:100. j,i:2 55
[D][05:19:14][COMM]bat msg 0242 lo

2025-07-31 18:25:32:204 ==>> ss. last_tick:84371. cur_tick:85381. period:100. j,i:3 56
[D][05:19:14][COMM]bat msg 0244 loss. last_tick:84371. cur_tick:85382. period:100. j,i:5 58
[D][05:19:14][COMM]bat msg 024E loss. last_tick:84371. cur_tick:85382. period:100. j,i:15 68
[D][05:19:14][COMM]bat msg 024F loss. last_tick:84371. cur_tick:85383. period:100. j,i:16 69
[D][05:19:14][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 85383
[D][05:19:14][COMM]CAN message bat fault change: 0x00000000->0x0001802E 85383
[D][05:19:14][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 85384
                                                                              

2025-07-31 18:25:32:370 ==>> [D][05:19:14][COMM]msg 0222 loss. last_tick:84370. cur_tick:85878. period:150
[D][05:19:14][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 85879


2025-07-31 18:25:32:445 ==>> [D][05:19:14][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:14][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:14][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:25:32:656 ==>> [E][05:19:15][COMM]1x1 rx timeout
[E][05:19:15][COMM]1x1 tp timeout
[D][05:19:15][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[E][05:19:15][COMM]1x1 error -3.
[W][05:19:15][COMM]CAN STOP!
[D][05:19:15][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:15][COMM]------------ready to Power off Acckey 1------------
[D][05:19:15][COMM]------------ready to Power off Acckey 2------------
[D][05:19:15][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:15][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 106
[D][05:19:15][COMM]bat sleep fail, reason:-1
[D][05:19:15][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:15][COMM]accel parse set 0
[D][05:19:15][COMM]imu rest ok. 86071
[D][05:19:15][COMM]imu sleep 0
[W][05:19:15][COMM]now sleep


2025-07-31 18:25:32:935 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:25:32:941 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:25:32:979 ==>> 开始小电池电流采样
2025-07-31 18:25:32:990 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:33:039 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:25:34:054 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:25:34:129 ==>> CurrentBattery:ƽ��:70.22

2025-07-31 18:25:34:559 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:34:567 ==>> 【检测小电池休眠电流】通过,【70.22uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:25:34:597 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:25:34:614 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:25:34:681 ==>> 5A A5 01 5A A5 


2025-07-31 18:25:34:771 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:25:34:857 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:25:34:868 ==>> 该项需要延时执行
2025-07-31 18:25:35:013 ==>> [D][05:19:17][COMM]------------ready to Power on Acckey 1------------
[D][05:19:17][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:17][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:17][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 13
[D][05:19:17][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:17][COMM]----- get Acckey 1 and value:1------------
[W][05:19:17][COMM]CAN START!
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:17][COMM]CAN message bat fault change: 0x0001802E->0x00000000 88378
[D][05:19:17][COMM][Audio]exec status ready.
[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 6
[D][05:19:17][COMM]imu wakeup ok. 88392
[D][05:19:17][COMM]imu wakeup 1
[W][05:19:17][COMM]wake up system, wakeupEvt=0x80
[D][05:19:17][COMM]frm_can_weigth_power_set 1
[D][05:19:17][COMM]Clear Sleep Block Evt
[D][05:19:17][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:17][COMM]1x1 frm_can_tp_send ok
[D][05:19:17][COMM]read battery soc:0


2025-07-31 18:25:35:273 ==>> [E][05:19:17][COMM]1x1 rx timeout
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:25:35:363 ==>> 此处延时了:【500】毫秒
2025-07-31 18:25:35:374 ==>> 检测【检测唤醒】
2025-07-31 18:25:35:398 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:25:35:411 ==>> [D][05:19:17][COMM]msg 02A0 loss. last_tick:88360. cur_tick:88871. perio

2025-07-31 18:25:35:438 ==>> d:50
[D][05:19:17][COMM]msg 02A4 loss. last_tick:88360. cur_tick:88872. period:50
[D][05:19:17][COMM]msg 02A5 loss. last_tick:88360. cur_tick:88872. period:50
[D][05:19:17][COMM]msg 02A6 loss. last_tick:88360. cur_tick:88872. period:50
[D][05:19:17][COMM]msg 02A7 loss. last_tick:88360. cur_tick:88873. period:50
[D][05:19:17][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 88873
[D][05:19:17][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 88874


2025-07-31 18:25:36:144 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:18][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:18][FCTY]==========Modules-nRF5340 ==========
[D][05:19:18][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:18][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:18][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:18][FCTY]DeviceID    = 460130071544987
[D][05:19:18][FCTY]HardwareID  = 867222087820504
[D][05:19:18][FCTY]MoBikeID    = 9999999999
[D][05:19:18][FCTY]LockID      = FFFFFFFFFF
[D][05:19:18][FCTY]BLEFWVersion= 105
[D][05:19:18][FCTY]BLEMacAddr   = D1BB8C5D9388
[D][05:19:18][FCTY]Bat         = 3884 mv
[D][05:19:18][FCTY]Current     = 0 ma
[D][05:19:18][FCTY]VBUS        = 2600 mv
[D][05:19:18][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:18][FCTY]Ext battery vol = 32, adc = 1283
[D][05:19:18][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 75 mv
[D][05:19:18][FCTY]Bike Type flag is invalied
[D][05:19:18][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:18][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:18][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:18][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:18][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:18][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:18][FCTY]Bat1         = 3809 mv
[D][05

2025-07-31 18:25:36:249 ==>> :19:18][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:18][FCTY]==========Modules-nRF5340 ==========
[E][05:19:18][COMM]1x1 rx timeout
[E][05:19:18][COMM]1x1 tp timeout
[E][05:19:18][COMM]1x1 error -3.
[D][05:19:18][COMM]Main Task receive event:28 finished processing
[D][05:19:18][COMM]Main Task receive event:65
[D][05:19:18][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:18][COMM]Main Task receive event:65 finished processing
[D][05:19:18][COMM]Main Task receive event:60
[D][05:19:18][COMM]smart_helmet_vol=255,255
[D][05:19:18][COMM]report elecbike
[W][05:19:18][PROT]remove success[1629955158],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:18][COMM]Main Task receive event:60 finished processing
[D][05:19:18][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:18][PROT]index:0
[D][05:19:18][PROT]is_send:1
[D][05:19:18][PROT]sequence_num:11
[D][05:19:18][PROT]retry_timeout:0
[D][05:19:18][PROT]retry_times:3
[D][05:19:18][PROT]send_path:0x3
[D][05:19:18][PROT]msg_type:0x5d03
[D][05:19:18][PROT]=====================

2025-07-31 18:25:36:354 ==>> ======================================
[W][05:19:18][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]Sending traceid[999999999990000C]
[D][05:19:18][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:18][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:18][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][CAT1]gsm read msg sub id: 8
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:1

2025-07-31 18:25:36:426 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:25:36:441 ==>> 检测【关机】
2025-07-31 18:25:36:471 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:25:36:485 ==>> 9:18][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:18][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:18][CAT1]<<< 
+CME ERROR: 100

[D][05:19:18][COMM]msg 0220 loss. last_tick:88360. cur_tick:89367. period:100
[D][05:19:18][COMM]msg 0221 loss. last_tick:88360. cur_tick:89368. period:100
[D][05:19:18][COMM]msg 0224 loss. last_tick:88360. cur_tick:89368. period:100
[D][05:19:18][COMM]msg 0260 loss. last_tick:88360. cur_tick:89368. period:100
[D][05:19:18][COMM]msg 0280 loss. last_tick:88360. cur_tick:89369. period:100
[D][05:19:18][COMM]msg 02C0 loss. last_tick:88360. cur_tick:89369. period:100
[D][05:19:18][COMM]msg 02C1 loss. last_tick:88360. cur_tick:89370. period:100
[D][05:19:18][COMM]msg 02C2 loss. last_tick:88360. cur_tick:89370. period:100
[D][05:19:18][COMM]msg 02E0 loss. last_tick:88360. cur_tick:89371. period:100
[D][05:19:18][COMM]msg 02E1 loss. last_tick:88360. cur_tick:89371. period:100
[D][05:19:18][COMM]msg 02E2 loss. last_tick:88360. cur_tick:89371. period:100
[D][05:19:18][COMM]msg 0300 loss. last_tick:88360. cur_tick:89372. period:100
[D][05:19:18][COMM]msg 0301 loss. last_tick:88360. cur_tick:89372. 

2025-07-31 18:25:36:549 ==>> period:100
[D][05:19:18][COMM]bat msg 0240 loss. last_tick:88360. cur_tick:89372. period:100. j,i:1 54
[D][05:19:18][COMM]bat msg 0241 loss. last_tick:88360. cur_tick:89373. period:100. j,i:2 55
[D][05:19:18][COMM]bat msg 0242 loss. last_tick:88360. cur_tick:89373. period:100. j,i:3 56
[D][05:19:18][COMM]bat msg 0244 loss. last_tick:88360. cur_tick:89373. period:100. j,i:5 58
[D][05:19:18][COMM]bat msg 024E loss. last_tick:88360. cur_tick:89374. period:100. j,i:15 68
[D][05:19:18][COMM]bat msg 024F loss. last_tick:88360. cur_tick:89374. period:100. j,i:16 69
[D][05:19:18][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 89375
[D][05:19:18][COMM]CAN message bat fault change: 0x00000000->0x0001802E 89375
[D][05:19:18][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 89376


2025-07-31 18:25:37:237 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 18:25:37:342 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            OMM]read file success
[W][05:19:19][COMM][Audio].l:[936].close hexlog save
[D][05:19:19][COMM]accel parse set 1
[D][05:19:19][COMM][Audio]mon:9,05:19:19
[D][05:19:19][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:19][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:19][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:19][COMM]f:[ec800

2025-07-31 18:25:37:447 ==>> m_audio_start].l:[691].recv ok
[D][05:19:19][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:19][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:19][COMM]Main Task receive event:65
[D][05:19:19][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:19][COMM]Main Task receive event:65 finished processing
[D][05:19:19][COMM]Main Task receive event:66
[D][05:19:19][COMM]Try to Auto Lock Bat
[D][05:19:19][COMM]Main Task receive event:66 finished processing
[D][05:19:19][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:19][COMM]Main Task receive event:60
[D][05:19:19][COMM]smart_helmet_vol=255,255
[D][05:19:19][COMM]BAT CAN get state1 Fail 204
[D][05:19:19][COMM]BAT CAN get soc Fail, 204
[D][05:19:19][COMM]BAT CAN get state2 fail 204
[D][05:19:19][COMM]get soh error
[E][05:19:19][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:19][COMM]report elecbike
[W][05:19:19][PROT]remove success[1629955159],send

2025-07-31 18:25:37:465 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:25:37:552 ==>> _path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:19][PROT]add success [1629955159],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:19][COMM]Main Task receive event:60 finished processing
[D][05:19:19][COMM]Main Task receive event:61
[D][05:19:19][COMM][D301]:type:3, trace id:280
[D][05:19:19][COMM]id[], hw[000
[D][05:19:19][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:19][COMM]get mcMaincircuitVolt error
[D][05:19:19][COMM]get mcSubcircuitVolt error
[D][05:19:19][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][PROT]index:1
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:19][PROT]is_send:1
[D][05:19:19][PROT]sequence_num:12
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:3
[D][05:19:19][PROT]send_path:0x3
[D][05:19:19][PROT]msg_type:0x5d03
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][PROT]Sending traceid[999999999990000D]
[D]

2025-07-31 18:25:37:657 ==>> [05:19:19][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:19][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:19][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:19][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:19][COMM]Receive Bat Lock cmd 0
[D][05:19:19][COMM]VBUS is 1
[D][05:19:19][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:19][COMM]BAT CAN get state1 Fail 204
[D][05:19:19][COMM]BAT CAN get soc Fail, 204
[D][05:19:19][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:19][COMM]BAT CAN get state2 fail 204
[D][05:19:19][COMM]get bat work mode err
[W][05:19:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:19][COMM]Main Task r

2025-07-31 18:25:37:762 ==>> eceive event:61 finished processing
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:19][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:19][COMM]arm_hub_enable: hub power: 0
[D][05:19:19][HSDK]hexlog index save 0 6912 236 @ 0 : 0
[D][05:19:19][HSDK]write save hexlog index [0]
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:19][COMM]read battery soc:255
[D][05:19:19][CO

2025-07-31 18:25:37:867 ==>> MM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:19][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:19][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:19][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:19][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 18:25:37:972 ==>>                                                                                                                                                                                                                                                                                                 a flash
[D][05:19:20][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:25:38:400 ==>> [W][05:19:20][COMM]Power Off


2025-07-31 18:25:38:509 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:25:38:516 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:25:38:537 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:25:38:582 ==>> 5A A5 02 5A A5 


2025-07-31 18:25:38:672 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:25:38:777 ==>> [D][05:19:21][FCTY]get_ext_48v_vol retry i = 0,volt = 16
[D][05:19:2

2025-07-31 18:25:38:788 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:25:38:797 ==>> 检测【检测小电池关机电流】
2025-07-31 18:25:38:826 ==>> 开始小电池电流采样
2025-07-31 18:25:38:834 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:38:857 ==>> 1][FCTY]get_ext_48v_vol retry i = 1,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 2,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 3,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 4,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 5,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 6,volt = 16
[D][05:19:21][FCTY]get_ext_48v_vol retry i = 7,volt 1 

2025-07-31 18:25:38:897 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:25:39:902 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:25:39:980 ==>> CurrentBattery:ƽ��:68.24

2025-07-31 18:25:40:404 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:25:40:412 ==>> 【检测小电池关机电流】通过,【68.24uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:25:40:683 ==>> MES过站成功
2025-07-31 18:25:40:690 ==>> #################### 【测试结束】 ####################
2025-07-31 18:25:40:716 ==>> 关闭5V供电
2025-07-31 18:25:40:729 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:25:40:784 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:25:41:728 ==>> 关闭5V供电成功
2025-07-31 18:25:41:740 ==>> 关闭33V供电
2025-07-31 18:25:41:765 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:25:41:777 ==>> 5A A5 02 5A A5 


2025-07-31 18:25:41:880 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:25:42:743 ==>> 关闭33V供电成功
2025-07-31 18:25:42:756 ==>> 关闭3.7V供电
2025-07-31 18:25:42:779 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:25:42:881 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:25:43:703 ==>>  

2025-07-31 18:25:43:748 ==>> 关闭3.7V供电成功
