2025-07-31 22:34:14:997 ==>> MES查站成功:
查站序号:P510001005313412验证通过
2025-07-31 22:34:15:008 ==>> 扫码结果:P510001005313412
2025-07-31 22:34:15:012 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:34:15:015 ==>> 测试参数版本:2024.10.11
2025-07-31 22:34:15:017 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:34:15:019 ==>> 检测【打开透传】
2025-07-31 22:34:15:021 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:34:15:105 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:34:15:368 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:34:15:372 ==>> 检测【检测接地电压】
2025-07-31 22:34:15:374 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:34:15:498 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:34:15:659 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:34:15:663 ==>> 检测【打开小电池】
2025-07-31 22:34:15:666 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:34:15:798 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:34:15:936 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:34:15:938 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:34:15:941 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:34:16:011 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:34:16:210 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:34:16:213 ==>> 检测【等待设备启动】
2025-07-31 22:34:16:216 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:34:16:585 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:34:16:767 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:34:17:238 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:34:17:418 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:34:17:463 ==>>                                     Will Not Open


2025-07-31 22:34:17:861 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:34:18:270 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:34:18:346 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:34:18:608 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:34:18:610 ==>> 检测【产品通信】
2025-07-31 22:34:18:611 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:34:18:786 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 22:34:18:909 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:34:18:911 ==>> 检测【初始化完成检测】
2025-07-31 22:34:18:914 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:34:18:968 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:34:19:133 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:34:19:205 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:34:19:208 ==>> 检测【关闭大灯控制1】
2025-07-31 22:34:19:211 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:34:19:392 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:34:19:526 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:34:19:557 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:34:19:558 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:34:19:560 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:34:19:661 ==>> [W][05:17:51][COMM]>>>>>Input

2025-07-31 22:34:19:706 ==>>  command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:34:19:812 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:34:19:815 ==>> 检测【关闭仪表供电】
2025-07-31 22:34:19:816 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:34:19:994 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:34:20:087 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:34:20:089 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:34:20:091 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:34:20:283 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:34:20:367 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:34:20:369 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:34:20:371 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:34:20:388 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:34:20:585 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:34:20:648 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:34:20:661 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:34:20:663 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:20:767 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:34:20:921 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:34:20:923 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:34:20:925 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:34:20:998 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:34:21:088 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:34:21:197 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:34:21:199 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:34:21:201 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:34:21:300 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:34:21:405 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:

2025-07-31 22:34:21:435 ==>> [-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 22:34:21:468 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:34:21:472 ==>> 该项需要延时执行
2025-07-31 22:34:21:939 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault

2025-07-31 22:34:21:969 ==>>  change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 22:34:22:429 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:34:22:691 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:34:23:182 ==>>                                                                                   ----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904

2025-07-31 22:34:23:287 ==>> ].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[

2025-07-31 22:34:23:392 ==>> ], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get 

2025-07-31 22:34:23:482 ==>> state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 22:34:24:473 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 22:34:25:088 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:34:25:452 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:34:25:482 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:34:25:486 ==>> 检测【33V输入电压ADC】
2025-07-31 22:34:25:490 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:25:818 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:17:57][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:17:57][COMM]adc read left brake adc:8  volt:10 mv
[D][05:17:57][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:34:26:016 ==>> 【33V输入电压ADC】通过,【32450mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:34:26:030 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:34:26:031 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:34:26:118 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1674mV
Get AD_V4 0mV
Get AD_V5 2776mV
Get AD_V6 1988mV
Get AD_V7 1085mV
OVER 150


2025-07-31 22:34:26:289 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:26:292 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:34:26:309 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1674mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:26:311 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:34:26:312 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 22:34:26:328 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:34:26:330 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:34:26:347 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:34:26:349 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:34:26:369 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:34:26:370 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:34:26:469 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:34:26:514 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1676mV
Get AD_V4 0mV
Get AD_V5 2775mV
Get AD_V6 1988mV
Get AD_V7 1086mV
OVER 150


2025-07-31 22:34:26:660 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:26:662 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:34:26:690 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1676mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:26:692 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:34:26:694 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 22:34:26:708 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:34:26:711 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:34:26:726 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:34:26:730 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:34:26:750 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:34:26:752 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:34:26:832 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10003
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10003
1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1675mV
Get AD_V4 0mV
Get AD_V5 2776mV
Get AD_V6 1989mV
Get AD_V7 1086mV
OVER 150


2025-07-31 22:34:27:050 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:27:052 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:34:27:078 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1675mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:27:080 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:34:27:082 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 22:34:27:092 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:34:27:096 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:34:27:098 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:34:27:133 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:34:27:139 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:34:27:158 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:34:27:160 ==>> 检测【打开WIFI(1)】
2025-07-31 22:34:27:161 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:34:27:723 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg

2025-07-31 22:34:27:768 ==>>  sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:34:27:976 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:34:27:979 ==>> 检测【清空消息队列(1)】
2025-07-31 22:34:27:980 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:34:28:273 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087442242

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539892

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input c

2025-07-31 22:34:28:303 ==>> ommand = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:34:28:482 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:34:28:533 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:34:28:537 ==>> 检测【打开GPS(1)】
2025-07-31 22:34:28:540 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:34:28:692 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:34:28:863 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:34:28:870 ==>> 检测【打开GSM联网】
2025-07-31 22:34:28:873 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:34:29:113 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][COMM]read battery soc:255


2025-07-31 22:34:29:190 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:34:29:193 ==>> 检测【打开仪表供电1】
2025-07-31 22:34:29:204 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:34:29:492 ==>>                                [D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:1

2025-07-31 22:34:29:568 ==>> 8:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:34:29:672 ==>>                                                                                                                                                                  

2025-07-31 22:34:29:721 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:34:29:727 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:34:29:730 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:34:29:779 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:34:29:884 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:34:29:993 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:34:29:998 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:34:30:002 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:34:30:186 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33015]


2025-07-31 22:34:30:264 ==>> 【读取主控ADC采集的仪表电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:34:30:267 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:34:30:271 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:34:30:521 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13727 imu init OK


2025-07-31 22:34:30:810 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:34:30:813 ==>> 检测【AD_V20电压】
2025-07-31 22:34:30:817 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:34:30:921 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:34:31:012 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:34:31:116 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:34:31:326 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,25,,,39,34,,,39,33,,,37,4,,,52,1*44

$GBGSV,2,2,06,59,,,42,11,,,41,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1589.135,1589.135,50.740,2097152,2097152,2097152*49

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 22:34:31:371 ==>> 本次取值间隔时间:445ms
2025-07-31 22:34:31:389 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:34:31:491 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:34:31:494 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:34:31:596 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:34:31:656 ==>> 本次取值间隔时间:156ms
2025-07-31 22:34:31:675 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:34:31:791 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:34:31:911 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1664mV
OVER 150


2025-07-31 22:34:32:259 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,59,,,40,25,,,39,34,,,39,60,,,39,1*75

$GBGSV,4,2,13,33,,,37,16,,,35,11,,,34,9,,,33,1*49

$GBGSV,4,3,13,41,,,17,40,,,15,39,,,41,3,,,39,1*46

$GBGSV,4,4,13,4,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1360.135,1360.135,43.810,2097152,2097152,2097152*41



2025-07-31 22:34:32:289 ==>> 本次取值间隔时间:484ms
2025-07-31 22:34:32:307 ==>> 【AD_V20电压】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:32:311 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:34:32:314 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:34:32:410 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:34:32:594 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:34:32:597 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:34:32:610 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:34:32:809 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:34:32:886 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:34:32:889 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:34:32:892 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:34:33:005 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:34:33:111 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 22:34:33:159 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:34:33:162 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:34:33:166 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:34:33:216 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,59,,,40,60,,,40,41,,,40,39,,,39,1*7F

$GBGSV,4,2,16,25,,,39,34,,,39,40,,,38,33,,,37,1*7C

$GBGSV,4,3,16,16,,,36,2,,,36,11

2025-07-31 22:34:33:262 ==>> ,,,35,9,,,34,1*7B

$GBGSV,4,4,16,5,,,34,4,,,31,3,,,38,1,,,36,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1533.948,1533.948,49.063,2097152,2097152,2097152*47



2025-07-31 22:34:33:367 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]

2025-07-31 22:34:33:397 ==>> oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:34:33:432 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:34:33:435 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:34:33:438 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:34:33:593 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:34:33:702 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:34:33:706 ==>> 检测【AD_V21电压】
2025-07-31 22:34:33:716 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:34:33:805 ==>> 1A A1 20 00 00 
Get AD_V21 1662mV
OVER 150


2025-07-31 22:34:33:972 ==>> 本次取值间隔时间:260ms
2025-07-31 22:34:33:991 ==>> 【AD_V21电压】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:33:994 ==>> 检测【关闭仪表供电2】
2025-07-31 22:34:33:997 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:34:34:321 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,59,,,40,41,,,40,34,,,40,60,,,39,1*77

$GBGSV,6,2,22,39,,,39,25,,,39,3,,,39,40,,,39,1*48

$GBGSV,6,3,22,1,,,38,33,,,37,16,,,37,12,,,37,1*49

$GBGSV,6,4,22,2,,,36,11,,,36,24,,,36,5,,,35,1*76

$GBGSV,6,5,22,9,,,34,4,,,32,32,,,31,23,,,30,1*7F

$GBGSV,6,6,22,10,,,15,7,,,36,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1478.812,1478.812,47.438,2097152,2097152,2097152*43



2025-07-31 22:34:34:521 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:34:34:525 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:34:34:527 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:34:34:688 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:34:34:797 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:34:34:801 ==>> 检测【打开AccKey2供电】
2025-07-31 22:34:34:806 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:34:34:979 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:34:35:073 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:34:35:078 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:34:35:082 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:35:129 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 22:34:35:433 ==>> $GBGGA,143439.136,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,59,,,40,41,,,40,34,,,40,3,,,40,1*4A

$GBGSV,7,2,25,40,,,40,60,,,39,39,,,39,25,,,39,1*75

$GBGSV,7,3,25,1,,,37,33,,,37,16,,,37,11,,,37,1*43

$GBGSV,7,4,25,7,,,37,12,,,36,2,,,36,24,,,36,1*73

$GBGSV,7,5,25,6,,,35,43,,,35,10,,,35,9,,,34,1*7B

$GBGSV,7,6,25,5,,,33,23,,,33,4,,,32,32,,,32,1*71

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143439.136,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143439.136,0.000,1509.078,1509.078,48.274,2097152,2097152,2097152*50

[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:07][COMM]adc read out 24v adc:1313  volt:33209 mv
[D][05:18:07][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:07][COMM]adc read battery ts volt:13 mv
[D][05:18:07][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2381  volt:3836 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:0

2025-07-31 22:34:35:463 ==>> 7][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:34:35:619 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33209mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:34:35:622 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:34:35:626 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:34:35:705 ==>> $GBGGA,143439.536,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,59,,,40,41,,,40,34,,,40,3,,,40,1*4A

$GBGSV,7,2,25,40,,,40,60,,,40,39,,,39,25,,,39,1*7B

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,16,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,12,,,36,2,,,36,1*45

$GBGSV,7,5,25,6,,,35,43,,,35,10,,,35,9,,,35,1*7A

$GBGSV,7,6,25,23,,,34,5,,,33,4,,,32,32,,,32,1*76

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143439.536,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143439.536,0.000,1517.369,1517.369,48.537,2097152,2097152,2097152*54



2025-07-31 22:34:35:780 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:34:35:906 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:34:35:909 ==>> 该项需要延时执行
2025-07-31 22:34:36:687 ==>> $GBGGA,143440.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,41,,,41,40,,,41,59,,,40,34,,,40,1*7D

$GBGSV,7,2,25,3,,,40,60,,,40,39,,,39,25,,,39,1*4C

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,16,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,12,,,36,2,,,36,1*45

$GBGSV,7,5,25,43,,,36,10,,,36,6,,,35,9,,,35,1*7A

$GBGSV,7,6,25,23,,,34,5,,,33,4,,,33,32,,,33,1*76

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143440.516,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143440.516,0.000,1527.316,1527.316,48.852,2097152,2097152,2097152*56



2025-07-31 22:34:37:131 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:34:37:689 ==>> $GBGGA,143441.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,41,,,40,40,,,40,59,,,40,34,,,40,1*7D

$GBGSV,7,2,25,3,,,40,39,,,40,60,,,39,25,,,39,1*4C

$GBGSV,7,3,25,7,,,38,1,,,38,33,,,38,16,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,12,,,36,2,,,36,1*45

$GBGSV,7,5,25,43,,,36,10,,,36,6,,,35,9,,,35,1*7A

$GBGSV,7,6,25,23,,,34,5,,,33,32,,,33,4,,,32,1*77

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143441.516,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143441.516,0.000,1525.656,1525.656,48.798,2097152,2097152,2097152*5E



2025-07-31 22:34:38:695 ==>> $GBGGA,143442.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,41,,,40,40,,,40,59,,,40,34,,,40,1*7D

$GBGSV,7,2,25,3,,,40,39,,,39,60,,,39,25,,,39,1*42

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,16,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,12,,,36,2,,,36,1*45

$GBGSV,7,5,25,43,,,36,10,,,36,6,,,35,9,,,35,1*7A

$GBGSV,7,6,25,23,,,35,5,,,33,32,,,33,4,,,33,1*77

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143442.516,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143442.516,0.000,1523.990,1523.990,48.737,2097152,2097152,2097152*58



2025-07-31 22:34:38:909 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:34:38:914 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:34:38:918 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:39:218 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:11][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:11][COMM]adc read battery ts volt:2 mv
[D][05:18:11][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2381  volt:3836 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:11][COMM]read battery soc:255


2025-07-31 22:34:39:473 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:34:39:477 ==>> 检测【打开AccKey1供电】
2025-07-31 22:34:39:479 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:34:39:740 ==>> $GBGGA,143443.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,41,,,40,40,,,40,59,,,40,34,,,40,1*7D

$GBGSV,7,2,25,3,,,40,39,,,40,60,,,39,25,,,39,1*4C

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,16,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,12,,,36,2,,,36,1*45

$GBGSV,7,5,25,43,,,36,10,,,36,6,,,35,9,,,35,1*7A

$GBGSV,7,6,25,23,,,35,5,,,33,32,,,33,4,,,32,1*76

$GBGSV,7,7,25,13,,,30,1*70

$GBRMC,143443.516,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143443.516,0.000,1522.342,1522.342,48.694,2097152,2097152,2097152*51

[W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:34:40:010 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:34:40:013 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:34:40:017 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:34:40:108 ==>> 1A A1 00 40 00 
Get AD_V14 2565mV
OVER 150


2025-07-31 22:34:40:275 ==>> 原始值:【2565】, 乘以分压基数【2】还原值:【5130】
2025-07-31 22:34:40:293 ==>> 【读取AccKey1电压(ADV14)前】通过,【5130mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:34:40:296 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:34:40:300 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:40:613 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:12][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:6 mv
[D][05:18:12][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2380  volt:3834 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:34:40:688 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:34:40:748 ==>>                         [-12,-7,-988] ret=24 AWAKE!


2025-07-31 22:34:40:828 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:34:40:831 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:34:40:835 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:34:40:974 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:34:41:109 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:34:41:112 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:34:41:127 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:34:41:139 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:34:41:199 ==>> 1A A1 00 40 00 
Get AD_V14 2594mV
OVER 150


2025-07-31 22:34:41:367 ==>> 原始值:【2594】, 乘以分压基数【2】还原值:【5188】
2025-07-31 22:34:41:386 ==>> 【读取AccKey1电压(ADV14)后】通过,【5188mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:34:41:390 ==>> 检测【打开WIFI(2)】
2025-07-31 22:34:41:394 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:34:41:737 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6
$GBGGA,143445.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,40,59,,,40,34,,,40,1*7C

$GBGSV,7,2,25,3,,,40,39,,,40,60,,,40,25,,,39,1*42

$GBGSV,7,3,25,7,,,38,16,,,38,33,,,38,1,,,37,1*7B

$GBGSV,7,4,25,11,,,37,24,,,37,43,,,37,12,,,36,1*71

$GBGSV,7,5,25,2,,,36,10,,,36,6,,,36,9,,,35,1*4C

$GBGSV,7,6,25,23,,,35,5,,,33,32,,,33,4,,,33,1*77

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143445.516,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143445.516,0.000,1535.604,1535.604,49.114,2097152,2097152,2097152*59



2025-07-31 22:34:41:949 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:34:41:953 ==>> 检测【转刹把供电】
2025-07-31 22:34:41:956 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:34:42:091 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:34:42:255 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:34:42:259 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:34:42:262 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:34:42:321 ==>> +WIFISCAN:4,0,CC057790A641,-67
+WIFISCAN:4,1,CC057790A7C0,-67
+WIFISCAN:4,2,CC057790A640,-71
+WIFISCAN:4,3,44A1917CAD81,-76

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 22:34:42:366 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:34:42:411 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:34:42:516 ==>> 1A A1 00 80 00 
Get AD_V15 2415mV
OVER 150
[D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 22:34:42:531 ==>> 原始值:【2415】, 乘以分压基数【2】还原值:【4830】
2025-07-31 22:34:42:549 ==>> 【读取AD_V15电压(前)】通过,【4830mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:34:42:554 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:34:42:557 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:34:42:653 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:34:42:759 ==>> $GBGGA,143446.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,59,,,40,34,,,40,1*7D

$GBGSV,7,2,25,3,,,40,39,,,40,60,,,39,25,,,39,1*4C

$GBGSV,7,3,25,7,,,38,16,,,38,33,,,38,11,,,38,1*45

$GBGSV,7,4,25,1,,,37,24,,,37,43,,,37,12,,,36,1*40

$GBGSV,7,5,25,2,,,36,10,,,36,6,,,36,9,,,35,1*4C

$GBGSV,7,6,25,23,,,35,5,,,34,32,,,33,4,,,33,1*70

$GBGSV,7,7,25,13,,,31,1*71

$GBRMC,143446.516,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143446.516,0.000,1538.918,1538.918,49.218,2097152,2097152,2097152*55

[D][05:18:14][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2449mV
OVER 150


2025-07-31 22:34:42:804 ==>> 原始值:【2449】, 乘以分压基数【2】还原值:【4898】
2025-07-31 22:34:42:827 ==>> 【读取AD_V16电压(前)】通过,【4898mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:34:42:830 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:34:42:834 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:43:109 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:15][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:15][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:15][COMM]adc read battery ts volt:10 mv
[D][05:18:15][COMM]adc read in 24v adc:1282  volt:32425 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3078  volt:5410 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2381  volt:3836 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:34:43:139 ==>>                                          

2025-07-31 22:34:43:358 ==>> 【转刹把供电电压(主控ADC)】通过,【5410mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:34:43:361 ==>> 检测【转刹把供电电压】
2025-07-31 22:34:43:366 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:43:768 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:15][COMM]adc read battery ts volt:2 mv
[D][05:18:15][COMM]adc read in 24v adc:1273  volt:32197 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3079  volt:5412 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2379  volt:3833 mv
$GBGGA,143447.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,40,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,12,,,36,1*4C

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,33,1*74

$GBGSV,7,7,26,13,,,31,14,,,16,1*70

$GBRMC,143447.516,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143447.516,0.000,1511.733,1511.733,48.459,2097152,2097152,2097152*56

[D][05:18:15][COMM]ar

2025-07-31 22:34:43:813 ==>> m_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:34:43:892 ==>> 【转刹把供电电压】通过,【5412mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:34:43:895 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:34:43:900 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:44:074 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:34:44:168 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:34:44:172 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:34:44:176 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:44:273 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:34:44:382 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:44:474 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:34:44:492 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:34:44:579 ==>> 1

2025-07-31 22:34:44:594 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:44:609 ==>> A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:34:44:699 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:34:44:704 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<
$GBGGA,143448.516,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,39,25,,,39,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,12,,,36,1*4C

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,33,4,,,33,1*73

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143448.516,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143448.516,0.000,1529.176,1529.176,48.922,2097152,2097152,2097152*58



2025-07-31 22:34:44:804 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:34:44:826 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:34:44:831 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:34:44:834 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:34:44:940 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:34:44:970 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:34:45:000 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:34:45:104 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:34:45:108 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:34:45:111 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:34:45:166 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:34:45:196 ==>> 3A A3 03 01 A3 


2025-07-31 22:34:45:301 ==>> ON_OUT3
OVER 150


2025-07-31 22:34:45:381 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:34:45:385 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:34:45:390 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:34:45:500 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:34:45:654 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:34:45:662 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:34:45:682 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:34:45:725 ==>> $GBGGA,143449.516,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,40,59,,,40,34,,,40,1*7F

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,39,1*41

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,12,,,36,1*4C

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,33,32,,,33,4,,,33,1*74

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143449.516,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143449.516,0.000,1527.583,1527.583,48.872,2097152,2097152,2097152*5D

3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:34:45:991 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:34:45:995 ==>> 检测【左刹电压测试1】
2025-07-31 22:34:46:000 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:46:311 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:18][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:18][COMM]adc read left brake adc:1703  volt:2245 mv
[D][05:18:18][COMM]adc read right brake adc:1713  volt:2258 mv
[D][05:18:18][COMM]adc read throttle adc:1711  volt:2255 mv
[D][05:18:18][COMM]adc read battery ts volt:10 mv
[D][05:18:18][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2380  volt:3834 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:34:46:547 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:46:830 ==>> $GBGGA,143450.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,40,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,12,,,36,1*4C

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,33,1*74

$GBGSV,7,7,26,13,,,31,14,,,31,1*75

$GBRMC,143450.516,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143450.516,0.000,1535.550,1535.550,49.122,2097152,2097152,2097152*58

[D][05:18:18][HSDK][0] flush to flash addr:[0xE42C00] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1708  volt:2251 mv
[D][05:18:18][COMM]adc read right brake adc:1710  volt:2254 mv
[D][05:18:18][COMM]adc read throttle adc:1712  volt:2257 mv
[D][05:18:18][COMM]adc read battery ts volt:7 mv
[D][05:18:18][COMM]adc read in 24v adc:1277  volt:32299 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][C

2025-07-31 22:34:46:875 ==>> OMM]arm_hub adc read vbat adc:2381  volt:3836 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:34:47:110 ==>> 【左刹电压测试1】通过,【2251】符合目标值【2250】至【2500】要求!
2025-07-31 22:34:47:116 ==>> 检测【右刹电压测试1】
2025-07-31 22:34:47:169 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:34:47:181 ==>> 【右刹电压测试1】通过,【2254】符合目标值【2250】至【2500】要求!
2025-07-31 22:34:47:185 ==>> 检测【转把电压测试1】
2025-07-31 22:34:47:385 ==>> 【转把电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 22:34:47:391 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:34:47:396 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:34:47:509 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:34:47:683 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:34:47:687 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:34:47:693 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:34:47:697 ==>> $GBGGA,143451.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,34,,,40,1*7F

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,33,,,38,11,,,38,1*47

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,12,,,36,1*4C

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,34,1*73

$GBGSV,7,7,26,13,,,31,14,,,31,1*75

$GBRMC,143451.516,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143451.516,0.000,1540.334,1540.334,49.275,2097152,2097152,2097152*58



2025-07-31 22:34:47:794 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:34:47:964 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:34:47:968 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:34:47:994 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:34:48:102 ==>> 3A A3 05 00 A3 


2025-07-31 22:34:48:207 ==>> OFF_OUT5
OVER 150


2025-07-31 22:34:48:241 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:34:48:244 ==>> 检测【左刹电压测试2】
2025-07-31 22:34:48:247 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:34:48:513 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3118  volt:5480 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:20][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:20][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:20][COMM]adc read battery ts volt:7 mv
[D][05:18:20][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2381  volt:3836 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:34:48:692 ==>> $GBGGA,143452.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,34,,,40,1*7F

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,40,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,38,43,,,37,12,,,36,1*43

$GBGSV,7,5,26,2,,,36,10,,,36,6,,,36,9,,,35,1*4F

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,34,1*73

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143452.516,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143452.516,0.000,1538.744,1538.744,49.228,2097152,2097152,2097152*53



2025-07-31 22:34:48:830 ==>> 【左刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:34:48:834 ==>> 检测【右刹电压测试2】
2025-07-31 22:34:48:883 ==>> 【右刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:34:48:886 ==>> 检测【转把电压测试2】
2025-07-31 22:34:48:966 ==>> 【转把电压测试2】通过,【2】符合目标值【0】至【50】要求!
2025-07-31 22:34:48:972 ==>> 检测【晶振检测】
2025-07-31 22:34:48:985 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:34:49:188 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]
[D][05:18:21][COMM]read battery soc:255


2025-07-31 22:34:49:248 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:34:49:252 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:34:49:255 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:34:49:293 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1676mV
Get AD_V4 1651mV
Get AD_V5 2776mV
Get AD_V6 1989mV
Get AD_V7 1085mV
OVER 150


2025-07-31 22:34:49:528 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:34:49:533 ==>> 检测【检测BootVer】
2025-07-31 22:34:49:539 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:49:863 ==>> $GBGGA,143453.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,60,,,40,25,,,40,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,38,43,,,37,10,,,37,1*40

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,9,,,35,1*4D

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,34,1*73

$GBGSV,7,7,26,13,,,32,14,,,30,1*77

$GBRMC,143453.516,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143453.516,0.000,1540.332,1540.332,49.273,2097152,2097152,2097152*5C

[W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539892
[D][05:18:21][FCTY]HardwareID  = 867222087442242
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   =

2025-07-31 22:34:49:953 ==>>  DA39AF64C196
[D][05:18:21][FCTY]Bat         = 3924 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11700 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1282
[D][05:18:21][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3767 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:34:50:064 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:34:50:067 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:34:50:071 ==>> 检测【检测固件版本】
2025-07-31 22:34:50:086 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:34:50:093 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:34:50:108 ==>> 检测【检测蓝牙版本】
2025-07-31 22:34:50:112 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:34:50:115 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:34:50:130 ==>> 检测【检测MoBikeId】
2025-07-31 22:34:50:136 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:34:50:170 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:34:50:176 ==>> 检测【检测蓝牙地址】
2025-07-31 22:34:50:187 ==>> 取到目标值:DA39AF64C196
2025-07-31 22:34:50:191 ==>> 【检测蓝牙地址】通过,【DA39AF64C196】符合目标值【】要求!
2025-07-31 22:34:50:212 ==>> 提取到蓝牙地址:DA39AF64C196
2025-07-31 22:34:50:215 ==>> 检测【BOARD_ID】
2025-07-31 22:34:50:219 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:34:50:222 ==>> 检测【检测充电电压】
2025-07-31 22:34:50:243 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:34:50:247 ==>> 检测【检测VBUS电压1】
2025-07-31 22:34:50:265 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:34:50:268 ==>> 检测【检测充电电流】
2025-07-31 22:34:50:284 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:34:50:288 ==>> 检测【检测IMEI】
2025-07-31 22:34:50:292 ==>> 取到目标值:867222087442242
2025-07-31 22:34:50:304 ==>> 【检测IMEI】通过,【867222087442242】符合目标值【】要求!
2025-07-31 22:34:50:308 ==>> 提取到IMEI:867222087442242
2025-07-31 22:34:50:311 ==>> 检测【检测IMSI】
2025-07-31 22:34:50:315 ==>> 取到目标值:460130071539892
2025-07-31 22:34:50:340 ==>> 【检测IMSI】通过,【460130071539892】符合目标值【】要求!
2025-07-31 22:34:50:345 ==>> 提取到IMSI:460130071539892
2025-07-31 22:34:50:367 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:34:50:370 ==>> 取到目标值:460130071539892
2025-07-31 22:34:50:375 ==>> 【校验网络运营商(移动)】通过,【460130071539892】符合目标值【】要求!
2025-07-31 22:34:50:397 ==>> 检测【打开CAN通信】
2025-07-31 22:34:50:403 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:34:50:428 ==>> [D][05:18:22][COMM]IMU: [8,0,-1062] ret=32 AWAKE!


2025-07-31 22:34:50:501 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:34:50:640 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:34:50:647 ==>> 检测【检测CAN通信】
2025-07-31 22:34:50:653 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:34:50:713 ==>> $GBGGA,143454.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,40,34,,,40,1*7F

$GBGSV,7,2,27,3,,,40,39,,,40,25,,,40,60,,,39,1*40

$GBGSV,7,3,27,7,,,38,16,,,38,33,,,38,11,,,38,1*47

$GBGSV,7,4,27,1,,,38,24,,,37,43,,,37,10,,,36,1*4F

$GBGSV,7,5,27,12,,,36,2,,,36,6,,,36,9,,,35,1*4C

$GBGSV,7,6,27,23,,,35,5,,,34,32,,,34,4,,,34,1*72

$GBGSV,7,7,27,13,,,31,14,,,30,20,,,51,1*73

$GBRMC,143454.516,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143454.516,0.000,1533.956,1533.956,49.071,2097152,2097152,2097152*5B

can send success


2025-07-31 22:34:50:744 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:34:50:803 ==>> [D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34041
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:34:50:863 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:34:50:912 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:34:50:916 ==>> 检测【关闭CAN通信】
2025-07-31 22:34:50:922 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:34:50:942 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:34:51:013 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:34:51:163 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:34:51:183 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:34:51:188 ==>> 检测【打印IMU STATE】
2025-07-31 22:34:51:192 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:34:51:406 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:34:51:463 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:34:51:482 ==>> 检测【六轴自检】
2025-07-31 22:34:51:487 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:34:51:754 ==>> $GBGGA,143455.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,38,1*46

$GBGSV,7,4,26,1,,,38,24,,,37,43,,,37,10,,,36,1*4E

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,9,,,35,1*4D

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,34,1*73

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143455.516,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143455.516,0.000,1535.552,1535.552,49.124,2097152,2097152,2097152*5B

[W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:34:52:719 ==>> $GBGGA,143456.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,39,,,40,25,,,40,60,,,40,3,,,39,1*41

$GBGSV,7,3,26,7,,,38,16,,,38,33,,,38,11,,,37,1*49

$GBGSV,7,4,26,1,,,37,24,,,37,43,,,37,10,,,36,1*41

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,9,,,35,1*4D

$GBGSV,7,6,26,23,,,35,5,,,34,32,,,34,4,,,33,1*74

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143456.516,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143456.516,0.000,1529.174,1529.174,48.920,2097152,2097152,2097152*55



2025-07-31 22:34:53:192 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 22:34:53:404 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:34:53:509 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36755 imu self test OK ######


2025-07-31 22:34:53:539 ==>> [D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-19,-3,4075]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 22:34:53:579 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:34:53:587 ==>> 检测【打印IMU STATE2】
2025-07-31 22:34:53:594 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:34:53:644 ==>> $GBGGA,143457.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,26,39,,,40,25,,,40,60,,,40,3,,,39,1*41

$GBGSV,7,3,26,7,,,38,16,,,38,11,,,38,33,,,37,1*49

$GBGSV,7,4,26,1,,,37,24,,,37,43,,,37,10,,,36,1*41

$

2025-07-31 22:34:53:689 ==>> GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,9,,,35,1*4D

$GBGSV,7,6,26,23,,,34,32,,,34,5,,,33,4,,,33,1*72

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143457.516,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143457.516,0.000,1525.989,1525.989,48.822,2097152,2097152,2097152*57



2025-07-31 22:34:53:794 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:34:53:858 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:34:53:863 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:34:53:870 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:34:54:005 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:34:54:110 ==>> [D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D

2025-07-31 22:34:54:128 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:34:54:134 ==>> 检测【检测VBUS电压2】
2025-07-31 22:34:54:140 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:54:170 ==>> ][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 3,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 19
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 19


2025-07-31 22:34:54:488 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539892
[D][05:18:26][FCTY]HardwareID  = 867222087442242
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:26][FCTY]Bat         = 3904 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11600 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 8, adc = 330
[D][05:18:26][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18

2025-07-31 22:34:54:533 ==>> :26][FCTY]Bat1         = 3767 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 22:34:54:657 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:54:713 ==>> $GBGGA,143458.516,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,40,59,,,40,34,,,40,1*7F

$GBGSV,7,2,26,39,,,40,25,,,40,60,,,39,3,,,39,1*4F

$GBGSV,7,3,26,7,,,38,11,,,38,16,,,37,33,,,37,1*46

$GBGSV,7,4,26,1,,,37,24,,,37,43,,,37,10,,,36,1*41

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,9,,,35,1*4D

$GBGSV,7,6,26,23,,,34,32,,,34,5,,,34,4,,,33,1*75

$GBGSV,7,7,26,13,,,31,14,,,30,1*74

$GBRMC,143458.516,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143458.516,0.000,1522.793,1522.793,48.714,2097152,2097152,2097152*52



2025-07-31 22:34:54:983 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539892
[D][05:18:27][FCTY]HardwareID  = 867222087442242
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:27][FCTY]Bat         = 3904 mv
[D][05:18:27][FCTY]Current     = 100 ma
[D][05:18:27][FCTY]VBUS        = 11600 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 177
[D][05:18:27][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4

2025-07-31 22:34:55:028 ==>> 
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3767 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:34:55:199 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:55:572 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539892
[D][05:18:27][FCTY]HardwareID  = 867222087442242
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:27][FCTY]Bat         = 3904 mv
[D][05:18:27][FCTY]Current     = 100 ma
[D][05:18:27][FCTY]VBUS        = 11600 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 152
[D][05:18:27][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3767 mv
[D][05:18:2

2025-07-31 22:34:55:602 ==>> 7][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:34:55:707 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:34:55:725 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:55:812 ==>> [D][05:18:28][COMM]msg 0601 loss. last_tick:34030. cur_tick:39038. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39039


2025-07-31 22:34:56:446 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539892
[D][05:18:28][FCTY]HardwareID  = 867222087442242
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:28][FCTY]Bat         = 3644 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 7800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 128
[D][05:18:28][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VE

2025-07-31 22:34:56:521 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:56:551 ==>> RSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3767 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:28][COMM]Main Task receive event:65
[D][05:18:28][COMM]main task tmp_sleep_event = 80
[D][05:18:28][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:28][COMM]Main Task receive event:65 finished processing
[D][05:18:28][COMM]Main Task receive event:60
[D][05:18:28][COMM]smart_helmet_vol=255,255
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[W][05:18:28][GNSS]stop locating
[D][05:18:28][GNSS]stop event:8
[D][05:18:28][GNSS]GPS stop. ret=0
[D][05:18:28][GNSS]all continue location stop
[D][05:18:28][COMM]report elecbike
[W][05:18:28][PROT]remove success[1629955108],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:28][HSDK][0] flush to flash addr:[0xE42D00] --- write len --- [256]
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]index:0
[D][05:18

2025-07-31 22:34:56:656 ==>> :28][PROT]is_send:1
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg_type:0x5d03
[D][05:18:28][PROT]===========================================================
[D][05:18:28][CAT1]gsm read msg sub id: 24
[D][05:18:28][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900005]
[D][05:18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPA

2025-07-31 22:34:56:762 ==>> TH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[198]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 24, ret: 6
[D][05:18:28][CAT1]sub id: 24, ret: 6

[D][05:18:28][CAT1]gsm read msg sub id: 15
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113

2025-07-31 22:34:56:836 ==>> 311331133113311331B88B57152F46192A651448D5A64287287D78604275F4D6F45CC77EFD3C757F92E333E826C3DBAA2DA62D2108C2E85C622A8D4F2AC85E9ACA11AB72604E7E80814F943B9EB535B3BFE10C02ADA41EF6AEFCDF64D05
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]


2025-07-31 22:34:57:123 ==>>                                                                                                                                            [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539892
[D][05:18:29][FCTY]HardwareID  = 867222087442242
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:29][FCTY]Bat         = 3624 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 4900 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 0,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:29][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 25 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D

2025-07-31 22:34:57:183 ==>> ][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3767 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:34:57:306 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:34:57:672 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539892
[D][05:18:29][FCTY]HardwareID  = 867222087442242
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:18:29][FCTY]Bat         = 3744 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 4900 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:29][FCTY]Acckey1 vol = 5489 mv, Acckey2 vol = 25 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3767 mv
[D][05:18:29][

2025-07-31 22:34:57:702 ==>> FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:34:57:845 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:34:57:853 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:34:57:874 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:34:57:902 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:34:58:004 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 19
[D][05:18:30][COMM]read battery soc:255


2025-07-31 22:34:58:133 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:34:58:138 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:34:58:154 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:34:58:200 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:34:58:415 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:34:58:420 ==>> 检测【打开WIFI(3)】
2025-07-31 22:34:58:428 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:34:58:630 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:34:58:693 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:34:58:702 ==>> 检测【扩展芯片hw】
2025-07-31 22:34:58:725 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:34:58:900 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:34:58:964 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:34:58:969 ==>> 检测【扩展芯片boot】
2025-07-31 22:34:58:983 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:34:58:988 ==>> 检测【扩展芯片sw】
2025-07-31 22:34:59:001 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:34:59:007 ==>> 检测【检测音频FLASH】
2025-07-31 22:34:59:014 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:34:59:185 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:34:59:336 ==>> +WIFISCAN:4,0,CC057790A7C0,-74
+WIFISCAN:4,1,CC057790A7C1,-74
+WIFISCAN:4,2,CC057790A641,-75
+WIFISCAN:4,3,CC057790A640,-75

[D][05:18:31][CAT1]wifi scan report total[4]


2025-07-31 22:35:00:271 ==>> [D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------
[D][05:18:31][GNSS]recv submsg id[3]
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and va

2025-07-31 22:35:00:376 ==>> lue:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]Main Task rec

2025-07-31 22:35:00:481 ==>> eive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][PROT]index:1
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]

2025-07-31 22:35:00:586 ==>> sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task rece

2025-07-31 22:35:00:691 ==>> ive event:61 finished processing
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[9

2025-07-31 22:35:00:781 ==>> 75].hexsend, index:3, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:32][COMM]read battery soc:255


2025-07-31 22:35:01:633 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3A53D06CBD478C9480AF6FCB43BD19A97D3D90B4EE16324AB841AB049A14028E5581382D93CA10681E0B325BAAD221E6BDB03A8BC9001B7845B915

2025-07-31 22:35:01:694 ==>> D5F584D59707286ABC3DE60FF01C6FED331ECCE87DF7493
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 22:35:02:045 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 22:35:02:410 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:35:02:605 ==>> [D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:35:03:305 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 22:35:03:610 ==>> [D][05:18:35][COMM]46851 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:35:03:715 ==>>                                                   s].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[86

2025-07-31 22:35:03:745 ==>> 3].recv ok
[D][05:18:35][COMM]accel parse set 0
[D][05:18:35][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:35:04:049 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 22:35:04:090 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:35:04:101 ==>> 检测【打开喇叭声音】
2025-07-31 22:35:04:114 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:35:04:847 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:36][COMM]read file, len:15228, num:4
[D][05:18:36][COMM]--->crc16:0x419c
[D][05:18:36][COMM]read file success
[D][05:18:36][HSDK]need to erase for write: is[0x0] ie[0x1E00]
[D][05:18:36][HSDK][0] flush to flash addr:[0xE42E00] --- write len --- [256]
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434

2025-07-31 22:35:04:908 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:35:04:915 ==>> 检测【打开大灯控制】
2025-07-31 22:35:04:925 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:35:04:952 ==>> B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D]

2025-07-31 22:35:05:057 ==>> [05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]47862 imu init OK
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:35:05:147 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:35:05:244 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:35:05:256 ==>> 检测【关闭仪表供电3】
2025-07-31 22:35:05:271 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:35:05:391 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 22:35:05:515 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:35:05:525 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:35:05:547 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:35:05:664 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:35:05:793 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:35:05:799 ==>> 检测【读大灯电压】
2025-07-31 22:35:05:815 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:35:05:995 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[32760]


2025-07-31 22:35:06:040 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 22:35:06:082 ==>> 【读大灯电压】通过,【32760mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:35:06:088 ==>> 检测【关闭大灯控制2】
2025-07-31 22:35:06:118 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:35:06:266 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:35:06:360 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:35:06:369 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:35:06:392 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:35:06:843 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]
[D][05:18:38][PROT]CLEAN,SEND:1
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>

2025-07-31 22:35:06:902 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:35:06:922 ==>> 检测【打开WIFI(4)】
2025-07-31 22:35:06:930 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:35:06:953 ==>> > AT+QISEND=0,198

[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B38882BF0CF8E41CB3E61458893F2CF971820748E6EC900C49618D38D48CE95F2B2C424223E6860710A9F8A07E06C377825E9068733D2ECF745336A8AEFE4A894DC473C185455827F4B272C74F601C42D21C80
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]


2025-07-31 22:35:07:129 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:39][CAT1]gsm read msg sub id: 12
[D][05:18:39][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:39][CAT1]<<< 
OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:35:07:223 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:35:07:229 ==>> 检测【EC800M模组版本】
2025-07-31 22:35:07:238 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:35:07:310 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:35:07:400 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:39][CAT1]gsm read msg sub id: 12
[D][05:18:39][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:35:07:640 ==>> [D][05:18:39][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:35:07:749 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:35:07:756 ==>> 检测【配置蓝牙地址】
2025-07-31 22:35:07:764 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:35:07:880 ==>> +WIFISCAN:4,0,CC057790A641,-72
+WIFISCAN:4,1,CC057790A640,-73
+WIFISCAN:4,2,CC057790A7C0,-74
+WIFISCAN:4,3,CC057790A7C1,-74

[D][05:18:40][CAT1]wifi scan report total[4]
[W][05:18:40][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:35:07:955 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:DA39AF64C196>】
2025-07-31 22:35:08:064 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 22:35:08:109 ==>> recv ble 1
recv ble 2
ble set mac ok :da,39,af,64,c1,96
enable filters ret : 0

2025-07-31 22:35:08:232 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:35:08:242 ==>> 检测【BLETEST】
2025-07-31 22:35:08:248 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:35:08:335 ==>> 4A A4 01 A4 4A 
[D][05:18:40][COMM]51565 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:35:08:410 ==>> recv ble 1
recv ble 2
<BSJ*MAC:DA39AF64C196*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9DA39AF64C19699999OVER 150


2025-07-31 22:35:08:638 ==>> [D][05:18:40][GNSS]recv submsg id[3]


2025-07-31 22:35:09:284 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:35:09:290 ==>> 该项需要延时执行
2025-07-31 22:35:09:352 ==>> [D][05:18:41][COMM]52576 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:35:09:968 ==>> [D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:35:10:073 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 22:35:10:329 ==>> [D][05:18:42][COMM]53588 imu init OK


2025-07-31 22:35:12:061 ==>> [D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][PROT]index:1 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:5
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:1
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900006]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B

2025-07-31 22:35:12:136 ==>> 981113311331133113311331B88B360078E463C9E617BDDB1F422321F7CFD0CA48235A853F7DE9D88FB77FE50AE12335BCA714C4988939F3E86E8CFED44E4CDDD58A0384A54FB21D2942026495AEBE39A373658EBA59850537C79A140D8CF604A
[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][05:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]


2025-07-31 22:35:12:166 ==>>                                          

2025-07-31 22:35:14:069 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 22:35:16:086 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 22:35:17:309 ==>> [D][05:18:49][PROT]CLEAN,SEND:1
[D][05:18:49][PROT]CLEAN:1
[D][05:18:49][PROT]index:0 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:4
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:2
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900005]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B58F5019565BABBF5D71EEDE2B42C3E411C2FDA

2025-07-31 22:35:17:384 ==>> 2C84CFE10BC2F2DBF7411807D29980C99FCA4E658B57405B343EB06293AE4460F3C0F9C7E05B6265D9F27F900791B9FC710347A7F498779AD1473F78A092970
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 22:35:18:080 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:35:19:296 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:35:19:302 ==>> 检测【检测WiFi结果】
2025-07-31 22:35:19:312 ==>> WiFi信号:【CC057790A641】,信号值:-67
2025-07-31 22:35:19:320 ==>> WiFi信号:【CC057790A7C0】,信号值:-67
2025-07-31 22:35:19:330 ==>> WiFi信号:【CC057790A640】,信号值:-71
2025-07-31 22:35:19:344 ==>> WiFi信号:【44A1917CAD81】,信号值:-76
2025-07-31 22:35:19:353 ==>> WiFi信号:【CC057790A7C1】,信号值:-74
2025-07-31 22:35:19:376 ==>> WiFi数量【5】, 最大信号值:-67
2025-07-31 22:35:19:381 ==>> 检测【检测GPS结果】
2025-07-31 22:35:19:393 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:35:19:497 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all continue location stop
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all sing location stop


2025-07-31 22:35:20:092 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 22:35:20:303 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:35:20:312 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:20:339 ==>> 定位已等待【1】秒.
2025-07-31 22:35:20:703 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:52][COMM]Open GPS Module...
[D][05:18:52][COMM]LOC_MODEL_CONT
[D][05:18:52][GNSS]start event:8
[D][05:18:52][GNSS]GPS start. ret=0
[W][05:18:52][GNSS]start cont locating
[D][05:18:52][CAT1]gsm read msg sub id: 23
[D][05:18:52][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:52][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:35:21:315 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:21:334 ==>> 定位已等待【2】秒.
2025-07-31 22:35:21:422 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:35:22:292 ==>> [D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:54][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,34,,,41,60,,,40,59,,,39,33,,,37,1*7F

[D][05:18:54][CAT1]<<< 
OK

$GBGSV,3,2,09,3,,,58,4,,,40,25,,,39,16,,,38,1*71

$GBGSV,3,3,09,24,,,37,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1627.163,1627.163,51.979,2097152,2097152,2097152*4C

[D][05:18:54][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:54][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]exec over: func id: 23, ret: 6
[D][05:18:54][CAT1]sub id: 23, ret: 6



2025-07-31 22:35:22:322 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:22:330 ==>> 定位已等待【3】秒.
2025-07-31 22:35:22:549 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B59F5531DB7D66ACC32646E1937A8EB61D0FF60E972301D14EC6295FA926E

2025-07-31 22:35:22:624 ==>> 8D0D7D296DD429EC277D573B860915B26742860B60CBC5733CA23285FC525E41195C6D2550285CDB3B84CF62ABF3A847B026CAC60
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]


2025-07-31 22:35:22:729 ==>> [D][05:18:54][GNSS]recv submsg id[1]
[D][05:18:54][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:35:23:213 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,34,,,40,60,,,40,59,,,40,41,,,40,1*7E

$GBGSV,4,2,14,39,,,40,25,,,39,7,,,38,16,,,37,1*49

$GBGSV,4,3,14,24,,,37,33,,,37,40,,,37,44,,,37,1*76

$GBGSV,4,4,14,2,,,37,3,,,39,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1591.283,1591.283,50.830,2097152,2097152,2097152*41



2025-07-31 22:35:23:333 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:23:342 ==>> 定位已等待【4】秒.
2025-07-31 22:35:24:103 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 22:35:24:208 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,34,,,40,60,,,40,59,,,40,41,,,40,1*72

$GBGSV,5,2,19,39,,,40,3,,,40,25,,,39,40,,,39,1*43

$GBGSV,5,3,19,7,,,38,16,,,37,24,,,37,33,,,37,1*41

$GBGSV,5,4,19,1,,,37,44,,,36,2,,,36,32,,,35,1*7F

$GBGSV,5,5,19,5,,,34,4,,,32,23,,,36,1*7D

$GBRMC,,

2025-07-31 22:35:24:238 ==>> V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1559.263,1559.263,49.853,2097152,2097152,2097152*4C



2025-07-31 22:35:24:343 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:24:352 ==>> 定位已等待【5】秒.
2025-07-31 22:35:25:262 ==>> $GBGGA,143529.095,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,34,,,40,60,,,40,59,,,40,41,,,40,1*72

$GBGSV,5,2,19,39,,,40,3,,,40,40,,,40,25,,,39,1*4D

$GBGSV,5,3,19,7,,,38,1,,,38,16,,,37,24,,,37,1*7F

$GBGSV,5,4,19,33,,,37,44,,,35,2,,,35,32,,,34,1*4F

$GBGSV,5,5,19,10,,,34,5,,,33,4,,,32,1*78

$GBRMC,143529.095,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143529.095,0.000,1547.038,1547.038,49.480,2097152,2097152,2097152*54



2025-07-31 22:35:25:352 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:25:362 ==>> 定位已等待【6】秒.
2025-07-31 22:35:25:774 ==>> $GBGGA,143529.595,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,34,,,40,60,,,40,59,,,40,1*78

$GBGSV,6,2,23,41,,,40,39,,,40,3,,,40,25,,,39,1*46

$GBGSV,6,3,23,7,,,39,24,,,38,1,,,37,16,,,37,1*74

$GBGSV,6,4,23,33,,,37,44,,,35,2,,,35,10,,,35,1*44

$GBGSV,6,5,23,32,,,34,5,,,33,6,,,33,4,,,32,1*44

$GBGSV,6,6,23,9,,,31,12,,,31,11,,,38,1*46

$GBRMC,143529.595,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143529.595,0.000,1520.784,1520.784,48.669,2097152,2097152,2097152*55



2025-07-31 22:35:26:110 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 22:35:26:366 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:26:376 ==>> 定位已等待【7】秒.
2025-07-31 22:35:26:754 ==>> $GBGGA,143530.575,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,34,,,40,60,,,40,59,,,40,1*7F

$GBGSV,6,2,24,41,,,40,39,,,40,3,,,40,25,,,39,1*41

$GBGSV,6,3,24,7,,,38,24,,,38,1,,,38,16,,,37,1*7D

$GBGSV,6,4,24,33,,,37,10,,,36,44,,,35,2,,,35,1*40

$GBGSV,6,5,24,12,,,35,11,,,34,32,,,34,6,,,34,1*46

$GBGSV,6,6,24,5,,,33,4,,,33,9,,,33,43,,,33,1*4F

$GBRMC,143530.575,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143530.575,0.000,1525.314,1525.314,48.791,2097152,2097152,2097152*55



2025-07-31 22:35:27:376 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:27:385 ==>> 定位已等待【8】秒.
2025-07-31 22:35:27:800 ==>> [D][05:18:59][PROT]CLEAN,SEND:0
[D][05:18:59][PROT]CLEAN:0
[D][05:18:59][PROT]index:2 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:6
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:3
[D][05:18:59][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:18:59][PROT]sending traceid [9999999999900007]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[134]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:59][CAT1]

2025-07-31 22:35:27:905 ==>> gsm read msg sub id: 15
[D][05:18:59][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:59][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE99FFF1648609D88D2B1C0810A51CD59A39596E107AB18164934B350CA0392AA3F375C5E9764BC06905FBB63AA2D5CC1E1C04
[D][05:18:59][CAT1]<<< 
SEND OK

[D][05:18:59][CAT1]exec over: func id: 15, ret: 11
[D][05:18:59][CAT1]sub id: 15, ret: 11

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:59][M2M ]g_m2m_is_idle become true
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:59][PROT]M2M Send ok [1629955139]
$GBGGA,143531.555,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,41,34,,,40,60,,,40,1*77

$GBGSV,6,2,24,59,,,40,39,,,40,3,,,40,25,,,39,1*48

$GBGSV,6,3,24,7,,,38,24,,,38,1,,,37,16,,,37,1*72

$GBGSV,6,4,24,33,,,37,10,,,36,11,,,36,44,,,35,1*71

$GBGSV,6,5,24,2,,,35,12,,,35,6,,,35,43,,,35,1*73

$GBGSV,6,6,24,32,,,34,9,,,34,5,,,33,4,,,32,1*48

$GBRMC,143531.555,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143

2025-07-31 22:35:27:935 ==>> 531.555,0.000,765.532,765.532,700.096,2097152,2097152,2097152*6D



2025-07-31 22:35:28:101 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:35:28:390 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:28:400 ==>> 定位已等待【9】秒.
2025-07-31 22:35:28:729 ==>> $GBGGA,143532.535,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,24,39,,,40,59,,,40,34,,,40,25,,,39,1*7C

$GBGSV,6,3,24,7,,,38,24,,,38,16,,,37,1,,,37,1*72

$GBGSV,6,4,24,11,,,37,33,,,37,10,,,36,12,,,36,1*70

$GBGSV,6,5,24,43,,,36,2,,,35,6,,,35,44,,,35,1*73

$GBGSV,6,6,24,9,,,34,32,,,34,5,,,33,4,,,32,1*48

$GBRMC,143532.535,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143532.535,0.000,768.115,768.115,702.457,2097152,2097152,2097152*63



2025-07-31 22:35:29:400 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:29:410 ==>> 定位已等待【10】秒.
2025-07-31 22:35:29:705 ==>> $GBGGA,143533.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,24,59,,,40,39,,,40,34,,,40,7,,,39,1*4C

$GBGSV,6,3,24,25,,,39,24,,,38,1,,,37,16,,,37,1*43

$GBGSV,6,4,24,11,,,37,33,,,37,10,,,36,12,,,36,1*70

$GBGSV,6,5,24,43,,,36,2,,,35,6,,,35,44,,,35,1*73

$GBGSV,6,6,24,9,,,34,32,,,34,5,,,33,4,,,32,1*48

$GBRMC,143533.515,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143533.515,0.000,768.978,768.978,703.247,2097152,2097152,2097152*66



2025-07-31 22:35:30:121 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:35:30:408 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:30:418 ==>> 定位已等待【11】秒.
2025-07-31 22:35:30:714 ==>> $GBGGA,143534.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,60,,,40,3,,,40,1*43

$GBGSV,7,2,25,39,,,40,59,,,40,34,,,40,25,,,40,1*72

$GBGSV,7,3,25,7,,,39,24,,,38,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,33,,,37,43,,,37,10,,,36,1*44

$GBGSV,7,5,25,12,,,36,2,,,35,6,,,35,9,,,35,1*4E

$GBGSV,7,6,25,44,,,35,32,,,34,5,,,33,4,,,32,1*70

$GBGSV,7,7,25,14,,,31,1*76

$GBRMC,143534.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143534.515,0.000,767.197,767.197,701.618,2097152,2097152,2097152*6D



2025-07-31 22:35:31:414 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:31:424 ==>> 定位已等待【12】秒.
2025-07-31 22:35:31:705 ==>> $GBGGA,143535.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,60,,,40,3,,,40,1*43

$GBGSV,7,2,25,39,,,40,59,,,40,34,,,40,7,,,39,1*4C

$GBGSV,7,3,25,25,,,39,24,,,38,11,,,38,16,,,37,1*7D

$GBGSV,7,4,25,1,,,37,33,,,37,43,,,37,10,,,36,1*44

$GBGSV,7,5,25,12,,,36,2,,,35,6,,,35,9,,,35,1*4E

$GBGSV,7,6,25,44,,,35,5,,,34,32,,,34,4,,,33,1*76

$GBGSV,7,7,25,14,,,31,1*76

$GBRMC,143535.515,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143535.515,0.000,768.016,768.016,702.367,2097152,2097152,2097152*62



2025-07-31 22:35:32:121 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:35:32:429 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:32:440 ==>> 定位已等待【13】秒.
2025-07-31 22:35:32:990 ==>> $GBGGA,143532.522,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,39,3,60,190,40,1*74

$GBGSV,7,2,25,39,60,35,40,16,57,9,38,59,52,129,40,25,50,353,39,1*45

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,35,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,40,33,33,200,37,1*7B

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,31,1*74

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBRMC,143532.522,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,143532.522,0.234,0.155,0.152,0.266,1.757,2.088,12*59

[D][05:19:04][PROT]CLEAN,SEND:2
[D][05:19:04][PROT]index:2 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:6
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:2
[D][05:19:04][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:05][PROT]===========================================================
[W][05:19:05][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A

2025-07-31 22:35:33:095 ==>> 0908A7C89C8906980220
[D][05:19:05][PROT]sending traceid [9999999999900007]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[134]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][05:19:05][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:05][CAT1]Send Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BE344C2E1C6EC59C4B9B6AD35AC88B66D8A2893F502AB17736D434BEE372E83AEE46A925E4552E25EC1886FE8C6D940554711F
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:

2025-07-31 22:35:33:125 ==>> 05][PROT]M2M Send ok [1629955145]


2025-07-31 22:35:33:443 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:33:453 ==>> 定位已等待【14】秒.
2025-07-31 22:35:33:773 ==>> $GBGGA,143533.502,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,39,3,60,190,39,1*7A

$GBGSV,7,2,25,39,60,35,40,16,57,9,38,59,52,129,40,25,50,353,39,1*45

$GBGSV,7,3,25,10,49,223,36,1,48,125,38,9,46,332,35,2,45,237,35,1*43

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,33,33,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,31,1*74

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,40,39,60,35,41,25,50,353,40,34,44,106,39,5*45

$GBGSV,2,2,06,41,42,266,40,33,33,200,36,5*76

$GBRMC,143533.502,V,,,,,,,310725,1.0,E,N,V*49

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,143533.502,1.346,0.198,0.193,0.346,1.680,1.824,8.132*73



2025-07-31 22:35:34:150 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:35:34:451 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:34:458 ==>> 定位已等待【15】秒.
2025-07-31 22:35:34:770 ==>> $GBGGA,143534.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,39,3,60,190,40,1*74

$GBGSV,7,2,25,39,60,35,40,16,57,9,38,59,52,129,40,25,50,353,39,1*45

$GBGSV,7,3,25,10,49,223,36,1,48,125,38,9,46,332,35,2,45,237,34,1*42

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,33,33,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,31,1*74

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,41,33,33,200,37,5*76

$GBRMC,143534.502,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,143534.502,1.223,0.203,0.199,0.349,1.339,1.440,6.372*73



2025-07-31 22:35:35:456 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:35:35:462 ==>> 定位已等待【16】秒.
2025-07-31 22:35:36:361 ==>> $GBGGA,143535.502,2301.2575114,N,11421.9431613,E,1,07,1.35,84.689,M,-1.770,M,,*56

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.25,1.35,4.03,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,39,3,60,190,40,1*74

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,35,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,40,33,33,200,37,1*7B

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,31,1*74

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,41,33,33,200,38,5*79

$GBRMC,143535.502,A,2301.2575114,N,11421.9431613,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

[D][05:19:07][GNSS]HD8040 GPS
[D][05:19:07][GNSS]GPS diff_sec 124017388, report 0x42 frame
$GBGST,143535.502,1.035,0.241,0.235,0.421,1.061,1.144,5.350*71

[D][05:19:07][COMM]Main Task receive event:131
[D][05:19:07][COMM]index:0,power_mode:0xFF
[D][05:19:07][COMM]index:1,sound_mode:0xFF
[D][05:19:07][COMM]index:2,gsensor_mode:

2025-07-31 22:35:36:466 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 22:35:36:485 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【7】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【16】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【25】,信号值:【41】
北斗星号:【10】,信号值:【36】
北斗星号:【1】,信号值:【37】
北斗星号:【9】,信号值:【35】
北斗星号:【2】,信号值:【35】
北斗星号:【34】,信号值:【40】
北斗星号:【41】,信号值:【41】
北斗星号:【60】,信号值:【40】
北斗星号:【33】,信号值:【38】
北斗星号:【24】,信号值:【38】
北斗星号:【44】,信号值:【35】
北斗星号:【11】,信号值:【38】
北斗星号:【43】,信号值:【37】
北斗星号:【12】,信号值:【36】

2025-07-31 22:35:36:492 ==>> 0xFF
[D][05:19:07][COMM]index:3,report_freq_mode:0xFF
[D][05:19:07][COMM]index:4,report_period:0xFF
[D][05:19:07][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:07][COMM]index:6,normal_reset_period:0xFF
[D][05:19:07][COMM]index:7,spock_over_speed:0xFF
[D][05:19:07][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:07][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:07][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:07][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:07][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:08][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:08][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:08][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:08][COMM]index:16,imu_config_params:0xFF
[D][05:19:08][COMM]index:17,long_connect_params:0xFF
[D][05:19:08][COMM]index:18,detain_mark:0xFF
[D][05:19:08][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:08][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:08][COMM]index:21,mc_mode:0xFF
[D][05:19:08][COMM]index:22,S_mode:0xFF
[D][05:19:08][COMM]index:23,overweight:0xFF
[D][05:19:08][COMM]index:24,standstill_mode:0xFF
[D][05:19:08][COMM]index:25,night_mode:0xFF
[D][05:19:08][COMM]index:

2025-07-31 22:35:36:516 ==>> 检测【CSQ强度】
2025-07-31 22:35:36:523 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:35:36:571 ==>> 26,experiment1:0xFF
[D][05:19:08][COMM]index:27,experiment2:0xFF
[D][05:19:08][COMM]index:28,experiment3:0xFF
[D][05:19:08][COMM]index:29,experiment4:0xFF
[D][05:19:08][COMM]index:30,night_mode_start:0xFF
[D][05:19:08][COMM]index:31,night_mode_end:0xFF
[D][05:19:08][COMM]index:33,park_report_minutes:0xFF
[D][05:19:08][COMM]index:34,park_report_mode:0xFF
[D][05:19:08][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:08][COMM]index:38,charge_battery_para: FF
[D][05:19:08][COMM]index:39,multirider_mode:0xFF
[D][05:19:08][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:08][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:08][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:08][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:08][COMM]index:44,riding_duration_config:0xFF
[D][05:19:08][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:08][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:08][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:08][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:08][COMM]index:49,mc_load_startup:0xFF
[D][05:19:08][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:08][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:08][COMM]index:52,traffic_mode

2025-07-31 22:35:36:676 ==>> :0xFF
[D][05:19:08][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:08][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:08][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:08][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:08][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:08][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:08][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:08][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:08][COMM]index:63,experiment5:0xFF
[D][05:19:08][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:08][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:08][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:08][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:08][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:08][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:08][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:08][COMM]index:72,experiment6:0xFF
[D][05:19:08][COMM]index:73,experiment7:0xFF
[D][05:19:08][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:08][COMM]index:75,zero_value_from_server:-1
[D][05:19:08][COMM]index:76,multirider_threshold:255
[D][05:19:08][COMM]inde

2025-07-31 22:35:36:781 ==>> x:77,experiment8:255
[D][05:19:08][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:08][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:08][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:08][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:08][COMM]index:83,loc_report_interval:255
[D][05:19:08][COMM]index:84,multirider_threshold_p2:255
[D][05:19:08][COMM]index:85,multirider_strategy:255
[D][05:19:08][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:08][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:08][COMM]index:90,weight_param:0xFF
[D][05:19:08][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:08][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:08][COMM]index:95,current_limit:0xFF
[D][05:19:08][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:08][COMM]index:100,location_mode:0xFF

[W][05:19:08][PROT]remove success[1629955148],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:08][HSDK][0] flush to flash addr:[0xE42F00] --- write len --- [256]
[D][05:19:08][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:08][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][0

2025-07-31 22:35:36:886 ==>> 5:19:08][PROT]add success [1629955148],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:08][COMM]Main Task receive event:131 finished processing
$GBGGA,143536.002,2301.2575613,N,11421.9433025,E,1,07,1.35,84.024,M,-1.770,M,,*50

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.25,1.35,4.03,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,35,7,61,222,39,3,60,190,40,1*77

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,34,2,45,237,35,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,40,33,33,200,37,1*7B

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,35,11,,,37,43,,,37,12,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,41,33,33,200,38,5*79

$GBRMC,143536.002,A,2301.2575613,N,11421.9433025,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,143536.002,1.077,0.240,0.234,0.413,1.013,1.081,4.736*78

[D][05:19:08][COMM]read battery soc:255


2025-07-31 22:35:37:036 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:09][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:35:37:292 ==>> $GBGGA,143537.000,2301.2575929,N,11421.9433381,E,1,07,1.35,83.761,M,-1.770,M,,*59

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.25,1.35,4.03,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,38,3,60,190,40,1*75

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,34,2,45,237,35,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,33,33,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,37,14,13,321,31,1*7B

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,42,33,33,200,38,5*7A

$GBRMC,143537.000,A,2301.2575929,N,11421.9433381,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,143537.000,1.222,0.238,0.233,0.412,1.080,1.132,4.350*77



2025-07-31 22:35:37:340 ==>> 【CSQ强度】通过,【22】符合目标值【18】至【31】要求!
2025-07-31 22:35:37:357 ==>> 检测【关闭GSM联网】
2025-07-31 22:35:37:368 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:35:37:489 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:09][COMM]GSM test
[D][05:19:09][COMM]GSM test disable


2025-07-31 22:35:37:613 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:35:37:633 ==>> 检测【4G联网测试】
2025-07-31 22:35:37:647 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:35:38:565 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:10][COMM]Main Task receive event:14
[D][05:19:10][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955150, allstateRepSeconds = 0
[D][05:19:10][COMM]index:0,power_mode:0xFF
[D][05:19:10][COMM]index:1,sound_mode:0xFF
[D][05:19:10][COMM]index:2,gsensor_mode:0xFF
[D][05:19:10][COMM]index:3,report_freq_mode:0xFF
[D][05:19:10][COMM]index:4,report_period:0xFF
[D][05:19:10][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:10][COMM]index:6,normal_reset_period:0xFF
[D][05:19:10][COMM]index:7,spock_over_speed:0xFF
[D][05:19:10][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:10][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:10][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:10][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:10][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:10][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:10][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:10][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:10][COMM]index:16,imu_config_params:0xFF
[D][05:19:10][COMM]index:17,long_connect_params:0xFF
[D][05:19:10][COMM]index:18,detain_mark:0xFF
[D][05:19:10][COMM]index:19,lo

2025-07-31 22:35:38:670 ==>> ck_pos_report_count:0xFF
[D][05:19:10][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:10][COMM]index:21,mc_mode:0xFF
[D][05:19:10][COMM]index:22,S_mode:0xFF
[D][05:19:10][COMM]index:23,overweight:0xFF
[D][05:19:10][COMM]index:24,standstill_mode:0xFF
[D][05:19:10][COMM]index:25,night_mode:0xFF
[D][05:19:10][COMM]index:26,experiment1:0xFF
[D][05:19:10][COMM]index:27,experiment2:0xFF
[D][05:19:10][COMM]index:28,experiment3:0xFF
[D][05:19:10][COMM]index:29,experiment4:0xFF
[D][05:19:10][COMM]index:30,night_mode_start:0xFF
[D][05:19:10][COMM]index:31,night_mode_end:0xFF
[D][05:19:10][COMM]index:33,park_report_minutes:0xFF
[D][05:19:10][COMM]index:34,park_report_mode:0xFF
[D][05:19:10][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:10][COMM]index:38,charge_battery_para: FF
[D][05:19:10][COMM]index:39,multirider_mode:0xFF
[D][05:19:10][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:10][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:10][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:10][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:10][COMM]index:44,riding_duration_config:0xFF
[D][05:19:10][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05

2025-07-31 22:35:38:775 ==>> :19:10][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:10][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:10][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:10][COMM]index:49,mc_load_startup:0xFF
[D][05:19:10][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:10][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:10][COMM]index:52,traffic_mode:0xFF
[D][05:19:10][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:10][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:10][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:10][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:10][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:10][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:10][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:10][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:10][COMM]index:63,experiment5:0xFF
[D][05:19:10][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:10][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:10][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:10][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:10][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:10][COMM]index:70,camera_park_light_cfg:0xFF
[D

2025-07-31 22:35:38:880 ==>> ][05:19:10][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:10][COMM]index:72,experiment6:0xFF
[D][05:19:10][COMM]index:73,experiment7:0xFF
[D][05:19:10][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:10][COMM]index:75,zero_value_from_server:-1
[D][05:19:10][COMM]index:76,multirider_threshold:255
[D][05:19:10][COMM]index:77,experiment8:255
[D][05:19:10][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:10][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:10][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:10][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:10][COMM]index:83,loc_report_interval:255
[D][05:19:10][COMM]index:84,multirider_threshold_p2:255
[D][05:19:10][COMM]index:85,multirider_strategy:255
[D][05:19:10][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:10][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:10][COMM]index:90,weight_param:0xFF
[D][05:19:10][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:10][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:10][COMM]index:95,current_limit:0xFF
[D][05:19:10][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05

2025-07-31 22:35:38:985 ==>> :19:10][COMM]index:100,location_mode:0xFF

[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:10][PROT]remove success[1629955150],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:10][PROT]index:0 1629955150
[D][05:19:10][PROT]is_send:0
[D][05:19:10][PROT]sequence_num:8
[D][05:19:10][PROT]retry_timeout:0
[D][05:19:10][PROT]retry_times:1
[D][05:19:10][PROT]send_path:0x2
[D][05:19:10][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:10][PROT]===========================================================
[W][05:19:10][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955150]
[D][05:19:10][PROT]===========================================================
[D][05:19:10][PROT]sending traceid [9999999999900009]
[D][05:19:10][PROT]Send_TO_M2M [1629955150]
[W][05:19:10][PROT]add success [1629955150],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:10][CAT1]gsm read msg sub id: 13
[D][05:19:10][SAL ]sock send credit cnt[6]
[D][05:19:10][SAL ]sock send ind credit cnt[6]
[D][05:19:10][M2M ]m2m send data len[294]
[D][05:19:10][SAL ]Cellular t

2025-07-31 22:35:39:090 ==>> ask submsg id[10]
[D][05:19:10][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:10][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:10][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:10][CAT1]exec over: func id: 13, ret: 21
[D][05:19:10][M2M ]get csq[22]
[D][05:19:10][CAT1]gsm read msg sub id: 15
[D][05:19:10][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:10][CAT1]<<< 
ERROR

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:10][PROT]remove success[1629955150],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:10][COMM]------>period, report file manifest
[D][05:19:10][COMM]Main Task receive event:14 finished processing
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,143538.000,2301.2576197,N,11421.9433333,E,1,07,1.35,83.669,M,-1.770,M,,*58

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.25,1.35,4.03,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,38,3,60,190,40,1*75

$GBGSV,7,2,25,39,60,35,40,16,57,9,38,59,52,129,41,25,50,

2025-07-31 22:35:39:165 ==>> 353,39,1*44

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,35,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,33,33,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,31,1*74

$GBGSV,7,6,25,44,1,185,35,11,,,37,43,,,37,12,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*48

$GBGSV,2,2,06,41,42,266,42,33,33,200,38,5*7A

$GBRMC,143538.000,A,2301.2576197,N,11421.9433333,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,143538.000,1.291,0.211,0.207,0.360,1.098,1.141,4.046*77

[D][05:19:10][COMM]read battery soc:255


2025-07-31 22:35:39:270 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 22:35:40:300 ==>> $GBGGA,143540.000,2301.2576599,N,11421.9433933,E,1,07,1.35,83.768,M,-1.770,M,,*57

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.24,1.35,4.03,4*06

$GBGSV,7,1,25,40,65,171,41,6,63,76,35,7,61,222,38,3,60,190,39,1*78

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,34,2,45,237,34,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,40,33,33,200,37,1*7B

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,34,11,,,38,43,,,37,12,,,36,1*74

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,42,33,33,200,38,5*7A

$GBRMC,143540.000,A,2301.2576599,N,11421.9433933,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,143540.000,0.903,0.224,0.219,0.380,0.710,0.748,3.403*74

[D][05:19:12][COMM]read battery soc:255


2025-07-31 22:35:41:272 ==>> $GBGGA,143541.000,2301.2576586,N,11421.9434371,E,1,07,1.35,83.601,M,-1.770,M,,*5D

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.24,1.35,4.02,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,35,7,61,222,38,3,60,190,40,1*76

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,34,2,45,237,35,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,39,33,32,200,37,1*74

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,34,11,,,37,43,,,37,12,,,36,1*7B

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,41,25,50,353,41,34,44,106,40,5*4B

$GBGSV,2,2,06,41,42,266,42,33,32,200,38,5*7B

$GBRMC,143541.000,A,2301.2576586,N,11421.9434371,E,0.003,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,143541.000,1.105,0.216,0.211,0.367,0.875,0.907,3.316*70



2025-07-31 22:35:42:308 ==>> $GBGGA,143542.000,2301.2576680,N,11421.9434497,E,1,07,1.35,83.481,M,-1.770,M,,*5E

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.24,1.35,4.02,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,222,38,3,60,190,40,1*75

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,34,2,45,237,35,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,40,60,41,238,40,33,32,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,34,11,,,37,43,,,37,12,,,36,1*7B

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*48

$GBGSV,2,2,06,41,42,266,42,33,32,200,38,5*7B

$GBRMC,143542.000,A,2301.2576680,N,11421.9434497,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,143542.000,1.192,0.209,0.205,0.352,0.937,0.964,3.208*7C

[D][05:19:14][COMM]read battery soc:255


2025-07-31 22:35:43:306 ==>> $GBGGA,143543.000,2301.2576755,N,11421.9434580,E,1,07,1.35,83.359,M,-1.770,M,,*53

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.24,1.35,4.02,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,223,38,3,60,190,40,1*74

$GBGSV,7,2,25,39,60,35,40,16,57,9,37,59,52,129,40,25,50,353,39,1*4A

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,35,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,33,32,200,37,1*7B

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,35,11,,,37,43,,,37,12,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*48

$GBGSV,2,2,06,41,42,266,41,33,32,200,38,5*78

$GBRMC,143543.000,A,2301.2576755,N,11421.9434580,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,143543.000,1.260,0.163,0.160,0.271,0.983,1.007,3.114*70



2025-07-31 22:35:43:676 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:35:44:550 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:16][COMM]Main Task receive event:14
[D][05:19:16][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955156, allstateRepSeconds = 0
[D][05:19:16][COMM]index:0,power_mode:0xFF
[D][05:19:16][COMM]index:1,sound_mode:0xFF
[D][05:19:16][COMM]index:2,gsensor_mode:0xFF
[D][05:19:16][COMM]index:3,report_freq_mode:0xFF
[D][05:19:16][COMM]index:4,report_period:0xFF
[D][05:19:16][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:16][COMM]index:6,normal_reset_period:0xFF
[D][05:19:16][COMM]index:7,spock_over_speed:0xFF
[D][05:19:16][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:16][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:16][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:16][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:16][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:16][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:16][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:16][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:16][COMM]index:16,imu_config_params:0xFF
[D][05:19:16][COMM]index:17,long_connect_params:0xFF
[D][05:19:16][COMM]index:18,detain_mark:0xFF
[D][05:19:16][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:16][COMM]index:20,lock

2025-07-31 22:35:44:655 ==>> _pos_report_interval:0xFF
[D][05:19:16][COMM]index:21,mc_mode:0xFF
[D][05:19:16][COMM]index:22,S_mode:0xFF
[D][05:19:16][COMM]index:23,overweight:0xFF
[D][05:19:16][COMM]index:24,standstill_mode:0xFF
[D][05:19:16][COMM]index:25,night_mode:0xFF
[D][05:19:16][COMM]index:26,experiment1:0xFF
[D][05:19:16][COMM]index:27,experiment2:0xFF
[D][05:19:16][COMM]index:28,experiment3:0xFF
[D][05:19:16][COMM]index:29,experiment4:0xFF
[D][05:19:16][COMM]index:30,night_mode_start:0xFF
[D][05:19:16][COMM]index:31,night_mode_end:0xFF
[D][05:19:16][COMM]index:33,park_report_minutes:0xFF
[D][05:19:16][COMM]index:34,park_report_mode:0xFF
[D][05:19:16][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:16][COMM]index:38,charge_battery_para: FF
[D][05:19:16][COMM]index:39,multirider_mode:0xFF
[D][05:19:16][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:16][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:16][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:16][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:16][COMM]index:44,riding_duration_config:0xFF
[D][05:19:16][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:16][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:1

2025-07-31 22:35:44:760 ==>> 9:16][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:16][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:16][COMM]index:49,mc_load_startup:0xFF
[D][05:19:16][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:16][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:16][COMM]index:52,traffic_mode:0xFF
[D][05:19:16][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:16][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:16][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:16][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:16][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:16][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:16][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:16][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:16][COMM]index:63,experiment5:0xFF
[D][05:19:16][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:16][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:16][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:16][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:16][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:16][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:16][COMM]index:71,camera_park_self_check_cfg:0

2025-07-31 22:35:44:865 ==>> xFF
[D][05:19:16][COMM]index:72,experiment6:0xFF
[D][05:19:16][COMM]index:73,experiment7:0xFF
[D][05:19:16][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:16][COMM]index:75,zero_value_from_server:-1
[D][05:19:16][COMM]index:76,multirider_threshold:255
[D][05:19:16][COMM]index:77,experiment8:255
[D][05:19:16][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:16][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:16][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:16][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:16][COMM]index:83,loc_report_interval:255
[D][05:19:16][COMM]index:84,multirider_threshold_p2:255
[D][05:19:16][COMM]index:85,multirider_strategy:255
[D][05:19:16][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:16][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:16][COMM]index:90,weight_param:0xFF
[D][05:19:16][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:16][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:16][COMM]index:95,current_limit:0xFF
[D][05:19:16][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:16][COMM]index:100,location_mode:0xFF

[D][05:19:16][HSD

2025-07-31 22:35:44:970 ==>> K][0] flush to flash addr:[0xE43000] --- write len --- [256]
[W][05:19:16][PROT]remove success[1629955156],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,143544.000,2301.2576736,N,11421.9434873,E,1,07,1.35,83.332,M,-1.770,M,,*5D

$GBGSA,A,3,40,39,16,25,34,41,33,,,,,,4.24,1.35,4.02,4*07

$GBGSV,7,1,25,40,65,171,41,6,63,76,36,7,61,223,39,3,60,190,40,1*75

$GBGSV,7,2,25,39,60,35,40,16,57,9,38,59,52,129,40,25,50,353,39,1*45

$GBGSV,7,3,25,10,49,223,36,1,48,125,37,9,46,332,35,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,41,41,42,266,41,60,41,238,40,33,32,200,37,1*7A

$GBGSV,7,5,25,4,32,111,33,5,21,256,33,24,13,259,38,14,13,321,30,1*75

$GBGSV,7,6,25,44,1,185,35,11,,,38,43,,,37,12,,,36,1*75

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*48

$GBGSV,2,2,06,41,42,266,42,33,32,200,38,5*7B

$GBRMC,143544.000,A,2301.2576736,N,11421.9434873,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29



2025-07-31 22:35:45:000 ==>> $GBGST,143544.000,1.053,0.254,0.248,0.435,0.791,0.816,2.898*75

[D][05:19:16][COMM]read battery soc:255


2025-07-31 22:35:45:320 ==>> $GBGGA,143545.000,2301.2576789,N,11421.9435036,E,1,12,0.98,83.277,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,33,24,2.37,0.98,2.15,4*0A

$GBGSV,7,1,25,40,65,171,41,7,62,197,39,3,60,190,40,39,60,35,40,1*43

$GBGSV,7,2,25,6,58,5,36,16,57,9,37,59,52,129,40,10,51,206,36,1*46

$GBGSV,7,3,25,9,50,342,35,25,50,353,40,1,48,125,37,2,45,237,35,1*45

$GBGSV,7,4,25,34,44,106,41,41,42,266,41,60,41,238,40,11,38,138,38,1*77

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,30,1*48

$GBGSV,7,7,25,32,10,316,34,1*42

$GBGSV,2,1,06,40,65,171,40,39,60,35,41,25,50,353,41,34,44,106,40,5*4A

$GBGSV,2,2,06,41,42,266,42,33,32,200,38,5*7B

$GBRMC,143545.000,A,2301.2576789,N,11421.9435036,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,143545.000,0.955,0.210,0.198,0.321,0.687,0.709,2.669*71



2025-07-31 22:35:46:018 ==>> [D][05:19:18][M2M ]get csq[-1]


2025-07-31 22:35:46:413 ==>> $GBGGA,143546.000,2301.2576819,N,11421.9435510,E,1,17,0.69,82.951,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,39,3,60,190,40,39,60,35,40,1*43

$GBGSV,7,2,25,6,58,5,36,16,57,9,37,59,52,129,40,10,51,206,36,1*46

$GBGSV,7,3,25,9,50,342,35,25,50,353,40,1,48,125,37,2,45,237,34,1*44

$GBGSV,7,4,25,34,44,106,41,41,42,266,41,60,41,238,40,11,38,138,37,1*78

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,30,1*48

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*4E

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,33,24,25,62,35,5*44

$GBGSV,3,3,10,44,16,51,35,32,10,316,33,5*42

$GBRMC,143546.000,A,2301.2576819,N,11421.9435510,E,0.004,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.004,N,0.008,K,A*23

$GBGST,143546.000,2.681,0.526,0.499,0.733,1.948,1.960,3.369*7E

[D][05:19:18][COMM]read battery soc:255
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[5004],priority[2],index[

2025-07-31 22:35:46:458 ==>> 1],used[1]
[D][05:19:18][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:18][COMM][LOC]wifi scan is already running, error
[D][05:19:18][COMM]Main Task receive event:14 finished processing
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 22:35:47:353 ==>> $GBGGA,143547.000,2301.2576751,N,11421.9435884,E,1,17,0.69,82.694,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,60,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,38,59,52,129,40,10,51,206,36,1*49

$GBGSV,7,3,25,9,50,342,35,25,50,353,39,1,48,125,37,2,45,237,34,1*4A

$GBGSV,7,4,25,34,44,106,41,41,42,266,41,60,41,238,40,11,38,138,38,1*77

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,30,1*48

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,42,25,50,353,41,34,44,106,40,5*4E

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,33,24,25,62,36,5*47

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143547.000,A,2301.2576751,N,11421.9435884,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,143547.000,2.457,0.204,0.198,0.290,1.805,1.815,3.134*78



2025-07-31 22:35:48:087 ==>> [D][05:19:20][CAT1]exec over: func id: 15, ret: -93
[D][05:19:20][CAT1]sub id: 15, ret: -93

[D][05:19:20][SAL ]Cellular task submsg id[68]
[D][05:19:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:20][SAL ]socket send fail. id[4]
[D][05:19:20][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:20][CAT1]gsm read msg sub id: 21
[D][05:19:20][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:20][M2M ]m2m select fd[4]
[D][05:19:20][M2M ]socket[4] Link is disconnected
[D][05:19:20][M2M ]tcpclient close[4]
[D][05:19:20][SAL ]socket[4] has closed
[D][05:19:20][PROT]protocol read data ok
[E][05:19:20][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:20][PROT]M2M Send Fail [1629955160]
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]cell info report total[0]
[D][05:19:20][CAT1]exec over: func id: 21, ret: 6
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:20][CAT1]gsm read msg sub id: 13
[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:20][CAT1]exec over: func id: 13, ret: 21
[D][05:19:20][CAT1]gsm read msg sub id: 10
[D][05:19:20][CAT1]tx ret[11] >>> AT+CGAT

2025-07-31 22:35:48:117 ==>> T?

[D][05:19:20][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:35:48:342 ==>>                                     21.9436071,E,1,17,0.69,82.409,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,60,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,37,59,52,129,40,10,51,206,36,1*46

$GBGSV,7,3,25,9,50,342,35,25,50,354,39,1,48,125,37,2,45,237,34,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,11,38,138,38,1*76

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,32,44,16,51,35,14,13,321,30,1*49

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,40,39,60,35,42,25,50,354,41,34,44,106,40,5*48

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143548.000,A,2301.2576896,N,11421.9436071,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,143548.000,2.594,0.210,0.203,0.300,1.885,1.893,3.097*7B

[D][05:19:20][COMM]read battery soc:255


2025-07-31 22:35:48:447 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 22:35:48:477 ==>>                                                                                                                                   

2025-07-31 22:35:48:766 ==>> [D][05:19:21][CAT1]pdpdeact urc len[22]


2025-07-31 22:35:49:348 ==>> $GBGGA,143549.000,2301.2576879,N,11421.9436186,E,1,17,0.69,82.193,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,60,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,38,59,52,129,40,10,51,206,36,1*49

$GBGSV,7,3,25,9,50,342,35,25,50,354,39,1,48,125,37,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,11,38,138,37,1*79

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,30,1*48

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,42,25,50,354,40,34,44,106,40,5*48

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143549.000,A,2301.2576879,N,11421.9436186,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,143549.000,2.295,0.211,0.204,0.303,1.692,1.700,2.862*77



2025-07-31 22:35:49:735 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:35:50:802 ==>> [D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:22][CAT1]<<< 
+CGATT: 1

OK

[W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:22][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_a

2025-07-31 22:35:50:907 ==>> larm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:22][COMM]index:21,mc_mode:0xFF
[D][05:19:22][COMM]index:22,S_mode:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mo

2025-07-31 22:35:51:012 ==>> de:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:22][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:22][COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0

2025-07-31 22:35:51:117 ==>> xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:72,experiment6:0xFF
[D][05:19:22][COMM]index:73,experiment7:0xFF
[D][05:19:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0x

2025-07-31 22:35:51:222 ==>> FF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 8, ret: 6
[D][05:19:22][CAT1]gsm read msg sub id: 13
[D][05:19:22][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:22][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:22][CAT1]exec over: func id: 13, ret: 21
[D][05:19:22][M2M ]get csq[26]
$GBGGA,143550.000,2301.2576738,N,11421.9436406,E,1,17,0.69,82

2025-07-31 22:35:51:327 ==>> .010,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,60,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,37,59,52,129,40,10,51,206,36,1*46

$GBGSV,7,3,25,9,50,342,35,25,50,354,39,1,48,125,37,2,45,237,35,1*4C

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,11,38,138,38,1*76

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,31,1*49

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,41,25,50,354,40,34,44,106,40,5*4B

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143550.000,A,2301.2576738,N,11421.9436406,E,0.004,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,143550.000,2.181,0.196,0.190,0.284,1.614,1.622,2.731*73

[D][05:19:22][CAT1]opened : 0, 0
[D][05:19:22][SAL ]Cellular task submsg id[68]
[D][05:19:22][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:22][SAL ]socket connect ind. id[4], rst[3]
[D][05:19

2025-07-31 22:35:51:432 ==>> :22][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:22][M2M ]g_m2m_is_idle become true
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:22][PROT]index:0 1629955162
[D][05:19:22][PROT]is_send:0
[D][05:19:22][PROT]sequence_num:12
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:1
[D][05:19:22][PROT]send_path:0x2
[D][05:19:22][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:22][PROT]===========================================================
[D][05:19:22][HSDK][0] flush to flash addr:[0xE43100] --- write len --- [256]
[D][05:19:22][COMM]read battery soc:255
[W][05:19:22][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]sending traceid [999999999990000D]
[D][05:19:22][PROT]Send_TO_M2M [1629955162]
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:22][SAL ]sock send credit cnt[6]
[D][05:19:22]

2025-07-31 22:35:51:537 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 22:35:52:365 ==>> $GBGGA,143552.000,2301.2576664,N,11421.9436769,E,1,17,0.69,81.677,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,39,3,60,190,40,39,60,35,40,1*43

$GBGSV,7,2,25,6,58,5,36,16,57,9,38,59,52,129,40,10,51,206,36,1*49

$GBGSV,7,3,25,9,50,342,35,25,50,354,40,1,48,125,38,2,45,237,35,1*4D

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,11,38,138,38,1*76

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,32,44,16,51,35,14,13,321,31,1*48

$GBGSV,7,7,25,32,10,316,34,1*42

$GBGSV,3,1,10,40,65,171,41,39,60,35,42,25,50,354,41,34,44,106,40,5*49

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143552.000,A,2301.2576664,N,11421.9436769,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,143552.000,2.367,0.195,0.189,0.280,1.729,1.735,2.727*7B

[D][05:19:24][COMM]read battery soc:255


2025-07-31 22:35:53:341 ==>> $GBGGA,143553.000,2301.2576719,N,11421.9436971,E,1,17,0.69,81.564,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.36,0.69,1.17,4*01

$GBGSA,A,3,12,43,24,44,32,,,,,,,,1.36,0.69,1.17,4*07

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,60,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,37,59,52,129,40,10,51,206,36,1*46

$GBGSV,7,3,25,9,50,342,35,25,50,354,40,1,48,125,37,2,45,237,35,1*42

$GBGSV,7,4,25,34,44,106,40,41,42,266,41,60,41,238,40,11,38,138,38,1*76

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,31,1*49

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,41,25,50,354,41,34,44,106,40,5*4A

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143553.000,A,2301.2576719,N,11421.9436971,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,143553.000,2.297,0.189,0.183,0.274,1.681,1.687,2.647*74



2025-07-31 22:35:54:369 ==>> $GBGGA,143554.000,2301.2576598,N,11421.9437024,E,1,21,0.63,81.536,M,-1.770,M,,*55

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,01,34,1.30,0.63,1.14,4*05

$GBGSA,A,3,60,41,11,33,12,43,24,44,32,,,,1.30,0.63,1.14,4*0B

$GBGSV,7,1,25,40,65,171,41,7,62,197,38,3,61,190,40,39,60,35,40,1*43

$GBGSV,7,2,25,6,58,5,36,16,57,9,38,10,51,206,36,9,50,342,35,1*73

$GBGSV,7,3,25,25,50,354,40,59,49,130,40,1,45,125,37,2,45,237,35,1*77

$GBGSV,7,4,25,34,44,106,40,60,42,239,40,41,42,266,41,11,38,138,38,1*74

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,31,1*49

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,41,25,50,354,41,34,44,106,40,5*4A

$GBGSV,3,2,10,41,42,266,42,33,32,200,38,43,29,170,34,24,25,62,37,5*41

$GBGSV,3,3,10,44,16,51,34,32,10,316,33,5*43

$GBRMC,143554.000,A,2301.2576598,N,11421.9437024,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,143554.000,2.365,0.185,0.182,0.273,1.723,1.728,2.650*7E

[D][05:19:26][COMM]read battery soc:255


2025-07-31 22:35:55:343 ==>> $GBGGA,143555.000,2301.2576518,N,11421.9437125,E,1,21,0.63,81.490,M,-1.770,M,,*51

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,01,34,1.30,0.63,1.14,4*05

$GBGSA,A,3,60,41,11,33,12,43,24,44,32,,,,1.30,0.63,1.14,4*0B

$GBGSV,7,1,25,40,65,171,41,7,62,197,39,3,61,190,40,39,60,35,40,1*42

$GBGSV,7,2,25,6,58,5,36,16,57,9,38,10,51,206,36,9,50,342,35,1*73

$GBGSV,7,3,25,25,50,354,40,59,49,130,40,1,45,125,37,2,45,237,35,1*77

$GBGSV,7,4,25,34,44,106,40,60,42,239,40,41,42,265,41,11,38,138,38,1*77

$GBGSV,7,5,25,33,32,200,37,4,32,111,33,12,31,73,36,43,29,170,37,1*7E

$GBGSV,7,6,25,24,25,62,38,5,21,256,33,44,16,51,35,14,13,321,31,1*49

$GBGSV,7,7,25,32,10,316,33,1*45

$GBGSV,3,1,10,40,65,171,41,39,60,35,41,25,50,354,41,34,44,106,40,5*4A

$GBGSV,3,2,10,41,42,265,42,33,32,200,38,43,29,170,34,24,25,62,37,5*42

$GBGSV,3,3,10,44,16,51,34,32,10,316,32,5*42

$GBRMC,143555.000,A,2301.2576518,N,11421.9437125,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,143555.000,2.271,0.188,0.185,0.276,1.660,1.665,2.566*7C



2025-07-31 22:35:55:679 ==>> [D][05:19:27][PROT]CLEAN,SEND:0
[D][05:19:27][PROT]index:1 1629955167
[D][05:19:27][PROT]is_send:0
[D][05:19:27][PROT]sequence_num:13
[D][05:19:27][PROT]retry_timeout:0
[D][05:19:27][PROT]retry_times:1
[D][05:19:27][PROT]send_path:0x2
[D][05:19:27][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:27][PROT]===========================================================
[W][05:19:27][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955167]
[D][05:19:27][PROT]===========================================================
[D][05:19:27][PROT]sending traceid [999999999990000E]
[D][05:19:27][PROT]Send_TO_M2M [1629955167]
[D][05:19:27][PROT]CLEAN:0
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:27][SAL ]sock send credit cnt[6]
[D][05:19:27][SAL ]sock send ind credit cnt[6]
[D][05:19:27][M2M ]m2m send data len[166]
[D][05:19:27][SAL ]Cellular task submsg id[10]
[D][05:19:27][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:27][CAT1]gsm read msg sub id: 15
[D][05:19:27][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:

2025-07-31 22:35:55:755 ==>> 27][CAT1]Send Data To Server[166][169] ... ->:
0053B987113311331133113311331B88BA561E8E54EE170C395CA60A4D995D83E5714EDFB0690B6F0DB70F75D7EEBF508E355471AABA583D397BE132DE58BBAB81C33D3CE2BBE512A679AFB66D428CB0F9C4EC
[D][05:19:27][CAT1]<<< 
SEND OK

[D][05:19:27][CAT1]exec over: func id: 15, ret: 11
[D][05:19:27][CAT1]sub id: 15, ret: 11

[D][05:19:27][SAL ]Cellular task submsg id[68]
[D][05:19:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:27][M2M ]g_m2m_is_idle become true
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:27][PROT]M2M Send ok [1629955167]


2025-07-31 22:35:55:789 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:35:55:805 ==>> 检测【关闭GPS】
2025-07-31 22:35:55:831 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:35:56:167 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]stop event:8
[D][05:19:28][GNSS]GPS stop. ret=0
[D][05:19:28][GNSS]all continue location stop
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]all sing location stop
[D][05:19:28][CAT1]gsm read msg sub id: 24
[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]exec over: func id: 24, ret: 6
[D][05:19:28][CAT1]sub id: 24, ret: 6



2025-07-31 22:35:56:241 ==>> [D][05:19:28][COMM]read battery soc:255


2025-07-31 22:35:56:320 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:35:56:333 ==>> 检测【清空消息队列2】
2025-07-31 22:35:56:352 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:35:56:486 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:28][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:35:56:595 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:35:56:609 ==>> 检测【轮动检测】
2025-07-31 22:35:56:633 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:35:56:700 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:35:57:099 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:35:57:145 ==>> [D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_ti

2025-07-31 22:35:57:250 ==>> ck:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100028. period:1000

2025-07-31 22:35:57:354 ==>> 0. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100028. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100028. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100029. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100029. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100030. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100030. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100030. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100031
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100031
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100031
[D][05:19:29][COMM]Wheel signal detected, lock state = 2, singal = 1
[D][05:19:29][GNSS]recv submsg id[1]
[D][05:19:29][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:29][GNSS]location stop evt done evt
3A

2025-07-31 22:35:57:384 ==>>  A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:35:57:628 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:35:57:640 ==>> 检测【关闭小电池】
2025-07-31 22:35:57:653 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:35:57:700 ==>> 6A A6 02 A6 6A 


2025-07-31 22:35:57:804 ==>> Battery OFF
OVER 150


2025-07-31 22:35:57:901 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:35:57:910 ==>> 检测【进入休眠模式】
2025-07-31 22:35:57:923 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:35:58:155 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:30][COMM]Main Task receive event:28
[D][05:19:30][COMM]main task tmp_sleep_event = 8
[D][05:19:30][COMM]prepare to sleep
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:35:58:245 ==>> [D][05:19:30][COMM]read battery soc:255


2025-07-31 22:35:58:986 ==>> [D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 12, ret: 6
[D][05:19:31][M2M ]tcpclient close[4]
[D][05:19:31][SAL ]Cellular task submsg id[12]
[D][05:19:31][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:31][CAT1]gsm read msg sub id: 9
[D][05:19:31][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 9, ret: 6
[D][05:19:31][CAT1]sub id: 9, ret: 6

[D][05:19:31][SAL ]Cellular task submsg id[68]
[D][05:19:31][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:31][SAL ]socket close ind. id[4]
[D][05:19:31][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:31][COMM]1x1 frm_can_tp_send ok
[D][05:19:31][GNSS]handler GSMGet Base timeout
[D][05:19:31][CAT1]pdpdeact urc len[22]


2025-07-31 22:35:59:292 ==>> [E][05:19:31][COMM]1x1 rx timeout
[D][05:19:31][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:35:59:798 ==>> [E][05:19:31][COMM]1x1 rx timeout
[E][05:19:31][COMM]1x1 tp timeout
[E][05:19:31][COMM]1x1 error -3.
[W][05:19:31][COMM]CAN STOP!
[D][05:19:31][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:31][COMM]------------ready to Power off Acckey 1------------
[D][05:19:31][COMM]------------ready to Power off Acckey 2------------
[D][05:19:31][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:31][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1284
[D][05:19:31][COMM]bat sleep fail, reason:-1
[D][05:19:31][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:31][COMM]accel parse set 0
[D][05:19:31][COMM]imu rest ok. 102939
[D][05:19:31][COMM]imu sleep 0
[W][05:19:31][COMM]now sleep


2025-07-31 22:35:59:983 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:35:59:996 ==>> 检测【检测33V休眠电流】
2025-07-31 22:36:00:023 ==>> 开始33V电流采样
2025-07-31 22:36:00:036 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:00:087 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:36:01:097 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:36:01:144 ==>> Current33V:????:15.60

2025-07-31 22:36:01:604 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:01:627 ==>> 【检测33V休眠电流】通过,【15.6uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:36:01:635 ==>> 该项需要延时执行
2025-07-31 22:36:03:640 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:36:03:654 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:36:03:678 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:36:03:717 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1677mV
Get AD_V4 0mV
Get AD_V5 2761mV
Get AD_V6 1976mV
Get AD_V7 1086mV
OVER 150


2025-07-31 22:36:04:663 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:36:04:672 ==>> 检测【打开小电池2】
2025-07-31 22:36:04:686 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:36:04:801 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:36:04:947 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:36:04:954 ==>> 该项需要延时执行
2025-07-31 22:36:05:447 ==>> 此处延时了:【500】毫秒
2025-07-31 22:36:05:455 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:36:05:469 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:36:05:508 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:36:05:734 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:36:05:743 ==>> 该项需要延时执行
2025-07-31 22:36:06:208 ==>> [D][05:19:38][COMM]------------ready to Power on Acckey 1------------
[D][05:19:38][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:38][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:38][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:38][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:38][COMM]----- get Acckey 1 and value:1------------
[D][05:19:38][HSDK][0] flush to flash addr:[0xE43200] --- write len --- [256]
[W][05:19:38][COMM]CAN START!
[D][05:19:38][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 109258
[D][05:19:38][CAT1]gsm read msg sub id: 12
[D][05:19:38][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:38][COMM][Audio]exec status ready.
[D][

2025-07-31 22:36:06:239 ==>> 此处延时了:【500】毫秒
2025-07-31 22:36:06:253 ==>> 检测【进入休眠模式2】
2025-07-31 22:36:06:274 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:36:06:294 ==>> 05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]exec over: func id: 12, ret: 6
[D][05:19:38][COMM]imu wakeup ok. 109282
[D][05:19:38][COMM]imu wakeup 1
[W][05:19:38][COMM]wake up system, wakeupEvt=0x80
[D][05:19:38][COMM]frm_can_weigth_power_set 1
[D][05:19:38][COMM]Clear Sleep Block Evt
[D][05:19:38][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:38][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:36:06:373 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:36:06:599 ==>> [E][05:19:38][COMM]1x1 rx timeout
[D][05:19:38][COMM]1x1 frm_can_tp_send ok
[D][05:19:38][COMM]msg 02A0 loss. last_tick:109243. cur_tick:109751. period:50
[D][05:19:38][COMM]msg 02A4 loss. last_tick:109243. cur_tick:109752. period:50
[D][05:19:38][COMM]msg 02A5 loss. last_tick:109243. cur_tick:109752. period:50
[D][05:19:38][COMM]msg 02A6 loss. last_tick:109243. cur_tick:109753. period:50
[D][05:19:38][COMM]msg 02A7 loss. last_tick:109243. cur_tick:109753. period:50
[D][05:19:38][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 109754
[D][05:19:38][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 109754


2025-07-31 22:36:06:931 ==>> [E][05:19:39][COMM]1x1 rx timeout
[E][05:19:39][COMM]1x1 tp timeout
[E][05:19:39][COMM]1x1 error -3.
[D][05:19:39][COMM]Main Task receive event:28 finished processing
[D][05:19:39][COMM]Main Task receive event:28
[D][05:19:39][COMM]prepare to sleep
[D][05:19:39][CAT1]gsm read msg sub id: 12
[D][05:19:39][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 12, ret: 6
[D][05:19:39][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:39][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:36:07:233 ==>> [D][05:19:39][COMM]msg 0220 loss. last_tick:109243. cur_tick:110248. period:100
[D][05:19:39][COMM]msg 0221 loss. last_tick:109243. cur_tick:110248. period:100
[D][05:19:39][COMM]msg 0224 loss. last_tick:109243. cur_tick:110248. period:100
[D][05:19:39][COMM]msg 0260 loss. last_tick:109243. cur_tick:110249. period:100
[D][05:19:39][COMM]msg 0280 loss. last_tick:109243. cur_tick:110249. period:100
[D][05:19:39][COMM]msg 02C0 loss. last_tick:109243. cur_tick:110249. period:100
[D][05:19:39][COMM]msg 02C1 loss. last_tick:109243. cur_tick:110250. period:100
[D][05:19:39][COMM]msg 02C2 loss. last_tick:109243. cur_tick:110250. period:100
[D][05:19:39][COMM]msg 02E0 loss. last_tick:109243. cur_tick:110250. period:100
[D][05:19:39][COMM]msg 02E1 loss. last_tick:109243. cur_tick:110251. period:100
[D][05:19:39][COMM]msg 02E2 loss. last_tick:109243. cur_tick:110251. period:100
[D][05:19:39][COMM]msg 0300 loss. last_tick:109243. cur_tick:110251. period:100
[D][05:19:39][COMM]msg 0301 loss. last_tick:109243. cur_tick:110252. period:100
[D][05:19:39][COMM]bat msg 0240 loss. last_tick:109243. cur_tick:110252. period:100. j,i:1 54
[D][05:19:39][COMM]bat msg 0241 loss. last_tick:109243. cur_tick:110253. period:100. j,i:2 55
[D][05:19:39][COMM]bat msg 0242 loss. last_tick:109243. cur_ti

2025-07-31 22:36:07:322 ==>> ck:110253. period:100. j,i:3 56
[D][05:19:39][COMM]bat msg 0244 loss. last_tick:109243. cur_tick:110253. period:100. j,i:5 58
[D][05:19:39][COMM]bat msg 024E loss. last_tick:109243. cur_tick:110254. period:100. j,i:15 68
[D][05:19:39][COMM]bat msg 024F loss. last_tick:109243. cur_tick:110254. period:100. j,i:16 69
[D][05:19:39][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 110255
[D][05:19:39][COMM]CAN message bat fault change: 0x00000000->0x0001802E 110255
[D][05:19:39][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 110255
                                                                              

2025-07-31 22:36:07:519 ==>> [D][05:19:39][COMM]msg 0222 loss. last_tick:109243. cur_tick:110750. period:150
[D][05:19:39][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 110751


2025-07-31 22:36:07:594 ==>>                                                                                         ]frm_peripheral_device_poweroff type 16.... 
[D][05:19:39][COMM]------------ready to Power off Acckey 2------------


2025-07-31 22:36:07:804 ==>> [E][05:19:39][COMM]1x1 rx timeout
[E][05:19:39][COMM]1x1 tp timeout
[E][05:19:39][COMM]1x1 error -3.
[W][05:19:39][COMM]CAN STOP!
[D][05:19:39][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:39][COMM]------------ready to Power off Acckey 1------------
[D][05:19:39][COMM]------------ready to Power off Acckey 2------------
[D][05:19:39][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:39][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 93
[D][05:19:39][COMM]bat sleep fail, reason:-1
[D][05:19:39][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:39][COMM]accel parse set 0
[D][05:19:39][COMM]imu rest ok. 110940
[D][05:19:39][COMM]imu sleep 0
[W][05:19:39][COMM]now sleep


2025-07-31 22:36:08:072 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:36:08:086 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:36:08:113 ==>> 开始小电池电流采样
2025-07-31 22:36:08:122 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:08:180 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:36:09:186 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:36:09:231 ==>> CurrentBattery:ƽ��:69.15

2025-07-31 22:36:09:690 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:09:721 ==>> 【检测小电池休眠电流】通过,【69.15uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:36:09:729 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:36:09:751 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:36:09:810 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:36:09:994 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:36:10:007 ==>> 该项需要延时执行
2025-07-31 22:36:10:021 ==>> [D][05:19:42][COMM]------------ready to Power on Acckey 1------------
[D][05:19:42][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:42][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:42][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:42][COMM]----- get Acckey 1 and value:1------------
[W][05:19:42][COMM]CAN START!
[D][05:19:42][COMM]read battery soc:0
[D][05:19:42][CAT1]gsm read msg sub id: 12
[D][05:19:42][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:42][COMM]CAN message bat fault change: 0x0001802E->0x00000000 113140
[D][05:19:42][COMM][Audio]exec status ready.
[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 12, ret: 6
[D][05:19:42][COMM]imu wakeup ok. 113154
[D][05:19:42][COMM]imu wakeup 1
[W][05:19:42][COMM]wake up system, wakeupEvt=0x80
[D][05:19:42][COMM]frm_can_weigth_power_set 1
[D][05:19:42][COMM]Clear Sleep Block Evt
[D][05:19:42][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:36:10:309 ==>> [E][05:19:42][COMM]1x1 rx timeout
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:36:10:414 ==>> [D][05:19:42][COMM]msg 02A0 loss. last_tick:113122. cur_tick:113634. period:50
[D][05:19:42][COMM]msg 02A4 loss. last_tick:113122. cur_tick:113635. period:50
[D][05

2025-07-31 22:36:10:474 ==>> :19:42][COMM]msg 02A5 loss. last_tick:113122. cur_tick:113635. period:50
[D][05:19:42][COMM]msg 02A6 loss. last_tick:113122. cur_tick:113636. period:50
[D][05:19:42][COMM]msg 02A7 loss. last_tick:113122. cur_tick:113636. period:50
[D][05:19:42][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 113636
[D][05:19:42][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 113637


2025-07-31 22:36:10:505 ==>> 此处延时了:【500】毫秒
2025-07-31 22:36:10:535 ==>> 检测【检测唤醒】
2025-07-31 22:36:10:547 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:36:11:286 ==>> [W][05:19:42][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:42][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:42][FCTY]==========Modules-nRF5340 ==========
[D][05:19:42][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:42][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:42][FCTY]DeviceID    = 460130071539892
[D][05:19:42][FCTY]HardwareID  = 867222087442242
[D][05:19:42][FCTY]MoBikeID    = 9999999999
[D][05:19:42][FCTY]LockID      = FFFFFFFFFF
[D][05:19:42][FCTY]BLEFWVersion= 105
[D][05:19:42][FCTY]BLEMacAddr   = DA39AF64C196
[D][05:19:42][FCTY]Bat         = 3684 mv
[D][05:19:42][FCTY]Current     = 0 ma
[D][05:19:42][FCTY]VBUS        = 2600 mv
[D][05:19:42][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:42][FCTY]Ext battery vol = 32, adc = 1283
[D][05:19:42][FCTY]Acckey1 vol = 5494 mv, Acckey2 vol = 0 mv
[D][05:19:42][FCTY]Bike Type flag is invalied
[D][05:19:42][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:42][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:42][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:42][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:42][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:42][FCTY]CAT1_GNSS_

2025-07-31 22:36:11:391 ==>> VERSION = V3465b5b1
[D][05:19:42][FCTY]Bat1         = 3767 mv
[D][05:19:42][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:42][FCTY]==========Modules-nRF5340 ==========
[E][05:19:42][COMM]1x1 rx timeout
[E][05:19:42][COMM]1x1 tp timeout
[E][05:19:42][COMM]1x1 error -3.
[D][05:19:42][COMM]Main Task receive event:28 finished processing
[D][05:19:42][COMM]Main Task receive event:65
[D][05:19:42][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:42][COMM]Main Task receive event:65 finished processing
[D][05:19:42][COMM]Main Task receive event:60
[D][05:19:42][COMM]smart_helmet_vol=255,255
[D][05:19:42][COMM]report elecbike
[D][05:19:42][HSDK][0] flush to flash addr:[0xE43300] --- write len --- [256]
[D][05:19:42][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:42][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:42][PROT]remove success[1629955182],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:42][PROT]add success [1629955182],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:42][COMM]Main Task receive event:60 finished processing
[D][05:19:42][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:

2025-07-31 22:36:11:496 ==>> 42][PROT]index:0
[D][05:19:42][PROT]is_send:1
[D][05:19:42][PROT]sequence_num:14
[D][05:19:42][PROT]retry_timeout:0
[D][05:19:42][PROT]retry_times:3
[D][05:19:42][PROT]send_path:0x3
[D][05:19:42][PROT]msg_type:0x5d03
[D][05:19:42][PROT]===========================================================
[W][05:19:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955182]
[D][05:19:42][PROT]===========================================================
[D][05:19:42][PROT]Sending traceid[999999999990000F]
[D][05:19:42][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:42][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:42][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:42][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:42][SAL ]open socket ind id[4], rst[0]
[D][05:19:42][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:42][SAL ]Cellular task submsg id[8]
[D][05:19:42][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:

2025-07-31 22:36:11:584 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:36:11:596 ==>> 检测【关机】
2025-07-31 22:36:11:613 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:36:11:633 ==>> 19:42][CAT1]gsm read msg sub id: 8
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:42][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:43][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:43][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:43][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:43][CAT1]<<< 
+CME ERROR: 100

[D][05:19:43][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:43][COMM]msg 0220 loss. last_tick:113122. cur_tick:114134. period:100
[D][05:19:43][COMM]msg 0221 loss. last_tick:113122. cur_tick:114134. period:100
[D][05:19:43][COMM]msg 0224 loss. last_tick:113122. cur_tick:114135. period:100
[D][05:19:43][COMM]msg 0260 loss. last_tick:113122. cur_tick:114135. period:100
[D][05:19:43][COMM]msg 0280 loss. last_tick:113122. cur_tick:114136. period:100
[D][05:19:43][COMM]msg 02C0 loss. last_tick:113122. cur_tick:114136. period:100
[D][05:19:43][COMM]msg 02C1 loss. last_tick:113122. cur_tick:114136. period:100
[D][05:19:43][COMM]msg 02C2 loss. last_tick:113122. cur_tick:114137. period:100
[D][05:19:43][COMM]msg 02E0 loss. last

2025-07-31 22:36:11:706 ==>> _tick:113122. cur_tick:114137. period:100
[D][05:19:43][COMM]msg 02E1 loss. last_tick:113122. cur_tick:114137. period:100
[D][05:19:43][COMM]msg 02E2 loss. last_tick:113122. cur_tick:114138. period:100
[D][05:19:43][COMM]msg 0300 loss. last_tick:113122. cur_tick:114138. period:100
[D][05:19:43][COMM]msg 0301 loss. last_tick:113122. cur_tick:114138. period:100
[D][05:19:43][COMM]bat msg 0240 loss. last_tick:113123. cur_tick:114139. period:100. j,i:1 54
[D][05:19:43][COMM]bat msg 0241 loss. last_tick:113123. cur_tick:114139. period:100. j,i:2 55
[D][05:19:43][COMM]bat msg 0242 loss. last_tick:113123. cur_tick:114140. period:100. j,i:3 56
[D][05:19:43][COMM]bat msg 0244 loss. last_tick:113123. cur_tick:114140. period:100. j,i:5 58
[D][05:19:43][COMM]bat msg 024E loss. last_tick:113123. cur_tick:114140. period:100. j,i:15 68
[D][05:19:43][COMM]bat msg 024F loss. last_tick:113123. cur_tick:114141. period:100. j,i:16 69
[D][05:19:43][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 114141
[D][05:19:43][COMM]CAN message bat fault change: 0x00000000->0x0001802E 114141
[D][05:19:43][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 114142


2025-07-31 22:36:11:811 ==>>                                                                                                                                        

2025-07-31 22:36:11:916 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:36:12:021 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 22:36:12:126 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ate2 fail 204
[D][05:19:43][COMM]get soh error
[E][05:19:43][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:43][COMM]report elecbike
[W][05:19:43][PROT]remove success[1629955183],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:

2025-07-31 22:36:12:156 ==>> 43][PROT]add success [1629955183],send_path[3],type[5D03],priority[4],index[1],used[1

2025-07-31 22:36:12:261 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:36:12:366 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:36:12:471 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:36:12:576 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]read battery soc:255
[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 22:36:12:606 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:36:12:831 ==>> [W][05:19:45][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:45][COMM]arm_hub_enable: hub power: 0
[D][05:19:45][HSDK]hexlog index save 0 9216 113 @ 0 : 0
[D][05:19:45][HSDK]write save hexlog index [0]
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:36:13:283 ==>> [W][05:19:45][COMM]Power Off


2025-07-31 22:36:13:397 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:36:13:425 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:36:13:445 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:36:13:510 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:36:13:585 ==>> [D][05:19:45][FCTY]get_ext_48v_vol retry i = 0,volt = 16
[D][05:19:45][FCTY]get_ext_48v_vol retry i = 1,volt = 16
[

2025-07-31 22:36:13:694 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:36:13:708 ==>> 检测【检测小电池关机电流】
2025-07-31 22:36:13:723 ==>> 开始小电池电流采样
2025-07-31 22:36:13:730 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:13:795 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:36:14:806 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:36:14:881 ==>> CurrentBattery:ƽ��:69.03

2025-07-31 22:36:15:313 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:36:15:323 ==>> 【检测小电池关机电流】通过,【69.03uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:36:15:662 ==>> MES过站成功
2025-07-31 22:36:15:677 ==>> #################### 【测试结束】 ####################
2025-07-31 22:36:15:696 ==>> 关闭5V供电
2025-07-31 22:36:15:713 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:36:15:802 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:36:16:707 ==>> 关闭5V供电成功
2025-07-31 22:36:16:729 ==>> 关闭33V供电
2025-07-31 22:36:16:758 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:36:16:797 ==>> 5A A5 02 5A A5 


2025-07-31 22:36:16:902 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:36:17:708 ==>> 关闭33V供电成功
2025-07-31 22:36:17:722 ==>> 关闭3.7V供电
2025-07-31 22:36:17:744 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:36:17:798 ==>> 6A A6 02 A6 6A 


2025-07-31 22:36:17:903 ==>> Battery OFF
OVER 150


2025-07-31 22:36:18:701 ==>>  

