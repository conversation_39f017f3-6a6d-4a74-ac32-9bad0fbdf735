2025-07-31 20:41:50:674 ==>> MES查站成功:
查站序号:P5100010053135FD验证通过
2025-07-31 20:41:50:681 ==>> 扫码结果:P5100010053135FD
2025-07-31 20:41:50:690 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:41:50:691 ==>> 测试参数版本:2024.10.11
2025-07-31 20:41:50:693 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:41:50:694 ==>> 检测【打开透传】
2025-07-31 20:41:50:696 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:41:50:753 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:41:51:324 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:41:51:330 ==>> 检测【检测接地电压】
2025-07-31 20:41:51:335 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:41:51:450 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:41:51:657 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:41:51:660 ==>> 检测【打开小电池】
2025-07-31 20:41:51:663 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:41:51:750 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:41:51:962 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:41:51:965 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:41:51:968 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:41:52:050 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 20:41:52:263 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:41:52:265 ==>> 检测【等待设备启动】
2025-07-31 20:41:52:268 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:53:294 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:54:327 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:55:366 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:55:728 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:41:55:926 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer st 

2025-07-31 20:41:56:398 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:56:428 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:41:56:626 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:41:57:334 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:41:57:424 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:57:711 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:41:58:194 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:41:58:491 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:41:58:493 ==>> 检测【产品通信】
2025-07-31 20:41:58:495 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:41:58:624 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:41:58:780 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:41:58:782 ==>> 检测【初始化完成检测】
2025-07-31 20:41:58:784 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:41:58:837 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:41:58:943 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51

2025-07-31 20:41:58:988 ==>> ][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:41:59:114 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:41:59:116 ==>> 检测【关闭大灯控制1】
2025-07-31 20:41:59:118 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:41:59:263 ==>> [D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:41:59:368 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeri

2025-07-31 20:41:59:413 ==>> odCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:41:59:717 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:41:59:721 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:41:59:724 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:41:59:952 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:42:00:018 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:42:00:020 ==>> 检测【关闭仪表供电】
2025-07-31 20:42:00:022 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:42:00:303 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0
[D][05:17:52][COMM]3648 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:00:568 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:42:00:570 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:42:00:572 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:42:00:720 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:42:00:868 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:42:00:871 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:42:00:873 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:42:01:039 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:42:01:163 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:42:01:166 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:42:01:168 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:01:356 ==>> [D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]4659 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:42:01:452 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:42:01:455 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:42:01:458 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:42:01:554 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:42:01:740 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:42:01:746 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:42:01:753 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:42:01:812 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5010. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5011. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5011. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5011. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5012. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5012. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5013. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5013. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00

2025-07-31 20:42:01:857 ==>> C71E22217->0x0008F00C71E22217 5014
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5014
[D][05:17:54][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:17:54][COMM]read battery soc:255
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:42:02:043 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:42:02:047 ==>> 该项需要延时执行
2025-07-31 20:42:02:296 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:03:242 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:42:03:733 ==>>                                                                                                                                                                         value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904

2025-07-31 20:42:03:838 ==>> ].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]6691 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]

2025-07-31 20:42:03:943 ==>> Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:

2025-07-31 20:42:04:018 ==>> 17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:56][COMM]read battery soc:255


2025-07-31 20:42:04:350 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7702 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:05:345 ==>> [D][05:17:57][COMM]8713 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:05:680 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 20:42:06:055 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:42:06:072 ==>> 检测【33V输入电压ADC】
2025-07-31 20:42:06:077 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:06:436 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:17:58][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:2  volt:2 mv
[D][05:17:58][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:58][COMM]adc read battery ts volt:7 mv
[D][05:17:58][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv
[D][05:17:58][COMM]9725 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:06:605 ==>> 【33V输入电压ADC】通过,【32627mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:42:06:608 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:42:06:609 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:42:06:694 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1664mV
Get AD_V4 0mV
Get AD_V5 2781mV
Get AD_V6 1991mV
Get AD_V7 1091mV
OVER 150
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10022


2025-07-31 20:42:06:933 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:06:936 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:42:06:990 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:06:993 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:42:06:996 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 20:42:07:046 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:42:07:050 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:42:07:106 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:42:07:110 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:42:07:173 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:42:07:175 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:42:07:268 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2781mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:42:07:533 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:07:538 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:42:07:584 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10735 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:

2025-07-31 20:42:07:608 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:07:612 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:42:07:616 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 20:42:07:620 ==>> 59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:42:07:662 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:42:07:666 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:42:07:716 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:42:07:720 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:42:07:775 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:42:07:794 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:42:07:868 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2782mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:42:08:092 ==>>                                                                                                                                                     ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][COMM]read battery soc:255
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087784668

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539108

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 


2025-07-31 20:42:08:101 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:08:103 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:42:08:123 ==>> OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:42:08:159 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:08:162 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:42:08:166 ==>> 原始值:【2782】, 乘以分压基数【2】还原值:【5564】
2025-07-31 20:42:08:224 ==>> 【TP68_VCC5V5(ADV5)】通过,【5564mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:42:08:227 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:42:08:286 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:42:08:288 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:42:08:344 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:42:08:347 ==>> 检测【打开WIFI(1)】
2025-07-31 20:42:08:350 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:08:363 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:42:08:514 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:42:08:637 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:42:08:640 ==>> 检测【清空消息队列(1)】
2025-07-31 20:42:08:642 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:42:08:849 ==>> [D][05:18:01][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:01][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:42:08:934 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:42:08:936 ==>> 检测【打开GPS(1)】
2025-07-31 20:42:08:939 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:42:09:153 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 20:42:09:242 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:42:09:246 ==>> 检测【打开GSM联网】
2025-07-31 20:42:09:260 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:42:09:438 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:42:09:569 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:42:09:573 ==>> 检测【打开仪表供电1】
2025-07-31 20:42:09:576 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:42:09:755 ==>> [D][05:18:02][COMM]read battery soc:255
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:42:09:879 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:42:09:882 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:42:09:885 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:42:10:275 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05

2025-07-31 20:42:10:365 ==>> :18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"**************"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:42:10:439 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:42:10:442 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:42:10:444 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:42:10:715 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                              [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub 

2025-07-31 20:42:10:745 ==>> read adc[3],val[33317]


2025-07-31 20:42:10:991 ==>> 【读取主控ADC采集的仪表电压】通过,【33317mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:42:10:995 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:42:10:999 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:42:11:151 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:42:11:288 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:42:11:291 ==>> 检测【AD_V20电压】
2025-07-31 20:42:11:294 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:42:11:361 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:42:11:391 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:42:11:451 ==>>                                                                               :18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:42:11:676 ==>> [D][05:18:04][COMM]read battery soc:255


2025-07-31 20:42:11:901 ==>> 本次取值间隔时间:497ms
2025-07-31 20:42:11:944 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:42:12:026 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:42:12:056 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:42:12:132 ==>> 1A A1 10 00 00 

2025-07-31 20:42:12:236 ==>> 
Get AD_V20 1656mV
OVER 150
[D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,42,24,,,40,60,,,39,42,,,38,1*77

$GBGSV,2,2,07,39,,,38,41,,,36,14,,,43,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1609.909,1609.909,51.447,2097152,2097152,2097152*4C

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]s

2025-07-31 20:42:12:266 ==>> ub id: 23, ret: 6



2025-07-31 20:42:12:371 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:42:12:554 ==>> 本次取值间隔时间:491ms
2025-07-31 20:42:12:594 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:12:597 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:42:12:599 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:42:12:661 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:42:12:894 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:42:12:897 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:42:12:902 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:42:13:159 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,42,24,,,40,3,,,40,60,,,39,1*42

$GBGSV,3,2,09,39,,,39,14,,,38,42,,,38,59,,,38,1*7A

$GBGSV,3,3,09,41,,,36,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1612.199,1612.199,51.507,2097152,2097152,2097152*49



2025-07-31 20:42:13:475 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:42:13:480 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:42:13:484 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:42:13:551 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:42:13:701 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 20:42:13:844 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:42:13:848 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:42:13:852 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:42:14:050 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:42:14:127 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:42:14:130 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:42:14:134 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:42:14:155 ==>>                                                                                                                                                                                        ,15,40,,,37,41,,,36,5,,,34,38,,,34,1*4B

$GBGSV,4,4,15,4,,,33,2,,,33,25,,,37,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1542.835,1542.835,49.350,2097152,2097152,2097152*44



2025-07-31 20:42:14:351 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:42:14:414 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:42:14:417 ==>> 检测【AD_V21电压】
2025-07-31 20:42:14:422 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:42:14:471 ==>> 本次取值间隔时间:54ms
2025-07-31 20:42:14:563 ==>> 1A A1 20 00 00 
Get AD_V21 1067mV
OVER 150


2025-07-31 20:42:14:886 ==>> 本次取值间隔时间:404ms
2025-07-31 20:42:14:922 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:42:15:057 ==>> 1A A1 20 00 00 
Get AD_V21 1650mV
OVER 150


2025-07-31 20:42:15:162 ==>> 本次取值间隔时间:231ms
2025-07-31 20:42:15:180 ==>> $GBGGA,124218.997,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,24,,,40,3,,,40,60,,,40,1*4B

$GBGSV,5,2,19,39,,,39,14,,,39,59,,,39,25,,,39,1*7D

$GBGSV,5,3,19,42,,,38,40,,,37,41,,,36,16,,,36,1*77

$GBGSV,5,4,19,38,,,35,1,,,35,2,,,34,5,,,33,1*45

$GBGSV,5,5,19,4,,,32,34,,,32,44,,,31,1*4F

$GBRMC,124218.997,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124218.997,0.000,1520.880,1520.880,48.669,2097152,2097152,2097152*5F



2025-07-31 20:42:15:198 ==>> 【AD_V21电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:15:201 ==>> 检测【关闭仪表供电2】
2025-07-31 20:42:15:203 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:42:15:344 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:42:15:481 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:42:15:485 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:42:15:488 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:42:15:644 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:42:15:749 ==>> [D][05:18:08][COMM]read battery soc:255
$GBGGA,124219.597,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,40,3,,,40,60,,,40,1*46

$GBGSV,6,2,24,25,,,40,39,,,39,14,,,39,59,,,39,1*7E

$G

2025-07-31 20:42:15:768 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:42:15:773 ==>> 检测【打开AccKey2供电】
2025-07-31 20:42:15:778 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:42:15:809 ==>> BGSV,6,3,24,42,,,38,40,,,37,41,,,36,16,,,36,1*7A

$GBGSV,6,4,24,6,,,36,38,,,35,1,,,35,7,,,34,1*4B

$GBGSV,6,5,24,2,,,33,5,,,32,34,,,32,4,,,31,1*45

$GBGSV,6,6,24,44,,,31,13,,,30,10,,,28,9,,,38,1*4A

$GBRMC,124219.597,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124219.597,0.000,1483.535,1483.535,47.508,2097152,2097152,2097152*59



2025-07-31 20:42:15:914 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:42:16:065 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:42:16:070 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:42:16:075 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:16:379 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:08][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:08][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:08][COMM]adc read battery ts volt:5 mv
[D][05:18:08][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:08][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:42:16:611 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33234mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:42:16:615 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:42:16:619 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:42:16:779 ==>> $GBGGA,124220.577,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,40,3,,,40,60,,,40,1*46

$GBGSV,6,2,24,25,,,40,39,,,39,14,,,39,59,,,39,1*7E

$GBGSV,6,3,24,42,,,38,40,,,37,41,,,37,16,,,36,1*7B

$GBGSV,6,4,24,1,,,36,9,,,35,6,,,35,38,,,35,1*44

$GBGSV,6,5,24,7,,,35,2,,,34,13,,,34,5,,,32,1*46

$GBGSV,6,6,24,34,,,32,4,,,31,44,,,31,10,,,29,1*48

$GBRMC,124220.577,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124220.577,0.000,1495.982,1495.982,47.887,2097152,2097152,2097152*57



2025-07-31 20:42:16:854 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:42:16:908 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:42:16:911 ==>> 该项需要延时执行
2025-07-31 20:42:17:757 ==>> $GBGGA,124221.557,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,40,3,,,40,25,,,40,1*47

$GBGSV,6,2,24,60,,,39,39,,,39,14,,,39,59,,,39,1*71

$GBGSV,6,3,24,42,,,38,40,,,37,41,,,37,16,,,36,1*7B

$GBGSV,6,4,24,1,,,36,9,,,35,6,,,35,38,,,35,1*44

$GBGSV,6,5,24,7,,,35,13,,,35,2,,,34,34,,,32,1*75

$GBGSV,6,6,24,44,,,32,5,,,31,4,,,31,10,,,30,1*72

$GBRMC,124221.557,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124221.557,0.000,1497.702,1497.702,47.934,2097152,2097152,2097152*5D

[D][05:18:10][COMM]read battery soc:255


2025-07-31 20:42:18:743 ==>> $GBGGA,124222.537,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,3,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,40,39,,,39,14,,,39,59,,,39,1*7F

$GBGSV,7,3,25,42,,,38,40,,,37,41,,,37,16,,,36,1*7B

$GBGSV,7,4,25,1,,,36,9,,,35,6,,,35,38,,,35,1*44

$GBGSV,7,5,25,7,,,35,13,,,35,2,,,34,44,,,32,1*72

$GBGSV,7,6,25,34,,,31,5,,,31,4,,,31,10,,,31,1*77

$GBGSV,7,7,25,26,,,30,1*76

$GBRMC,124222.537,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124222.537,0.000,1489.213,1489.213,47.672,2097152,2097152,2097152*55



2025-07-31 20:42:19:773 ==>> $GBGGA,124223.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,3,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,40,39,,,39,14,,,39,59,,,39,1*7F

$GBGSV,7,3,25,42,,,38,40,,,37,41,,,36,16,,,36,1*7A

$GBGSV,7,4,25,1,,,36,9,,,35,6,,,35,38,,,35,1*44

$GBGSV,7,5,25,7,,,35,13,,,35,2,,,34,44,,,32,1*72

$GBGSV,7,6,25,10,,,32,34,,,31,5,,,31,4,,,31,1*74

$GBGSV,7,7,25,26,,,31,1*77

$GBRMC,124223.517,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124223.517,0.000,1490.865,1490.865,47.719,2097152,2097152,2097152*5A

[D][05:18:12][COMM]read battery soc:255


2025-07-31 20:42:19:909 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:42:19:914 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:42:19:927 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:20:264 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:0 mv
[D][05:18:12][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:42:20:515 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:42:20:519 ==>> 检测【打开AccKey1供电】
2025-07-31 20:42:20:522 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:42:20:798 ==>> $GBGGA,124224.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,25,,,40,3,,,39,1*49

$GBGSV,7,2,25,60,,,39,39,,,39,14,,,39,59,,,39,1*71

$GBGSV,7,3,25,42,,,38,40,,,38,41,,,37,16,,,36,1*74

$GBGSV,7,4,25,1,,,36,9,,,35,6,,,35,38,,,35,1*44

$GBGSV,7,5,25,7,,,35,13,,,35,2,,,34,44,,,32,1*72

$GBGSV,7,6,25,10,,,32,34,,,31,5,,,31,4,,,31,1*74

$GBGSV,7,7,25,26,,,31,1*77

$GBRMC,124224.517,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124224.517,0.000,1490.862,1490.862,47.716,2097152,2097152,2097152*52

[W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:42:21:394 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:42:21:398 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:42:21:402 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:42:21:459 ==>> 1A A1 00 40 00 
Get AD_V14 2677mV
OVER 150


2025-07-31 20:42:21:658 ==>> 原始值:【2677】, 乘以分压基数【2】还原值:【5354】
2025-07-31 20:42:21:781 ==>> $GBGGA,124225.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,3,,,40,1*46

$GBGSV,7,2,25,60,,,40,39,,,39,14,,,39,59,,,39,1*7F

$GBGSV,7,3,25,42,,,38,40,,,38,41,,,37,16,,,36,1*74

$GBGSV,7,4,25,1,,,36,6,,,36,9,,,35,38,,,35,1*47

$GBGSV,7,5,25,7,,,35,13,,,35,2,,,34,44,,,32,1*72

$GBGSV,7,6,25,10,,,32,5,,,32,4,,,32,34,,,31,1*74

$GBGSV,7,7,25,26,,,31,1*77

$GBRMC,124225.517,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124225.517,0.000,1500.812,1500.812,48.034,2097152,2097152,2097152*5B

[D][05:18:14][COMM]read battery soc:255


2025-07-31 20:42:21:848 ==>> 【读取AccKey1电压(ADV14)前】通过,【5354mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:42:21:852 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:42:21:854 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:22:162 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:2 mv
[D][05:18:14][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:42:22:550 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:42:22:554 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:42:22:556 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:42:22:771 ==>> $GBGGA,124226.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,40,24,,,40,3,,,40,1*47

$GBGSV,7,2,25,60,,,39,39,,,39,14,,,39,59,,,39,1*71

$GBGSV,7,3,25,42,,,38,40,,,38,41,,,37,16,,,37,1*75

$GBGSV,7,4,25,1,,,36,13,,,36,6,,,35,9,,,35,1*4E

$GBGSV,7,5,25,38,,,35,7,,,35,2,,,34,10,,,33,1*7B

$GBGSV,7,6,25,44,,,32,5,,,32,4,,,32,26,,,32,1*75

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,124226.517,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124226.517,0.000,1502.461,1502.461,48.077,2097152,2097152,2097152*5F

[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:42:22:997 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:42:23:000 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:42:23:005 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:42:23:063 ==>> 1A A1 00 40 00 
Get AD_V14 2675mV
OVER 150


2025-07-31 20:42:23:262 ==>> 原始值:【2675】, 乘以分压基数【2】还原值:【5350】
2025-07-31 20:42:23:309 ==>> [D][05:18:15][COMM]S->M yaw:INVALID


2025-07-31 20:42:23:465 ==>> 【读取AccKey1电压(ADV14)后】通过,【5350mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:42:23:468 ==>> 检测【打开WIFI(2)】
2025-07-31 20:42:23:473 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:23:812 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

$GBGGA,124227.517,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,40,24,,,40,3,,,40,1*47

$GBGSV,7,2,25,60,,,40,39,,,39,14,,,39,59,,,39,1*7F

$GBGSV,7,3,25,42,,,38,40,,,38,41,,,37,16,,,36,1*74

$GBGSV,7,4,25,1,,,36,13,,,35,6,,,35,9,,,35,1*4D

$GBGSV,7,5,25,38,,,35,7,,,35,2,,,34,10,,,33,1*7B

$GBGSV,7,6,25,44,,,32,4,,,32,26,,,32,5,,,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,124227.517,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124227.517,0.000,1499.150,1499.150,47.977,2097152,2097152,2097152*58

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6
[D][05:18:16][COMM]read battery soc:255


2025-07-31 20:42:24:182 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:42:24:188 ==>> 检测【转刹把供电】
2025-07-31 20:42:24:193 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:42:24:360 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:16][COMM]M->S yaw:INVALID


2025-07-31 20:42:24:571 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-67
+WIFISCAN:4,2,CC057790A741,-75
+WIFISCAN:4,3,44A1917CAD81,-85

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 20:42:24:630 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:42:24:636 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:42:24:641 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:42:24:676 ==>> $GBGGA,124228.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,24,,,40,3,,,40,1*44

$GBGSV,7,2,26,60,,,40,39,,,39,14,,,39,59,,,39,1*7C

$GBGSV,7,3,26,42,,,38,40,,,37,41,,,37,16,,,36,1*78

$GBGSV,7,4,26,1,,,36,13,,,35,6,,,35,9,,,35,1*4E

$GBGSV,7,5,26,38,

2025-07-31 20:42:24:721 ==>> ,,35,7,,,35,2,,,34,10,,,33,1*78

$GBGSV,7,6,26,44,,,32,4,,,32,26,,,32,5,,,31,1*75

$GBGSV,7,7,26,34,,,31,23,,,30,1*75

$GBRMC,124228.517,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124228.517,0.000,1487.740,1487.740,47.620,2097152,2097152,2097152*5A



2025-07-31 20:42:24:736 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:42:24:826 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:42:24:856 ==>> 1A A1 00 80 00 
Get AD_V15 2402mV
OVER 150


2025-07-31 20:42:24:901 ==>> 原始值:【2402】, 乘以分压基数【2】还原值:【4804】
2025-07-31 20:42:25:002 ==>> 【读取AD_V15电压(前)】通过,【4804mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:42:25:008 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:42:25:012 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:42:25:116 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:25:121 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:42:25:252 ==>> 1A A1 01 00 00 
Get AD_V16 2449mV
OVER 150


2025-07-31 20:42:25:267 ==>> 原始值:【2449】, 乘以分压基数【2】还原值:【4898】
2025-07-31 20:42:25:394 ==>> 【读取AD_V16电压(前)】通过,【4898mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:42:25:397 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:42:25:400 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:25:417 ==>> [D][05:18:17][GNSS]recv submsg id[3]


2025-07-31 20:42:25:840 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:17][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:17][COMM]adc read battery ts volt:0 mv
[D][05:18:17][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3101  volt:5450 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,124229.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,24,,,40,3,,,40,1*44

$GBGSV,7,2,26,60,,,40,39,,,39,14,,,39,59,,,39,1*7C

$GBGSV,7,3,26,42,,,38,40,,,37,41,,,37,16,,,36,1*78

$GBGSV,7,4,26,1,,,36,13,,,35,6,,,35,9,,,35,1*4E

$GBGSV,7,5,26,38,,,35,7,,,35,2,,,34,10,,,33,1*78



2025-07-31 20:42:25:885 ==>> 
$GBGSV,7,6,26,26,,,33,44,,,32,4,,,32,5,,,31,1*74

$GBGSV,7,7,26,34,,,31,23,,,31,1*74

$GBRMC,124229.517,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124229.517,0.000,1490.924,1490.924,47.716,2097152,2097152,2097152*5F

[D][05:18:18][COMM]read battery soc:255


2025-07-31 20:42:25:953 ==>> 【转刹把供电电压(主控ADC)】通过,【5450mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:42:25:957 ==>> 检测【转刹把供电电压】
2025-07-31 20:42:25:962 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:26:265 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:18][COMM]adc read battery ts volt:2 mv
[D][05:18:18][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3100  volt:5449 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:42:26:495 ==>> 【转刹把供电电压】通过,【5449mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:42:26:501 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:42:26:506 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:26:750 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,124230.517,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,24,,,40,3,,,40,1*44

$GBGSV,7,2,26,60,,,40,39,,,39,14,,,39,59,,,39,1*7C

$GBGSV,7,3,26,42,,,38,40,,,38,41,,,37,16,,,36,1*77

$GBGSV,7,4,26,1,,,36,13,,,35,6,,,35,9,,,35,1*4E

$GBGSV,7,5,26,38,,,35,7,,,35,2,,,34,10,,,33,1*78

$GBGSV,7,6,26,26,,,33,44,,,32,4,,,32,5,,,31,1*74

$GBGSV,7,7,26,34,,,31,23,,,31,1*74

$GBRMC,124230.517,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124230.517,0.000,1492.519,1492.519,47.768,2097152,2097152,2097152*5E



2025-07-31 20:42:27:050 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:42:27:055 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:42:27:059 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:27:158 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:42:27:263 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:27:269 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:42:27:368 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:42:27:413 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:42:27:458 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:42:27:508 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:42:27:513 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:42:27:518 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:27:609 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:27:716 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:27:806 ==>> $GBGGA,124231.517,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,24,,,40,3,,,40,1*45

$GBGSV,7,2,27,60,,,40,39,,,39,14,,,39,59,,,39,1*7D

$GBGSV,7,3,27,42,,,38,40,,,38,41,,,37,16,,,36,1*76

$GBGSV,7,4,27,1,,,36,13,,,35,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,38,,,35,7,,,35,2,,,34,8,,,33,1*40

$GBGSV,7,6,27,10,,,33,26,,,33,44,,,32,4,,,32,1*43

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,31,1*42

$GBRMC,124231.517,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124231.517,0.000,742.576,742.576,679.106,2097152,2097152,2097152*6A

[D][05:18:20][COMM]read battery soc:255


2025-07-31 20:42:27:821 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:27:911 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:20][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:42:27:926 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:28:031 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:28:139 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:28:154 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<<
00 00 00 00 00 
head err!


2025-07-31 20:42:28:244 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:28:319 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:42:28:349 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:42:28:362 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:42:28:454 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:42:28:574 ==>> [D][05:18:20][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:20][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:42:28:617 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:42:28:621 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:42:28:627 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:42:28:679 ==>> $GBGGA,124232.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,

2025-07-31 20:42:28:754 ==>> 60,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,14,,,40,25,,,40,59,,,39,39,,,39,1*72

$GBGSV,7,3,27,40,,,38,42,,,38,41,,,37,1,,,36,1*40

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,33,1*4F

$GBGSV,7,6,27,10,,,33,8,,,33,44,,,32,4,,,32,1*7F

$GBGSV,7,7,27,23,,,32,5,,,31,34,,,31,1*41

$GBRMC,124232.517,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124232.517,0.000,744.878,744.878,681.211,2097152,2097152,2097152*6B

3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:42:28:926 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:42:28:929 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:42:28:934 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:42:29:060 ==>> 3A A3 04 01 A3 


2025-07-31 20:42:29:150 ==>> ON_OUT4
OVER 150


2025-07-31 20:42:29:235 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:42:29:240 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:42:29:243 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:42:29:360 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:42:29:563 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:42:29:571 ==>> 检测【左刹电压测试1】
2025-07-31 20:42:29:579 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:29:872 ==>> $GBGGA,124233.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,40,,,38,42,,,38,41,,,37,1,,,36,1*40

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,4,,,33,26,,,32,44,,,32,1*7A

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,31,1*42

$GBRMC,124233.517,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124233.517,0.000,742.576,742.576,679.106,2097152,2097152,2097152*68

[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1716  volt:2262 mv
[D][05:18:22][COMM]adc read right brake adc:1712  volt:2257 mv
[D][05:18:22][COMM]adc read throttle adc:1711  volt:2255 mv
[D][05:18:22][COMM]adc read battery ts volt:10 mv
[D][05:18:22][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_

2025-07-31 20:42:29:917 ==>> id adc:9  volt:7 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:22][COMM]read battery soc:255
[D][05:18:22][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:42:30:118 ==>> 【左刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 20:42:30:124 ==>> 检测【右刹电压测试1】
2025-07-31 20:42:30:157 ==>> 【右刹电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 20:42:30:161 ==>> 检测【转把电压测试1】
2025-07-31 20:42:30:189 ==>> 【转把电压测试1】通过,【2255】符合目标值【2250】至【2500】要求!
2025-07-31 20:42:30:192 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:42:30:195 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:42:30:247 ==>> 3A A3 03 00 A3 


2025-07-31 20:42:30:352 ==>> OFF_OUT3
OVER 150


2025-07-31 20:42:30:473 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:42:30:477 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:42:30:483 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:42:30:562 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:42:30:758 ==>> $GBGGA,124234.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,42,,,38,40,,,37,41,,,37,1,,,36,1*4F

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,44,,,32,4,,,32,1*7B

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,31,1*42

$GBRMC,124234.517,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124234.517,0.000,741.044,741.044,677.705,2097152,2097152,2097152*64



2025-07-31 20:42:30:762 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:42:30:766 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:42:30:769 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:42:30:848 ==>> 3A A3 05 00 A3 


2025-07-31 20:42:30:953 ==>> OFF_OUT5
OVER 150


2025-07-31 20:42:31:043 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:42:31:047 ==>> 检测【左刹电压测试2】
2025-07-31 20:42:31:052 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:42:31:363 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:23][COMM]adc read battery ts volt:6 mv
[D][05:18:23][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:42:31:595 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:42:31:599 ==>> 检测【右刹电压测试2】
2025-07-31 20:42:31:627 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:42:31:644 ==>> 检测【转把电压测试2】
2025-07-31 20:42:31:660 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:42:31:664 ==>> 检测【晶振检测】
2025-07-31 20:42:31:667 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:42:31:746 ==>> $GBGGA,124235.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,40,,,38,42,,,38,41,,,37,1,,,36,1*40

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,5,,,32,44,,,32,1*7A

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,31,1*40

$GBRMC,124235.517,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124235.517,0.000,742.575,742.575,679.105,2097152,2097152,2097152*6D



2025-07-31 20:42:31:851 ==>>                                          [W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]


2025-07-31 20:42:31:956 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:42:31:961 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:42:31:964 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:42:32:062 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1663mV
Get AD_V4 1650mV
Get AD_V5 2785mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:42:32:247 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:42:32:251 ==>> 检测【检测BootVer】
2025-07-31 20:42:32:254 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:42:32:615 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071539108
[D][05:18:24][FCTY]HardwareID  = 867222087784668
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = E1A129508726
[D][05:18:24][FCTY]Bat         = 3944 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 32, adc = 1287
[D][05:18:24][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][F

2025-07-31 20:42:32:660 ==>> CTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3694 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:42:32:765 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:42:32:797 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:42:32:809 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:42:32:827 ==>> 检测【检测固件版本】
2025-07-31 20:42:32:831 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:42:32:837 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:42:32:843 ==>> 检测【检测蓝牙版本】
2025-07-31 20:42:32:855 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:42:32:860 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:42:32:863 ==>> 检测【检测MoBikeId】
2025-07-31 20:42:32:883 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:42:32:887 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:42:32:893 ==>> 检测【检测蓝牙地址】
2025-07-31 20:42:32:911 ==>> 取到目标值:E1A129508726
2025-07-31 20:42:32:918 ==>> 【检测蓝牙地址】通过,【E1A129508726】符合目标值【】要求!
2025-07-31 20:42:32:926 ==>> 提取到蓝牙地址:E1A129508726
2025-07-31 20:42:32:933 ==>> 检测【BOARD_ID】
2025-07-31 20:42:32:941 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:42:32:962 ==>> 检测【检测充电电压】
2025-07-31 20:42:32:972 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:42:32:977 ==>> 检测【检测VBUS电压1】
2025-07-31 20:42:33:001 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:42:33:009 ==>> 检测【检测充电电流】
2025-07-31 20:42:33:031 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:42:33:039 ==>> 检测【检测IMEI】
2025-07-31 20:42:33:044 ==>> 取到目标值:867222087784668
2025-07-31 20:42:33:060 ==>> 【检测IMEI】通过,【867222087784668】符合目标值【】要求!
2025-07-31 20:42:33:064 ==>> 提取到IMEI:867222087784668
2025-07-31 20:42:33:068 ==>> 检测【检测IMSI】
2025-07-31 20:42:33:072 ==>> 取到目标值:460130071539108
2025-07-31 20:42:33:093 ==>> 【检测IMSI】通过,【460130071539108】符合目标值【】要求!
2025-07-31 20:42:33:098 ==>> 提取到IMSI:460130071539108
2025-07-31 20:42:33:104 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:42:33:114 ==>> 取到目标值:460130071539108
2025-07-31 20:42:33:123 ==>> 【校验网络运营商(移动)】通过,【460130071539108】符合目标值【】要求!
2025-07-31 20:42:33:143 ==>> 检测【打开CAN通信】
2025-07-31 20:42:33:148 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:42:33:263 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:42:33:421 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:42:33:425 ==>> 检测【检测CAN通信】
2025-07-31 20:42:33:428 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:42:33:551 ==>> can send success


2025-07-31 20:42:33:596 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:42:33:656 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:42:33:716 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:42:33:731 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:42:33:736 ==>> 检测【关闭CAN通信】
2025-07-31 20:42:33:739 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:42:33:821 ==>> [D][05:18:26][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 37022
$GBGGA,124237.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,14,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,41,,,37,1,,,36,1*4F

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,44,,,32,4,,,32,1*7B

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,124237.517,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124237.517,0.000,739.513,739.513,676.305,2097152,2097152,2097152*62

[D][05:18:26][COMM]read battery soc:255
标准帧 ID:00000600 DATA:51 52 53 5

2025-07-31 20:42:33:851 ==>> 4 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:42:34:018 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:42:34:022 ==>> 检测【打印IMU STATE】
2025-07-31 20:42:34:028 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:42:34:251 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:42:34:316 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:42:34:322 ==>> 检测【六轴自检】
2025-07-31 20:42:34:327 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:42:34:550 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:42:35:023 ==>> $GBGGA,124238.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,14,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,41,,,37,1,,,36,1*4F

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,44,,,32,5,,,31,1*79

$GBGSV,7,7,27,4,,,31,34,,,31,23,,,30,1*42

$GBRMC,124238.517,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124238.517,0.000,738.749,738.749,675.607,2097152,2097152,2097152*69



2025-07-31 20:42:35:746 ==>> $GBGGA,124239.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,42,,,38,40,,,37,41,,,37,1,,,36,1*4F

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,44,,,32,34,,,32,1*48

$GBGSV,7,7,27,4,,,32,5,,,31,23,,,30,1*73

$GBRMC,124239.517,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124239.517,0.000,741.045,741.045,677.706,2097152,2097152,2097152*6A



2025-07-31 20:42:35:776 ==>>                                          

2025-07-31 20:42:36:264 ==>> [D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:42:36:415 ==>> [D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39772 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-6,5,4034]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 20:42:36:691 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:42:36:698 ==>> 检测【打印IMU STATE2】
2025-07-31 20:42:36:704 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:42:36:751 ==>> $GBGGA,124240.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,40,,,38,42,,,38,41,,,37,1,,,36,1*40

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,10,,,33,1*4A

$GBGSV,7,6,27,8,,,33,26,,,32,44,,,32,4,,,32,1*7B

$GBGSV,7,7,27,34,,,32,5,,,31,23,,,31,1*41

$GBRMC,124240.517,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124240.517,0.000,742.575,742.575,679.105,2097152,2097152,2097152*6F



2025-07-31 20:42:36:856 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:42:36:979 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:42:36:986 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:42:36:992 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:42:37:054 ==>> 5A A5 02 5A A5 


2025-07-31 20:42:37:159 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:42:37:268 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:42:37:275 ==>> 检测【检测VBUS电压2】
2025-07-31 20:42:37:281 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:42:37:339 ==>> [D][05:18:29][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:42:37:656 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539108
[D][05:18:29][FCTY]HardwareID  = 867222087784668
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E1A129508726
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 8, adc = 341
[D][05:18:29][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1

2025-07-31 20:42:37:760 ==>> _GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3694 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 20:42:37:805 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:42:38:121 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539108
[D][05:18:30][FCTY]HardwareID  = 867222087784668
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E1A129508726
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 11800 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 180
[D][05:18:30][FCTY]Acckey1 vol = 5559 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3694 m

2025-07-31 20:42:38:151 ==>> v
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:42:38:359 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:42:38:804 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539108
[D][05:18:30][FCTY]HardwareID  = 867222087784668
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E1A129508726
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 50 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 145
[D][05:18:30][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3694 mv
[D][05:18:30][FCTY]======

2025-07-31 20:42:38:909 ==>> ============== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][COMM]msg 0601 loss. last_tick:37002. cur_tick:42018. period:500
[D][05:18:31][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 42018
$GBGGA,124242.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,14,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,41,,,37,1,,,36,1*4F

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,33,1*4F

$GBGSV,7,6,27,10,,,33,8,,,33,44,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,32,23,,,31,1*43

$GBRMC,124242.517,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124242.517,0.000,743.332,743.332,679.796,2097152,2097152,2097152*61



2025-07-31 20:42:38:914 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:42:38:922 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:42:38:943 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:42:39:059 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:42:39:134 ==>> [D][05:18:31][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 3


2025-07-31 20:42:39:219 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:42:39:223 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:42:39:230 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:42:39:364 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:42:39:515 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:42:39:522 ==>> 检测【打开WIFI(3)】
2025-07-31 20:42:39:535 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:39:576 ==>> [D][05:18:31][COMM]read battery soc:255
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:31][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][PROT]index:0
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]=========================================

2025-07-31 20:42:39:679 ==>> ==================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900005]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][PROT]index:0 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900005]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][M2M ]m2m switch to:

2025-07-31 20:42:39:784 ==>>  M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B545308B407167D3A61C6B1313AB9375FB547C9A7BB68742CEADBBC39E0FE018D8B12591844D549DCA325782201A24B9D6E7958381BA2A2E78AF535BF5EB07A231CB2C5DA3768459B7FE3675ACBA7B30484B5E
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 20:42:39:859 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:42:39:934 ==>>                                                                                                                                                                                                                                                       

2025-07-31 20:42:40:537 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:41:583 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:41:613 ==>> $GBGGA,124244.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,14,,,39,1*7C

$GBGSV,7,3,27,40,,,38,42,,,38,41,,,37,1,,,36,1*40

$GBGSV,7,4,27,16,,,36,7,,,35,13,,,35,38,,,35,1*4A

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,33,1*4F

$GBGSV,7,6,27,10,,,33,8,,,33,44,,,33,4,,,32,1*7E

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,124244.517,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124244.517,0.000,742.577,742.577,679.107,2097152,2097152,2097152*69

[D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------
[W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6

2025-07-31 20:42:41:718 ==>> 
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]more than the number of battery plugs
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:33][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:33][COMM]Bat auth off fail, error:-1
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec8

2025-07-31 20:42:41:823 ==>> 00m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-66
+WIFISCAN:4,2,44A1917CAD80,-80
+WIFISCAN:4,3,CC057790A7C0,-83

[D][05:18:33][CAT1]wifi scan report total[4]
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get sta

2025-07-31 20:42:41:872 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:42:41:879 ==>> 检测【扩展芯片hw】
2025-07-31 20:42:41:886 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:42:41:928 ==>> te1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get soc error
[E][05:18:33][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][COMM]Main Task receive event:61
[D][05:18:33][COMM][D301]:type:3, trace id:280
[D][05:18:33][COMM]id[], hw[000
[D][05:18:33][COMM]get mcMaincircuitVolt error
[D][05:18:33][COMM]get mcSubcircuitVolt error
[D][05:18:33][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get bat work state err
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][PROT]index:1
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]re

2025-07-31 20:42:42:033 ==>> try_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][COMM]Receive Bat Lock cmd 0
[D][05:18:33][COMM]VBUS is 1
[W][05:18:33][PROT]remove success[1629955113],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:33][COMM]Main Task receive event:61 finished processing
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][COMM]read battery soc:255
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A4

2025-07-31 20:42:42:138 ==>> 53A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM

2025-07-31 20:42:42:228 ==>> ]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][GNSS]recv submsg id[3]
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:42:42:333 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:42:42:378 ==>>                                                                                                                 [W][05:18:34][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:34][CAT1]gsm read msg sub id: 12
[D][05:18:34][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 20:42:42:633 ==>>                                                                                            PTION i[1] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:500

[D][05:18:34][CAT1]<<< 
+MT ERROR:500

+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-67


2025-07-31 20:42:42:678 ==>> +WIFISCAN:4,2,603A7CF67DD4,-82
+WIFISCAN:4,3,CC057790A6E0,-82

[D][05:18:34][CAT1]exec over: func id: 12, ret: 147
[W][05:18:34][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:34][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:42:42:768 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:42:42:921 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:42:42:927 ==>> 检测【扩展芯片boot】
2025-07-31 20:42:42:954 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:42:42:958 ==>> 检测【扩展芯片sw】
2025-07-31 20:42:42:986 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:42:42:994 ==>> 检测【检测音频FLASH】
2025-07-31 20:42:43:013 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:42:43:135 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:42:43:195 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:42:43:782 ==>> $GBGGA,124243.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,61,190,40,16,54,25,36,1*72

$GBGSV,7,2,27,6,53,38,35,59,52,129,39,39,52,9,39,24,49,16,40,1*46

$GBGSV,7,3,27,1,48,126,36,2,46,238,34,40,45,172,38,7,42,189,35,1*49

$GBGSV,7,4,27,60,41,238,40,42,40,165,38,9,38,319,35,4,32,112,32,1*7C

$GBGSV,7,5,27,10,32,197,33,26,28,238,33,41,25,319,37,13,24,209,35,1*7D

$GBGSV,7,6,27,5,22,257,31,38,21,200,35,8,21,204,33,25,,,40,1*47

$GBGSV,7,7,27,44,,,33,34,,,31,23,,,29,1*7C

$GBRMC,124243.504,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,124243.504,0.781,0.238,0.204,0.312,3.096,6.709,13*5F

[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:42:44:652 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D1

2025-07-31 20:42:44:727 ==>> 13311331133113311331B88B337F372F0DB8197A728C434CA3773D0A7BC8988CE08A702AFAF9B255DA50A03DA9A975706F09CEAA2105949E5C3C6DE174D50E73375261835CEAEFF00F10AD46449F10B45FDE43EC55CB0CB051535361558A8
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 20:42:44:817 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:42:45:276 ==>> [D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:37][COMM]read battery soc:255
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:37][COMM]accel parse set 0
[D][05:18:37][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:42:45:738 ==>> [D][05:18:38][COMM]49127 imu init OK


2025-07-31 20:42:46:494 ==>> $GBGGA,124245.504,2301.2575817,N,11421.9412991,E,1,23,0.59,72.048,M,-1.770,M,,*50

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,25,60,1.10,0.59,0.93,4*03

$GBGSA,A,3,07,42,13,40,10,38,41,44,34,26,23,,1.10,0.59,0.93,4*04

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,1,48,126,36,2,48,239,34,9,47,322,35,25,43,286,40,1*4C

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,2,1,05,33,69,272,42,39,52,9,40,24,49,16,41,42,40,165,39,5*49

$GBGSV,2,2,05,41,25,319,36,5*4B

$GBRMC,124245.504,A,2301.2575817,N,11421.9412991,E,0.000,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[D][05:18:38][GNSS]HD8040 GPS
[D][05:18:38][GNSS]GPS diff_sec 124010647, report 0x42 frame
$GBGST,124245.504,2.398,0.401,0.376,0.537,2.193,2.877,5.998*78

[D][05:18:38][COMM]Main Task receive event:131
[D][05:18:38][COMM]index:0,power_mode

2025-07-31 20:42:46:599 ==>> :0xFF
[D][05:18:38][COMM]index:1,sound_mode:0xFF
[D][05:18:38][COMM]index:2,gsensor_mode:0xFF
[D][05:18:38][COMM]index:3,report_freq_mode:0xFF
[D][05:18:38][COMM]index:4,report_period:0xFF
[D][05:18:38][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:38][COMM]index:6,normal_reset_period:0xFF
[D][05:18:38][COMM]index:7,spock_over_speed:0xFF
[D][05:18:38][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:38][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:38][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:38][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:38][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:38][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:38][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:38][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:38][COMM]index:16,imu_config_params:0xFF
[D][05:18:38][COMM]index:17,long_connect_params:0xFF
[D][05:18:38][COMM]index:18,detain_mark:0xFF
[D][05:18:38][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:38][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:38][COMM]index:21,mc_mode:0xFF
[D][05:18:38][COMM]index:22,S_mode:0xFF
[D][05:18:38][COMM]index:23,overweight:0xFF
[D][05:18:38][COMM]index:24,standstil

2025-07-31 20:42:46:704 ==>> l_mode:0xFF
[D][05:18:38][COMM]index:25,night_mode:0xFF
[D][05:18:38][COMM]index:26,experiment1:0xFF
[D][05:18:38][COMM]index:27,experiment2:0xFF
[D][05:18:38][COMM]index:28,experiment3:0xFF
[D][05:18:38][COMM]index:29,experiment4:0xFF
[D][05:18:38][COMM]index:30,night_mode_start:0xFF
[D][05:18:38][COMM]index:31,night_mode_end:0xFF
[D][05:18:38][COMM]index:33,park_report_minutes:0xFF
[D][05:18:38][COMM]index:34,park_report_mode:0xFF
[D][05:18:38][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:38][COMM]index:38,charge_battery_para: FF
[D][05:18:38][COMM]index:39,multirider_mode:0xFF
[D][05:18:38][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:38][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:38][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:38][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:38][COMM]index:44,riding_duration_config:0xFF
[D][05:18:38][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:38][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:38][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:38][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:38][COMM]index:49,mc_load_startup:0xFF
[D][05:18:38][COMM]index:50,mc_tcs_mode:0xFF
[D][0

2025-07-31 20:42:46:809 ==>> 5:18:38][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:38][COMM]index:52,traffic_mode:0xFF
[D][05:18:38][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:38][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:38][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:38][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:38][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:38][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:38][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:38][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:38][COMM]index:63,experiment5:0xFF
[D][05:18:38][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:38][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:38][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:38][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:38][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:38][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:38][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:38][COMM]index:72,experiment6:0xFF
[D][05:18:38][COMM]index:73,experiment7:0xFF
[D][05:18:38][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:38][COMM]index:75,zero_value_from_

2025-07-31 20:42:46:914 ==>> server:-1
[D][05:18:38][COMM]index:76,multirider_threshold:255
[D][05:18:38][COMM]index:77,experiment8:255
[D][05:18:38][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:38][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:38][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:38][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:38][COMM]index:83,loc_report_interval:255
[D][05:18:38][COMM]index:84,multirider_threshold_p2:255
[D][05:18:38][COMM]index:85,multirider_strategy:255
[D][05:18:38][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:38][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:38][COMM]index:90,weight_param:0xFF
[D][05:18:38][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:38][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:38][COMM]index:95,current_limit:0xFF
[D][05:18:38][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:38][COMM]index:100,location_mode:0xFF

[D][05:18:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:38][PROT]remove success[1629955118],send_path[2],type[0000],priority[0],index[3],u

2025-07-31 20:42:47:019 ==>> sed[0]
[W][05:18:38][PROT]add success [1629955118],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:38][COMM]Main Task receive event:131 finished processing
$GBGGA,124246.004,2301.2578111,N,11421.9415021,E,1,22,0.67,72.812,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,25,60,1.37,0.67,1.20,4*02

$GBGSA,A,3,07,42,13,40,10,38,41,44,34,26,,,1.37,0.67,1.20,4*04

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,1,48,126,36,2,48,239,34,9,47,322,35,25,43,286,40,1*4C

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,32,1*42

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,2,1,07,33,69,272,43,39,52,9,40,24,49,16,41,42,40,165,39,5*4A

$GBGSV,2,2,07,40,37,160,36,41,25,319,36,44,16,98,33,5*7D

$GBRMC,124246.004,A,2301.2578111,N,11421.9415021,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124246.004,1.874,0.342,0.304,0.468,1.648,2.053,4.333*7C



2025-07-31 20:42:47:401 ==>> [D][05:18:39][COMM]crc 108B
[D][05:18:39][COMM]flash test ok
$GBGGA,124247.000,2301.2579596,N,11421.9414453,E,1,22,0.67,73.310,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,25,60,1.37,0.67,1.20,4*02

$GBGSA,A,3,07,42,13,40,10,38,41,44,34,26,,,1.37,0.67,1.20,4*04

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,1,48,126,36,2,48,239,34,9,47,322,35,25,43,286,40,1*4C

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,40,5*4D

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,34,41,25,319,36,5*79

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,30,5*45

$GBRMC,124247.000,A,2301.2579596,N,11421.9414453,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124247.000,2.420,0.296,0.268,0.404,1.904,2.156,3.956*73

[D][05:18:39][COMM]read battery soc:255


2025-07-31 20:42:48:057 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:42:48:062 ==>> 检测【打开喇叭声音】
2025-07-31 20:42:48:069 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:42:48:761 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:40][COMM]file:A20 exist
[D][05:18:40][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:40][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
$GBGGA,124248.000,2301.2580600,N,11421.9413763,E,1,23,0.64,73.789,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,322,35,1,46,125,35,25,43,286,40,1*42

$GBGSV,7,4,27,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*46

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,33,23,4,254,30,1*41

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,40,5*4D

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,35,5*7B

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,30,5*45

$GBRMC,124248.000,A,2301.2580600,N,11421.9413763,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,

2025-07-31 20:42:48:866 ==>> 0.002,N,0.003,K,A*2E

$GBGST,124248.000,2.730,0.214,0.201,0.296,2.044,2.230,3.770*71

[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:40][COMM]file:A20 exist
[D][05:18:40][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:40][COMM]read file, len:15228, num:4
[D][05:18:40][COMM]--->crc16:0x419c
[D][05:18:40][COMM]read file success
[D][05:18:40][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:40][COMM][Audio].l:[936].close hexlog save
[D][05:18:40][COMM]accel parse set 1
[D][05:18:40][COMM][Audio]mon:9,05:18:40
[D][05:18:40][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:40][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:40][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1


2025-07-31 20:42:48:895 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:42:48:902 ==>> 检测【打开大灯控制】
2025-07-31 20:42:48:908 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:42:48:971 ==>> 
[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_

2025-07-31 20:42:49:061 ==>> audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:42:49:136 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:42:49:214 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:42:49:223 ==>> 检测【关闭仪表供电3】
2025-07-31 20:42:49:229 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:42:49:393 ==>> $GBGGA,124249.000,2301.2581440,N,11421.9414005,E,1,23,0.64,74.063,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,40,24,49,16,40,1*4D

$GBGSV,7,3,27,2,48,239,34,9,47,323,35,1,46,125,35,25,43,286,40,1*43

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,32,8,21,204,33,44,16,98,33,1*40

$GBGSV,7,7,27,34,15,151,31,26,11,52,33,23,4,254,29,1*49

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,36,5*78

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,29,5*4D

$GBRMC,124249.000,A,2301.2581440,N,11421.9414005,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124249.000,2.580,0.237,0.222,0.325,1.929,2.082,3.481*77

[D][05:18:41][COMM]read battery soc:255


2025-07-31 20:42:49:498 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:41][COMM]set POWER 0


2025-07-31 20:42:49:892 ==>> [D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][PROT]index:1 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:2
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->

2025-07-31 20:42:49:966 ==>> :
0063B98C113311331133113311331B88B379CAD0EB121D7A13B40D3FC391B5B0F8B9BBC8D51CCBB6880266D6FD6A8EA82BE0A7B7D223B11817BC6DBBB2FD74AFC007755FFCBD57F0D1D0B2BB7FFC97105DAAFDE248BD7561753E97BB00188BBB14C34D
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]


2025-07-31 20:42:50:394 ==>> $GBGGA,124250.000,2301.2581930,N,11421.9414293,E,1,23,0.64,74.299,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*40

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,31,10,31,188,33,38,26,192,35,1*46

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,33,23,4,254,30,1*41

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,29,5*4D

$GBRMC,124250.000,A,2301.2581930,N,11421.9414293,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,124250.000,2.576,0.256,0.239,0.351,1.908,2.038,3.316*73



2025-07-31 20:42:50:619 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:42:50:625 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:42:50:634 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:42:50:819 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:42:50:937 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:42:50:944 ==>> 检测【读大灯电压】
2025-07-31 20:42:50:949 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:42:51:141 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:43][COMM]arm_hub read adc[5],val[33015]


2025-07-31 20:42:51:252 ==>> 【读大灯电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:42:51:262 ==>> 检测【关闭大灯控制2】
2025-07-31 20:42:51:294 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:42:51:413 ==>> $GBGGA,124251.000,2301.2582377,N,11421.9414301,E,1,23,0.64,74.607,M,-1.770,M,,*51

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,39,6,52,345,35,1*7E

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,33,9,47,323,35,1,46,125,35,25,43,286,40,1*44

$GBGSV,7,4,27,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*48

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,35,5*7B

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,29,5*4C

$GBRMC,124251.000,A,2301.2582377,N,11421.9414301,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124251.000,2.408,0.215,0.203,0.295,1.792,1.907,3.114*76

[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:43][COM

2025-07-31 20:42:51:443 ==>> M]read battery soc:255


2025-07-31 20:42:51:518 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:42:51:805 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:42:51:811 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:42:51:817 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:42:51:935 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:42:52:117 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:42:52:124 ==>> 检测【打开WIFI(4)】
2025-07-31 20:42:52:134 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:42:52:434 ==>> $GBGGA,124252.000,2301.2582765,N,11421.9414248,E,1,23,0.64,74.791,M,-1.770,M,,*57

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,39,6,52,345,35,1*7E

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,323,35,1,46,125,35,25,43,286,40,1*43

$GBGSV,7,4,27,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*46

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,35,5*7B

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,29,5*4D

$GBRMC,124252.000,A,2301.2582765,N,11421.9414248,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124252.000,2.454,0.243,0.227,0.334,1.811,1.913,3.042*70

[D][05:18:44][COMM]55637 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]

2025-07-31 20:42:52:479 ==>> . goto init
[W][05:18:44][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:42:52:760 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:42:52:767 ==>> 检测【EC800M模组版本】
2025-07-31 20:42:52:773 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:42:53:004 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:45][CAT1]gsm read msg sub id: 12
[D][05:18:45][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:45][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:45][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:42:53:086 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:42:53:093 ==>> 检测【配置蓝牙地址】
2025-07-31 20:42:53:102 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:42:53:292 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E1A129508726>】
2025-07-31 20:42:53:442 ==>> [W][05:18:45][COMM]>>>>>Input command = nRFReset<<<<<
$GBGGA,124253.000,2301.2582841,N,11421.9414093,E,1,23,0.64,74.887,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,323,35,1,46,125,35,25,43,286,40,1*43

$GBGSV,7,4,27,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*46

$GBGSV,7,5,27,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*45

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,33,23,4,254,29,1*49

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124253.000,A,2301.2582841,N,11421.9414093,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124253.000,2.488,0.190,0.183,0.260,1.824,1.914,2.979*71

[D][05:18:45][COMM]56648 imu init OK
[D][05:18:45][COMM]read battery soc:255
[D][05:18:45][COMM]imu_task imu work 

2025-07-31 20:42:53:487 ==>> error:[-1]. goto init
+WIFISCAN:4,0,F42A7D1297A3,-65
+WIFISCAN:4,1,CC057790A741,-72
+WIFISCAN:4,2,44A1917CAD80,-83
+WIFISCAN:4,3,44A1917CAD81,-83

[D][05:18:45][CAT1]wifi scan report total[4]


2025-07-31 20:42:53:562 ==>>                                       recv ble 1
recv ble 2
ble set mac ok :e1,a1,29,50,87,26
enable filters ret : 0

2025-07-31 20:42:53:835 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:42:53:851 ==>> 检测【BLETEST】
2025-07-31 20:42:53:862 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:42:53:878 ==>> [D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:46][COMM]accel parse set 0
[D][05:18:46][COMM][Audio].l:[1012].open hexlog save
4A A4 01 A4 4A 


2025-07-31 20:42:53:967 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E1A129508726*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E1A12950872699999OVER 150


2025-07-31 20:42:54:396 ==>> $GBGGA,124254.000,2301.2582878,N,11421.9413967,E,1,23,0.64,74.959,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,27,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*70

$GBGSV,7,2,27,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,323,34,1,46,125,35,25,43,286,40,1*42

$GBGSV,7,4,27,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*46

$GBGSV,7,5,27,40,37,160,37,4,32,112,31,10,31,188,33,38,26,192,34,1*47

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,32,23,4,254,29,1*48

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124254.000,A,2301.2582878,N,11421.9413967,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124254.000,2.476,0.214,0.203,0.291,1.810,1.891,2.909*71

[D][05:18:46][COMM]57659 imu init OK


2025-07-31 20:42:54:893 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:42:54:904 ==>> 该项需要延时执行
2025-07-31 20:42:55:121 ==>> [D][05:18:47][PROT]CLEAN,SEND:1
[D][05:18:47][PROT]index:1 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:5
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:1
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900006]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3A094B1E9F7B2606E72C608E51F368E833BA9B56C46158CE5184D7B5FF47DAFBEAA44D

2025-07-31 20:42:55:196 ==>> 80D48AB54C648604441C19AFE1AEF2AF9D85C083D81CF4DD9FDC9F48E86B6DDE78794C6F8C1FC97B4DF4F3156328131
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]


2025-07-31 20:42:55:301 ==>>                                                                                                                              

2025-07-31 20:42:55:406 ==>>                                                                                                                                                                                                                                    16,40,1*43

$GBGSV,7,3,27,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*40

$GBGSV,7,4,27,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*46

$GBGSV,7,5,27,40,37,160,37,4,32,112,31,10,31,188,33,38,26,192,35,1*46

$GBGSV,7,6,27,41,25,319,37,5,22,257,31,8,21,204,33,44,16,98,33,1*43

$GBGSV,7,7,27,34,15,151,31,26,11,52,33,23,4,254,30,1*41

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124255.000,A,2301.2582918,N,11421.9413907,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124255.000,2.503,0.242,0.227,0.331,1.822,1.896,2.872*76

[D][05:18:47][COMM]read battery soc:255


2025-07-31 20:42:56:371 ==>> $GBGGA,124256.000,2301.2583044,N,11421.9413710,E,1,23,0.64,75.204,M,-1.770,M,,*51

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,33,9,47,323,35,1,46,125,36,25,43,286,40,1*46

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*44

$GBGSV,7,6,26,41,25,319,37,8,21,204,33,44,16,98,33,34,15,151,31,1*71

$GBGSV,7,7,26,26,11,52,33,23,4,254,30,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124256.000,A,2301.2583044,N,11421.9413710,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124256.000,2.315,0.207,0.196,0.283,1.698,1.769,2.724*78



2025-07-31 20:42:57:397 ==>> $GBGGA,124257.000,2301.2583196,N,11421.9413745,E,1,23,0.64,75.215,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,33,9,47,323,35,1,46,125,36,25,43,286,40,1*46

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*44

$GBGSV,7,6,26,41,25,319,37,8,21,204,33,44,16,98,33,34,15,151,31,1*71

$GBGSV,7,7,26,26,11,52,33,23,4,254,30,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,41,40,37,160,38,38,26,192,35,41,25,319,36,5*76

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124257.000,A,2301.2583196,N,11421.9413745,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124257.000,2.404,0.225,0.212,0.309,1.752,1.816,2.732*75

[D][05:18:49][COMM]read battery soc:255


2025-07-31 20:42:58:390 ==>> $GBGGA,124258.000,2301.2583259,N,11421.9413811,E,1,23,0.64,75.261,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.33,0.64,1.17,4*06

$GBGSA,A,3,60,07,42,13,40,10,38,41,44,34,26,,1.33,0.64,1.17,4*01

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,4,32,112,32,10,31,188,33,38,26,192,35,1*44

$GBGSV,7,6,26,41,25,319,37,8,21,204,33,44,16,98,33,34,15,151,31,1*71

$GBGSV,7,7,26,26,11,52,33,23,4,254,30,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,36,5*78

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124258.000,A,2301.2583259,N,11421.9413811,E,0.000,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,124258.000,2.447,0.203,0.193,0.278,1.776,1.835,2.721*71



2025-07-31 20:42:59:397 ==>> $GBGGA,124259.000,2301.2583245,N,11421.9413724,E,1,24,0.62,75.350,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,33,1*7C

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,33,23,4,254,30,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,36,5*78

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124259.000,A,2301.2583245,N,11421.9413724,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124259.000,2.616,0.213,0.201,0.293,1.878,1.932,2.784*72

[D][05:18:51][COMM]read battery soc:255


2025-07-31 20:43:00:460 ==>> [D][05:18:52][PROT]CLEAN,SEND:1
[D][05:18:52][PROT]CLEAN:1
[D][05:18:52][PROT]index:0 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:4
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900005]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5386AEFBE8D3EE13374962396

2025-07-31 20:43:00:565 ==>> 1D5921E2B144F0EA4241FC3F90F30C3C7128461FDF013EFD633B067A90C5AC4889F00244F3F924991F500F58CE49247E57C6CE680831D6F77769E79E087A39679D47E9F4081F
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]
$GBGGA,124300.000,2301.2583212,N,11421.9413669,E,1,24,0.62,75.413,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,40,24,49,16,40,1*4C

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*49

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,33,1*7C

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,

2025-07-31 20:43:00:626 ==>> 16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,33,23,4,254,31,1*75

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,35,5*7B

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124300.000,A,2301.2583212,N,11421.9413669,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,124300.000,2.633,0.216,0.204,0.298,1.886,1.937,2.769*74



2025-07-31 20:43:01:397 ==>> $GBGGA,124301.000,2301.2583330,N,11421.9413704,E,1,24,0.62,75.474,M,-1.770,M,,*57

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*49

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,33,1*7C

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,31,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,40,5*4E

$GBGSV,3,2,12,42,40,165,40,40,37,160,37,38,26,192,35,41,25,319,35,5*7B

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124301.000,A,2301.2583330,N,11421.9413704,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124301.000,2.776,0.256,0.238,0.353,1.971,2.017,2.825*7E

[D][05:18:53][COMM]read battery soc:255


2025-07-31 20:43:02:396 ==>> $GBGGA,124302.000,2301.2583400,N,11421.9413602,E,1,24,0.62,75.471,M,-1.770,M,,*52

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,32,1*7D

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,31,1*74

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,40,5*4D

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,34,34,15,151,30,26,11,52,32,23,,,28,5*4C

$GBRMC,124302.000,A,2301.2583400,N,11421.9413602,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,124302.000,2.895,0.214,0.202,0.296,2.039,2.082,2.868*7B



2025-07-31 20:43:03:423 ==>> $GBGGA,124303.000,2301.2583467,N,11421.9413477,E,1,24,0.62,75.462,M,-1.770,M,,*50

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*49

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,33,1*7C

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,32,1*77

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124303.000,A,2301.2583467,N,11421.9413477,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124303.000,2.699,0.189,0.181,0.261,1.922,1.964,2.745*7D

[D][05:18:55][COMM]read battery soc:255


2025-07-31 20:43:04:402 ==>> $GBGGA,124304.000,2301.2583426,N,11421.9413426,E,1,24,0.62,75.491,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,40,24,49,16,40,1*4C

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,32,1*7D

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,32,1*77

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,41,25,43,286,41,5*4C

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,35,5*74

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124304.000,A,2301.2583426,N,11421.9413426,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124304.000,2.768,0.231,0.217,0.319,1.962,2.002,2.766*78



2025-07-31 20:43:04:897 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:43:04:904 ==>> 检测【检测WiFi结果】
2025-07-31 20:43:04:911 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 20:43:04:944 ==>> WiFi信号:【F42A7D1297A3】,信号值:-67
2025-07-31 20:43:04:956 ==>> WiFi信号:【CC057790A741】,信号值:-75
2025-07-31 20:43:04:980 ==>> WiFi信号:【44A1917CAD81】,信号值:-85
2025-07-31 20:43:04:995 ==>> WiFi信号:【44A1917CAD80】,信号值:-80
2025-07-31 20:43:05:022 ==>> WiFi信号:【CC057790A7C0】,信号值:-83
2025-07-31 20:43:05:033 ==>> WiFi信号:【603A7CF67DD4】,信号值:-82
2025-07-31 20:43:05:054 ==>> WiFi信号:【CC057790A6E0】,信号值:-82
2025-07-31 20:43:05:065 ==>> WiFi数量【8】, 最大信号值:-34
2025-07-31 20:43:05:085 ==>> 检测【检测GPS结果】
2025-07-31 20:43:05:097 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 20:43:05:115 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【14】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【6】,信号值:【35】
北斗星号:【39】,信号值:【40】
北斗星号:【16】,信号值:【36】
北斗星号:【59】,信号值:【39】
北斗星号:【24】,信号值:【41】
北斗星号:【1】,信号值:【36】
北斗星号:【9】,信号值:【35】
北斗星号:【25】,信号值:【40】
北斗星号:【60】,信号值:【40】
北斗星号:【7】,信号值:【35】
北斗星号:【42】,信号值:【39】
北斗星号:【13】,信号值:【35】
北斗星号:【40】,信号值:【37】
北斗星号:【38】,信号值:【35】
北斗星号:【41】,信号值:【36】

2025-07-31 20:43:05:127 ==>> 检测【CSQ强度】
2025-07-31 20:43:05:147 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:43:05:173 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:57][CAT1]gsm read msg sub id: 12
[D][05:18:57][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:57][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:57][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:43:05:456 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 20:43:05:463 ==>> 检测【关闭GSM联网】
2025-07-31 20:43:05:471 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:43:05:574 ==>>                                                                                                                           ,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,38,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,32,10,31,188,33,1*7C

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,32,1*77

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,36,5*77

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124305.000,A,2301.2583467,N,11421.9413423,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124305.000,2.715,0.250,0.232,0.345,1.929,1.967,2.721*7F

[D][05:18:57][COMM]read battery soc:255
[D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][

2025-07-31 20:43:05:679 ==>> PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:4
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900005]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[198]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B56E39F9AD819A7B690DFE5B5E2BB54B328A5DCF5176708846AB

2025-07-31 20:43:05:754 ==>> 842FF52B9C9905E4B84574C12526193E1B2CCE229A38DE34313A5795D859BD45FF26E0D04B8B6584EAAF87E1487B278D873BD6918563C7D94A
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 20:43:05:829 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:58][COMM]GSM test
[D][05:18:58][COMM]GSM test disable


2025-07-31 20:43:05:995 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:43:06:008 ==>> 检测【4G联网测试】
2025-07-31 20:43:06:014 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:43:07:044 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:58][COMM]Main Task receive event:14
[D][05:18:58][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955138, allstateRepSeconds = 0
[D][05:18:58][COMM]index:0,power_mode:0xFF
[D][05:18:58][COMM]index:1,sound_mode:0xFF
[D][05:18:58][COMM]index:2,gsensor_mode:0xFF
[D][05:18:58][COMM]index:3,report_freq_mode:0xFF
[D][05:18:58][COMM]index:4,report_period:0xFF
[D][05:18:58][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:58][COMM]index:6,normal_reset_period:0xFF
[D][05:18:58][COMM]index:7,spock_over_speed:0xFF
[D][05:18:58][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:58][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:58][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:58][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:58][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:58][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:58][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:58][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:58][COMM]index:16,imu_config_params:0xFF
[D][05:18:58][COMM]index:17,long_connect_params:0xFF
[D][05:18:58][COMM]index:18,detain_mark:0xFF
[D][05:18:58][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:58][COMM]index:20,lock_pos_report_interval:0xFF
[D

2025-07-31 20:43:07:149 ==>> ][05:18:58][COMM]index:21,mc_mode:0xFF
[D][05:18:58][COMM]index:22,S_mode:0xFF
[D][05:18:58][COMM]index:23,overweight:0xFF
[D][05:18:58][COMM]index:24,standstill_mode:0xFF
[D][05:18:58][COMM]index:25,night_mode:0xFF
[D][05:18:58][COMM]index:26,experiment1:0xFF
[D][05:18:58][COMM]index:27,experiment2:0xFF
[D][05:18:58][COMM]index:28,experiment3:0xFF
[D][05:18:58][COMM]index:29,experiment4:0xFF
[D][05:18:58][COMM]index:30,night_mode_start:0xFF
[D][05:18:58][COMM]index:31,night_mode_end:0xFF
[D][05:18:58][COMM]index:33,park_report_minutes:0xFF
[D][05:18:58][COMM]index:34,park_report_mode:0xFF
[D][05:18:58][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:58][COMM]index:38,charge_battery_para: FF
[D][05:18:58][COMM]index:39,multirider_mode:0xFF
[D][05:18:58][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:58][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:58][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:58][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:58][COMM]index:44,riding_duration_config:0xFF
[D][05:18:58][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:58][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:58][COMM]index:47,bat_info_rep_

2025-07-31 20:43:07:254 ==>> cfg:0xFF
[D][05:18:58][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:58][COMM]index:49,mc_load_startup:0xFF
[D][05:18:58][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:58][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:58][COMM]index:52,traffic_mode:0xFF
[D][05:18:58][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:58][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:58][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:58][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:58][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:58][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:58][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:58][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:58][COMM]index:63,experiment5:0xFF
[D][05:18:58][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:58][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:58][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:58][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:58][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:58][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:58][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:58][COMM]index:72,e

2025-07-31 20:43:07:359 ==>> xperiment6:0xFF
[D][05:18:58][COMM]index:73,experiment7:0xFF
[D][05:18:58][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:58][COMM]index:75,zero_value_from_server:-1
[D][05:18:58][COMM]index:76,multirider_threshold:255
[D][05:18:58][COMM]index:77,experiment8:255
[D][05:18:58][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:58][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:58][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:58][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:58][COMM]index:83,loc_report_interval:255
[D][05:18:58][COMM]index:84,multirider_threshold_p2:255
[D][05:18:58][COMM]index:85,multirider_strategy:255
[D][05:18:58][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:58][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:58][COMM]index:90,weight_param:0xFF
[D][05:18:58][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:58][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:58][COMM]index:95,current_limit:0xFF
[D][05:18:58][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:58][COMM]index:100,location_mode:0xFF

[W][05:18:58][PROT]remove success[1629955138],

2025-07-31 20:43:07:464 ==>> send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:58][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:58][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:58][PROT]add success [1629955138],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:58][PROT]index:0 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:8
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:1
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][PROT]sending traceid [9999999999900009]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][CAT1]gsm read msg sub id: 13
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[294]
[D][05:18:58][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellul

2025-07-31 20:43:07:569 ==>> ar SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
$GBGGA,124306.000,2301.2583502,N,11421.9413394,E,1,24,0.62,75.601,M,-1.770,M,,*5A

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,38,16,52,349,36,59,50,128,39,24,49,16,40,1*43

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,39,7,40,176,35,42,40,165,38,13,39,219,35,1*47

$GBGSV,7,5,26,40,37,160,37,8,35,207,33,4,32,112,31,10,31,188,33,1*7F

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,32,23,4,254,32,1*77

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,41,40,37,160,38,38,26,192,35,41,25,319,35,5*75

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124306.000,A,2301.2583502,N,11421.9413394,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124306.000,2.797,0.207,0.197,0.285,1.9

2025-07-31 20:43:07:659 ==>> 76,2.013,2.753*73

[D][05:18:58][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:58][CAT1]exec over: func id: 13, ret: 21
[D][05:18:58][M2M ]get csq[23]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:58][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B1F6445D8CE739C4895731834001EC8B199FA4322665FEBF1342F709578B7EB47294BA791EE4903514EC16497AC22AA5D235B076663B61FBEBE32BB0D47880BD01EEA5FDA869E87353ACD1D19E9148A8E5F5F62EDD6BAE0B3C4AAA04323A884E57EC279962A3FDC00D94A22D890B99B911A2DF6D9FE466B98C22DBD11DF8D5C0977F45
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[

2025-07-31 20:43:07:764 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:43:07:824 ==>>                                                                 

2025-07-31 20:43:08:090 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:43:08:097 ==>> 检测【关闭GPS】
2025-07-31 20:43:08:121 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:43:08:459 ==>> $GBGGA,124308.000,2301.2583654,N,11421.9413251,E,1,24,0.62,75.708,M,-1.770,M,,*54

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.32,0.62,1.16,4*00

$GBGSA,A,3,60,07,42,13,40,08,10,38,41,44,34,26,1.32,0.62,1.16,4*0F

$GBGSV,7,1,26,33,69,272,42,14,65,191,39,3,62,190,40,6,52,345,35,1*71

$GBGSV,7,2,26,39,52,9,39,16,52,349,36,59,50,128,39,24,49,16,40,1*42

$GBGSV,7,3,26,2,48,239,34,9,47,323,35,1,46,125,36,25,43,286,40,1*41

$GBGSV,7,4,26,60,43,241,40,7,40,176,35,42,40,165,38,13,39,219,35,1*49

$GBGSV,7,5,26,40,37,160,38,8,35,207,33,4,32,112,32,10,31,188,33,1*73

$GBGSV,7,6,26,38,26,192,35,41,25,319,37,44,16,98,33,34,15,151,31,1*4F

$GBGSV,7,7,26,26,11,52,33,23,4,254,32,1*76

$GBGSV,3,1,12,33,69,272,43,39,52,9,40,24,49,16,42,25,43,286,41,5*4F

$GBGSV,3,2,12,42,40,165,40,40,37,160,38,38,26,192,35,41,25,319,35,5*74

$GBGSV,3,3,12,44,16,98,35,34,15,151,30,26,11,52,32,23,,,28,5*4D

$GBRMC,124308.000,A,2301.2583654,N,11421.9413251,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124308.000,2.627,0.210,0.198,0.289,1.873,1.907,2.636*7B

[W][05:19:00][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:00][GNSS]stop locating
[D][05:19:00][GNSS]stop event:8
[D][05:19:00][GNSS]GPS stop.

2025-07-31 20:43:08:519 ==>>  ret=0
[D][05:19:00][GNSS]all continue location stop
[W][05:19:00][GNSS]stop locating
[D][05:19:00][GNSS]all sing location stop
[D][05:19:00][CAT1]gsm read msg sub id: 24
[D][05:19:00][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]exec over: func id: 24, ret: 6
[D][05:19:00][CAT1]sub id: 24, ret: 6



2025-07-31 20:43:08:625 ==>> [D][05:19:01][GNSS]recv submsg id[1]
[D][05:19:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:01][GNSS]location stop evt done evt


2025-07-31 20:43:08:649 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:43:08:657 ==>> 检测【清空消息队列2】
2025-07-31 20:43:08:663 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:43:08:849 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:01][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:43:08:945 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:43:08:957 ==>> 检测【轮动检测】
2025-07-31 20:43:08:977 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:43:09:061 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:43:09:121 ==>> [D][05:19:01][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:43:09:335 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 20:43:09:455 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:43:09:561 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:43:09:744 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:43:09:759 ==>> 检测【关闭小电池】
2025-07-31 20:43:09:782 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:43:09:861 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:43:10:049 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:43:10:057 ==>> 检测【进入休眠模式】
2025-07-31 20:43:10:064 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:43:10:211 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:43:10:316 ==>> [D][05:19:02][COMM]Main Task receive event:28
[D][05:19:02][COMM]main task tmp_sleep_event = 8
[D][05:19:02][COMM]prepare to sleep
[D][05:19:02][CAT1]gsm read msg sub id: 12
[D][05:19:02][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:43:11:176 ==>> [D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 6
[D][05:19:03][M2M ]tcpclient close[4]
[D][05:19:03][SAL ]Cellular task submsg id[12]
[D][05:19:03][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:03][CAT1]gsm read msg sub id: 9
[D][05:19:03][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 9, ret: 6
[D][05:19:03][CAT1]sub id: 9, ret: 6

[D][05:19:03][SAL ]Cellular task submsg id[68]
[D][05:19:03][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:03][SAL ]socket close ind. id[4]
[D][05:19:03][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:03][COMM]1x1 frm_can_tp_send ok
[D][05:19:03][CAT1]pdpdeact urc len[22]


2025-07-31 20:43:11:341 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 20:43:11:476 ==>> [E][05:19:03][COMM]1x1 rx timeout
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:43:11:993 ==>> [E][05:19:04][COMM]1x1 rx timeout
[E][05:19:04][COMM]1x1 tp timeout
[E][05:19:04][COMM]1x1 error -3.
[W][05:19:04][COMM]CAN STOP!
[D][05:19:04][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 1------------
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------
[D][05:19:04][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:04][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1290
[D][05:19:04][COMM]bat sleep fail, reason:-1
[D][05:19:04][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM]imu rest ok. 75264
[D][05:19:04][COMM]imu sleep 0
[W][05:19:04][COMM]now sleep


2025-07-31 20:43:12:131 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:43:12:144 ==>> 检测【检测33V休眠电流】
2025-07-31 20:43:12:167 ==>> 开始33V电流采样
2025-07-31 20:43:12:181 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:12:235 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:43:13:240 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:43:13:301 ==>> Current33V:????:18.30

2025-07-31 20:43:13:746 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:13:753 ==>> 【检测33V休眠电流】通过,【18.3uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:43:13:783 ==>> 该项需要延时执行
2025-07-31 20:43:15:768 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:43:15:780 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:43:15:793 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:43:15:860 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1664mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 2030mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:43:16:838 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:43:16:847 ==>> 检测【打开小电池2】
2025-07-31 20:43:16:854 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:43:16:950 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:43:17:162 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:43:17:169 ==>> 该项需要延时执行
2025-07-31 20:43:17:677 ==>> 此处延时了:【500】毫秒
2025-07-31 20:43:17:688 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:43:17:700 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:43:17:756 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:43:17:988 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:43:17:995 ==>> 该项需要延时执行
2025-07-31 20:43:18:452 ==>> [D][05:19:10][COMM]------------ready to Power on Acckey 1------------
[D][05:19:10][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:10][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:10][COMM]----- get Acckey 1 and value:1------------
[W][05:19:10][COMM]CAN START!
[D][05:19:10][CAT1]gsm read msg sub id: 12
[D][05:19:10][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:10][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 81650
[D][05:19:10][COMM][Audio]exec status re

2025-07-31 20:43:18:497 ==>> 此处延时了:【500】毫秒
2025-07-31 20:43:18:511 ==>> 检测【进入休眠模式2】
2025-07-31 20:43:18:535 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:43:18:562 ==>> ady.
[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[D][05:19:10][COMM]imu wakeup ok. 81664
[D][05:19:10][COMM]imu wakeup 1
[W][05:19:10][COMM]wake up system, wakeupEvt=0x80
[D][05:19:10][COMM]frm_can_weigth_power_set 1
[D][05:19:10][COMM]Clear Sleep Block Evt
[D][05:19:10][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:10][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:43:18:616 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:43:18:707 ==>> [D][05:19:11][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[E][05:19:11][COMM]1x1 rx timeout
[D][05:19:11][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:43:18:812 ==>>                                                                                                                                      tick:82145. period:50
[D][05:19:11][COMM]msg 02A5 loss. last_tick:81635. cur_tick:82145. period:50
[D][05:19:11][COMM]msg 02A6 loss. last_tick:81635. cur_tick:82146. period:50
[D][05:19:11][COMM]msg 02A7 loss. last_tick:81635. cur_tick:82146. period:50
[D][05:19:11][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 82146
[

2025-07-31 20:43:18:842 ==>> D][05:19:11][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 82147


2025-07-31 20:43:19:179 ==>> [E][05:19:11][COMM]1x1 rx timeout
[E][05:19:11][COMM]1x1 tp timeout
[E][05:19:11][COMM]1x1 error -3.
[D][05:19:11][COMM]Main Task receive event:28 finished processing
[D][05:19:11][COMM]Main Task receive event:28
[D][05:19:11][COMM]prepare to sleep
[D][05:19:11][CAT1]gsm read msg sub id: 12
[D][05:19:11][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 12, ret: 6
[D][05:19:11][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:11][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:43:19:480 ==>> [D][05:19:11][COMM]msg 0220 loss. last_tick:81635. cur_tick:82640. period:100
[D][05:19:11][COMM]msg 0221 loss. last_tick:81635. cur_tick:82641. period:100
[D][05:19:11][COMM]msg 0224 loss. last_tick:81635. cur_tick:82641. period:100
[D][05:19:11][COMM]msg 0260 loss. last_tick:81635. cur_tick:82642. period:100
[D][05:19:11][COMM]msg 0280 loss. last_tick:81635. cur_tick:82642. period:100
[D][05:19:11][COMM]msg 02C0 loss. last_tick:81635. cur_tick:82642. period:100
[D][05:19:11][COMM]msg 02C1 loss. last_tick:81635. cur_tick:82643. period:100
[D][05:19:11][COMM]msg 02C2 loss. last_tick:81635. cur_tick:82643. period:100
[D][05:19:11][COMM]msg 02E0 loss. last_tick:81635. cur_tick:82643. period:100
[D][05:19:11][COMM]msg 02E1 loss. last_tick:81635. cur_tick:82644. period:100
[D][05:19:11][COMM]msg 02E2 loss. last_tick:81635. cur_tick:82644. period:100
[D][05:19:11][COMM]msg 0300 loss. last_tick:81635. cur_tick:82644. period:100
[D][05:19:11][COMM]msg 0301 loss. last_tick:81635. cur_tick:82645. period:100
[D][05:19:11][COMM]bat msg 0240 loss. last_tick:81635. cur_tick:82645. period:100. j,i:1 54
[D][05:19:11][COMM]bat msg 0241 loss. last_tick:81635. cur_tick:82645. period:100. j,i:2 55
[D][05:19:11][COMM]bat msg 0242 loss. last_tick:8

2025-07-31 20:43:19:585 ==>> 1635. cur_tick:82646. period:100. j,i:3 56
[D][05:19:11][COMM]bat msg 0244 loss. last_tick:81635. cur_tick:82646. period:100. j,i:5 58
[D][05:19:11][COMM]bat msg 024E loss. last_tick:81635. cur_tick:82647. period:100. j,i:15 68
[D][05:19:11][COMM]bat msg 024F loss. last_tick:81635. cur_tick:82647. period:100. j,i:16 69
[D][05:19:11][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 82647
[D][05:19:11][COMM]CAN message bat fault change: 0x00000000->0x0001802E 82648
[D][05:19:11][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 82649
                                                                              

2025-07-31 20:43:19:767 ==>> [D][05:19:12][COMM]msg 0222 loss. last_tick:81635. cur_tick:83142. period:150
[D][05:19:12][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 83143


2025-07-31 20:43:19:857 ==>>                            ove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:12][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:12][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:43:20:052 ==>> [E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[E][05:19:12][COMM]1x1 error -3.
[W][05:19:12][COMM]CAN STOP!
[D][05:19:12][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:12][COMM]------------ready to Power off Acckey 1------------
[D][05:19:12][COMM]------------ready to Power off Acckey 2------------
[D][05:19:12][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:12][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 92
[D][05:19:12][COMM]bat sleep fail, reason:-1
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:12][COMM]accel parse set 0
[D][05:19:12][COMM]imu rest ok. 83334
[D][05:19:12][COMM]imu sleep 0
[W][05:19:12][COMM]now sleep


2025-07-31 20:43:20:332 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:43:20:339 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:43:20:351 ==>> 开始小电池电流采样
2025-07-31 20:43:20:366 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:20:436 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:43:21:445 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:43:21:491 ==>> CurrentBattery:ƽ��:67.58

2025-07-31 20:43:21:955 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:21:963 ==>> 【检测小电池休眠电流】通过,【67.58uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:43:21:971 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:43:21:989 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:43:22:062 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:43:22:250 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:43:22:260 ==>> 该项需要延时执行
2025-07-31 20:43:22:304 ==>> [D][05:19:14][COMM]------------ready to Power on Acckey 1------------
[D][05:19:14][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:14][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 13
[D][05:19:14][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:14][COMM]----- get Acckey 1 and value:1------------
[W][05:19:14][COMM]CAN START!
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:14][COMM]CAN message bat fault change: 0x0001802E->0x00000000 85527
[D][05:19:14][COMM][Audio]exec status ready.
[D][05:19:14][CAT1]<<< 
OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 6
[D][05:19:14][COMM]imu wakeup ok. 85541
[D][05:19:14][COMM]imu wakeup 1
[W][05:19:14][COMM]wake up system, wakeupEvt=0x80
[D][05:19:14][COMM]frm_can_weigth_power_set 1
[D][05:19:14][COMM]Clear Sleep Block Evt
[D][05:19:14][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:14][COMM]1x1 frm_can_tp_send ok
[D][05:19:14][COMM]read battery soc:0


2025-07-31 20:43:22:564 ==>> [E][05:19:14][COMM]1x1 rx timeout
[D][05:19:14][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:43:22:669 ==>> [D][05:19:15][COMM]msg 02A0 loss. last_tick:85509. cur_tick:86020. period:50
[D][05:19:15][COMM]msg 02A4 loss. last_tick:85509. cur_tick:86021. period:50
[D][05:19:15][COMM]msg 02A5 loss. last_tick:85509. cur_ti

2025-07-31 20:43:22:729 ==>> ck:86021. period:50
[D][05:19:15][COMM]msg 02A6 loss. last_tick:85509. cur_tick:86021. period:50
[D][05:19:15][COMM]msg 02A7 loss. last_tick:85509. cur_tick:86022. period:50
[D][05:19:15][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 86022
[D][05:19:15][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 86023


2025-07-31 20:43:22:759 ==>> 此处延时了:【500】毫秒
2025-07-31 20:43:22:770 ==>> 检测【检测唤醒】
2025-07-31 20:43:22:783 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:43:23:516 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:15][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:15][FCTY]==========Modules-nRF5340 ==========
[D][05:19:15][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:15][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:15][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:15][FCTY]DeviceID    = 460130071539108
[D][05:19:15][FCTY]HardwareID  = 867222087784668
[D][05:19:15][FCTY]MoBikeID    = 9999999999
[D][05:19:15][FCTY]LockID      = FFFFFFFFFF
[D][05:19:15][FCTY]BLEFWVersion= 105
[D][05:19:15][FCTY]BLEMacAddr   = E1A129508726
[D][05:19:15][FCTY]Bat         = 3704 mv
[D][05:19:15][FCTY]Current     = 0 ma
[D][05:19:15][FCTY]VBUS        = 2600 mv
[D][05:19:15][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:15][FCTY]Ext battery vol = 32, adc = 1290
[D][05:19:15][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 0 mv
[D][05:19:15][FCTY]Bike Type flag is invalied
[D][05:19:15][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:15][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:15][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:15][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:15][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:15][FC

2025-07-31 20:43:23:586 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:43:23:596 ==>> 检测【关机】
2025-07-31 20:43:23:605 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:43:23:627 ==>> TY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:15][FCTY]Bat1         = 3694 mv
[D][05:19:15][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:15][FCTY]==========Modules-nRF5340 ==========
[E][05:19:15][COMM]1x1 rx timeout
[E][05:19:15][COMM]1x1 tp timeout
[E][05:19:15][COMM]1x1 error -3.
[D][05:19:15][COMM]Main Task receive event:28 finished processing
[D][05:19:15][COMM]Main Task receive event:65
[D][05:19:15][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:15][COMM]Main Task receive event:65 finished processing
[D][05:19:15][COMM]Main Task receive event:60
[D][05:19:15][COMM]smart_helmet_vol=255,255
[D][05:19:15][COMM]report elecbike
[W][05:19:15][PROT]remove success[1629955155],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:15][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:15][PROT]add success [1629955155],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:15][COMM]Main Task receive event:60 finished processing
[D][05:19:15][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:15][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:15][PROT]min_index:0, type:0x5D03, priority

2025-07-31 20:43:23:726 ==>> :3
[D][05:19:15][PROT]index:0
[D][05:19:15][PROT]is_send:1
[D][05:19:15][PROT]sequence_num:10
[D][05:19:15][PROT]retry_timeout:0
[D][05:19:15][PROT]retry_times:3
[D][05:19:15][PROT]send_path:0x3
[D][05:19:15][PROT]msg_type:0x5d03
[D][05:19:15][PROT]===========================================================
[W][05:19:15][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955155]
[D][05:19:15][PROT]===========================================================
[D][05:19:15][PROT]Sending traceid[999999999990000B]
[D][05:19:15][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:15][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:15][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:15][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:15][SAL ]open socket ind id[4], rst[0]
[D][05:19:15][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:15][SAL ]Cellular task submsg id[8]
[D][05:19:15][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:15][SAL ]domain[bikeapi.mobike.com] port[9999] typ

2025-07-31 20:43:23:831 ==>> e[1]
[D][05:19:15][CAT1]gsm read msg sub id: 8
[D][05:19:15][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:15][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:15][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:15][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:15][CAT1]<<< 
+CME ERROR: 100

[D][05:19:15][COMM]msg 0220 loss. last_tick:85509. cur_tick:86518. period:100
[D][05:19:15][COMM]msg 0221 loss. last_tick:85509. cur_tick:86519. period:100
[D][05:19:15][COMM]msg 0224 loss. last_tick:85509. cur_tick:86519. period:100
[D][05:19:15][COMM]msg 0260 loss. last_tick:85509. cur_tick:86519. period:100
[D][05:19:15][COMM]msg 0280 loss. last_tick:85509. cur_tick:86520. period:100
[D][05:19:15][COMM]msg 02C0 loss. last_tick:85509. cur_tick:86520. period:100
[D][05:19:15][COMM]msg 02C1 loss. last_tick:85509. cur_tick:86521. period:100
[D][05:19:15][COMM]msg 02C2 loss. last_tick:85509. cur_tick:86521. period:100
[D][05:19:15][COMM]msg 02E0 loss. last_tick:85509. cur_tick:86521. period:100
[D][05:19:15][COMM]msg 02E1 loss. last_tick:85509. cur_tick:86522. period:100
[D][05:19:15][COMM]msg 02E2 loss. last_tick:85509. cur_tick:865

2025-07-31 20:43:23:936 ==>> 22. period:100
[D][05:19:15][COMM]msg 0300 loss. last_tick:85509. cur_tick:86522. period:100
[D][05:19:15][COMM]msg 0301 loss. last_tick:85509. cur_tick:86522. period:100
[D][05:19:15][COMM]bat msg 0240 loss. last_tick:85509. cur_tick:86523. period:100. j,i:1 54
[D][05:19:15][COMM]bat msg 0241 loss. last_tick:85509. cur_tick:86523. period:100. j,i:2 55
[D][05:19:15][COMM]bat msg 0242 loss. last_tick:85509. cur_tick:86524. period:100. j,i:3 56
[D][05:19:15][COMM]bat msg 0244 loss. last_tick:85509. cur_tick:86524. period:100. j,i:5 58
[D][05:19:15][COMM]bat msg 024E loss. last_tick:85509. cur_tick:86524. period:100. j,i:15 68
[D][05:19:15][COMM]bat msg 024F loss. last_tick:85509. cur_tick:86525. period:100. j,i:16 69
[D][05:19:15][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 86525
[D][05:19:15][COMM]CAN message bat fault change: 0x00000000->0x0001802E 86526
[D][05:19:15][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 86526


2025-07-31 20:43:24:552 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:43:24:612 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:43:24:657 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:43:24:762 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      play_process].l:[956].start ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:16][COMM]Main Task receive event:60
[D][05:19:16][COMM]smart_helmet_vol=255,255
[D][05:19:16][COMM]BAT CAN get state1 Fail 204
[D][05:19:16][COMM]BAT CAN get soc Fail, 204
[D][05:19:16][COMM]BAT CAN get state2 fail 204
[D][05:19:16][COMM]get soh error
[E][05:19:16][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:16][COMM]report elecbike
[W

2025-07-31 20:43:24:867 ==>> ][05:19:16][PROT]remove success[1629955156],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:16][COMM]Main Task receive event:60 finished processing
[D][05:19:16][COMM]Main Task receive event:61
[D][05:19:16][COMM][D301]:type:3, trace id:280
[D][05:19:16][COMM]id[], hw[000
[D][05:19:16][COMM]get mcMaincircuitVolt error
[D][05:19:16][COMM]get mcSubcircuitVolt error
[D][05:19:16][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][PROT]index:1
[D][05:19:16][PROT]is_send:1
[D][05:19:16][PROT]sequence_num:11
[D][05:19:16][PROT]retry_timeout:0
[D][05:19:16][PROT]retry_times:3
[D][05:19:16][PROT]send_path:0x3
[D][05:19:16][PROT]msg_type:0x5d03
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][PROT]Send

2025-07-31 20:43:24:972 ==>> ing traceid[999999999990000C]
[D][05:19:16][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:16][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:16][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:16][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:16][COMM]Receive Bat Lock cmd 0
[D][05:19:16][COMM]VBUS is 1
[D][05:19:16][COMM]BAT CAN get state1 Fail 204
[D][05:19:16][COMM]BAT CAN get soc Fail, 204
[D][05:19:16][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:16][COMM]BAT CAN get state2 fail 204
[D][05:19:16][COMM]get bat work mode err
[D][05:19:16][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:19:16][PROT]remove success[1629955156],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[2],type[D302],priority[

2025-07-31 20:43:25:047 ==>> 0],index[2],used[1]
[D][05:19:16][COMM]Main Task receive event:61 finished processing
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]

2025-07-31 20:43:25:152 ==>> f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:16][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:16][COMM]arm_hub_enable: hub power: 0
[D][05:19:16][HSDK]hexlog index save 0 5120 62 @ 0 : 0
[D][05:19:16][HSDK]write save hexlog index [0]
[D][05:19:16][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:16][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:16][COMM]read battery soc:255
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:16][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:16][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:16][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:16][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                              

2025-07-31 20:43:25:182 ==>>                                                                                                                                 

2025-07-31 20:43:25:287 ==>>                                                                                                                                                                                                                                                                                                                19:17][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:43:25:641 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:43:25:720 ==>> [W][05:19:18][COMM]Power Off


2025-07-31 20:43:25:885 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:18][COMM]arm_hub_enable: hub power: 0
[D][05:19:18][HSDK]hexlog index save 0 5120 62 @ 0 : 0
[D][05:19:18][HSDK]write save hexlog index [0]
[D][05:19:18][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:18][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:43:25:964 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:43:25:972 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:43:25:979 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:43:26:051 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:43:26:231 ==>> [D][05:19:18][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:43:26:295 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:43:26:303 ==>> 检测【检测小电池关机电流】
2025-07-31 20:43:26:317 ==>> 开始小电池电流采样
2025-07-31 20:43:26:341 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:26:396 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:43:27:410 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:43:27:456 ==>> CurrentBattery:ƽ��:66.39

2025-07-31 20:43:27:921 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:43:27:929 ==>> 【检测小电池关机电流】通过,【66.39uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:43:28:297 ==>> MES过站成功
2025-07-31 20:43:28:305 ==>> #################### 【测试结束】 ####################
2025-07-31 20:43:28:335 ==>> 关闭5V供电
2025-07-31 20:43:28:348 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:43:28:450 ==>> 5A A5 04 5A A5 


2025-07-31 20:43:28:555 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:43:29:345 ==>> 关闭5V供电成功
2025-07-31 20:43:29:358 ==>> 关闭33V供电
2025-07-31 20:43:29:370 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:43:29:454 ==>> 5A A5 02 5A A5 


2025-07-31 20:43:29:559 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:43:30:351 ==>> 关闭33V供电成功
2025-07-31 20:43:30:364 ==>> 关闭3.7V供电
2025-07-31 20:43:30:371 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:43:30:460 ==>> 6A A6 02 A6 6A 


2025-07-31 20:43:30:550 ==>> Battery OFF
OVER 150


2025-07-31 20:43:31:341 ==>>  

2025-07-31 20:43:31:356 ==>> 关闭3.7V供电成功
