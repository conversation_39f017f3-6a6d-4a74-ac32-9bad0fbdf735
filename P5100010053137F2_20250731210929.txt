2025-07-31 21:09:29:099 ==>> MES查站成功:
查站序号:P5100010053137F2验证通过
2025-07-31 21:09:29:106 ==>> 扫码结果:P5100010053137F2
2025-07-31 21:09:29:107 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:09:29:109 ==>> 测试参数版本:2024.10.11
2025-07-31 21:09:29:110 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:09:29:112 ==>> 检测【打开透传】
2025-07-31 21:09:29:113 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:09:29:175 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:09:29:391 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:09:29:398 ==>> 检测【检测接地电压】
2025-07-31 21:09:29:400 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:09:29:465 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:09:29:682 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:09:29:684 ==>> 检测【打开小电池】
2025-07-31 21:09:29:687 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:09:29:768 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:09:29:969 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:09:29:974 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:09:29:978 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:09:30:068 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:09:30:270 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:09:30:273 ==>> 检测【等待设备启动】
2025-07-31 21:09:30:276 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:30:519 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:30:716 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 21:09:31:207 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:31:312 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:31:387 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:09:32:060 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:09:32:135 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:09:32:345 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:32:525 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:09:33:005 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:09:33:141 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:09:33:143 ==>> 检测【产品通信】
2025-07-31 21:09:33:145 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:09:33:686 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:33:881 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:09:34:166 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:09:34:584 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 21:09:34:968 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:09:35:208 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:09:35:946 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[D][05:17:50][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[D][05:17:50][COMM][MC]get MC real state err,rt:-3
[D][05:17:50][COMM]frm_can_weigth_power_set 1
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[D][05:17:50][COMM]index:0,power_mode:0xFF
[D][05:17:50][COMM]index:1,sound_mode:0xFF
[D][05:17:50][COMM]index:2,gsensor_mode:0xFF
[D][05:17:50][COMM]index:3,report_freq_mode:0xFF
[D][05:17:50][COMM]index:4,report_period:0xFF
[D][05:17:50][COMM]index:5,normal_reset_mode:0xFF
[D][05:17:50][COMM]index:6,normal_reset_period:0xFF
[D][05:17:50][COMM]index:7,spock_over_speed:0xFF
[D][05:17:50][COMM]index:8,spock_limit_speed:0xFF
[D][05:17:50][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:17:50][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:17:50][COMM]index:11,ble_scan_mode:0xFF
[D][05:17:50]

2025-07-31 21:09:36:030 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:09:36:033 ==>> 检测【初始化完成检测】
2025-07-31 21:09:36:035 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:09:36:051 ==>> [COMM]index:12,ble_adv_mode:0xFF
[D][05:17:50][COMM]index:13,spock_audio_volumn:0xFF
[D][05:17:50][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:17:50][COMM]index:15,bat_auth_mode:0xFF
[D][05:17:50][COMM]index:16,imu_config_params:0xFF
[D][05:17:50][COMM]index:17,long_connect_params:0xFF
[D][05:17:50][COMM]index:18,detain_mark:0xFF
[D][05:17:50][COMM]index:19,lock_pos_report_count:0xFF
[D][05:17:50][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:17:50][COMM]index:21,mc_mode:0xFF
[D][05:17:50][COMM]index:22,S_mode:0xFF
[D][05:17:50][COMM]index:23,overweight:0xFF
[D][05:17:50][COMM]index:24,standstill_mode:0xFF
[D][05:17:50][COMM]index:25,night_mode:0xFF
[D][05:17:50][COMM]index:26,experiment1:0xFF
[D][05:17:50][COMM]index:27,experiment2:0xFF
[D][05:17:50][COMM]msg 0222 loss. last_tick:0. cur_tick:1518. period:150
[D][05:17:50][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 1519
[D][05:17:50][COMM]index:28,experiment3:0xFF
[D][05:17:50][COMM]index:29,experiment4:0xFF
[D][05:17:50][COMM]index:30,night_mode_start:0xFF
[D][05:17:50][COMM]index:31,night_mode_end:0xFF
[D][05:17:50][COMM]index:33,park_report_minutes:0xFF
[D][05:17:50][COMM]inde

2025-07-31 21:09:36:156 ==>> x:34,park_report_mode:0xFF
[D][05:17:50][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:17:50][COMM]index:38,charge_battery_para: FF
[D][05:17:50][COMM]index:39,multirider_mode:0xFF
[D][05:17:50][COMM]index:40,mc_launch_mode:0xFF
[D][05:17:50][COMM]index:41,head_light_enable_mode:0xFF
[D][05:17:50][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:17:50][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:17:50][COMM]index:44,riding_duration_config:0xFF
[D][05:17:50][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:17:50][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:17:50][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:17:50][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:17:50][COMM]index:49,mc_load_startup:0xFF
[D][05:17:50][COMM]index:50,mc_tcs_mode:0xFF
[D][05:17:50][COMM]index:51,traffic_audio_play:0xFF
[D][05:17:50][COMM]index:52,traffic_mode:0xFF
[D][05:17:50][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:17:50][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:17:50][COMM]index:55,wheel_alarm_play_switch:255
[D][05:17:50][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:17:50][COMM]index:58,traffic_light_threshold:0xFF
[D][05:17:50][COMM]index:59,traf

2025-07-31 21:09:36:261 ==>> fic_retrograde_threshold:0xFF
[D][05:17:50][COMM]index:60,traffic_road_threshold:0xFF
[D][05:17:50][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:17:50][COMM]index:63,experiment5:0xFF
[D][05:17:50][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:17:50][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:17:50][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:17:50][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:17:50][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:17:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:17:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:2

2025-07-31 21:09:36:366 ==>> 55
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1637 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
                                                 

2025-07-31 21:09:36:396 ==>>             

2025-07-31 21:09:36:561 ==>>                                     h addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]2649 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:36:666 ==>>                                                                                         vent = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:09:36:829 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:09:36:832 ==>> 检测【关闭大灯控制1】
2025-07-31 21:09:36:834 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:09:37:031 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:09:37:122 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:09:37:126 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:09:37:130 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:09:37:363 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:09:37:407 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:09:37:410 ==>> 检测【关闭仪表供电】
2025-07-31 21:09:37:412 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:09:37:575 ==>> [D][05:17:52][COMM]3660 imu init OK
[W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:37:708 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:09:37:711 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:09:37:714 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:09:37:833 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:09:38:008 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:09:38:010 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:09:38:013 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:09:38:123 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:09:38:310 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:09:38:313 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:09:38:316 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:09:38:457 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:09:38:547 ==>> [D][05:17:53][COMM]4672 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:38:598 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:09:38:601 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:09:38:603 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:09:38:652 ==>> 5A A5 01 5A A5 


2025-07-31 21:09:38:757 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:09:38:904 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:09:38:907 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:09:38:910 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:09:39:046 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 15
[D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last

2025-07-31 21:09:39:106 ==>> _tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5018. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019
[D][05:17:54][COMM]read battery soc:255


2025-07-31 21:09:39:218 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:09:39:220 ==>> 该项需要延时执行
2025-07-31 21:09:39:571 ==>> [D][05:17:54][COMM]5683 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:40:455 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:09:40:947 ==>>                                                                                                        d value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file s

2025-07-31 21:09:41:052 ==>> tatus fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_nu

2025-07-31 21:09:41:157 ==>> m:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work st

2025-07-31 21:09:41:247 ==>> ate err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6695 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][CAT1]power_urc_cb ret[5]
                                         

2025-07-31 21:09:41:589 ==>> [D][05:17:56][COMM]7706 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:42:598 ==>> [D][05:17:57][COMM]8718 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:09:42:922 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 21:09:43:231 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:09:43:235 ==>> 检测【33V输入电压ADC】
2025-07-31 21:09:43:237 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:09:43:572 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:17:58][COMM]adc read out 24v adc:1327  volt:33563 mv
[D][05:17:58][COMM]adc read left brake adc:24  volt:31 mv
[D][05:17:58][COMM]adc read right brake adc:17  volt:22 mv
[D][05:17:58][COMM]adc read throttle adc:25  volt:32 mv
[D][05:17:58][COMM]adc read battery ts volt:21 mv
[D][05:17:58][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:17:58][COMM]adc read throttle brake in adc:22  volt:38 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:09:43:617 ==>>                                                                                                  

2025-07-31 21:09:43:775 ==>> 【33V输入电压ADC】通过,【32906mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:09:43:778 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:09:43:781 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:09:43:876 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2756mV
Get AD_V6 1991mV
Get AD_V7 1090mV
OVER 150


2025-07-31 21:09:43:936 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10004
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10005


2025-07-31 21:09:44:057 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:44:060 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:09:44:090 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:44:092 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:09:44:094 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:09:44:122 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:09:44:125 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:09:44:158 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:09:44:161 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:09:44:194 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:09:44:197 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:09:44:272 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1995mV
Get AD_V7 1091mV
OVER 150


2025-07-31 21:09:44:498 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:44:500 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:09:44:542 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:44:545 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:09:44:548 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:09:44:588 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:09:44:591 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:09:44:638 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:09:44:640 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:09:44:685 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:09:44:688 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:09:44:812 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10740 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:

2025-07-31 21:09:44:917 ==>> 59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1994mV
Get AD_V7 1091mV
OVER 150
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:09:44:977 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 21:09:45:007 ==>>                                          

2025-07-31 21:09:45:066 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:45:069 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:09:45:125 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:45:128 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:09:45:130 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:09:45:190 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:09:45:194 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:09:45:259 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:09:45:262 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:09:45:330 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:09:45:333 ==>> 检测【打开WIFI(1)】
2025-07-31 21:09:45:336 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:09:45:561 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:09:45:606 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:09:45:626 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:09:45:632 ==>> 检测【清空消息队列(1)】
2025-07-31 21:09:45:653 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:09:45:851 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:09:45:925 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:09:45:928 ==>> 检测【打开GPS(1)】
2025-07-31 21:09:45:933 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:09:46:156 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:09:46:224 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:09:46:229 ==>> 检测【打开GSM联网】
2025-07-31 21:09:46:248 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:09:46:462 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:09:46:574 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:09:46:579 ==>> 检测【打开仪表供电1】
2025-07-31 21:09:46:582 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:09:46:769 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:09:46:900 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:09:46:903 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:09:46:906 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:09:47:289 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][COMM]read battery soc:255
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087907640

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130020290504

OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[

2025-07-31 21:09:47:319 ==>> D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:09:47:493 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:09:47:496 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:09:47:498 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:09:47:656 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33433]


2025-07-31 21:09:47:789 ==>> 【读取主控ADC采集的仪表电压】通过,【33433mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:09:47:793 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:09:47:795 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:09:47:960 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:09:48:089 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:09:48:092 ==>> 检测【AD_V20电压】
2025-07-31 21:09:48:096 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:09:48:191 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:09:48:297 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:09:48:480 ==>> 本次取值间隔时间:279ms
2025-07-31 21:09:48:512 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:09:48:622 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:09:48:667 ==>> [D][05:18:03][COMM]14754 imu init OK
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:09:48:990 ==>> 本次取值间隔时间:353ms
2025-07-31 21:09:49:031 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:09:49:095 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][COMM]read battery soc:255
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub i

2025-07-31 21:09:49:125 ==>> d: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:09:49:140 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:09:49:230 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 21:09:49:305 ==>>                                                                                                                                                                                                                                                                                                                                                                                                    [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1505mV
OVER 150


2025-07-31 21:09:49:470 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:09:49:591 ==>> 本次取值间隔时间:439ms
2025-07-31 21:09:49:624 ==>> 【AD_V20电压】通过,【1505mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:49:627 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:09:49:631 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:09:49:840 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:09:49:923 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:09:49:926 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:09:49:930 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:09:50:161 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:09:50:213 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:09:50:216 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:09:50:220 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:09:50:266 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:09:50:514 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:09:50:518 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:09:50:522 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:09:50:543 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:09:50:646 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:09:50:802 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:09:50:805 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:09:50:808 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:09:50:970 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:09:51:015 ==>>                                          

2025-07-31 21:09:51:091 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:09:51:095 ==>> 检测【AD_V21电压】
2025-07-31 21:09:51:097 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:09:51:166 ==>> 1A A1 20 00 00 
Get AD_V21 1037mV
OVER 150


2025-07-31 21:09:51:211 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:09:51:271 ==>> 本次取值间隔时间:170ms
2025-07-31 21:09:51:304 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:09:51:438 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,25,,,40,59,,,40,24,,,39,33,,,39,1*7E

$GBGSV,2,2,06,16,,,37,14,,,36,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.478,1637.478,52.260,2097152,2097152,2097152*4C

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

1A A1 20 00 00 
Get AD_V21 1642mV
OVER 150


2025-07-31 21:09:51:604 ==>> 本次取值间隔时间:294ms
2025-07-31 21:09:51:619 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:09:51:638 ==>> 【AD_V21电压】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:09:51:641 ==>> 检测【关闭仪表供电2】
2025-07-31 21:09:51:643 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:09:51:863 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:09:51:942 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:09:51:945 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:09:51:947 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:09:52:167 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:09:52:228 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:09:52:231 ==>> 检测【打开AccKey2供电】
2025-07-31 21:09:52:233 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:09:52:364 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,41,25,,,40,59,,,40,24,,,40,1*7D

$GBGSV,4,2,13,60,,,39,16,,,36,14,,,34,44,,,31,1*7C

$GBGSV,4,3,13,39,,,41,41,,,37,3,,,37,40,,,36,1*4B

$GBGSV,4,4,13,5,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1559.891,1559.891,49.923,2097152,2097152,2097152*4A



2025-07-31 21:09:52:454 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:09:52:517 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:09:52:522 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:09:52:525 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:09:52:773 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:07][COMM]adc read out 24v adc:1331  volt:33664 mv
[D][05:18:07][COMM]adc read left brake adc:31  volt:40 mv
[D][05:18:07][COMM]adc read right brake adc:20  volt:26 mv
[D][05:18:07][COMM]adc read throttle adc:25  volt:32 mv
[D][05:18:07][COMM]adc read battery ts volt:26 mv
[D][05:18:07][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:07][COMM]adc read throttle brake in adc:16  volt:28 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:09:53:031 ==>> [D][05:18:08][COMM]read battery soc:255


2025-07-31 21:09:53:065 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33664mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:09:53:069 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:09:53:072 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:09:53:228 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:09:53:333 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,25,,,40,59,,,40,24,,,40,1*74

$G

2025-07-31 21:09:53:378 ==>> BGSV,5,2,18,60,,,39,39,,,38,41,,,38,3,,,38,1*43

$GBGSV,5,3,18,5,,,38,40,,,37,16,,,36,1,,,36,1*71

$GBGSV,5,4,18,14,,,35,7,,,35,4,,,34,38,,,34,1*73

$GBGSV,5,5,18,2,,,32,44,,,32,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1529.346,1529.346,48.921,2097152,2097152,2097152*49



2025-07-31 21:09:53:391 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:09:53:410 ==>> 该项需要延时执行
2025-07-31 21:09:54:385 ==>> $GBGGA,130958.201,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,25,,,40,59,,,40,24,,,40,1*7E

$GBGSV,6,2,22,60,,,39,39,,,39,3,,,39,41,,,38,1*49

$GBGSV,6,3,22,40,,,38,16,,,36,1,,,36,14,,,36,1*4A

$GBGSV,6,4,22,7,,,36,5,,,34,38,,,34,34,,,34,1*78

$GBGSV,6,5,22,23,,,34,4,,,33,2,,,33,44,,,32,1*74

$GBGSV,6,6,22,9,,,42,10,,,38,1*43

$GBRMC,130958.201,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130958.201,0.000,1519.446,1519.446,48.608,2097152,2097152,2097152*56



2025-07-31 21:09:54:691 ==>> $GBGGA,130958.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,25,,,40,59,,,40,24,,,40,1*7E

$GBGSV,6,2,22,60,,,39,39,,,39,3,,,39,41,,,38,1*49

$GBGSV,6,3,22,40,,,38,16,,,36,1,,,36,14,,,36,1*4A

$GBGSV,6,4,22,7,,,36,5,,,34,38,,,34,34,,,34,1*78

$GBGSV,6,5,22,23,,,34,4,,,33,2,,,33,44,,,33,1*75

$GBGSV,6,6,22,9,,,40,6,,,37,1*79

$GBRMC,130958.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130958.501,0.000,1521.515,1521.515,48.670,2097152,2097152,2097152*5E



2025-07-31 21:09:55:028 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 21:09:55:709 ==>> $GBGGA,130959.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,40,59,,,40,24,,,40,1*78

$GBGSV,6,2,24,3,,,40,60,,,39,39,,,39,41,,,38,1*41

$GBGSV,6,3,24,40,,,38,16,,,37,14,,,37,1,,,36,1*4C

$GBGSV,6,4,24,7,,,36,38,,,35,34,,,34,23,,,34,1*4B

$GBGSV,6,5,24,2,,,34,9,,,33,6,,,33,5,,,33,1*7C

$GBGSV,6,6,24,4,,,33,44,,,33,12,,,32,42,,,38,1*4B

$GBRMC,130959.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130959.501,0.000,1506.925,1506.925,48.214,2097152,2097152,2097152*59



2025-07-31 21:09:56:395 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:09:56:400 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:09:56:405 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:09:56:751 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:11][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:11][COMM]adc read left brake adc:20  volt:26 mv
[D][05:18:11][COMM]adc read right brake adc:22  volt:29 mv
[D][05:18:11][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:11][COMM]adc read battery ts volt:26 mv
[D][05:18:11][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:11][COMM]adc read throttle brake in adc:16  volt:28 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,131000.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,59,,,40,24,,,40,1*7B

$GBGSV,7,2,26,3,,,40,60,,,40,39,,,39,41,,,38,1*4C

$GBGSV,7,3,26,40,,,38,16,,,37,14,,,37,1,,,36,1*4F

$GBGSV,7,4,26,7,,,36,38,,,35,2,,,35,42,,,34,1*7B

$GBGSV,7,5,26,34,,,34,23,,,34,9,,,34,6,,,34,1*79

$GBGSV,7,6,26,44,,,34,10,,,34,4,,,33,5,,,32,1*72

$GBGSV,7

2025-07-31 21:09:56:796 ==>> ,7,26,12,,,32,32,,,40,1*75

$GBRMC,131000.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131000.501,0.000,1505.767,1505.767,48.173,2097152,2097152,2097152*5F



2025-07-31 21:09:56:960 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【404mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:09:56:963 ==>> 检测【打开AccKey1供电】
2025-07-31 21:09:56:968 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:09:57:025 ==>> [D][05:18:12][COMM]read battery soc:255


2025-07-31 21:09:57:130 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripher

2025-07-31 21:09:57:160 ==>> al_device_poweron type 5.... 


2025-07-31 21:09:57:252 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:09:57:256 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:09:57:259 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:09:57:373 ==>> 1A A1 00 40 00 
Get AD_V14 2557mV
OVER 150


2025-07-31 21:09:57:510 ==>> 原始值:【2557】, 乘以分压基数【2】还原值:【5114】
2025-07-31 21:09:57:578 ==>> 【读取AccKey1电压(ADV14)前】通过,【5114mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:09:57:583 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:09:57:587 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:09:57:896 ==>> $GBGGA,131001.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,40,59,,,40,24,,,40,1*78

$GBGSV,7,2,25,3,,,40,60,,,39,39,,,39,41,,,38,1*41

$GBGSV,7,3,25,40,,,38,16,,,37,14,,,37,1,,,36,1*4C

$GBGSV,7,4,25,7,,,36,38,,,35,42,,,35,6,,,35,1*7D

$GBGSV,7,5,25,2,,,34,34,,,34,23,,,34,9,,,34,1*7E

$GBGSV,7,6,25,44,,,34,10,,,34,4,,,33,12,,,33,1*46

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,131001.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131001.501,0.000,751.474,751.474,687.240,2097152,2097152,2097152*68

[W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:18:12][COMM]adc read out 24v adc:26  volt:657 mv
[D][05:18:12][COMM]adc read left brake adc:23  volt:30 mv
[D][05:18:12][COMM]adc read right brake adc:30  volt:39 mv
[D][05:18:12][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:12][COMM]adc read battery ts volt:26 mv
[D][05:18:12][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:12][COMM]adc read throttle brake in adc:18  volt:31 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv


2025-07-31 21:09:57:926 ==>> 
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:09:58:149 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5531mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:09:58:153 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:09:58:158 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:09:58:341 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:09:58:463 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:09:58:468 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:09:58:473 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:09:58:574 ==>> 1A A1 00 40 00 
Get AD_V14 2557mV
OVER 150


2025-07-31 21:09:58:677 ==>> $GBGGA,131002.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,40,59,,,40,60,,,40,1*4F

$GBGSV,7,2,26,24,,,40,25,,,40,39,,,39,40,,,38,1*79

$GBGSV,7,3,26,41,,,38,1,,,37,16,,,37,14,,,37,1*4F

$GBGSV,7,4,26,7,,,36,42,,,36,38,,,35,6,,,35,1*7D

$GBGSV,7,5,26,9,,,35,10,,,34,2,,,34,44,,,34,1*7B

$GBGSV,7,6,26,34,,,34,23,,,34,4,,,33,12,,,33,1*42

$GBGSV,7,7,26,

2025-07-31 21:09:58:722 ==>> 原始值:【2557】, 乘以分压基数【2】还原值:【5114】
2025-07-31 21:09:58:727 ==>> 5,,,32,8,,,16,1*79

$GBRMC,131002.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131002.501,0.000,739.383,739.383,676.192,2097152,2097152,2097152*69



2025-07-31 21:09:58:853 ==>> 【读取AccKey1电压(ADV14)后】通过,【5114mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:09:58:856 ==>> 检测【打开WIFI(2)】
2025-07-31 21:09:58:860 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:09:59:088 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][COMM]read battery soc:255
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:09:59:203 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:09:59:207 ==>> 检测【转刹把供电】
2025-07-31 21:09:59:212 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:09:59:331 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:09:59:541 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:09:59:545 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:09:59:548 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:09:59:656 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:09:59:824 ==>> $GBGGA,131003.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,59,,,40,24,,,40,1*4E

$GBGSV,7,2,26,25,,,40,60,,,39,39,,,39,40,,,38,1*77

$GBGSV,7,3,26,41,,,38,16,,,37,14,,,37,7,,,36,1*48

$GBGSV,7,4,26,1,,,36,42,,,36,38,,,35,6,,,35,1*7B

$GBGSV,7,5,26,9,,,35,2,,,34,44,,,34,34,,,34,1*7D

$GBGSV,7,6,26,23,,,34,10,,,33,4,,,33,12,,,33,1*43

$GBGSV,7,7,26,8,,,32,5,,,32,1*7F

$GBRMC,131003.501,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131003.501,0.000,750.427,750.427,686.283,2097152,2097152,2097152*64

[D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2393mV
OVER 150


2025-07-31 21:09:59:960 ==>> 原始值:【2393】, 乘以分压基数【2】还原值:【4786】
2025-07-31 21:10:00:016 ==>> 【读取AD_V15电压(前)】通过,【4786mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:10:00:022 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:10:00:043 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:10:00:131 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:10:00:146 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:10:00:251 ==>> 1A A1 01 00 00 
Get AD_V16 2425mV
OVER 150


2025-07-31 21:10:00:296 ==>> 原始值:【2425】, 乘以分压基数【2】还原值:【4850】
2025-07-31 21:10:00:341 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-64
+WIFISCAN:4,2,F62A7D2297A3,-65
+WIFISCAN:4,3,74C330CCAB10,-66

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 21:10:00:358 ==>> 【读取AD_V16电压(前)】通过,【4850mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:10:00:361 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:10:00:367 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:10:00:791 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:15][COMM]adc read out 24v adc:23  volt:581 mv
[D][05:18:15][COMM]adc read left brake adc:27  volt:35 mv
[D][05:18:15][COMM]adc read right brake adc:27  volt:35 mv
[D][05:18:15][COMM]adc read throttle adc:27  volt:35 mv
[D][05:18:15][COMM]adc read battery ts volt:27 mv
[D][05:18:15][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3101  volt:5450 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,131004.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,59,,,40,25,,,40,39,,,39,40,,,38,1*72

$GBGSV,7,3,27,41,,,38,16,,,37,42,,,37,14,,,37,1*79

$GBGSV,7,4,27,7,,,36,1,,,36,38,,,35,9,,,35,1*44

$GBGSV,7,5,27,6,,,35,2,,,34,13,,,34,44,,,34,1*76

$GBGSV,7,6,27,34,,,34,23,,,34,10,,,33,12,,,33,1*76

$G

2025-07-31 21:10:00:836 ==>> BGSV,7,7,27,4,,,33,8,,,32,5,,,32,1*4A

$GBRMC,131004.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131004.501,0.000,749.453,749.453,685.393,2097152,2097152,2097152*60

[D][05:18:15][GNSS]recv submsg id[3]
[D][05:18:15][COMM]IMU: [-3,10,-1027] ret=23 AWAKE!


2025-07-31 21:10:00:963 ==>> 【转刹把供电电压(主控ADC)】通过,【5450mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:10:00:968 ==>> 检测【转刹把供电电压】
2025-07-31 21:10:00:974 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:10:01:047 ==>> [D][05:18:16][COMM]read battery soc:255


2025-07-31 21:10:01:272 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:16][COMM]adc read out 24v adc:24  volt:607 mv
[D][05:18:16][COMM]adc read left brake adc:27  volt:35 mv
[D][05:18:16][COMM]adc read right brake adc:24  volt:31 mv
[D][05:18:16][COMM]adc read throttle adc:23  volt:30 mv
[D][05:18:16][COMM]adc read battery ts volt:29 mv
[D][05:18:16][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3093  volt:5436 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:10:01:606 ==>> 【转刹把供电电压】通过,【5436mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:10:01:611 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:10:01:615 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:01:716 ==>> $GBGGA,131005.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,39,,,39,40,,,38,1*76

$GBGSV,7,3,27,41,,,38,16,,,37,42,,,37,14,,,37,1*79

$GBGSV,7,4,27,7,,,36,1,,,36,2,,,35,13,,,35,1*46

$GBGSV,7,5,27,38,,,35,9,,,35,6,,,35,34,,,35,1*72

$GBGSV,7,6,27,23,,,35,44,,,34,10,,,33,12,,,33,1*70

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,131005.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131005.501,0.000,751.744,751.744,687.488,2097152,2097152,2097152*6E



2025-07-31 21:10:01:792 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:10:01:921 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:10:01:928 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:10:01:948 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:02:036 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:10:02:145 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:02:253 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:10:02:259 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:10:02:328 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:10:02:358 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:02:373 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:10:02:463 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:10:02:572 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:10:02:642 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:10:02:646 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:10:02:650 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:02:675 ==>> $GBGGA,131006.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60

2025-07-31 21:10:02:735 ==>> ,,,39,39,,,39,40,,,38,1*76

$GBGSV,7,3,27,41,,,38,1,,,37,42,,,37,14,,,37,1*4F

$GBGSV,7,4,27,7,,,36,16,,,36,2,,,35,13,,,35,1*70

$GBGSV,7,5,27,38,,,35,9,,,35,6,,,35,34,,,35,1*72

$GBGSV,7,6,27,10,,,34,44,,,34,23,,,34,8,,,33,1*4D

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131006.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131006.501,0.000,752.507,752.507,688.185,2097152,2097152,2097152*6A



2025-07-31 21:10:02:750 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:10:02:840 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:10:02:855 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:10:02:870 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:10:02:960 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:10:03:066 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:18][COMM]read battery soc:255
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:10:03:135 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:10:03:141 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:10:03:146 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:10:03:263 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:10:03:429 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:10:03:435 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:10:03:441 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:10:03:572 ==>> 3A A3 04 01 A3 


2025-07-31 21:10:03:675 ==>> $GBGGA,131007.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,39,,,39,40,,,38,1*76

$GBGSV,7,3,27,14,,,38,41,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,27,42,,,37,7,,,36,2,,,35,13,,,35,1*70

$GBGSV,7,5,27,38,,,35,9,,,35,6,,,35,34,,,35,1*72

$GBGSV,7,6,27,23,,,35,10,,,34,44,

2025-07-31 21:10:03:720 ==>> ,,34,8,,,33,1*4C

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131007.501,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131007.501,0.000,754.805,754.805,690.286,2097152,2097152,2097152*62

ON_OUT4
OVER 150


2025-07-31 21:10:03:984 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:10:03:991 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:10:03:997 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:10:04:074 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:10:04:272 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:10:04:277 ==>> 检测【左刹电压测试1】
2025-07-31 21:10:04:285 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:10:04:582 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:19][COMM]adc read out 24v adc:17  volt:429 mv
[D][05:18:19][COMM]adc read left brake adc:1737  volt:2289 mv
[D][05:18:19][COMM]adc read right brake adc:1743  volt:2297 mv
[D][05:18:19][COMM]adc read throttle adc:1737  volt:2289 mv
[D][05:18:19][COMM]adc read battery ts volt:23 mv
[D][05:18:19][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:19][COMM]adc read throttle brake in adc:19  volt:33 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:10:04:685 ==>>                                                                                                                                          ,2,27,25,,,40,60,,,39,39,,,39,40,,,38,1*76

$GBGSV,7,3,27,14,,,38,41,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,27,42,,,37,7,,,36,6,,,36,2,,,35,1*47

$GBGSV,7,5,27,13,,,35,38,,,35,44,,,35,9,,,35,1*41

$GBGSV,7,6,27,34,,,35,10,,,34,23,,,34,8,,,33,1*4B

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131008.501,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00

2025-07-31 21:10:04:715 ==>> ,T,,M,0.000,N,0.000,K,N*20

$GBGST,131008.501,0.000,755.571,755.571,690.986,2097152,2097152,2097152*66



2025-07-31 21:10:04:847 ==>> 【左刹电压测试1】通过,【2289】符合目标值【2250】至【2500】要求!
2025-07-31 21:10:04:852 ==>> 检测【右刹电压测试1】
2025-07-31 21:10:04:883 ==>> 【右刹电压测试1】通过,【2297】符合目标值【2250】至【2500】要求!
2025-07-31 21:10:04:887 ==>> 检测【转把电压测试1】
2025-07-31 21:10:04:913 ==>> 【转把电压测试1】通过,【2289】符合目标值【2250】至【2500】要求!
2025-07-31 21:10:04:917 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:10:04:922 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:10:04:960 ==>> 3A A3 03 00 A3 


2025-07-31 21:10:05:065 ==>> [D][05:18:20][COMM]read battery soc:255
OFF_OUT3
OVER 150


2025-07-31 21:10:05:222 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:10:05:226 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:10:05:229 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:10:05:371 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:10:05:515 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:10:05:519 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:10:05:524 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:10:05:569 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:10:05:674 ==>> $GBGGA,131009.501,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,41,,,38,1,,,37,16,,,37,42,,,37,1*4D

$GBGSV,7,4,27,14,,,37,7,,,36,2,,,35,38,,,35,1*7A

$GBGSV,7,5,27,44,,,35,9,,,35,6,,,35,34,,,35,1*79

$GBGSV,7,6,27,10,,,34,13,,,34,2

2025-07-31 21:10:05:719 ==>> 3,,,34,8,,,33,1*4F

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131009.501,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131009.501,0.000,754.042,754.042,689.588,2097152,2097152,2097152*6D



2025-07-31 21:10:05:814 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:10:05:818 ==>> 检测【左刹电压测试2】
2025-07-31 21:10:05:822 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:10:06:085 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:21][COMM]adc read out 24v adc:26  volt:657 mv
[D][05:18:21][COMM]adc read left brake adc:26  volt:34 mv
[D][05:18:21][COMM]adc read right brake adc:25  volt:32 mv
[D][05:18:21][COMM]adc read throttle adc:30  volt:39 mv
[D][05:18:21][COMM]adc read battery ts volt:29 mv
[D][05:18:21][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:21][COMM]adc read throttle brake in adc:20  volt:35 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:10:06:363 ==>> 【左刹电压测试2】通过,【34】符合目标值【0】至【50】要求!
2025-07-31 21:10:06:367 ==>> 检测【右刹电压测试2】
2025-07-31 21:10:06:397 ==>> 【右刹电压测试2】通过,【32】符合目标值【0】至【50】要求!
2025-07-31 21:10:06:401 ==>> 检测【转把电压测试2】
2025-07-31 21:10:06:432 ==>> 【转把电压测试2】通过,【39】符合目标值【0】至【50】要求!
2025-07-31 21:10:06:436 ==>> 检测【晶振检测】
2025-07-31 21:10:06:442 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:10:06:746 ==>> $GBGGA,131010.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,39,,,39,40,,,38,1*76

$GBGSV,7,3,27,14,,,38,41,,,38,16,,,37,42,,,37,1*76

$GBGSV,7,4,27,7,,,36,1,,,36,2,,,35,38,,,35,1*4F

$GBGSV,7,5,27,9,,,35,6,,,35,34,,,35,13,,,34,1*7A

$GBGSV,7,6,27,44,,,34,23,,,34,10,,,33,8,,,33,1*4A

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131010.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131010.501,0.000,751.745,751.745,687.488,2097152,2097152,2097152*6A

[W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 21:10:07:000 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:10:07:005 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:10:07:011 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:10:07:086 ==>> [D][05:18:22][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1656mV
Get AD_V4 1652mV
Get AD_V5 2754mV
Get AD_V6 1996mV
Get AD_V7 1091mV
OVER 150


2025-07-31 21:10:07:283 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:10:07:287 ==>> 检测【检测BootVer】
2025-07-31 21:10:07:293 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:07:633 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130020290504
[D][05:18:22][FCTY]HardwareID  = 867222087907640
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:22][FCTY]Bat         = 3924 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1299
[D][05:18:22][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 404 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1      

2025-07-31 21:10:07:738 ==>>    = 3707 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:10:07:834 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:10:07:838 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:10:07:844 ==>> 检测【检测固件版本】
2025-07-31 21:10:07:875 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:10:07:879 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:10:07:883 ==>> 检测【检测蓝牙版本】
2025-07-31 21:10:07:921 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:10:07:925 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:10:07:930 ==>> 检测【检测MoBikeId】
2025-07-31 21:10:07:954 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:10:07:961 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:10:07:967 ==>> 检测【检测蓝牙地址】
2025-07-31 21:10:07:989 ==>> 取到目标值:EBD2099DF656
2025-07-31 21:10:07:993 ==>> 【检测蓝牙地址】通过,【EBD2099DF656】符合目标值【】要求!
2025-07-31 21:10:07:997 ==>> 提取到蓝牙地址:EBD2099DF656
2025-07-31 21:10:08:000 ==>> 检测【BOARD_ID】
2025-07-31 21:10:08:031 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:10:08:036 ==>> 检测【检测充电电压】
2025-07-31 21:10:08:077 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:10:08:083 ==>> 检测【检测VBUS电压1】
2025-07-31 21:10:08:123 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:10:08:128 ==>> 检测【检测充电电流】
2025-07-31 21:10:08:350 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:10:08:355 ==>> 检测【检测IMEI】
2025-07-31 21:10:08:358 ==>> 取到目标值:867222087907640
2025-07-31 21:10:08:411 ==>> 【检测IMEI】通过,【867222087907640】符合目标值【】要求!
2025-07-31 21:10:08:415 ==>> 提取到IMEI:867222087907640
2025-07-31 21:10:08:421 ==>> 检测【检测IMSI】
2025-07-31 21:10:08:427 ==>> 取到目标值:460130020290504
2025-07-31 21:10:08:462 ==>> 【检测IMSI】通过,【460130020290504】符合目标值【】要求!
2025-07-31 21:10:08:466 ==>> 提取到IMSI:460130020290504
2025-07-31 21:10:08:470 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:10:08:474 ==>> 取到目标值:460130020290504
2025-07-31 21:10:08:516 ==>> 【校验网络运营商(移动)】通过,【460130020290504】符合目标值【】要求!
2025-07-31 21:10:08:523 ==>> 检测【打开CAN通信】
2025-07-31 21:10:08:529 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:10:08:577 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:10:08:682 ==>> $GBGGA,131012.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,60,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,14,,,38,41,,,38,7,,,37,1,,,37,1*71

$GBGSV,7,4,27,16,,,37,42,,,37,9,,,36,2,,,35,1*79

$GBGSV,7,5,27,13,,,35,38,,,35,6,,,35,34,,,35,1*49

$GBGSV,7,6,27,23,,,35,10,,,34,44,,,34,8,,,33,1*4C

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131012.501,V,,,,,,

2025-07-31 21:10:08:712 ==>> ,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131012.501,0.000,757.874,757.874,693.093,2097152,2097152,2097152*63



2025-07-31 21:10:08:822 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:10:08:828 ==>> 检测【检测CAN通信】
2025-07-31 21:10:08:834 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:10:08:987 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:10:09:077 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35166
[D][05:18:24][COMM]read battery soc:255


2025-07-31 21:10:09:107 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:10:09:118 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:10:09:138 ==>> 检测【关闭CAN通信】
2025-07-31 21:10:09:142 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:10:09:167 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:10:09:434 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:10:09:439 ==>> 检测【打印IMU STATE】
2025-07-31 21:10:09:445 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:10:09:781 ==>> $GBGGA,131013.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,59,,,41,25,,,41,60,,,40,1*7B

$GBGSV,7,2,27,3,,,40,24,,,40,39,,,39,40,,,38,1*4C

$GBGSV,7,3,27,14,,,38,41,,,38,7,,,37,1,,,37,1*71

$GBGSV,7,4,27,16,,,37,42,,,37,9,,,36,34,,,36,1*4F

$GBGSV,7,5,27,2,,,35,13,,,35,38,,,35,6,,,35,1*7C

$GBGSV,7,6,27,23,,,35,10,,,34,44,,,34,4,,,34,1*47

$GBGSV,7,7,27,8,,,33,12,,,33,5,,,32,1*7C

$GBRMC,131013.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131013.501,0.000,760.943,760.943,695.899,2097152,2097152,2097152*66

[W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:10:09:979 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:10:09:983 ==>> 检测【六轴自检】
2025-07-31 21:10:09:987 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:10:10:152 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:10:10:714 ==>> $GBGGA,131014.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,40,3,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,14,,,38,41,,,38,7,,,37,1,,,37,1*71

$GBGSV,7,4,27,16,,,37,42,,,37,9,,,36,34,,,36,1*4F

$GBGSV,7,5,27,2,,,35,13,,,35,38,,,35,6,,,35,1*7C

$GBGSV,7,6,27,23,,,35,10,,,34,44,,,34,5,,,33,1*41

$GBGSV,7,7,27,8,,,33,12,,,33,4,,,33,1*7C

$GBRMC,131014.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131014.501,0.000,759.404,759.404,694.492,2097152,2097152,2097152*67



2025-07-31 21:10:11:096 ==>> [D][05:18:26][COMM]read battery soc:255


2025-07-31 21:10:11:722 ==>> $GBGGA,131015.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,14,,,38,41,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,27,7,,,36,42,,,36,2,,,35,38,,,35,1*78

$GBGSV,7,5,27,9,,,35,6,,,35,34,,,35,23,,,35,1*78

$GBGSV,7,6,27,10,,,34,13,,,34,44,,,34,8,,,33,1*4E

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131015.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131015.501,0.000,754.043,754.043,689.589,2097152,2097152,2097152*61



2025-07-31 21:10:11:827 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec o

2025-07-31 21:10:11:857 ==>> ver: func id: 12, ret: 6


2025-07-31 21:10:12:022 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38118 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-7,-11,4059]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 21:10:12:088 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:10:12:093 ==>> 检测【打印IMU STATE2】
2025-07-31 21:10:12:100 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:10:12:265 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:10:12:380 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:10:12:384 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:10:12:388 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:10:12:463 ==>> 5A A5 02 5A A5 


2025-07-31 21:10:12:568 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:10:12:686 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:10:12:690 ==>> 检测【检测VBUS电压2】
2025-07-31 21:10:12:696 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:12:809 ==>> $GBGGA,131016.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,41,,,38,16,,,37,14,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,42,,,36,2,,,35,38,,,35,1*7E

$GBGSV,7,5,27,9,,,35,6,,,35,34,,,35,23,,,35,1*78

$GBGSV,7,6,27,13,,,34,44,,,34,10,,,33,8,,,33,1*49

$GBGSV,7,7,27,12,,,33,4,,,33,5,,,32,1*70

$GBRMC,131016.501,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131016.501,0.000,751.745,751.745,687.488,2097152,2097152,2097152*6C

[D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 21:10:13:111 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130020290504
[D][05:18:28][FCTY]HardwareID  = 867222087907640
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 8, adc = 331
[D][05:18:28][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 379 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:

2025-07-31 21:10:13:156 ==>> 18:28][FCTY]Bat1         = 3707 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 21:10:13:234 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:13:632 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130020290504
[D][05:18:28][FCTY]HardwareID  = 867222087907640
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 50 ma
[D][05:18:28][FCTY]VBUS        = 8500 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 188
[D][05:18:28][FCTY]Acckey1 vol = 5535 mv, Acckey2 vol = 581 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b

2025-07-31 21:10:13:735 ==>> 5b1
[D][05:18:28][FCTY]Bat1         = 3707 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:10:13:783 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:14:144 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130020290504
[D][05:18:29][FCTY]HardwareID  = 867222087907640
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 50 ma
[D][05:18:29][FCTY]VBUS        = 8500 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:29][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 354 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][

2025-07-31 21:10:14:204 ==>> FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3707 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]msg 0601 loss. last_tick:35159. cur_tick:40179. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40180


2025-07-31 21:10:14:325 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:15:056 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130020290504
[D][05:18:29][FCTY]HardwareID  = 867222087907640
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:29][FCTY]Bat         = 3504 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 8500 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 139
[D][05:18:29][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 480 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCT

2025-07-31 21:10:15:161 ==>> Y]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3707 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]all continue location stop
[W][05:18:29][GNSS]sing locating running
[D][05:18:29][COMM]report elecbike
[D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished 

2025-07-31 21:10:15:173 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:10:15:266 ==>> processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, S

2025-07-31 21:10:15:371 ==>> ENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B50A1D8DE1284D4A5CDEF9CC409BFBD4CF4C8C8E01EC3EBC94EDE635F98E2A99B638CBE261D53D954E6B28B2185D52F9BF4FE4A43FD29BFFF2AE12A125F67AD8C26C6E0B4888E2060CE794F6DBBEB02FA52754
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][

2025-07-31 21:10:15:415 ==>> 05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]


2025-07-31 21:10:15:688 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130020290504
[D][05:18:30][FCTY]HardwareID  = 867222087907640
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = EBD2099DF656
[D][05:18:30][FCTY]Bat         = 3544 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 119
[D][05:18:30][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 607 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][F

2025-07-31 21:10:15:733 ==>> CTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3707 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:10:15:985 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:10:15:994 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:10:16:001 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:10:16:071 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:10:16:222 ==>> [D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:18:31][COMM]read battery soc:255


2025-07-31 21:10:16:279 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:10:16:284 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:10:16:292 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:10:16:374 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:10:16:577 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:10:16:583 ==>> 检测【打开WIFI(3)】
2025-07-31 21:10:16:617 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:10:16:765 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:10:16:874 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:10:16:885 ==>> 检测【扩展芯片hw】
2025-07-31 21:10:16:892 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:10:17:733 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:17:901 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:10:18:295 ==>>                                                                                       - get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, l

2025-07-31 21:10:18:400 ==>> en:10800, num:3
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18

2025-07-31 21:10:18:505 ==>> :32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:32][PROT]add success [1629955112],s

2025-07-31 21:10:18:611 ==>> end_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][COMM]read battery soc:255


2025-07-31 21:10:18:716 ==>> [D][05:18:33][COMM]44845 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:18:821 ==>>                                                            [D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:10:18:911 ==>>                                                                                                                                                                                                                                                                

2025-07-31 21:10:18:967 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:10:18:972 ==>> 检测【扩展芯片boot】
2025-07-31 21:10:19:001 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:10:19:006 ==>> 检测【扩展芯片sw】
2025-07-31 21:10:19:034 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:10:19:042 ==>> 检测【检测音频FLASH】
2025-07-31 21:10:19:050 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:10:19:234 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:10:19:759 ==>> [D][05:18:34][COMM]45856 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][CAT1]SEND RAW data timeout
[D][05:18:34][CAT1]exec over: func id: 12, ret: -52


2025-07-31 21:10:20:032 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46038]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18

2025-07-31 21:10:20:062 ==>> :35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:10:20:212 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 21:10:20:748 ==>> [D][05:18:35][COMM]46868 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:20:961 ==>> [D][05:18:36][COMM]f:[drv_audio_ack_receive].wait ack timeout!![47070]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:10:21:756 ==>> [D][05:18:36][COMM]47880 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:22:030 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:22:227 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:10:22:775 ==>> [D][05:18:37][COMM]48891 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:23:136 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 21:10:23:779 ==>> [E][05:18:38][GNSS]GPS module no nmea data!
[D][05:18:38][GNSS]GPS reload stop. ret=0
[D][05:18:38][GNSS]GPS reload start. ret=0
[D][05:18:38][COMM]49902 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:24:109 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:10:24:114 ==>> 检测【打开喇叭声音】
2025-07-31 21:10:24:121 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:10:24:266 ==>> [D][05:18:39][COMM]read battery soc:255
[W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:10:24:400 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:10:24:406 ==>> 检测【打开大灯控制】
2025-07-31 21:10:24:412 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:10:24:551 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:10:24:685 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:10:24:693 ==>> 检测【关闭仪表供电3】
2025-07-31 21:10:24:701 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:10:24:866 ==>> [D][05:18:39][COMM]50913 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 21:10:24:974 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:10:24:980 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:10:24:988 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:10:25:136 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:10:25:262 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:10:25:267 ==>> 检测【读大灯电压】
2025-07-31 21:10:25:276 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:10:25:452 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33154]


2025-07-31 21:10:25:558 ==>> 【读大灯电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:10:25:563 ==>> 检测【关闭大灯控制2】
2025-07-31 21:10:25:568 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:10:25:822 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:18:40][COMM]51924 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:26:112 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:10:26:119 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:10:26:126 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:10:26:262 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]read battery soc:255
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 21:10:26:398 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:10:26:403 ==>> 检测【打开WIFI(4)】
2025-07-31 21:10:26:410 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:10:26:532 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:10:26:753 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:10:26:758 ==>> 检测【EC800M模组版本】
2025-07-31 21:10:26:765 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:26:817 ==>> [D][05:18:41][COMM]52935 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:27:786 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:27:831 ==>> [D][05:18:42][COMM]53946 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:10:28:242 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 21:10:28:594 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:28:821 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:28:836 ==>> [D][05:18:43][COMM]imu error,enter wait


2025-07-31 21:10:29:523 ==>> [D][05:18:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:29:855 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:30:023 ==>> [D][05:18:44][CAT1]exec over: func id: 15, ret: -93
[D][05:18:44][CAT1]sub id: 15, ret: -93

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:45][SAL ]socket send fail. id[4]
[D][05:18:45][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:45][CAT1]gsm read msg sub id: 24
[D][05:18:45][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:45][M2M ]m2m select fd[4]
[D][05:18:45][M2M ]socket[4] Link is disconnected
[D][05:18:45][M2M ]tcpclient close[4]
[D][05:18:45][SAL ]socket[4] has closed
[D][05:18:45][PROT]protocol read data ok
[E][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:45][PROT]M2M Send Fail [1629955125]
[D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 21:10:30:385 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56332]
[D][05:18:45][COMM]accel parse set 0
[D][05:18:45][COMM][Audio].l:[1032].open hexlog save
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:45][COMM]file:A20 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:45][COMM]read file, len:15228, num:4
[D][05:18:45][COMM]read battery soc:255
[D][05:18:45][COMM]--->crc16:0x419c
[D][05:18:45][COMM]read file success
[W][05:18:45][COMM][Audio].l:[936].close hexlog save
[D][05:18:45][COMM]accel parse set 1
[D][05:18:45][COMM][Audio]mon:9,05:18:45
[D][05:18:45][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:10:30:626 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:30:881 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:31:340 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57439]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:10:31:916 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:31:925 ==>> [D][05:18:47][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 21:10:32:266 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 21:10:32:371 ==>> [D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58467]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:10:32:686 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:32:941 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:33:384 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59496]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:10:33:836 ==>> [E][05:18:48][GNSS]GPS module no nmea data!
[D][05:18:48][GNSS]GPS reload stop. ret=0
[D][05:18:48][GNSS]GPS reload start. ret=0


2025-07-31 21:10:33:941 ==>> [D][05:18:49][CAT1]exec over: func id: 24, ret: -181
[D][05:18:49][CAT1]sub id: 24, ret: -181

[D][05:18:49][CAT1]gsm read msg sub id: 23
[D][05:18:49][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 21:10:33:971 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:34:275 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 21:10:34:455 ==>> [D][05:18:49][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 21:10:34:530 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:34:725 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:34:830 ==>> [D][05:18:49][GNSS]recv submsg id[1]
[D][05:18:49][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:49][GNSS]stop gps fail


2025-07-31 21:10:34:981 ==>> [D][05:18:50][CAT1]exec over: func id: 23, ret: -151
[D][05:18:50][CAT1]sub id: 23, ret: -151

[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:10:34:996 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:35:838 ==>> [D][05:18:50][GNSS]recv submsg id[1]
[D][05:18:50][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:50][GNSS]start gps fail


2025-07-31 21:10:36:036 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:36:271 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:10:36:792 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:37:038 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:37:083 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:38:010 ==>> [D][05:18:53][CAT1]SEND RAW data timeout
[D][05:18:53][CAT1]exec over: func id: 12, ret: -52
[W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:10:38:115 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:38:283 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:10:39:154 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:39:523 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:40:010 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:40:193 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:40:286 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:10:41:031 ==>> [D][05:18:56][CAT1]SEND RAW data timeout
[D][05:18:56][CAT1]exec over: func id: 12, ret: -52
[W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:56][CAT1]gsm read msg sub id: 10
[D][05:18:56][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:10:41:229 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:42:031 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:42:262 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:42:292 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 21:10:43:052 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:43:294 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:43:897 ==>> [E][05:18:59][GNSS]GPS module no nmea data!
[D][05:18:59][GNSS]GPS reload stop. ret=0
[D][05:18:59][GNSS]GPS reload start. ret=0


2025-07-31 21:10:44:293 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 21:10:44:323 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:44:533 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:45:091 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:45:347 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:45:742 ==>> [D][05:19:00][COMM]f:[drv_audio_ack_receive].wait ack timeout!![71839]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM][Audio].l:[1032].open hexlog save
[D][05:19:00][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:10:46:306 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 21:10:46:381 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:47:035 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:47:140 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:47:410 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:48:313 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 21:10:48:448 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:49:008 ==>> [D][05:19:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:10:49:190 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:49:476 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:49:537 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:50:305 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 21:10:50:519 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:51:250 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:51:551 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:52:033 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:52:333 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 21:10:52:579 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:53:284 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:53:623 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:53:997 ==>> [E][05:19:09][GNSS]GPS module no nmea data!
[D][05:19:09][GNSS]GPS reload stop. ret=0
[D][05:19:09][GNSS]GPS reload start. ret=0


2025-07-31 21:10:54:327 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 21:10:54:537 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:54:642 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:55:338 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:55:674 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:56:322 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 21:10:56:700 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:10:57:042 ==>> [D][05:19:12][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:10:57:564 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:12][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:12][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:10:57:744 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:10:57:754 ==>> #################### 【测试结束】 ####################
2025-07-31 21:10:57:929 ==>> 关闭5V供电
2025-07-31 21:10:57:940 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:10:58:074 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:10:58:332 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 21:10:58:933 ==>> 关闭5V供电成功
2025-07-31 21:10:58:941 ==>> 关闭33V供电
2025-07-31 21:10:58:951 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:10:59:073 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:10:59:253 ==>> [D][05:19:14][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 8,volt = 12
[D][05:19:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 21:10:59:940 ==>> 关闭33V供电成功
2025-07-31 21:10:59:950 ==>> 关闭3.7V供电
2025-07-31 21:10:59:974 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:11:00:076 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:11:00:541 ==>>  

