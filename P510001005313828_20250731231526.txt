2025-07-31 23:15:26:298 ==>> MES查站成功:
查站序号:P510001005313828验证通过
2025-07-31 23:15:26:302 ==>> 扫码结果:P510001005313828
2025-07-31 23:15:26:304 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:15:26:305 ==>> 测试参数版本:2024.10.11
2025-07-31 23:15:26:307 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:15:26:308 ==>> 检测【打开透传】
2025-07-31 23:15:26:311 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:15:26:419 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:15:26:644 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:15:26:655 ==>> 检测【检测接地电压】
2025-07-31 23:15:26:657 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:15:26:727 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:15:26:927 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:15:26:930 ==>> 检测【打开小电池】
2025-07-31 23:15:26:932 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:15:27:018 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:15:27:204 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:15:27:206 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:15:27:209 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:15:27:324 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:15:27:519 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:15:27:521 ==>> 检测【等待设备启动】
2025-07-31 23:15:27:524 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:15:27:834 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:15:28:031 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:15:28:556 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:15:28:665 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:15:28:725 ==>>                    Battery Low, GPS Will Not Open


2025-07-31 23:15:29:125 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:15:29:603 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:15:29:622 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:15:29:624 ==>> 检测【产品通信】
2025-07-31 23:15:29:625 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:15:29:783 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:15:29:891 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:15:29:894 ==>> 检测【初始化完成检测】
2025-07-31 23:15:29:897 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:15:30:151 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:15:30:241 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:15:30:425 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:15:30:429 ==>> 检测【关闭大灯控制1】
2025-07-31 23:15:30:431 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:15:30:590 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:15:30:666 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:30:698 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:15:30:700 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:15:30:701 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:15:30:770 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L

2025-07-31 23:15:30:815 ==>> :[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:15:30:921 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:15:30:969 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:15:30:973 ==>> 检测【关闭仪表供电】
2025-07-31 23:15:30:975 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:15:31:116 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:15:31:240 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:15:31:243 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:15:31:245 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:15:31:378 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:15:31:520 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:15:31:522 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:15:31:524 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:15:31:697 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:15:31:791 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:15:31:794 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:15:31:796 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:32:006 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:15:32:078 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:15:32:080 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:15:32:083 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:15:32:219 ==>> 5A A5 01 5A A5 


2025-07-31 23:15:32:323 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:15:32:384 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 21


2025-07-31 23:15:32:387 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:15:32:389 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:15:32:391 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:15:32:444 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 23:15:32:519 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:15:32:667 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:15:32:669 ==>> 该项需要延时执行
2025-07-31 23:15:32:685 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:33:206 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x000180

2025-07-31 23:15:33:236 ==>> 2E->0x01B987FE 5011


2025-07-31 23:15:33:695 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:33:981 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:15:34:479 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:17:55][COMM]Main Task receive event:65
[W][05:17:55][COMM]Bat auth off fail, error:-1
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_

2025-07-31 23:15:34:584 ==>> peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2

2025-07-31 23:15:34:689 ==>> M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN

2025-07-31 23:15:34:749 ==>>  get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 23:15:34:794 ==>>                                                                                                                                           

2025-07-31 23:15:35:738 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:36:468 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:15:36:682 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:15:36:685 ==>> 检测【33V输入电压ADC】
2025-07-31 23:15:36:688 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:36:727 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:36:937 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:17:57][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:17:57][COMM]adc read left brake adc:2  volt:2 mv
[D][05:17:57][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:15:37:216 ==>> 【33V输入电压ADC】通过,【32627mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:15:37:218 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:15:37:220 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:15:37:332 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2771mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:15:37:485 ==>> 【TP7_VCC3V3(ADV2)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:37:489 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:15:37:505 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:37:507 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:15:37:509 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 23:15:37:523 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:15:37:525 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:15:37:541 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:15:37:543 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:15:37:565 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:15:37:567 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:15:37:638 ==>> 1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1989mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:15:37:743 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:15:37:843 ==>> 【TP7_VCC3V3(ADV2)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:37:846 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:15:37:861 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:37:881 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:15:37:883 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 23:15:37:886 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:15:37:889 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:15:37:902 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:15:37:904 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:15:37:936 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:15:37:954 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:15:38:029 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2770mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 23:15:38:089 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10022


2025-07-31 23:15:38:276 ==>> 【TP7_VCC3V3(ADV2)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:38:278 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:15:38:322 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:38:345 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:15:38:348 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 23:15:38:388 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:15:38:391 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:15:38:437 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:15:38:440 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:15:38:480 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:15:38:580 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:15:38:582 ==>> 检测【打开WIFI(1)】
2025-07-31 23:15:38:585 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:15:38:958 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu defaul

2025-07-31 23:15:39:003 ==>> t ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 23:15:39:112 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:15:39:115 ==>> 检测【清空消息队列(1)】
2025-07-31 23:15:39:116 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:15:39:489 ==>>                                                                                   [D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087618403

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> 

2025-07-31 23:15:39:519 ==>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:15:39:642 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:15:39:645 ==>> 检测【打开GPS(1)】
2025-07-31 23:15:39:648 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:15:39:826 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 23:15:39:940 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:15:39:963 ==>> 检测【打开GSM联网】
2025-07-31 23:15:39:966 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:15:40:116 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:15:40:210 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:15:40:212 ==>> 检测【打开仪表供电1】
2025-07-31 23:15:40:216 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:15:40:417 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:15:40:477 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 23:15:40:480 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:15:40:483 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:15:40:486 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:15:40:721 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:15:40:756 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:15:40:759 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:15:40:762 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:15:40:901 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33409]


2025-07-31 23:15:41:040 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:15:41:043 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:15:41:046 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:15:41:217 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:15:41:310 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:15:41:324 ==>> 检测【AD_V20电压】
2025-07-31 23:15:41:329 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:15:41:412 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:15:41:519 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1639mV
OVER 150


2025-07-31 23:15:41:744 ==>> [D][05:18:02][COMM]13727 imu init OK


2025-07-31 23:15:41:909 ==>> 本次取值间隔时间:484ms
2025-07-31 23:15:41:927 ==>> 【AD_V20电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:41:930 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:15:41:934 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:15:42:014 ==>> 3A A3 02 00 A3 


2025-07-31 23:15:42:119 ==>> OFF_OUT2
OVER 150


2025-07-31 23:15:42:212 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:15:42:214 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:15:42:218 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:15:42:455 ==>> [D][05:18:03][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:15:42:485 ==>>                                          

2025-07-31 23:15:42:489 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:15:42:494 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:15:42:497 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:15:42:620 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:15:42:759 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:15:42:763 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:15:42:766 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:15:42:924 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:15:43:033 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:15:43:060 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:15:43:062 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:15:43:211 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:15:43:303 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:15:43:306 ==>> 检测【AD_V21电压】
2025-07-31 23:15:43:310 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:15:43:375 ==>> 本次取值间隔时间:59ms
2025-07-31 23:15:43:420 ==>> 1A A1 20 00 00 
Get AD_V21 1635mV
OVER 150


2025-07-31 23:15:43:720 ==>> 本次取值间隔时间:336ms
2025-07-31 23:15:43:741 ==>> 【AD_V21电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:43:744 ==>> 检测【关闭仪表供电2】
2025-07-31 23:15:43:747 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:15:43:917 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:04][COMM]set POWER 0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:15:44:029 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:15:44:034 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:15:44:039 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:15:44:219 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:15:44:311 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:15:44:315 ==>> 检测【打开AccKey2供电】
2025-07-31 23:15:44:319 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:15:44:488 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
[D][05:18:05][COMM]read battery soc:255


2025-07-31 23:15:44:583 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:15:44:588 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:15:44:590 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:44:927 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:05][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:05][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:18:05][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:05][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:05][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:05][COMM]adc read battery ts volt:10 mv
[D][05:18:05][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:05][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:05][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:05][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:05][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:05][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:05][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:15:45:111 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33133mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:15:45:116 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:15:45:119 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:15:45:306 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:15:45:391 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:15:45:394 ==>> 该项需要延时执行
2025-07-31 23:15:45:899 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:06][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:06][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:15:46:786 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:07][COMM]read battery soc:255
[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 5, ret: 6
[D][05:18:07][CAT1]sub id: 5, ret: 6

[D][05:18:07][SAL ]Cellular task submsg id[68]
[D][05:18:07][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:07][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:07][M2M ]M2M_GSM_INIT OK
[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:07][SAL ]open socket ind id[4], rst[0]
[D][05:18:07][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:07][SAL ]Cellular task submsg id[8]
[D][05:18:07][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:07][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:07][CAT1]gsm read msg sub id: 8
[D][05:18:07][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:07][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:07][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:07][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:07][COMM]Main Task receive event:4


2025-07-31 23:15:46:861 ==>> [D][05:18:07][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:07][COMM]init key as 
[D][05:18:07][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:07][COMM]Main Task receive event:4 finished processing
[D][05:18:07][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:07][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:07][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 8, ret: 6


2025-07-31 23:15:46:966 ==>>                                                                                                         

2025-07-31 23:15:47:071 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:07][CAT1]opened : 0, 0
[D][05:18:07][SAL ]Cellular task submsg id[68]
[D][05:18:07][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:07][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:07][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:07][M2M ]g_m2m_is_idle become true
[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:15:47:716 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:15:48:367 ==>> [D][05:18:09][CAT1]<<< 
OK

[D][05:18:09][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:15:48:397 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:15:48:401 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:15:48:405 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:48:592 ==>> [D][05:18:09][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,41,34,,,39,39,,,39,41,,,37,1*70

$GBGSV,2,2,08,25,,,33,23,,,26,11,,,42,59,,,41,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1485.701,1485.701,47.639,2097152,2097152,2097152*40

[D][05:18:09][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:09][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:09][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:09][COMM]read battery soc:255
[D][05:18:09][CAT1]<<< 
OK

[D][05:18:09][CAT1]exec over: func id: 23, ret: 6
[D][05:18:09][CAT1]sub id: 23, ret: 6



2025-07-31 23:15:48:863 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:09][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:09][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:09][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:09][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:09][COMM]adc read battery ts volt:9 mv
[D][05:18:09][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:09][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:09][GNSS]recv submsg id[1]
[D][05:18:09][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:15:48:936 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:15:48:940 ==>> 检测【打开AccKey1供电】
2025-07-31 23:15:48:942 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:15:49:104 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:15:49:225 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:15:49:230 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:15:49:234 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:15:49:329 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 23:15:49:480 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 23:15:49:514 ==>> 【读取AccKey1电压(ADV14)前】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:15:49:518 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:15:49:522 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:49:525 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,59,,,42,60,,,41,3,,,41,1*46

$GBGSV,5,2,18,34,,,40,39,,,40,7,,,40,11,,,38,1*4D

$GBGSV,5,3,18,41,,,38,23,,,38,16,,,37,10,,,37,1*7B

$GBGSV,5,4,18,25,,,35,5,,,32,4,,,30,43,,,38,1*70

$GBGSV,5,5,18,1,,,37,2,,,36,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1578.203,1578.203,50.510,2097152,2097152,2097152*4E



2025-07-31 23:15:49:830 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:10][COMM]adc read battery ts volt:8 mv
[D][05:18:10][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:15:50:048 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5523mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:15:50:052 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:15:50:074 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:15:50:207 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:15:50:334 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:15:50:338 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:15:50:343 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:15:50:417 ==>> 1A A1 00 40 00 
Get AD_V14 2667mV
OVER 150


2025-07-31 23:15:50:522 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,59,,,40,60,,,40,3,,,40,1*4F

$GBGSV,5,2,20,34,,,40,39,,,40,7,,,39,11,,,39,1*49

$GBGSV,5,3,20,41,,,39,23,,,38,16,,,38,25,,,38,1*77

$GBGSV,5,4,20,10,,,37,32,,,35,43,,,34,2,,,34,1*42

$GBGSV,5,5,20,5,,,32,1

2025-07-31 23:15:50:567 ==>> ,,,31,4,,,31,26,,,42,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1542.705,1542.705,49.371,2097152,2097152,2097152*47

[D][05:18:11][COMM]read battery soc:255


2025-07-31 23:15:50:597 ==>> 原始值:【2667】, 乘以分压基数【2】还原值:【5334】
2025-07-31 23:15:50:619 ==>> 【读取AccKey1电压(ADV14)后】通过,【5334mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:15:50:623 ==>> 检测【打开WIFI(2)】
2025-07-31 23:15:50:626 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:15:50:840 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:11][CAT1]gsm read msg sub id: 12
[D][05:18:11][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:11][CAT1]<<< 
OK

[D][05:18:11][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:15:50:889 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:15:50:894 ==>> 检测【转刹把供电】
2025-07-31 23:15:50:898 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:15:51:080 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:15:51:158 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:15:51:162 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:15:51:166 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:15:51:260 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:15:51:320 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2406mV
OVER 150


2025-07-31 23:15:51:425 ==>> 原始值:【2406】, 乘以分压基数【2】还原值:【4812】
2025-07-31 23:15:51:471 ==>> 【读取AD_V15电压(前)】通过,【4812mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:15:51:478 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:15:51:489 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:15:51:561 ==>> $GBGGA,151555.396,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,3,,,40,34,,,40,39,,,40,1*4A

$GBGSV,6,2,21,59,,,39,60,,,39,7,,,39,11,,,39,1*4C

$GBGSV,6,3,21,41,,,39,23,,,39,25,,,39,16,,,38,1*75

$GBGSV,6,4,21,10,,,37,43,,,36,32,,,34,2,,,34,1*43

$GBGSV,6,5,21,24,,,34,5,,,32,4,,,31,1,,,27,1*41

$GBGSV,6,6,21,26,,,37,1*75

$GBRMC,151555.396,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151555.396,0.000,1529.852,1529.852,48.981,2097152,2097152,2097152*51



2025-07-31 23:15:51:576 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:15:51:756 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,151555.596,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,3,,,40,34,,,40,39,,,40,1*48

$GBGSV,6,2,23,59,,,39,60,,,39,7,,,39,11,,,39,1*4E

$GBGSV,6,3,23,41,,,39,23,,,39,25,,,39,16,,,38,1*77

$GBGSV,6,4,23,10,,,37,43,,,37,6,,,36,24,,,35,1*40

$GBGSV,6,5,23,32,,,34,2,,,34,9,,,34,5,,,32,1*4D

$GBGSV,6,6,23,4,,,31,1,,,26,26,,,37,1*74

$GBRMC,151555.596,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151555.596,0.000,1524.575,1524.575,48.811,2097152,2097152,2097152*5F

1A A1 01 00 00 
Get AD_V16 2418mV
OVER 150


2025-07-31 23:15:51:892 ==>> 原始值:【2418】, 乘以分压基数【2】还原值:【4836】
2025-07-31 23:15:51:925 ==>> 【读取AD_V16电压(前)】通过,【4836mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:15:51:928 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:15:51:932 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:52:012 ==>> +WIFISCAN:4,0,F88C21BCF57D,-37
+WIFISCAN:4,1,74C330CCAB10,-71
+WIFISCAN:4,2,44A1917CA62B,-73
+WIFISCAN:4,3,44A1917CAD81,-76

[D][05:18:12][CAT1]wifi scan report total[4]


2025-07-31 23:15:52:239 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:13][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:13][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:13][COMM]adc read battery ts volt:14 mv
[D][05:18:13][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:13][COMM]adc read throttle brake in adc:3089  volt:5429 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:15:52:464 ==>> 【转刹把供电电压(主控ADC)】通过,【5429mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:15:52:468 ==>> 检测【转刹把供电电压】
2025-07-31 23:15:52:473 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:52:530 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 23:15:52:801 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:13][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:13][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:13][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:13][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:13][COMM]adc read battery ts volt:14 mv
[D][05:18:13][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:13][COMM]adc read throttle brake in adc:3092  volt:5435 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,151556.576,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,3,,,40,34,,,40,39,,,40,1*4C

$GBGSV,7,2,26,60,,,40,25,,,40,59,,,39,7,,,39,1*4D

$GBGSV,7,3,26,11,,,39,41,,,39,23,,,39,16,,,38,1*74

$GBGSV,7,4,26,43,,,38,1

2025-07-31 23:15:52:860 ==>> 0,,,37,6,,,36,24,,,35,1*4B

$GBGSV,7,5,26,12,,,35,33,,,35,2,,,34,9,,,34,1*78

$GBGSV,7,6,26,44,,,34,32,,,33,5,,,32,4,,,31,1*77

$GBGSV,7,7,26,1,,,22,26,,,,1*47

$GBRMC,151556.576,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151556.576,0.000,1510.805,1510.805,48.395,2097152,2097152,2097152*55

                                      

2025-07-31 23:15:53:004 ==>> 【转刹把供电电压】通过,【5435mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:15:53:008 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:15:53:027 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:53:212 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:15:53:277 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:15:53:280 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:15:53:283 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:53:378 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:15:53:490 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:53:598 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:15:53:704 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:53:765 ==>> $GBGGA,151557.556,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,3,,,40,34,,,40,39,,,40,1*4F

$GBGSV,7,2,25,25,,,40,59,,,40,60,,,39,7,,,39,1*4E

$GBGSV,7,3,25,11,,,39,41,,,39,23,,,39,16,,,38,1*77

$GBGSV,7,4,25,43,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,12,,,35,33,,,35,2,,,34,9,,,34,1*7B

$GBGSV,7,6,25,32,,,33,44,,,32,4,,,32,5,,,31,1*72

$GBGSV,7,7,25,1,,,21,1*43

$GBRMC,151557.556,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151557.556,0.000,1505.841,1505.841,48.247,2097152,2097152,2097152*58

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:14][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:15:53:809 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:15:53:839 ==>> [W][05:18:14][COMM]>>>>>Input command = <<<<<


2025-07-31 23:15:53:914 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:53:929 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:15:54:019 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:15:54:080 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:15:54:125 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:15:54:146 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:15:54:149 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:15:54:152 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:15:54:261 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:15:54:322 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:15:54:424 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:15:54:428 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:15:54:432 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:15:54:535 ==>> 3A A3 03 01 A3 
[D][05:18:15][COMM]read battery soc:255


2025-07-31 23:15:54:625 ==>> ON_OUT3
OVER 150


2025-07-31 23:15:54:713 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:15:54:717 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:15:54:721 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:15:54:725 ==>> $GBGGA,151558.536,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,40,39,,,40,25,,,40,1*7B

$GBGSV,7,2,25,59,,,40,60,,,40,3,,,39,7,,,39,1*7A

$GBGSV,7,3,25,11,,,39,41,,,39,23,,,39,43,,,39,1*76

$GBGSV,7,4,25,16,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,12,,,35,33,,,35,2,,,34,9,,,34,1*7B

$GBGSV,7,6,25,32,,,33,44,,,32,4,,,32,5,,,32,1*71

$GBGSV,7,7,25,1,,,20,1*42

$GBRMC,151558.536,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151558.536,0.000,1507.505,1507.505,48.306,2097152,2097152,2097152*55



2025-07-31 23:15:54:820 ==>> 3A A3 04 01 A3 


2025-07-31 23:15:54:925 ==>> ON_OUT4
OVER 150


2025-07-31 23:15:55:004 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:15:55:009 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:15:55:012 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:15:55:123 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:15:55:300 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:15:55:306 ==>> 检测【左刹电压测试1】
2025-07-31 23:15:55:312 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:55:730 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:1716  volt:2262 mv
[D][05:18:16][COMM]adc read right brake adc:1715  volt:2260 mv
[D][05:18:16][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:16][COMM]adc read battery ts volt:12 mv
[D][05:18:16][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,151559.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,40,39,,,40,25,,,40,1*7B

$GBGSV,7,2,25,59,,,40,3,,,40,60,,,39,7,,,39,1*7A

$GBGSV,7,3,25,11,,,39,41,,,39,23,,,39,43,,,38,1*77

$GBGSV,7,4,25,16,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,33,,,35,12,,,34,2,,,34,9,,,34,1*7A

$GBGSV,7,6,25,32,,,33,44,,,32,4,,,32,5,,,32,1*71

$GBGSV,7,7,25,1,,,14,1*45

$GBRMC,151559.516,V,,,,,,,,

2025-07-31 23:15:55:760 ==>> 0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151559.516,0.000,1494.286,1494.286,47.929,2097152,2097152,2097152*5E



2025-07-31 23:15:55:916 ==>> 【左刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 23:15:55:923 ==>> 检测【右刹电压测试1】
2025-07-31 23:15:56:472 ==>> 【右刹电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 23:15:56:475 ==>> 检测【转把电压测试1】
2025-07-31 23:15:56:549 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 23:15:56:654 ==>> $GBGGA,151600.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,34,,,40,39,,,40,25,,,40,1*78

$GBGSV,7,2,25,3,,,40,59,,,39,60,,,39,7,,,39,1*74

$GBGSV,7,3,25,11,,,39,41,,,39,23,,,39,43,,,38,1*77

$GBGSV,7,4,25,16,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,33,,,35,12,,,34,2,,,34,9,,,34,1*7A

$GBGSV,7,6,25,32,,,33,4,,,32,5,,,32,44,,,31,1*72

$GBGSV,7,7,25,1,,,18,1*49

$GBRMC,15160

2025-07-31 23:15:56:684 ==>> 0.516,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151600.516,0.000,1495.911,1495.911,47.948,2097152,2097152,2097152*56



2025-07-31 23:15:56:940 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 23:15:56:946 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:15:56:964 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:15:57:021 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:15:57:259 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:15:57:266 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:15:57:280 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:15:57:323 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:15:57:577 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:15:57:583 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:15:57:590 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:15:57:720 ==>> $GBGGA,151601.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,34,,,40,39,,,40,25,,,40,1*78

$GBGSV,7,2,25,3,,,39,59,,,39,60,,,39,7,,,39,1*7A

$GBGSV,7,3,25,41,,,39,23,,,39,11,,,38,43,,,38,1*76

$GBGSV,7,4,25,16,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,33,,,35,12,,,34,2,,,34,9,,,34,1*7A

$GBGSV,7,6,25,32,,,33,4,,,32,5,,,32,44,,,31,1*72

$GBGSV,7,7,25,1,,,17,1*46

$GBRMC,151601.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151601.516,0.000,744.127,744.127,680.531,2097152,2097152,2097152*68

3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:15:57:916 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:15:57:923 ==>> 检测【左刹电压测试2】
2025-07-31 23:15:57:930 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:15:58:232 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:19][COMM]adc read battery ts volt:9 mv
[D][05:18:19][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:15:58:505 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 23:15:58:510 ==>> 检测【右刹电压测试2】
2025-07-31 23:15:58:715 ==>> [D][05:18:19][COMM]read battery soc:255
$GBGGA,151602.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,3,,,40,39,,,40,1*4D

$GBGSV,7,2,25,25,,,40,7,,,39,59,,,39,34,,,39,1*41

$GBGSV,7,3,25,11,,,39,23,,,39,41,,,39,16,,,38,1*77

$GBGSV,7,4,25,43,,,38,10,,,37,24,,,35,6,,,35,1*4B

$GBGSV,7,5,25,33,,,35,2,,,34,9,,,34,12,,,34,1*7A

$GBGSV,7,6,25,32,,,33,5,,,32,4,,,32,44,,,31,1*72

$GBGSV,7,7,25,1,,,11,1*40

$GBRMC,151602.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151602.516,0.000,740.042,740.042,676.799,2097152,2097152,2097152*62



2025-07-31 23:15:58:721 ==>> 【右刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 23:15:58:727 ==>> 检测【转把电压测试2】
2025-07-31 23:15:58:773 ==>> 【转把电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 23:15:58:777 ==>> 检测【晶振检测】
2025-07-31 23:15:58:780 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:15:58:911 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 23:15:59:048 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:15:59:053 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:15:59:060 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:15:59:135 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1650mV
Get AD_V4 1650mV
Get AD_V5 2771mV
Get AD_V6 1989mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:15:59:320 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:15:59:324 ==>> 检测【检测BootVer】
2025-07-31 23:15:59:329 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:15:59:743 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = ***************
[D][05:18:20][FCTY]HardwareID  = 867222087618403
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1291
[D][05:18:20][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION =

2025-07-31 23:15:59:833 ==>>  V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3720 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
$GBGGA,151603.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,34,,,40,1*4C

$GBGSV,7,2,25,25,,,40,7,,,39,60,,,39,59,,,39,1*40

$GBGSV,7,3,25,11,,,39,23,,,39,41,,,39,16,,,38,1*77

$GBGSV,7,4,25,43,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,33,,,35,2,,,34,9,,,34,12,,,34,1*7A

$GBGSV,7,6,25,32,,,33,5,,,32,4,,,32,44,,,31,1*72

$GBGSV,7,7,25,1,,,14,1*45

$GBRMC,151603.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151603.516,0.000,743.326,743.326,679.801,2097152,2097152,2097152*62



2025-07-31 23:15:59:855 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:15:59:859 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:15:59:866 ==>> 检测【检测固件版本】
2025-07-31 23:15:59:880 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:15:59:885 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:15:59:891 ==>> 检测【检测蓝牙版本】
2025-07-31 23:15:59:903 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:15:59:906 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:15:59:910 ==>> 检测【检测MoBikeId】
2025-07-31 23:15:59:921 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:15:59:926 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:15:59:930 ==>> 检测【检测蓝牙地址】
2025-07-31 23:15:59:934 ==>> 取到目标值:FE6D905731CD
2025-07-31 23:15:59:942 ==>> 【检测蓝牙地址】通过,【FE6D905731CD】符合目标值【】要求!
2025-07-31 23:15:59:946 ==>> 提取到蓝牙地址:FE6D905731CD
2025-07-31 23:15:59:949 ==>> 检测【BOARD_ID】
2025-07-31 23:15:59:961 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:15:59:967 ==>> 检测【检测充电电压】
2025-07-31 23:16:00:000 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:16:00:006 ==>> 检测【检测VBUS电压1】
2025-07-31 23:16:00:044 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:16:00:048 ==>> 检测【检测充电电流】
2025-07-31 23:16:00:064 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:16:00:070 ==>> 检测【检测IMEI】
2025-07-31 23:16:00:092 ==>> 取到目标值:867222087618403
2025-07-31 23:16:00:097 ==>> 【检测IMEI】通过,【867222087618403】符合目标值【】要求!
2025-07-31 23:16:00:101 ==>> 提取到IMEI:867222087618403
2025-07-31 23:16:00:125 ==>> 检测【检测IMSI】
2025-07-31 23:16:00:130 ==>> 取到目标值:***************
2025-07-31 23:16:00:133 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 23:16:00:147 ==>> 提取到IMSI:***************
2025-07-31 23:16:00:152 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:16:00:156 ==>> 取到目标值:***************
2025-07-31 23:16:00:183 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 23:16:00:189 ==>> 检测【打开CAN通信】
2025-07-31 23:16:00:192 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:16:00:225 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:16:00:424 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:16:00:428 ==>> 检测【检测CAN通信】
2025-07-31 23:16:00:431 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:16:00:541 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:16:00:601 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:16:00:694 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:16:00:700 ==>> 检测【关闭CAN通信】
2025-07-31 23:16:00:706 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:16:00:727 ==>> [D][05:18:21][COMM]read battery soc:255
[D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32569
$GBGGA,151604.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,34,,,40,1*4C

$GBGSV,7,2,25,25,,,40,7,,,39,60,,,39,59,,,39,1*40

$GBGSV,7,3,25,11,,,39,43,,,39,23,,,39,41,,,39,1*76

$GBGSV,7,4,25,16,,,38,10,,,37,6,,,36,24,,,35,1*48

$GBGSV,7,5,25,33,,,35,2,,,34,9,,,34,12,,,34,1*7A

$GBGSV,7,6,25,32,,,34,5,,,33,4,,,32,44,,,31,1*74

$GBGSV,7,7,25,1,,,17,1*46

$GBRMC,151604.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151604.516,0.000,748.266,748.266,684.316,2097152,2097152,2097152*6A

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:16:00:781 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:16:00:826 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:16:00:965 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:16:00:969 ==>> 检测【打印IMU STATE】
2025-07-31 23:16:00:973 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:16:01:130 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:16:01:240 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:16:01:247 ==>> 检测【六轴自检】
2025-07-31 23:16:01:253 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:16:01:417 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:16:01:886 ==>> $GBGGA,151605.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,25,,,40,7,,,39,60,,,39,1*45

$GBGSV,7,3,25,11,,,39,43,,,39,23,,,39,41,,,39,1*76

$GBGSV,7,4,25,16,,,38,10,,,37,24,,,36,6,,,36,1*4B

$GBGSV,7,5,25,33,,,35,9,,,34,12,,,34,2,,,33,1*7D

$GBGSV,7,6,25,4,,,33,32,,,33,5,,,32,44,,,31,1*73

$GBGSV,7,7,25,1,,,24,1*46

$GBRMC,151605.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151605.516,0.000,754.011,754.011,689.566,2097152,2097152,2097152*67



2025-07-31 23:16:02:710 ==>> [D][05:18:23][COMM]read battery soc:255
$GBGGA,151606.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,7,2,26,7,,,39,60,,,39,3,,,39,59,,,39,1*79

$GBGSV,7,3,26,11,,,39,43,,,39,23,,,39,41,,,39,1*75

$GBGSV,7,4,26,16,,,38,10,,,37,24,,,36,6,,,35,1*4B

$GBGSV,7,5,26,33,,,35,9,,,34,12,,,34,32,,,34,1*4A

$GBGSV,7,6,26,2,,,33,4,,,33,5,,,32,44,,,31,1*43

$GBGSV,7,7,26,1,,,28,21,,,43,1*4D

$GBRMC,151606.516,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151606.516,0.000,755.639,755.639,691.052,2097152,2097152,2097152*6F



2025-07-31 23:16:03:132 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:16:03:298 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35257 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-8,-5,4049]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 23:16:03:600 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:16:03:604 ==>> 检测【打印IMU STATE2】
2025-07-31 23:16:03:610 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:16:03:707 ==>> $GBGGA,151607.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,7,2,26,7,,,39,60,,,39,3,,,39,59,,,39,1*79

$GBGSV,7,3,26,11,,,39,43,,,39,41,,,39,16,,,38,1*72

$GBGSV,7,4,26,23,,,38,10,,,37,24,,,36,6,,,36,1*4E

$GBGSV,7,5,26,33,,,35,9,,,34,12,,,34,2,,,33,1*7E

$GBGSV,7,6,26,32,,,33,5,,,32,4,,,32,1,,,30,1*41

$GBGSV,7,7,26,44,,,30,21,,,38,1*79

$GBRMC,151607.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151607.516,0.000,754.808,754.808,690.292,2097152,2097152,2097152*61



2025-07-31 23:16:03:811 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:16:03:874 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:16:03:879 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:16:03:903 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:16:04:022 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:16:04:127 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 18
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 18
[

2025-07-31 23:16:04:179 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:16:04:183 ==>> 检测【检测VBUS电压2】
2025-07-31 23:16:04:186 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:16:04:194 ==>> D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 18
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 18
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 18
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:16:04:598 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = ***************
[D][05:18:25][FCTY]HardwareID  = 867222087618403
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11700 mv
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:25][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 7, adc = 300
[D][05:18:25][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 25 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 2

2025-07-31 23:16:04:703 ==>> 1.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3720 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 23:16:04:712 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:16:05:080 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = ***************
[D][05:18:25][FCTY]HardwareID  = 867222087618403
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 50 ma
[D][05:18:25][FCTY]VBUS        = 11700 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 167
[D][05:18:25][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][

2025-07-31 23:16:05:125 ==>> 05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3720 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:16:05:249 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:16:05:585 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087618403
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:26][FCTY]Bat         = 3924 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 147
[D][05:18:26][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18

2025-07-31 23:16:05:615 ==>> :26][FCTY]Bat1         = 3720 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:16:05:721 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:16:05:785 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:16:06:530 ==>> [D][05:18:26][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:26][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:26][COMM]Main Task receive event:65
[D][05:18:26][COMM]main task tmp_sleep_event = 80
[D][05:18:26][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:26][COMM]Main Task receive event:65 finished processing
[D][05:18:26][COMM]Main Task receive event:60
[D][05:18:26][COMM]smart_helmet_vol=255,255
[D][05:18:26][COMM]BAT CAN get state1 Fail 204
[D][05:18:26][COMM]BAT CAN get soc Fail, 204
[W][05:18:26][GNSS]stop locating
[D][05:18:26][GNSS]stop event:8
[D][05:18:26][GNSS]GPS stop. ret=0
[D][05:18:26][GNSS]all continue location stop
[D][05:18:26][COMM]report elecbike
[W][05:18:26][PROT]remove success[1629955106],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:26][COMM]Main Task receive event:60 finished processing
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3


2025-07-31 23:16:06:635 ==>> 
[D][05:18:26][CAT1]gsm read msg sub id: 24
[D][05:18:26][PROT]index:0
[D][05:18:26][PROT]is_send:1
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x3
[D][05:18:26][PROT]msg_type:0x5d03
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]Sending traceid[9999999999900005]
[D][05:18:26][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:26][PROT]index:0 1629955106
[D][05:18:26][PROT]is_send:0
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x2
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]===========================================================
[D][05:18:26][HSDK][0] flush to flash addr:[0xE42200] --- writ

2025-07-31 23:16:06:740 ==>> e len --- [256]
[D][05:18:26][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]sending traceid [9999999999900005]
[D][05:18:26][PROT]Send_TO_M2M [1629955106]
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087618403
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:26][FCTY]Bat         = 3664 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 7400 mv
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:26][SAL ]sock send credit cnt[6]
[D][05:18:26][SAL ]sock send ind credit cnt[6]
[D][05:18:26][M2M ]m2m send data len[198]
[D][05:18:26][SAL ]Cellular task submsg id[10]
[D][05:18:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05

2025-07-31 23:16:06:846 ==>> :18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 120
[D][05:18:27][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 101 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3720 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B56A5AD50DD196CEB4B19783DBA38FEC8254FA7B44BCD270BDED099043A98B34BD09F4

2025-07-31 23:16:06:951 ==>> D80F14910354FD855CB93B7767F3D051C5A2926D3AB2904A066B0E4B09A602AED62990559B7321C4C085C8DB9A83F1C7
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
                                                                                                                                           

2025-07-31 23:16:07:121 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:16:07:486 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = ***************
[D][05:18:28][FCTY]HardwareID  = 867222087618403
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:28][FCTY]Bat         = 3744 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:28][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1     

2025-07-31 23:16:07:516 ==>>     = 3720 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:16:07:685 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:16:07:689 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:16:07:694 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:16:07:818 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:16:07:908 ==>> [D][05:18:28][COMM]read battery soc:255
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 24


2025-07-31 23:16:07:987 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:16:07:993 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:16:08:015 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:16:08:119 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:16:08:276 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:16:08:284 ==>> 检测【打开WIFI(3)】
2025-07-31 23:16:08:289 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:16:08:517 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 23:16:08:550 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:16:08:556 ==>> 检测【扩展芯片hw】
2025-07-31 23:16:08:568 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:16:09:210 ==>> [D][05:18:30][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:09:484 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:16:09:574 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:16:10:025 ==>> [D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 

2025-07-31 23:16:10:130 ==>> 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][0

2025-07-31 23:16:10:235 ==>> 5:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][PROT]index:1
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:

2025-07-31 23:16:10:340 ==>> 30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]read battery soc:255
                                            

2025-07-31 23:16:10:370 ==>>                                                       

2025-07-31 23:16:10:595 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:16:10:603 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:16:10:655 ==>>                                                                                                                                                                                                                                                                

2025-07-31 23:16:10:820 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:16:10:874 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:16:10:879 ==>> 检测【扩展芯片boot】
2025-07-31 23:16:10:918 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:16:10:923 ==>> 检测【扩展芯片sw】
2025-07-31 23:16:10:946 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:16:10:951 ==>> 检测【检测音频FLASH】
2025-07-31 23:16:10:960 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:16:11:080 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:16:11:231 ==>> [D][05:18:32][COMM]43197 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:11:505 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
                                                                                                  

2025-07-31 23:16:11:535 ==>>                                                                                               

2025-07-31 23:16:11:700 ==>> [D][05:18:32][COMM]f:[drv_audio_ack_receive].wait ack timeout!![43642]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 23:16:11:895 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:16:12:240 ==>> [D][05:18:33][COMM]44210 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:12:722 ==>> [D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44668]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:16:13:246 ==>> [D][05:18:34][COMM]45220 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:13:669 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:13:909 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:16:14:265 ==>> [D][05:18:35][COMM]46231 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:15:008 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 23:16:15:267 ==>> [D][05:18:36][COMM]47244 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:15:920 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 23:16:16:019 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:16:16:026 ==>> 检测【打开喇叭声音】
2025-07-31 23:16:16:033 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:16:16:220 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 23:16:16:280 ==>>                                       [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:16:327 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:16:16:333 ==>> 检测【打开大灯控制】
2025-07-31 23:16:16:357 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:16:16:505 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:16:16:603 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:16:16:609 ==>> 检测【关闭仪表供电3】
2025-07-31 23:16:16:641 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:16:16:805 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 23:16:16:883 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:16:16:888 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:16:16:894 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:16:17:079 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:16:17:174 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:16:17:180 ==>> 检测【读大灯电压】
2025-07-31 23:16:17:184 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:16:17:291 ==>> [D][05:18:38][COMM]49265 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:17:396 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[33386]


2025-07-31 23:16:17:445 ==>> 【读大灯电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:16:17:450 ==>> 检测【关闭大灯控制2】
2025-07-31 23:16:17:458 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:16:17:609 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:16:17:729 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:16:17:734 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:16:17:748 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:16:17:926 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]
[D][05:18:38][COMM]read battery soc:255


2025-07-31 23:16:18:004 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:16:18:011 ==>> 检测【打开WIFI(4)】
2025-07-31 23:16:18:032 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:16:18:184 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:16:18:289 ==>> [D][05:18:39][COMM]50276 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:18:338 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:16:18:343 ==>> 检测【EC800M模组版本】
2025-07-31 23:16:18:352 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:19:325 ==>> [D][05:18:40][COMM]51287 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:16:19:385 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:19:925 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:16:20:249 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:20:309 ==>> [D][05:18:41][COMM]imu error,enter wait


2025-07-31 23:16:20:414 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:21:166 ==>> [D][05:18:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:21:451 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:21:676 ==>> [D][05:18:42][CAT1]exec over: func id: 15, ret: -93
[D][05:18:42][CAT1]sub id: 15, ret: -93

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:42][SAL ]socket send fail. id[4]
[D][05:18:42][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][M2M ]m2m select fd[4]
[D][05:18:42][M2M ]socket[4] Link is disconnected
[D][05:18:42][M2M ]tcpclient close[4]
[D][05:18:42][SAL ]socket[4] has closed
[D][05:18:42][PROT]protocol read data ok
[E][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:42][PROT]M2M Send Fail [1629955122]
[D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 23:16:22:129 ==>> [D][05:18:42][COMM]read battery soc:255
[D][05:18:42][COMM]f:[drv_audio_ack_receive].wait ack timeout!![53927]
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1032].open hexlog save
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:42][COMM]read file, len:15228, num:4
[D][05:18:42][COMM]--->crc16:0x419c
[D][05:18:42][COMM]read file success
[W][05:18:42][COMM][Audio].l:[936].close hexlog save
[D][05:18:42][COMM]accel parse set 1
[D][05:18:42][COMM][Audio]mon:9,05:18:42
[D][05:18:42][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:16:22:279 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:22:474 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:23:075 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55025]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:16:23:495 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:23:941 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:16:24:106 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56054]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:16:24:346 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:24:528 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:24:588 ==>> [D][05:18:45][CAT1]SEND RAW data timeout
[D][05:18:45][CAT1]exec over: func id: 12, ret: -52
[W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:45][CAT1]gsm read msg sub id: 12
[D][05:18:45][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:16:25:117 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57082]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:16:25:557 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:25:945 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:16:26:159 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:26:599 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:26:607 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:27:600 ==>> [D][05:18:48][CAT1]SEND RAW data timeout
[D][05:18:48][CAT1]exec over: func id: 12, ret: -52
[W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:48][CAT1]gsm read msg sub id: 10
[D][05:18:48][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:16:27:630 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:27:969 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:16:28:666 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:28:676 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:29:618 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:29:693 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:29:969 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:16:30:733 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:31:164 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:31:666 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:31:770 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:31:956 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 23:16:32:805 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:33:725 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:33:846 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:33:986 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:16:34:883 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:35:595 ==>> [D][05:18:56][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:16:35:762 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:35:911 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:35:986 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 23:16:36:166 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:36:934 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:37:485 ==>> [D][05:18:58][COMM]f:[drv_audio_ack_receive].wait ack timeout!![69426]
[D][05:18:58][COMM]accel parse set 0
[D][05:18:58][COMM][Audio].l:[1032].open hexlog save
[D][05:18:58][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 23:16:37:831 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:37:966 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:37:981 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 23:16:38:670 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:39:006 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:39:867 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:40:002 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 23:16:40:032 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:41:070 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:41:163 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:41:919 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:42:009 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:16:42:114 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:43:156 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:43:609 ==>> [D][05:19:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:16:43:669 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:44:007 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:04][COMM]read battery soc:255


2025-07-31 23:16:44:190 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:45:219 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:46:030 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:06][COMM]read battery soc:255


2025-07-31 23:16:46:165 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:46:255 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:47:290 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:48:076 ==>> [D][05:19:08][COMM]read battery soc:255
[D][05:19:09][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:48:318 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:16:48:664 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:49:352 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 23:16:49:363 ==>> #################### 【测试结束】 ####################
2025-07-31 23:16:49:448 ==>> 关闭5V供电
2025-07-31 23:16:49:457 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:16:49:524 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:16:50:029 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 23:16:50:104 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:16:50:455 ==>> 关闭5V供电成功
2025-07-31 23:16:50:464 ==>> 关闭33V供电
2025-07-31 23:16:50:478 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:16:50:515 ==>> 5A A5 02 5A A5 


2025-07-31 23:16:50:620 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:16:50:815 ==>> [D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 23:16:51:160 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:16:51:469 ==>> 关闭33V供电成功
2025-07-31 23:16:51:478 ==>> 关闭3.7V供电
2025-07-31 23:16:51:491 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:16:51:516 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:16:51:850 ==>>  

2025-07-31 23:16:52:484 ==>> 关闭3.7V供电成功
