2025-07-31 18:03:08:791 ==>> MES查站成功:
查站序号:P510001005313251验证通过
2025-07-31 18:03:08:798 ==>> 扫码结果:P510001005313251
2025-07-31 18:03:08:799 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:03:08:801 ==>> 测试参数版本:2024.10.11
2025-07-31 18:03:08:802 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:03:08:804 ==>> 检测【打开透传】
2025-07-31 18:03:08:805 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:03:08:855 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:03:09:145 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:03:09:151 ==>> 检测【检测接地电压】
2025-07-31 18:03:09:154 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:03:09:252 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:03:09:421 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:03:09:424 ==>> 检测【打开小电池】
2025-07-31 18:03:09:427 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:03:09:556 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:03:09:695 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:03:09:697 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:03:09:700 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:03:09:751 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:03:09:968 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:03:09:970 ==>> 检测【等待设备启动】
2025-07-31 18:03:09:974 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:03:10:275 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:03:10:442 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:03:11:009 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:03:11:191 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM


2025-07-31 18:03:11:528 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:03:12:016 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:03:12:074 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:03:12:077 ==>> 检测【产品通信】
2025-07-31 18:03:12:079 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:03:12:244 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:03:12:352 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:03:12:354 ==>> 检测【初始化完成检测】
2025-07-31 18:03:12:356 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:03:12:579 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:03:12:628 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:03:12:630 ==>> 检测【关闭大灯控制1】
2025-07-31 18:03:12:631 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:03:12:669 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:03:12:835 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:03:12:910 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:03:12:913 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:03:12:914 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:03:13:264 ==>> [D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:03:13:449 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:03:13:452 ==>> 检测【关闭仪表供电】
2025-07-31 18:03:13:454 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:03:13:649 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:03:13:728 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:03:13:731 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:03:13:734 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:03:13:922 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:03:14:006 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:03:14:008 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:03:14:009 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:03:14:104 ==>> [D][05:17:52][COMM]3649 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:14:209 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:03:14:289 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:03:14:292 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:03:14:293 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:14:438 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:03:14:561 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:03:14:565 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:03:14:568 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:03:14:651 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:03:14:756 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read b

2025-07-31 18:03:14:786 ==>> attery soc:255


2025-07-31 18:03:14:834 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:03:14:837 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:03:14:839 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:03:14:953 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:03:15:107 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:03:15:110 ==>> 该项需要延时执行
2025-07-31 18:03:15:119 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:15:638 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5003. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5004. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5004. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5005. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5006. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5006. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5007
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802

2025-07-31 18:03:15:668 ==>> E->0x01B987FE 5007


2025-07-31 18:03:16:125 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:16:337 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:03:16:872 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio f

2025-07-31 18:03:16:977 ==>> ile status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]min_in

2025-07-31 18:03:17:082 ==>> dex:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work 

2025-07-31 18:03:17:157 ==>> state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 18:03:18:161 ==>> [D][05:17:56][CAT1]power_urc_cb ret[5]
[D][05:17:56][COMM]7695 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:18:791 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:03:19:115 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:03:19:118 ==>> 检测【33V输入电压ADC】
2025-07-31 18:03:19:122 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:19:161 ==>> [D][05:17:57][COMM]8708 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:19:388 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3187  volt:5602 mv
[D][05:17:57][COMM]adc read out 24v adc:1333  volt:33715 mv
[D][05:17:57][COMM]adc read left brake adc:23  volt:30 mv
[D][05:17:57][COMM]adc read right brake adc:23  volt:30 mv
[D][05:17:57][COMM]adc read throttle adc:28  volt:36 mv
[D][05:17:57][COMM]adc read battery ts volt:28 mv
[D][05:17:57][COMM]adc read in 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read throttle brake in adc:22  volt:38 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:03:19:652 ==>> 【33V输入电压ADC】通过,【33159mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:03:19:655 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:03:19:656 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:03:19:769 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2781mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:03:19:923 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:19:936 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:03:19:942 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:19:944 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:03:19:945 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 18:03:19:960 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:03:19:962 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:03:19:979 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:03:19:982 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:03:20:003 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:03:20:005 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:03:20:059 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1644mV
Get AD_V4 0mV
Get AD_V5 2776mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:03:20:165 ==>> [D][05:17:58][COMM]9720 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:20:277 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:20:280 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:03:20:297 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:20:318 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:03:20:321 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 18:03:20:323 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:03:20:326 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:03:20:337 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:03:20:339 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:03:20:359 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:03:20:379 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:03:20:500 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1645mV
Get AD_V4 0mV
Get AD_V5 2774mV
Get AD_V6 2024mV
Get AD_V7 1094mV
OVER 150
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10017


2025-07-31 18:03:20:646 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:20:648 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:03:20:664 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:20:667 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:03:20:684 ==>> 原始值:【2774】, 乘以分压基数【2】还原值:【5548】
2025-07-31 18:03:20:686 ==>> 【TP68_VCC5V5(ADV5)】通过,【5548mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:03:20:689 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:03:20:703 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:03:20:705 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:03:20:726 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:03:20:730 ==>> 检测【打开WIFI(1)】
2025-07-31 18:03:20:745 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:03:20:789 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:03:20:939 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:03:21:004 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:03:21:007 ==>> 检测【清空消息队列(1)】
2025-07-31 18:03:21:032 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:03:21:242 ==>> [D][05:17:59][COMM]10732 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:03:21:276 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:03:21:278 ==>> 检测【打开GPS(1)】
2025-07-31 18:03:21:280 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:03:21:453 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:17:59][COMM]Open GPS Module...
[D][05:17:59][COMM]LOC_MODEL_CONT
[D][05:17:59][GNSS]start event:8
[W][05:17:59][GNSS]start cont locating


2025-07-31 18:03:21:558 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:03:21:560 ==>> 检测【打开GSM联网】
2025-07-31 18:03:21:563 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:03:21:742 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:03:21:841 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:03:21:843 ==>> 检测【打开仪表供电1】
2025-07-31 18:03:21:846 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:03:22:293 ==>> [D][05:18:00][CAT1]power_urc_cb ret[76]
[D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][COMM]Main Task rec

2025-07-31 18:03:22:399 ==>> eive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:00][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[13] [17] 
+MT ER

2025-07-31 18:03:22:443 ==>> ROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:18:00][CAT1]<<< 
+MT ERROR:700

[D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:03:22:641 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:03:22:645 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:03:22:657 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:03:22:851 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:03:22:923 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:03:22:926 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:03:22:929 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:03:23:137 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33525]


2025-07-31 18:03:23:210 ==>> 【读取主控ADC采集的仪表电压】通过,【33525mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:03:23:214 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:03:23:216 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:03:23:441 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:03:23:532 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:03:23:535 ==>> 检测【AD_V20电压】
2025-07-31 18:03:23:537 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:03:23:640 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:03:23:730 ==>> 本次取值间隔时间:85ms
2025-07-31 18:03:23:775 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 18:03:23:835 ==>> 本次取值间隔时间:93ms
2025-07-31 18:03:23:854 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:03:23:958 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:03:24:081 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

1A A1 10 00 00 
Get AD_V20 1640mV
OVER 150


2025-07-31 18:03:24:111 ==>> 本次取值间隔时间:144ms
2025-07-31 18:03:24:130 ==>> 【AD_V20电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:24:133 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:03:24:136 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:03:24:247 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:03:24:437 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:03:24:440 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:03:24:445 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:03:24:715 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087567865

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539006

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:03][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:03][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle

2025-07-31 18:03:24:745 ==>> :0x0, power:1


2025-07-31 18:03:24:820 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 18:03:24:974 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:03:24:978 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:03:24:981 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:03:25:060 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:03:25:245 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:03:25:248 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:03:25:252 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:03:25:455 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:03:25:518 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:03:25:521 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:03:25:525 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:03:25:743 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:03:25:791 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:03:25:794 ==>> 检测【AD_V21电压】
2025-07-31 18:03:25:796 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:03:25:848 ==>> 1A A1 20 00 00 
Get AD_V21 1047mV
OVER 150


2025-07-31 18:03:26:281 ==>> 本次取值间隔时间:484ms
2025-07-31 18:03:26:299 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:03:26:432 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:04][COMM]15743 imu init OK
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][

2025-07-31 18:03:26:477 ==>> SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

1A A1 20 00 00 
Get AD_V21 1631mV
OVER 150


2025-07-31 18:03:26:582 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:03:26:612 ==>>                                  [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:03:26:777 ==>> 本次取值间隔时间:469ms
2025-07-31 18:03:26:796 ==>> 【AD_V21电压】通过,【1631mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:26:799 ==>> 检测【关闭仪表供电2】
2025-07-31 18:03:26:802 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:03:26:975 ==>> [D][05:18:05][CAT1]opened : 0, 0
[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:05][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:05][COMM]read battery soc:255
[D][05:18:05][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:05][M2M ]g_m2m_is_idle become true
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:03:27:069 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:03:27:072 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:03:27:075 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:03:27:428 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:05][GNSS]location recv gms init done evt
[D][05:18:05][GNSS]GPS start. ret=0
[D][05:18:05][CAT1]gsm read msg sub id: 23
[D][05:18:05][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:05][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:03:27:598 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:03:27:601 ==>> 检测【打开AccKey2供电】
2025-07-31 18:03:27:603 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:03:27:718 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:03:27:873 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:03:27:876 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:03:27:879 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:28:178 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3185  volt:5598 mv
[D][05:18:06][COMM]adc read out 24v adc:1334  volt:33740 mv
[D][05:18:06][COMM]adc read left brake adc:28  volt:36 mv
[D][05:18:06][COMM]adc read right brake adc:29  volt:38 mv
[D][05:18:06][COMM]adc read throttle adc:28  volt:36 mv
[D][05:18:06][COMM]adc read battery ts volt:30 mv
[D][05:18:06][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:06][COMM]adc read throttle brake in adc:24  volt:42 mv
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,36,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:03:28:407 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33740mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:03:28:414 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:03:28:435 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:03:28:778 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:03:28:974 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,59,,,40,24,,,40,26,,,40,39,,,38,1*7E

$GBGSV,3,2,10,38,,,36,33,,,34,16,,,32,60,,,42,1*79

$GBGSV,3,3,10,3,,,42,13,,,41,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1539.886,1539.886,49.268,2097152,2097152,2097152*4E

[D][05:18:07][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:07][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 23, ret: 6
[D][05:18:07][CAT1]sub id: 23, ret: 6



2025-07-31 18:03:29:171 ==>> [D][05:18:07][GNSS]recv submsg id[1]
[D][05:18:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:03:29:426 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:03:29:669 ==>> [D][05:18:08][COMM][arm_hub_gpio_read]: Failed -2
[D][05:18:08][COMM]arm_hub adc read bat_id adc:-1  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2453  volt:3952 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:03:29:709 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:03:29:714 ==>> 该项需要延时执行
2025-07-31 18:03:29:927 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,13,,,41,24,,,41,26,,,40,60,,,39,1*7F

$GBGSV,4,2,15,59,,,39,39,,,39,21,,,39,42,,,39,1*77

$GBGSV,4,3,15,3,,,38,38,,,38,16,,,36,33,,,35,1*49

$GBGSV,4,4,15,5,,,34,2,,,31,36,,,36,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1566.523,1566.523,50.105,2097152,2097152,2097152*4E



2025-07-31 18:03:30:933 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,42,13,,,41,26,,,41,38,,,40,1*7D

$GBGSV,4,2,16,60,,,39,59,,,39,39,,,39,21,,,39,1*74

$GBGSV,4,3,16,42,,,39,3,,,39,16,,,37,1,,,37,1*75

$GBGSV,4,4,16,33,,,36,5,,,33,4,,,32,2,,,32,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1567.648,1567.648,50.155,2097152,2097152,2097152*4B



2025-07-31 18:03:31:949 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,13,,,41,26,,,41,38,,,41,1*72

$GBGSV,5,2,19,39,,,40,3,,,40,8,,,40,60,,,39,1*70

$GBGSV,5,3,19,59,,,39,21,,,39,42,,,39,16,,,38,1*77

$GBGSV,5,4,19,1,,,37,33,,,36,14,,,35,5,,,33,1*79

$GBGSV,5,5,19,4,,,32,2,,,32,9,,,29,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1555.813,1555.813,49.805,2097152,2097152,2097152*4F



2025-07-31 18:03:32:723 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:03:32:727 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:03:32:731 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:33:075 ==>> [D][05:18:11][COMM]read battery soc:255
$GBGGA,100336.755,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,13,,,41,26,,,41,38,,,41,1*73

$GBGSV,5,2,19,8,,,40,39,,,39,3,,,39,60,,,39,1*70

$GBGSV,5,3,19,59,,,39,21,,,39,42,,,39,16,,,38,1*77

$GBGSV,5,4,19,1,,,37,33,,,36,14,,,35,5,,,33,1*79

$GBGSV,5,5,19,9,,,33,4,,,32,2,,,32,1*41

$GBRMC,100336.755,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100336.755,0.000,1562.339,1562.339,49.995,2097152,2097152,2097152*59

[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3184  volt:5596 mv
[D][05:18:11][COMM]adc read out 24v adc:24  volt:607 mv
[D][05:18:11][COMM]adc read left brake adc:24  volt:31 mv
[D][05:18:11][COMM]adc read right brake adc:20  volt:26 mv
[D][05:18:11][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:11][COMM]adc read battery ts volt:28 mv
[D][05:18:11][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:11][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2418  volt:3896 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:144

2025-07-31 18:03:33:105 ==>> 5  volt:33502 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:03:33:258 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【607mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:03:33:264 ==>> 检测【打开AccKey1供电】
2025-07-31 18:03:33:269 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:03:33:436 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:03:33:529 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:03:33:532 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:03:33:536 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:03:33:648 ==>> 1A A1 00 40 00 
Get AD_V14 2678mV
OVER 150


2025-07-31 18:03:33:753 ==>> $GBGGA,100337.555,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,13,,,41,26,,,41,38,,,41,1*72

$GBGSV,5,2,19,8,,,39,39,,,39,3,,,39,60,,,39,1*7E

$GBGSV,5,3,19,59,,,39,21,,,39,42,,,39,16,,,38,1*77

$GBGSV,5,4,19,1,,,37,33,,,36,14,,,35,9,,,34,1*72

$GBGSV,5,5,19,5,,,33,4,,,32,2,,,32,1*4D

$GBRMC,100337.555,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100337.555,0.000,1560.148,1560.148,49.916,2097152,2097152,2097152*51



2025-07-31 18:03:33:783 ==>> 原始值:【2678】, 乘以分压基数【2】还原值:【5356】
2025-07-31 18:03:33:802 ==>> 【读取AccKey1电压(ADV14)前】通过,【5356mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:03:33:809 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:03:33:829 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:34:070 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3177  volt:5584 mv
[D][05:18:12][COMM]adc read out 24v adc:14  volt:354 mv
[D][05:18:12][COMM]adc read left brake adc:23  volt:30 mv
[D][05:18:12][COMM]adc read right brake adc:22  volt:29 mv
[D][05:18:12][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:12][COMM]adc read battery ts volt:21 mv
[D][05:18:12][COMM]adc read in 24v adc:1311  volt:33159 mv
[D][05:18:12][COMM]adc read throttle brake in adc:16  volt:28 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2453  volt:3952 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:03:34:334 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5584mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:03:34:338 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:03:34:341 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:03:34:544 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:03:34:609 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:03:34:612 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:03:34:615 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:03:34:725 ==>> $GBGGA,100338.535,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,13,,,41,26,,,41,38,,,41,1*72

$GBGSV,5,2,19,3,,,40,8,,,39,39,,,39,60,,,39,1*70

$GBGSV,5,3,19,59,,,39,21,,,39,42,,,39,16,,,38,1*77

$GBGSV,5,4,19,1,,,37,33,,,36,9,,,36,14,,,35,1*70

$GBGSV,5,5,19,5,,,33,4,,,32,2,,,32,1*4D

$GBRMC,100338.535,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100338.535,0.000,1566.690,1566.690,50.122,2097152,2097152,2097152*5F



2025-07-31 18:03:34:756 ==>> 1A A1 00 40 00 
Get AD_V14 2674mV
OVER 150


2025-07-31 18:03:34:847 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 18:03:34:862 ==>> 原始值:【2674】, 乘以分压基数【2】还原值:【5348】
2025-07-31 18:03:34:881 ==>> 【读取AccKey1电压(ADV14)后】通过,【5348mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:03:34:884 ==>> 检测【打开WIFI(2)】
2025-07-31 18:03:34:887 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:03:35:073 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:03:35:152 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:03:35:156 ==>> 检测【转刹把供电】
2025-07-31 18:03:35:163 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:03:35:313 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:03:35:421 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:03:35:425 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:03:35:430 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:03:35:528 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:03:35:724 ==>> 00 00 00 80 00 
head err!
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,100339.515,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,13,,,41,26,,,41,38,,,41,1*78

$GBGSV,5,2,20,3,,,40,39,,,40,8,,,39,60,,,39,1*74

$GBGSV,5,3,20,59,,,39,21,,,39,42,,,39,16,,,38,1*7D

$GBGSV,5,4,20,1,,,37,33,,,36,9,,,36,14,,,35,1*7A

$GBGSV,5,5,20,5,,,33,4,,,32,2,,,32,45,,,31,1*44

$GBRMC,100339.515,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100339.515,0.000,1554.706,1554.706,49.754,2097152,2097152,2097152*53



2025-07-31 18:03:35:830 ==>> +WIFISCAN:4,0,

2025-07-31 18:03:35:835 ==>> System.IndexOutOfRangeException: 索引超出了数组界限。
   在 AppSe5x.FormMain.<ProcessMessagesAsync>d__105.MoveNext()
2025-07-31 18:03:35:861 ==>> CC057790A7C0,-77
+WIFISCAN:4,1,CC057790A5C1,-78
+WIFISCAN:4,2,CC057790A7C1,-78
+WIFISCAN:4,3,F86FB0660A82,-87

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 18:03:36:221 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 18:03:36:431 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:03:36:538 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:03:36:721 ==>> 1A A1 00 80 00 
Get AD_V15 2414mV
OVER 150
[D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,100340.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,13,,,41,26,,,41,38,,,41,1*78

$GBGSV,5,2,20,3,,,40,39,,,40,8,,,40,60,,,39,1*7A

$GBGSV,5,3,20,59,,,39,21,,,39,42,,,39,16,,,39,1*7C

$GBGSV,5,4,20,1,,,37,9,,,37,33,,,36,14,,,36,1*78

$GBGSV,5,5,20,5,,,32,4,,,32,2,,,32,45,,,31,1*45

$GBRMC,100340.515,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100340.515,0.000,1560.928,1560.928,49.956,2097152,2097152,2097152*51



2025-07-31 18:03:36:856 ==>> 原始值:【2414】, 乘以分压基数【2】还原值:【4828】
2025-07-31 18:03:36:871 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 18:03:36:880 ==>> 【读取AD_V15电压(前)】通过,【4828mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:03:36:884 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:03:36:888 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:03:36:991 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:03:37:022 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:03:37:052 ==>> 1A A1 01 00 00 
Get AD_V16 2441mV
OVER 150


2025-07-31 18:03:37:142 ==>> 原始值:【2441】, 乘以分压基数【2】还原值:【4882】
2025-07-31 18:03:37:168 ==>> 【读取AD_V16电压(前)】通过,【4882mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:03:37:172 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:03:37:177 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:37:465 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3183  volt:5595 mv
[D][05:18:15][COMM]adc read out 24v adc:15  volt:379 mv
[D][05:18:15][COMM]adc read left brake adc:21  volt:27 mv
[D][05:18:15][COMM]adc read right brake adc:20  volt:26 mv
[D][05:18:15][COMM]adc read throttle adc:22  volt:29 mv
[D][05:18:15][COMM]adc read battery ts volt:27 mv
[D][05:18:15][COMM]adc read in 24v adc:1313  volt:33209 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3131  volt:5503 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2418  volt:3896 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:03:37:700 ==>> 【转刹把供电电压(主控ADC)】通过,【5503mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:03:37:704 ==>> 检测【转刹把供电电压】
2025-07-31 18:03:37:710 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:37:728 ==>> $GBGGA,100341.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,41,26,,,41,38,,,41,1*7B

$GBGSV,6,2,21,39,,,40,42,,,40,3,,,39,8,,,39,1*76

$GBGSV,6,3,21,60,,,39,59,,,39,21,,,39,16,,,39,1*7E

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,14,,,36,1*7A

$GBGSV,6,5,21,6,,,35,5,,,33,2,,,33,4,,,32,1*74

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100341.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100341.515,0.000,1559.638,1559.638,49.909,2097152,2097152,2097152*5A



2025-07-31 18:03:37:965 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3183  volt:5595 mv
[D][05:18:16][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:16][COMM]adc read left brake adc:25  volt:32 mv
[D][05:18:16][COMM]adc read right brake adc:30  volt:39 mv
[D][05:18:16][COMM]adc read throttle adc:32  volt:42 mv
[D][05:18:16][COMM]adc read battery ts volt:37 mv
[D][05:18:16][COMM]adc read in 24v adc:1314  volt:33234 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3126  volt:5494 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:19  volt:15 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2487  volt:4007 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:03:38:246 ==>> 【转刹把供电电压】通过,【5494mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:03:38:252 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:03:38:261 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:38:414 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:03:38:537 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:03:38:542 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:03:38:544 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:38:642 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:03:38:749 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:38:779 ==>> $GBGGA,100342.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,13,,,41,26,,,41,38,,,41,1*7A

$GBGSV,6,2,21,39,,,40,42,,,40,3,,,40,8,,,40,1*76

$GBGSV,6,3,21,60,,,39,59,,,39,21,,,39,16,,,39,1*7E

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,14,,,36,1*7A

$GBGSV,6,5,21,6,,,36,5,,,34,2,,,33,4,,,32,1*70

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100342.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100342.515,0.000,1565.554,1565.554,50.092,2097152,2097152,2097152*5A

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:03:38:854 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:03:38:961 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:38:966 ==>> [D][05:18:17][COMM]read battery soc:255
[W][05:18:17][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 18:03:39:065 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:03:39:157 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 18:03:39:192 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:03:39:195 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:03:39:200 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:03:39:293 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:03:39:353 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:03:39:416 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:03:39:420 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:03:39:423 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:03:39:549 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:03:39:655 ==>> $GBGGA,100343.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14


2025-07-31 18:03:39:687 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:03:39:695 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:03:39:701 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:03:39:714 ==>> 
$GBGSV,6,1,21,24,,,42,13,,,41,26,,,41,38,,,41,1*7A

$GBGSV,6,2,21,39,,,40,42,,,40,3,,,40,8,,,40,1*76

$GBGSV,6,3,21,59,,,40,60,,,39,21,,,39,16,,,39,1*70

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,6,,,36,1*49

$GBGSV,6,5,21,14,,,35,5,,,34,2,,,33,4,,,32,1*40

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100343.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100343.515,0.000,1565.557,1565.557,50.095,2097152,2097152,2097152*5C



2025-07-31 18:03:39:744 ==>> 3A A3 04 01 A3 


2025-07-31 18:03:39:849 ==>> ON_OUT4
OVER 150


2025-07-31 18:03:39:958 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:03:39:962 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:03:39:965 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:03:40:060 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:03:40:229 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:03:40:233 ==>> 检测【左刹电压测试1】
2025-07-31 18:03:40:240 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:40:567 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3187  volt:5602 mv
[D][05:18:18][COMM]adc read out 24v adc:15  volt:379 mv
[D][05:18:18][COMM]adc read left brake adc:1752  volt:2309 mv
[D][05:18:18][COMM]adc read right brake adc:1751  volt:2308 mv
[D][05:18:18][COMM]adc read throttle adc:1742  volt:2296 mv
[D][05:18:18][COMM]adc read battery ts volt:28 mv
[D][05:18:18][COMM]adc read in 24v adc:1320  volt:33386 mv
[D][05:18:18][COMM]adc read throttle brake in adc:24  volt:42 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:20  volt:16 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2467  volt:3975 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:03:40:672 ==>> $GBGGA,100344.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,13,,,41,26,,,41,38,,,41,1*7A

$GBGSV,6,2,21,3,,,40,60,,,40,39,,,39,42,,,39,1*48

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,39,16,,,39,1*40

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,6,,,36,1*49


2025-07-31 18:03:40:717 ==>> 

$GBGSV,6,5,21,14,,,35,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100344.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100344.515,0.000,1561.601,1561.601,49.961,2097152,2097152,2097152*51



2025-07-31 18:03:40:766 ==>> 【左刹电压测试1】通过,【2309】符合目标值【2250】至【2500】要求!
2025-07-31 18:03:40:771 ==>> 检测【右刹电压测试1】
2025-07-31 18:03:40:797 ==>> 【右刹电压测试1】通过,【2308】符合目标值【2250】至【2500】要求!
2025-07-31 18:03:40:801 ==>> 检测【转把电压测试1】
2025-07-31 18:03:40:815 ==>> 【转把电压测试1】通过,【2296】符合目标值【2250】至【2500】要求!
2025-07-31 18:03:40:823 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:03:40:842 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:03:40:869 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 18:03:40:959 ==>> 3A A3 03 00 A3 


2025-07-31 18:03:41:049 ==>> OFF_OUT3
OVER 150


2025-07-31 18:03:41:097 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:03:41:103 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:03:41:110 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:03:41:154 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:03:41:372 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:03:41:376 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:03:41:383 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:03:41:445 ==>> 3A A3 05 00 A3 


2025-07-31 18:03:41:550 ==>> OFF_OUT5
OVER 150


2025-07-31 18:03:41:643 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:03:41:647 ==>> 检测【左刹电压测试2】
2025-07-31 18:03:41:654 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:03:41:674 ==>> $GBGGA,100345.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,

2025-07-31 18:03:41:715 ==>> 6,1,21,24,,,42,38,,,41,13,,,40,26,,,40,1*7A

$GBGSV,6,2,21,3,,,40,60,,,39,39,,,39,42,,,39,1*46

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,39,16,,,38,1*41

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,6,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,33,5,,,33,4,,,33,1*46

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100345.515,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100345.515,0.000,1553.696,1553.696,49.700,2097152,2097152,2097152*59



2025-07-31 18:03:41:970 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3182  volt:5593 mv
[D][05:18:20][COMM]adc read out 24v adc:20  volt:505 mv
[D][05:18:20][COMM]adc read left brake adc:20  volt:26 mv
[D][05:18:20][COMM]adc read right brake adc:24  volt:31 mv
[D][05:18:20][COMM]adc read throttle adc:26  volt:34 mv
[D][05:18:20][COMM]adc read battery ts volt:23 mv
[D][05:18:20][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:20][COMM]adc read throttle brake in adc:24  volt:42 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2424  volt:3905 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:03:42:172 ==>> 【左刹电压测试2】通过,【26】符合目标值【0】至【50】要求!
2025-07-31 18:03:42:178 ==>> 检测【右刹电压测试2】
2025-07-31 18:03:42:199 ==>> 【右刹电压测试2】通过,【31】符合目标值【0】至【50】要求!
2025-07-31 18:03:42:203 ==>> 检测【转把电压测试2】
2025-07-31 18:03:42:218 ==>> 【转把电压测试2】通过,【34】符合目标值【0】至【50】要求!
2025-07-31 18:03:42:224 ==>> 检测【晶振检测】
2025-07-31 18:03:42:229 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:03:42:417 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 18:03:42:500 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:03:42:504 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:03:42:510 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:03:42:567 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1651mV
Get AD_V4 1647mV
Get AD_V5 2785mV
Get AD_V6 1993mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:03:42:672 ==>> $GBGGA,100346.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,40,13,,,40,26,,,40,1*7B

$GBGSV,6,2,21,3,,,40,60,,,40,39,,,39,42,,,39,1*48

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,39,16,,,38,1*41

$GBGSV,6,4,21,1,,,37,9,,,37,33,,,36,6,,,3

2025-07-31 18:03:42:717 ==>> 6,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100346.515,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100346.515,0.000,1555.666,1555.666,49.759,2097152,2097152,2097152*56



2025-07-31 18:03:42:804 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:03:42:809 ==>> 检测【检测BootVer】
2025-07-31 18:03:42:815 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:03:43:122 ==>> [D][05:18:21][COMM]read battery soc:255
[W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539006
[D][05:18:21][FCTY]HardwareID  = 867222087567865
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = D73B1AC7DE82
[D][05:18:21][FCTY]Bat         = 4064 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 33, adc = 1312
[D][05:18:21][FCTY]Acckey1 vol = 5593 mv, Acckey2 vol = 531 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1

2025-07-31 18:03:43:152 ==>>          = 3833 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:03:43:341 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:03:43:345 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:03:43:351 ==>> 检测【检测固件版本】
2025-07-31 18:03:43:379 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:03:43:385 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:03:43:391 ==>> 检测【检测蓝牙版本】
2025-07-31 18:03:43:402 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:03:43:406 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:03:43:409 ==>> 检测【检测MoBikeId】
2025-07-31 18:03:43:433 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:03:43:437 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:03:43:442 ==>> 检测【检测蓝牙地址】
2025-07-31 18:03:43:450 ==>> 取到目标值:D73B1AC7DE82
2025-07-31 18:03:43:460 ==>> 【检测蓝牙地址】通过,【D73B1AC7DE82】符合目标值【】要求!
2025-07-31 18:03:43:463 ==>> 提取到蓝牙地址:D73B1AC7DE82
2025-07-31 18:03:43:467 ==>> 检测【BOARD_ID】
2025-07-31 18:03:43:484 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:03:43:488 ==>> 检测【检测充电电压】
2025-07-31 18:03:43:509 ==>> 【检测充电电压】通过,【4064mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:03:43:513 ==>> 检测【检测VBUS电压1】
2025-07-31 18:03:43:538 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:03:43:542 ==>> 检测【检测充电电流】
2025-07-31 18:03:43:565 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:03:43:570 ==>> 检测【检测IMEI】
2025-07-31 18:03:43:575 ==>> 取到目标值:867222087567865
2025-07-31 18:03:43:594 ==>> 【检测IMEI】通过,【867222087567865】符合目标值【】要求!
2025-07-31 18:03:43:598 ==>> 提取到IMEI:867222087567865
2025-07-31 18:03:43:601 ==>> 检测【检测IMSI】
2025-07-31 18:03:43:606 ==>> 取到目标值:460130071539006
2025-07-31 18:03:43:620 ==>> 【检测IMSI】通过,【460130071539006】符合目标值【】要求!
2025-07-31 18:03:43:624 ==>> 提取到IMSI:460130071539006
2025-07-31 18:03:43:633 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:03:43:638 ==>> 取到目标值:460130071539006
2025-07-31 18:03:43:642 ==>> 【校验网络运营商(移动)】通过,【460130071539006】符合目标值【】要求!
2025-07-31 18:03:43:649 ==>> 检测【打开CAN通信】
2025-07-31 18:03:43:666 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:03:43:722 ==>> $GBGGA,100347.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,40,13,,,40,26,,,40,1*7B

$GBGSV,6,2,21,3,,,40,60,,,40,39,,,39,42,,,39,1*48

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,38,16,,,38,1*40

$GBGSV,6,4,21,1,,,37,9,,,36,6,,,36,33,,,35,1*4B

$GBGSV,6,5,21,14,,,35,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100347.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100347.515,0.000,1549.745,1549.745,49.571,2097152,2097152,2097152*5F



2025-07-31 18:03:43:752 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:03:43:922 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:03:43:928 ==>> 检测【检测CAN通信】
2025-07-31 18:03:43:934 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:03:44:075 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:03:44:150 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33686
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:03:44:195 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:03:44:203 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:03:44:208 ==>> 检测【关闭CAN通信】
2025-07-31 18:03:44:226 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:03:44:255 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:03:44:472 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:03:44:477 ==>> 检测【打印IMU STATE】
2025-07-31 18:03:44:482 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:03:44:762 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-67 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,100348.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,13,,,40,26,,,40,1*7A

$GBGSV,6,2,21,3,,,40,60,,,39,39,,,39,42,,,39,1*46

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,39,16,,,38,1*41

$GBGSV,6,4,21,1,,,37,9,,,37,6,,,36,33,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100348.515,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100348.515,0.000,1555.667,1555.667,49.760,2097152,2097152,2097152*52



2025-07-31 18:03:44:897 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:03:45:012 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:03:45:017 ==>> 检测【六轴自检】
2025-07-31 18:03:45:020 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:03:45:243 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:03:45:714 ==>> $GBGGA,100349.515,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,13,,,40,26,,,40,1*7A

$GBGSV,6,2,21,3,,,40,60,,,40,39,,,39,42,,,39,1*48

$GBGSV,6,3,21,8,,,39,59,,,39,21,,,39,16,,,39,1*40

$GBGSV,6,4,21,1,,,37,9,,,36,6,,,36,33,,,36,1*48

$GBGSV,6,5,21,14,,,35,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100349.515,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100349.515,0.000,1557.644,1557.644,49.826,2097152,2097152,2097152*5E



2025-07-31 18:03:46:736 ==>> $GBGGA,100350.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,13,,,40,26,,,40,1*79

$GBGSV,6,2,22,3,,,40,8,,,40,59,,,40,60,,,39,1*7D

$GBGSV,6,3,22,39,,,39,42,,,39,21,,,39,16,,,39,1*7B

$GBGSV,6,4,22,1,,,37,9,,,37,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,36,2,,,34,5,,,34,4,,,33,1*46

$GBGSV,6,6,22,45,,,32,7,,,25,1*46

$GBRMC,100350.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100350.515,0.000,1541.540,1541.540,49.358,2097152,2097152,2097152*54



2025-07-31 18:03:46:947 ==>> [D][05:18:25][COMM]read battery soc:255
[D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:03:47:129 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36669 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-4,-2,4031]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 18:03:47:344 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:03:47:350 ==>> 检测【打印IMU STATE2】
2025-07-31 18:03:47:374 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:03:47:550 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:03:47:613 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:03:47:619 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:03:47:628 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:03:47:658 ==>> $GBGGA,100351.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,2

2025-07-31 18:03:47:715 ==>> 2,24,,,42,38,,,41,13,,,41,26,,,40,1*78

$GBGSV,6,2,22,3,,,40,60,,,40,8,,,39,59,,,39,1*73

$GBGSV,6,3,22,39,,,39,42,,,39,21,,,39,16,,,39,1*7B

$GBGSV,6,4,22,1,,,37,9,,,37,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,36,2,,,34,5,,,34,4,,,33,1*46

$GBGSV,6,6,22,45,,,32,7,,,26,1*45

$GBRMC,100351.515,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100351.515,0.000,1543.417,1543.417,49.411,2097152,2097152,2097152*5F



2025-07-31 18:03:47:745 ==>> 5A A5 02 5A A5 


2025-07-31 18:03:47:850 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:03:47:885 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:03:47:895 ==>> 检测【检测VBUS电压2】
2025-07-31 18:03:47:916 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:03:48:211 ==>> [D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539006
[D][05:18:26][FCTY]HardwareID  = 867222087567865
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D73B1AC7DE82
[D][05:18:26][FCTY]Bat         = 4064 mv

2025-07-31 18:03:48:318 ==>> 
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 9, adc = 389
[D][05:18:26][FCTY]Acckey1 vol = 5596 mv, Acckey2 vol = 379 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3833 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
                                                                       

2025-07-31 18:03:48:410 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:03:48:877 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539006
[D][05:18:27][FCTY]HardwareID  = 867222087567865
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D73B1AC7DE82
[D][05:18:27][FCTY]Bat         = 4064 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 8700 mv
$GBGGA,100352.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,13,,,40,26,,,40,1*79

$GBGSV,6,2,22,3,,,40,60,,,40,8,,,39,59,,,39,1*73

$GBGSV,6,3,22,39,,,39,42,,,39,21,,,39,16,,,38,1*7A

$GBGSV,6,4,22,1,,,37,9,,,37,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,36,2,,,34,5,,,33,4,,,33,1*41

$GBGSV,6,6,22,45,,,32,7,,,29,1*4A

$GBRMC,100352.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100352.515,0.000,1543.397,1543.397,49.391

2025-07-31 18:03:48:967 ==>> ,2097152,2097152,2097152*53

[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 5, adc = 199
[D][05:18:27][FCTY]Acckey1 vol = 5595 mv, Acckey2 vol = 379 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3833 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:03:49:148 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33675. cur_tick:38683. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38683


2025-07-31 18:03:49:197 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:03:49:512 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539006
[D][05:18:27][FCTY]HardwareID  = 867222087567865
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D73B1AC7DE82
[D][05:18:27][FCTY]Bat         = 3844 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:27][FCTY]Acckey1 vol = 5591 mv, Acckey2 vol = 429 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4

2025-07-31 18:03:49:557 ==>> 
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3833 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:03:49:662 ==>> $GBGGA,100353.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,13,,,41,26,,,40,1*78

$GBGSV,6,2,22,3,,,40,60,,,4

2025-07-31 18:03:49:722 ==>> 0,8,,,39,59,,,39,1*73

$GBGSV,6,3,22,39,,,39,42,,,39,21,,,39,16,,,38,1*7A

$GBGSV,6,4,22,1,,,37,9,,,37,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,36,2,,,34,5,,,34,4,,,33,1*46

$GBGSV,6,6,22,45,,,32,7,,,29,1*4A

$GBRMC,100353.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100353.515,0.000,1547.166,1547.166,49.511,2097152,2097152,2097152*5C



2025-07-31 18:03:49:728 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:03:49:755 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:03:49:759 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:03:49:856 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:03:50:014 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:03:50:040 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:03:50:048 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:03:50:232 ==>> [D][05:18:28][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:28][COMM]Main Task receive event:65
[D][05:18:28][COMM]main task tmp_sleep_event = 80
[D][05:18:28][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:28][COMM]Main Task receive event:65 finished processing
[D][05:18:28][COMM]Main Task receive event:60
[D][05:18:28][COMM]smart_helmet_vol=255,255
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[W][05:18:28][GNSS]stop locating
[D][05:18:28][GNSS]stop event:8
[D][05:18:28][GNSS]GPS stop. ret=0
[D][05:18:28][GNSS]all continue location stop
[D][05:18:28][COMM]report elecbike
[W][05:18:28][PROT]remove success[1629955108],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]index:0
[D][05:18:28][PROT]is_se

2025-07-31 18:03:50:337 ==>> nd:1
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg_type:0x5d03
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900005]
[D][05:18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[

2025-07-31 18:03:50:442 ==>> D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[198]
[D][05:18:28][CAT1]gsm read msg sub id: 24
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:28][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 24, ret: 6
[D][05:18:28][CAT1]sub id: 24, ret: 6

[D][05:18:28][CAT1]gsm read msg sub id: 15
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B54FB226B844C17B9367FCF45ABB3786D5D2FCF4A7F6925D3163C602C910454D1EAA79A26849796D773577215732

2025-07-31 18:03:50:547 ==>> C351FF6F8599813DD6C28D68C9FF2C8D8C658E7138EA8CEA90F6B53642DB4C4FC697557656
[D][05:18:28][COMM]read battery soc:255
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
                                                                                                                                           

2025-07-31 18:03:50:811 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:03:50:817 ==>> 检测【打开WIFI(3)】
2025-07-31 18:03:50:840 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:03:51:072 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:03:51:350 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:03:51:358 ==>> 检测【扩展芯片hw】
2025-07-31 18:03:51:370 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:03:52:244 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------
[W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][0

2025-07-31 18:03:52:349 ==>> 5:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]M

2025-07-31 18:03:52:413 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:03:52:419 ==>> 检测【扩展芯片boot】
2025-07-31 18:03:52:436 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:03:52:456 ==>> 检测【扩展芯片sw】
2025-07-31 18:03:52:465 ==>> ain Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][PROT]index:1
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][PROT]is_send:1
[D][05:18:30][COMM]Receive B

2025-07-31 18:03:52:474 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:03:52:503 ==>> 检测【检测音频FLASH】
2025-07-31 18:03:52:515 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:03:52:560 ==>> at Lock cmd 0
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat wo

2025-07-31 18:03:52:664 ==>> rk state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
+WIFISCAN:4,0,CC05779

2025-07-31 18:03:52:675 ==>> System.IndexOutOfRangeException: 索引超出了数组界限。
   在 AppSe5x.FormMain.<ProcessMessagesAsync>d__105.MoveNext()
2025-07-31 18:03:52:769 ==>> 0A740,-75
+WIFISCAN:4,1,44A1917CA62B,-76
+WIFISCAN:4,2,CC057790A7C1,-77
+WIFISCAN:4,3,F86FB0660A82,-86

[D][05:18:30][CAT1]wifi scan report total[4]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255


2025-07-31 18:03:52:875 ==>>                                       [W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:03:54:010 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 18:03:54:597 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:03:55:432 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311

2025-07-31 18:03:55:507 ==>> 331B88B33F90B80191FA2C32B418C45A32B570E92F92A838B5BC2C6F6DD1EF19F9F2D368D78492066F4D306BB103C36EBA783DCD7615C8B9483E3BDF67D5EDC32DF908BC23A6C055A08A9933A59A60D596D1988D85CD
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 18:03:55:702 ==>> [D][05:18:34][COMM]45155 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:03:56:040 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 18:03:56:100 ==>>                                 0003c6bd, wdg event is 0x0003c63d


2025-07-31 18:03:56:595 ==>> [D][05:18:35][COMM]46166 imu init OK


2025-07-31 18:03:56:824 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 18:03:57:506 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:03:57:512 ==>> 检测【打开喇叭声音】
2025-07-31 18:03:57:516 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:03:58:252 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:36][COMM]read file, len:15228, num:4
[D][05:18:36][COMM]--->crc16:0x419c
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[6

2025-07-31 18:03:58:316 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:03:58:323 ==>> 检测【打开大灯控制】
2025-07-31 18:03:58:330 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:03:58:357 ==>> 91].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18

2025-07-31 18:03:58:462 ==>> :36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:36][COMM]read battery soc:255
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:03:58:537 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:03:58:586 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:03:58:591 ==>> 检测【关闭仪表供电3】
2025-07-31 18:03:58:615 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:03:58:735 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 18:03:58:859 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:03:58:868 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:03:58:893 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:03:59:042 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:03:59:140 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:03:59:146 ==>> 检测【读大灯电压】
2025-07-31 18:03:59:169 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:03:59:333 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33479]


2025-07-31 18:03:59:411 ==>> 【读大灯电压】通过,【33479mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:03:59:417 ==>> 检测【关闭大灯控制2】
2025-07-31 18:03:59:427 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:03:59:609 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:03:59:688 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:03:59:694 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:03:59:703 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:03:59:838 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:03:59:962 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:03:59:968 ==>> 检测【打开WIFI(4)】
2025-07-31 18:03:59:978 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:04:00:052 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 18:04:00:157 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:04:00:296 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:04:00:302 ==>> 检测【EC800M模组版本】
2025-07-31 18:04:00:325 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:04:00:588 ==>> [D][05:18:38][PROT]CLEAN,SEND:1
[W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:39][M2M ]m2m switc

2025-07-31 18:04:00:618 ==>> h to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 18:04:00:860 ==>> [D][05:18:39][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 132
[D][05:18:39][CAT1]gsm read msg sub id: 15
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:39][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B39ACF5190ED34C0C0DF8B02BFC3F642555DF0E69516CF6A091F2FE0B46B7432585CC85A4289D09C018AB3B98D3D1137B3BDCA59113F634D505C95E23E4230D7FB8331EFBF26FA5511B1B35CC97A1F79C324D5
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 18:04:01:091 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:04:01:101 ==>> 检测【配置蓝牙地址】
2025-07-31 18:04:01:109 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:04:01:242 ==>> [W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:04:01:302 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D73B1AC7DE82>】
2025-07-31 18:04:01:456 ==>> recv ble 1
recv ble 2
ble set mac ok :d7,3b,1a,c7,de,82
enable filters ret : 0

2025-07-31 18:04:01:580 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:04:01:590 ==>> 检测【BLETEST】
2025-07-31 18:04:01:611 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:04:01:653 ==>> 4A A4 01 A4 4A 


2025-07-31 18:04:01:713 ==>> [D][05:18:40][COMM]51274 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:04:01:758 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D73B1AC7DE82*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D73B1AC7DE8299999OVER 150


2025-07-31 18:04:02:049 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 18:04:02:609 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:04:02:615 ==>> 该项需要延时执行
2025-07-31 18:04:02:746 ==>> [D][05:18:41][COMM]52285 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:04:03:379 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:04:03:733 ==>> [D][05:18:42][COMM]53296 imu init OK


2025-07-31 18:04:04:056 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 18:04:04:926 ==>> +WIFISCAN:4,0,44A1917CA62F,-72
+WIFISCAN:4,1,CC057790A7C0,-76
+WIFISCAN:4,2,44A1917CAD81,-80
+WIFISCAN:4,3,F86FB0660A82,-88

[D][05:18:43][CAT1]wifi scan report total[4]


2025-07-31 18:04:05:359 ==>> [D][05:18:43][GNSS]recv submsg id[3]


2025-07-31 18:04:06:134 ==>> [D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][PROT]index:1 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:5
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:1
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900006]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3A4812A823

2025-07-31 18:04:06:209 ==>> 3715098B142DF490FDC112C9D5FE7D95C756EC8074AA0D34026D932449F5EC6020265513AB86AE6E0EBAA979CC830C8F2DBE5B700E46CD4D7EFD39DAD68377F8B21C308FE9E1C8D4DACE2111204
[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][05:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]
[D][05:18:44][COMM]read battery soc:255


2025-07-31 18:04:08:080 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 18:04:10:070 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 18:04:11:354 ==>> [D][05:18:49][PROT]CLEAN,SEND:1
[D][05:18:49][PROT]CLEAN:1
[D][05:18:49][PROT]index:0 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:4
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:2
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900005]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B9821133113

2025-07-31 18:04:11:429 ==>> 31133113311331B88B5B963101746934A3518079C9D01B21C146A4AEAB7EEF9898E60894B1D29472DE0FCA7F65E164EA35B26AEE342450E1BD6E2D4F5DFEB0C8C60BA1D43762897FE0C7E0213B68512D14CBB9E70B56B27533A0A05
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 18:04:11:950 ==>> [D][05:18:50][COMM]IMU: [-1,2,-965] ret=31 AWAKE!


2025-07-31 18:04:12:100 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 18:04:12:282 ==>> [D][05:18:50][COMM]IMU: [7,2,-1002] ret=34 AWAKE!


2025-07-31 18:04:12:620 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:04:12:626 ==>> 检测【检测WiFi结果】
2025-07-31 18:04:12:634 ==>> WiFi信号:【CC057790A5C1】,信号值:-78
2025-07-31 18:04:12:659 ==>> WiFi信号:【CC057790A7C1】,信号值:-78
2025-07-31 18:04:12:667 ==>> WiFi信号:【F86FB0660A82】,信号值:-87
2025-07-31 18:04:12:686 ==>> WiFi信号:【44A1917CA62B】,信号值:-76
2025-07-31 18:04:12:696 ==>> WiFi信号:【44A1917CA62F】,信号值:-72
2025-07-31 18:04:12:717 ==>> WiFi信号:【CC057790A7C0】,信号值:-76
2025-07-31 18:04:12:734 ==>> WiFi信号:【44A1917CAD81】,信号值:-80
2025-07-31 18:04:12:740 ==>> WiFi数量【7】, 最大信号值:-72
2025-07-31 18:04:12:749 ==>> 检测【检测GPS结果】
2025-07-31 18:04:12:758 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:04:12:883 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[D][05:18:51][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all continue location stop
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all sing location stop


2025-07-31 18:04:13:629 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:04:13:638 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:13:649 ==>> 定位已等待【1】秒.
2025-07-31 18:04:14:059 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:52][COMM]Open GPS Module...
[D][05:18:52][COMM]LOC_MODEL_CONT
[D][05:18:52][GNSS]start event:8
[D][05:18:52][GNSS]GPS start. ret=0
[W][05:18:52][GNSS]start cont locating
[D][05:18:52][CAT1]gsm read msg sub id: 23
[D][05:18:52][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:52][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:04:14:089 ==>>                                          

2025-07-31 18:04:14:644 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:14:653 ==>> 定位已等待【2】秒.
2025-07-31 18:04:14:784 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:04:15:437 ==>> [D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:04:15:649 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:15:660 ==>> 定位已等待【3】秒.
2025-07-31 18:04:15:682 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,24,,,43,38,,,42,26,,,41,60,,,41,1*73

$GBGSV,2,2,08,16,,,40,39,,,38,8,,,42,5,,,39,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1692.806,1692.806,54.077,2097152,2097152,2097152*4E

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:54][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]exec over: func id: 23, ret: 6
[D][05:18:54][CAT1]sub id: 23, ret: 6



2025-07-31 18:04:16:114 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 18:04:16:639 ==>> [D][05:18:54][PROT]CLEAN,SEND:0
[D][05:18:54][PROT]index:0 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:4
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:1
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900005]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B53EF582CB9658BB6BB956681C8C0BFBEA95A15D81B96A10B1A1770EAFFB63F518C86C

2025-07-31 18:04:16:654 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:16:664 ==>> 定位已等待【4】秒.
2025-07-31 18:04:16:744 ==>> 4DD62926713B30BADB5B72E5B85249004806E5EFD4DC9366EEFEFAF113D926DB923766A2D89BF5760BB49FED07562888
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]
[D][05:18:54][GNSS]recv submsg id[1]
[D][05:18:54][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,24,,,42,38,,,42,26,,,41,60,,,41,1*78

$GBGSV,4,2,15,13,,,41,59,,,41,8,,,40,21,,,40,1*41

$GBGSV,4,3,15,16,,,39,39,,,39,42,,,39,1,,,39,1*4F

$GBGSV,4,4,15,2,,,38,9,,,36,4,,,35,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1638.933,1638.933,52.377,2097152,2097152,2097152*4B



2025-07-31 18:04:17:607 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,42,38,,,41,26,,,41,3,,,41,1*4D

$GBGSV,5,2,17,60,,,40,13,,,40,59,,,40,8,,,40,1*47

$GBGSV,5,3,17,21,,,39,16,,,39,39,,,39,42,,,39,1*7E

$GBGSV,5,4,17,1,,,38,2,,,37,9,,,37,4,,,33,1*74

$GBGSV,5,5,17,5,,,33,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1607.095,1607.095,51.387,2097152,2097152,2097152*47



2025-07-31 18:04:17:667 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:17:676 ==>> 定位已等待【5】秒.
2025-07-31 18:04:18:118 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 18:04:18:607 ==>> $GBGGA,100422.411,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,38,,,41,26,,,41,13,,,41,1*73

$GBGSV,5,2,19,3,,,40,60,,,40,59,,,40,8,,,40,1*78

$GBGSV,5,3,19,21,,,40,42,,,40,16,,,39,39,,,39,1*70

$GBGSV,5,4,19,1,,,38,2,,,37,9,,,37,33,,,35,1*48

$GBGSV,5,5,19,5,,,34,4,,,33,14,,,38,1*76

$GBRMC,100422.411,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100422.411,0.000,1607.642,1607.642,51.409,2097152,2097152,2097152*59



2025-07-31 18:04:18:682 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:18:692 ==>> 定位已等待【6】秒.
2025-07-31 18:04:19:599 ==>> $GBGGA,100423.391,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,41,26,,,41,13,,,41,1*78

$GBGSV,5,2,20,3,,,40,60,,,40,59,,,40,8,,,40,1*72

$GBGSV,5,3,20,21,,,40,42,,,40,16,,,39,39,,,39,1*7A

$GBGSV,5,4,20,1,,,38,2,,,37,9,,,37,33,,,35,1*42

$GBGSV,5,5,20,5,,,34,14,,,33,4,,,33,6,,,32,1*40

$GBRMC,100423.391,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100423.391,0.000,1579.566,1579.566,50.535,2097152,2097152,2097152*58



2025-07-31 18:04:19:689 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:19:698 ==>> 定位已等待【7】秒.
2025-07-31 18:04:19:795 ==>> $GBGGA,100423.591,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,41,26,,,41,13,,,41,1*78

$GBGSV,5,2,20,3,,,40,60,,,40,59,,,40,8,,,40,1*72

$GBGSV,5,3,20,21,,,40,42,,,40,16,,,39,39,,,39,1*7A

$GBGSV,5,4,20,1,,,38,2,,,37,9,,,37,33,,,35,1*42

$GBGSV,5,5,20,5,,,34,14,,,33,4,,,33,6,,,33,1*41

$GBRMC,100423.591,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100423.591,0.000,1581.634,1581.634,50.596,2097152,2097152,2097152*57



2025-07-31 18:04:20:115 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 18:04:20:690 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:20:699 ==>> 定位已等待【8】秒.
2025-07-31 18:04:20:780 ==>> $GBGGA,100424.571,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,41,26,,,41,13,,,41,1*78

$GBGSV,5,2,20,3,,,40,60,,,40,59,,,40,8,,,40,1*72

$GBGSV,5,3,20,21,,,40,42,,,40,16,,,39,39,,,39,1*7A

$GBGSV,5,4,20,1,,,37,2,,,37,9,,,37,33,,,35,1*4D

$GBGSV,5,5,20,6,,,35,5,,,34,14,,,34,4,,,33,1*40

$GBRMC,100424.571,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100424.571,0.000,1585.769,1585.769,50.717,2097152,2097152,2097152*55



2025-07-31 18:04:21:702 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:21:711 ==>> 定位已等待【9】秒.
2025-07-31 18:04:21:839 ==>> [D][05:19:00][PROT]CLEAN,SEND:0
[D][05:19:00][PROT]CLEAN:0
[D][05:19:00][PROT]index:2 1629955140
[D][05:19:00][PROT]is_send:0
[D][05:19:00][PROT]sequence_num:6
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:3
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:00][PROT]===========================================================
[W][05:19:00][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980221
[D][05:19:00][PROT]sending traceid [9999999999900007]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[134]
[D][05:19:00][SAL ]Cellular task submsg id[10]
[D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:00][CAT1]Send Data To Serv

2025-07-31 18:04:21:944 ==>> er[134][137] ... ->:
0043B683113311331133113311331B88BE5990455F07CDD3C61B63C29AD00187BE5DC78CAA5AE63DA2B13CC89FA23D683F478D6A3ECD1E0862FFA377EEDF74CFDDCF17
[D][05:19:00][CAT1]<<< 
SEND OK

[D][05:19:00][CAT1]exec over: func id: 15, ret: 11
[D][05:19:00][CAT1]sub id: 15, ret: 11

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:00][M2M ]g_m2m_is_idle become true
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:00][PROT]M2M Send ok [1629955140]
$GBGGA,100425.551,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,41,26,,,41,13,,,41,1*78

$GBGSV,5,2,20,3,,,41,60,,,40,59,,,40,8,,,40,1*73

$GBGSV,5,3,20,42,,,40,21,,,39,16,,,39,39,,,39,1*74

$GBGSV,5,4,20,1,,,38,2,,,37,9,,,37,6,,,36,1*77

$GBGSV,5,5,20,33,,,35,14,,,35,5,,,34,4,,,34,1*70

$GBRMC,100425.551,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100425.551,0.000,1594.049,1594.049,50.971,2097152,2097152,2097152*58



2025-07-31 18:04:22:141 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 18:04:22:712 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:22:723 ==>> 定位已等待【10】秒.
2025-07-31 18:04:22:757 ==>> $GBGGA,100426.531,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,13,,,41,1*7A

$GBGSV,6,2,21,3,,,40,60,,,40,59,,,40,8,,,40,1*70

$GBGSV,6,3,21,42,,,40,21,,,40,16,,,39,39,,,39,1*78

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,33,,,35,14,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100426.531,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100426.531,0.000,1579.362,1579.362,50.521,2097152,2097152,2097152*54



2025-07-31 18:04:23:722 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:23:731 ==>> $GBGGA,100427.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,13,,,41,1*7A

$GBGSV,6,2,21,3,,,40,60,,,40,59,,,40,8,,,40,1*70

$GBGSV,6,3,21,42,,,40,21,,,40,16,,,39,39,,,39,1*78

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,14,,,36,33,,,35,5,,,34,4,,,33,1*76

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100427.511,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100427.511,0.000,1579.363,1579.363,50.523,2097152,2097152,2097152*55



2025-07-31 18:04:23:753 ==>> 定位已等待【11】秒.
2025-07-31 18:04:24:137 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 18:04:24:720 ==>> $GBGGA,100428.511,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,41,26,,,41,13,,,41,1*7B

$GBGSV,6,2,21,3,,,40,60,,,40,59,,,40,8,,,40,1*70

$GBGSV,6,3,21,42,,,40,21,,,40,16,,,39,39,,,39,1*78

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,14,,,36,33,,,35,5,,,34,4,,,34,1*71

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100428.511,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100428.511,0.000,1583.311,1583.311,50.649,2097152,2097152,2097152*55



2025-07-31 18:04:24:735 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:24:744 ==>> 定位已等待【12】秒.
2025-07-31 18:04:25:714 ==>> $GBGGA,100429.511,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,3,,,41,1*4B

$GBGSV,6,2,21,13,,,40,60,,,40,59,,,40,8,,,40,1*41

$GBGSV,6,3,21,42,,,40,21,,,40,16,,,39,39,,,39,1*78

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,14,,,36,33,,,35,5,,,34,4,,,34,1*71

$GBGSV,6,6,21,45,,,31,1*76

$GBRMC,100429.511,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100429.511,0.000,1581.334,1581.334,50.582,2097152,2097152,2097152*50



2025-07-31 18:04:25:744 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:25:753 ==>> 定位已等待【13】秒.
2025-07-31 18:04:26:132 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 18:04:26:718 ==>> $GBGGA,100430.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,3,,,41,1*4B

$GBGSV,6,2,21,13,,,41,59,,,41,42,,,41,60,,,40,1*7E

$GBGSV,6,3,21,8,,,40,21,,,40,16,,,39,39,,,39,1*46

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,37,1*74

$GBGSV,6,5,21,14,,,36,33,,,35,5,,,34,4,,,34,1*71

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100430.511,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100430.511,0.000,1591.202,1591.202,50.896,2097152,2097152,2097152*50



2025-07-31 18:04:26:748 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:26:757 ==>> 定位已等待【14】秒.
2025-07-31 18:04:27:021 ==>> [D][05:19:05][PROT]CLEAN,SEND:2
[D][05:19:05][PROT]index:2 1629955145
[D][05:19:05][PROT]is_send:0
[D][05:19:05][PROT]sequence_num:6
[D][05:19:05][PROT]retry_timeout:0
[D][05:19:05][PROT]retry_times:2
[D][05:19:05][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:05][PROT]===========================================================
[W][05:19:05][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980221
[D][05:19:05][PROT]sending traceid [9999999999900007]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[134]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][05:19:05][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:05][CAT1]Send Data To Server[134][137] ... ->:
0043B68511331133113

2025-07-31 18:04:27:096 ==>> 3113311331B88BEDC0BBA89075B0EB42880B1F86D85EE1B527D03E3C96901B535522316698B247945FC43124C9AE5467CD3F7279FA0A531BF72
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:05][PROT]M2M Send ok [1629955145]


2025-07-31 18:04:27:737 ==>> $GBGGA,100431.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,3,,,41,1*4B

$GBGSV,6,2,21,13,,,41,60,,,41,59,,,40,42,,,40,1*7F

$GBGSV,6,3,21,8,,,40,21,,,40,16,,,39,39,,,39,1*46

$GBGSV,6,4,21,1,,,37,2,,,37,9,,,37,6,,,36,1*7A

$GBGSV,6,5,21,14,,,35,33,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100431.511,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100431.511,0.000,790.173,790.173,722.629,2097152,2097152,2097152*69



2025-07-31 18:04:27:752 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:27:762 ==>> 定位已等待【15】秒.
2025-07-31 18:04:27:842 ==>> [D][05:19:06][COMM]IMU: [-2,3,-974] ret=35 AWAKE!


2025-07-31 18:04:28:128 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 18:04:28:739 ==>> $GBGGA,100432.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,3,,,41,26,,,41,1*4B

$GBGSV,6,2,21,13,,,40,8,,,40,60,,,40,59,,,40,1*41

$GBGSV,6,3,21,42,,,40,39,,,39,16,,,39,21,,,39,1*76

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,14,,,35,33,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100432.511,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100432.511,0.000,788.197,788.197,720.822,2097152,2097152,2097152*6D



2025-07-31 18:04:28:754 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:28:764 ==>> 定位已等待【16】秒.
2025-07-31 18:04:29:734 ==>> $GBGGA,100433.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,13,,,40,1*7B

$GBGSV,6,2,21,60,,,40,3,,,40,59,,,40,42,,,40,1*4E

$GBGSV,6,3,21,8,,,39,39,,,39,16,,,39,21,,,39,1*46

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,37,6,,,36,1*75

$GBGSV,6,5,21,5,,,35,14,,,35,33,,,35,4,,,34,1*73

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100433.511,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100433.511,0.000,787.205,787.205,719.914,2097152,2097152,2097152*62



2025-07-31 18:04:29:764 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:29:776 ==>> 定位已等待【17】秒.
2025-07-31 18:04:30:150 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 18:04:30:731 ==>> $GBGGA,100434.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,13,,,40,1*7B

$GBGSV,6,2,21,8,,,40,60,,,40,3,,,40,59,,,40,1*70

$GBGSV,6,3,21,42,,,40,39,,,39,16,,,39,21,,,39,1*76

$GBGSV,6,4,21,2,,,37,9,,,37,1,,,37,6,,,36,1*7A

$GBGSV,6,5,21,14,,,35,33,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,45,,,32,1*75

$GBRMC,100434.511,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100434.511,0.000,786.225,786.225,719.018,2097152,2097152,2097152*60



2025-07-31 18:04:30:776 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:30:792 ==>> 定位已等待【18】秒.
2025-07-31 18:04:31:742 ==>> $GBGGA,100435.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,41,38,,,41,26,,,41,1*7B

$GBGSV,6,2,21,60,,,40,8,,,40,3,,,40,59,,,40,1*70

$GBGSV,6,3,21,42,,,40,39,,,39,16,,,39,21,,,39,1*76

$GBGSV,6,4,21,1,,,38,9,,,37,2,,,36,6,,,36,1*74

$GBGSV,6,5,21,14,,,35,33,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,45,,,33,1*74

$GBRMC,100435.511,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100435.511,0.000,789.182,789.182,721.723,2097152,2097152,2097152*65



2025-07-31 18:04:31:787 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:31:798 ==>> 定位已等待【19】秒.
2025-07-31 18:04:32:254 ==>> [D][05:19:10][PROT]CLEAN,SEND:2
[D][05:19:10][PROT]index:2 1629955150
[D][05:19:10][PROT]is_send:0
[D][05:19:10][PROT]sequence_num:6
[D][05:19:10][PROT]retry_timeout:0
[D][05:19:10][PROT]retry_times:1
[D][05:19:10][PROT]send_path:0x2
[D][05:19:10][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:10][PROT]===========================================================
[W][05:19:10][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955150]
[D][05:19:10][PROT]===========================================================
[D][05:19:10][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980221
[D][05:19:10][PROT]sending traceid [9999999999900007]
[D][05:19:10][PROT]Send_TO_M2M [1629955150]
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:10][SAL ]sock send credit cnt[6]
[D][05:19:10][SAL ]sock send ind credit cnt[6]
[D][05:19:10][M2M ]m2m send data len[134]
[D][05:19:10][SAL ]Cellular task submsg id[10]
[D][05:19:10][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:10][CAT1]gsm read msg sub id: 15
[D][05:19:10][CAT1]tx ret[17] >>> AT+QISEND=0,134



2025-07-31 18:04:32:329 ==>> [D][05:19:10][CAT1]Send Data To Server[134][137] ... ->:
0043B686113311331133113311331B88BE247DB13FE3C3CC9B69331492D1FC4A251093F176DB25EE130895FB0348E875E113BBA3704183A016A45FF14F61DE8F316209
[D][05:19:10][CAT1]<<< 
SEND OK

[D][05:19:10][CAT1]exec over: func id: 15, ret: 11
[D][05:19:10][CAT1]sub id: 15, ret: 11

[D][05:19:10][SAL ]Cellular task submsg id[68]
[D][05:19:10][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:10][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:10][M2M ]g_m2m_is_idle become true
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:10][PROT]M2M Send ok [1629955150]
[D][05:19:10][COMM]read battery soc:255


2025-07-31 18:04:32:788 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:04:32:799 ==>> 定位已等待【20】秒.
2025-07-31 18:04:33:373 ==>> $GBGGA,100432.517,2301.2564489,N,11421.9409789,E,1,11,1.41,69.297,M,-1.770,M,,*57

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,40,8,81,181,40,26,65,31,41,16,65,309,39,1*70

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,37,59,52,130,40,1*77

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,35,33,16,322,35,1*78

$GBGSV,6,6,21,14,,,35,1*76

$GBRMC,100432.517,A,2301.2564489,N,11421.9409789,E,0.000,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

[D][05:19:11][GNSS]HD8040 GPS
[D][05:19:11][GNSS]GPS diff_sec 124001121, report 0x42 frame
$GBGST,100432.517,0.419,0.300,0.307,0.412,3.325,3.362,11*53

[D][05:19:11][COMM]Main Task receive event:131
[D][05:19:11][COMM]index:0,power_mode:0xFF
[D][05:19:11][COMM]index:1,sound_mode:0xFF
[D][05:19:11][COMM]index:2,gsensor_mode:0xFF
[D][05:19:11][COMM]index:3,report_freq_mode:0xFF
[D][05:19:11][COMM]index:4,report_period:0xFF
[D][05:19:11][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:11][COMM]index:6,normal_reset_peri

2025-07-31 18:04:33:478 ==>> od:0xFF
[D][05:19:11][COMM]index:7,spock_over_speed:0xFF
[D][05:19:11][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:11][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:11][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:11][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:11][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:11][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:11][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:11][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:11][COMM]index:16,imu_config_params:0xFF
[D][05:19:11][COMM]index:17,long_connect_params:0xFF
[D][05:19:11][COMM]index:18,detain_mark:0xFF
[D][05:19:11][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:11][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:11][COMM]index:21,mc_mode:0xFF
[D][05:19:11][COMM]index:22,S_mode:0xFF
[D][05:19:11][COMM]index:23,overweight:0xFF
[D][05:19:11][COMM]index:24,standstill_mode:0xFF
[D][05:19:11][COMM]index:25,night_mode:0xFF
[D][05:19:11][COMM]index:26,experiment1:0xFF
[D][05:19:11][COMM]index:27,experiment2:0xFF
[D][05:19:11][COMM]index:28,experiment3:0xFF
[D][05:19:11][COMM]index:29,experiment4:0xFF
[D][05:19:11][COMM]index:30,night_mode_start:

2025-07-31 18:04:33:583 ==>> 0xFF
[D][05:19:11][COMM]index:31,night_mode_end:0xFF
[D][05:19:11][COMM]index:33,park_report_minutes:0xFF
[D][05:19:11][COMM]index:34,park_report_mode:0xFF
[D][05:19:11][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:11][COMM]index:38,charge_battery_para: FF
[D][05:19:11][COMM]index:39,multirider_mode:0xFF
[D][05:19:11][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:11][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:11][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:11][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:11][COMM]index:44,riding_duration_config:0xFF
[D][05:19:11][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:11][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:11][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:11][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:11][COMM]index:49,mc_load_startup:0xFF
[D][05:19:11][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:11][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:11][COMM]index:52,traffic_mode:0xFF
[D][05:19:11][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:11][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:11][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:11]

2025-07-31 18:04:33:688 ==>> [COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:11][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:11][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:11][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:11][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:11][COMM]index:63,experiment5:0xFF
[D][05:19:11][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:11][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:11][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:11][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:11][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:11][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:11][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:11][COMM]index:72,experiment6:0xFF
[D][05:19:11][COMM]index:73,experiment7:0xFF
[D][05:19:11][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:11][COMM]index:75,zero_value_from_server:-1
[D][05:19:11][COMM]index:76,multirider_threshold:255
[D][05:19:11][COMM]index:77,experiment8:255
[D][05:19:11][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:11][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:11][COMM]index:80,temp_park_remi

2025-07-31 18:04:33:793 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 18:04:33:802 ==>> nder_timeout_duration:255
[D][05:19:11][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:11][COMM]index:83,loc_report_interval:255
[D][05:19:11][COMM]index:84,multirider_threshold_p2:255
[D][05:19:11][COMM]index:85,multirider_strategy:255
[D][05:19:11][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:11][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:11][COMM]index:90,weight_param:0xFF
[D][05:19:11][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:11][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:11][COMM]index:95,current_limit:0xFF
[D][05:19:11][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:11][COMM]index:100,location_mode:0xFF

[W][05:19:11][PROT]remove success[1629955151],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:11][PROT]add success [1629955151],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:11][COMM]Main Task receive event:131 finished processing
[D][05:19:11][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:11][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,100433.017,2301.2572104,N,11421.9413795,E,1,11,1.41,71.342,M,-1.770,M,,*52

$GBGSA,A,3,13,08,26,16,42,38,3

2025-07-31 18:04:33:811 ==>> 
北斗星号:【13】,信号值:【40】
北斗星号:【8】,信号值:【40】
北斗星号:【26】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【42】,信号值:【40】
北斗星号:【38】,信号值:【41】
北斗星号:【39】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【24】,信号值:【42】
北斗星号:【9】,信号值:【37】
北斗星号:【6】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【21】,信号值:【39】
北斗星号:【2】,信号值:【36】
北斗星号:【60】,信号值:【40】
北斗星号:【5】,信号值:【35】
北斗星号:【33】,信号值:【35】
北斗星号:【14】,信号值:【35】

2025-07-31 18:04:33:824 ==>> 检测【CSQ强度】
2025-07-31 18:04:33:832 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:04:33:883 ==>> 9,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,41,8,81,181,40,26,65,31,41,16,65,309,39,1*71

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,43,9,58,275,37,6,54,7,37,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,06,26,65,31,41,42,64,1,42,38,64,166,40,39,63,338,39,5*45

$GBGSV,2,2,06,24,60,228,41,21,47,107,36,5*7A

$GBRMC,100433.017,A,2301.2572104,N,11421.9413795,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100433.017,1.179,0.759,0.780,0.999,1.855,2.090,7.602*74



2025-07-31 18:04:33:988 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CSQ



2025-07-31 18:04:34:334 ==>> [D][05:19:12][COMM]read battery soc:255
$GBGGA,100434.000,2301.2573774,N,11421.9414794,E,1,11,1.41,71.862,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,40,8,81,181,40,26,65,31,41,16,65,309,39,1*70

$GBGSV,6,2,21,42,64,1,41,38,64,166,41,39,63,338,39,3,62,191,40,1*4B

$GBGSV,6,3,21,24,60,228,43,9,58,275,37,6,54,7,36,59,52,130,40,1*77

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,42,38,64,166,40,39,63,338,40,5*4A

$GBGSV,2,2,07,24,60,228,42,21,47,107,38,33,16,322,30,5*41

$GBRMC,100434.000,A,2301.2573774,N,11421.9414794,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100434.000,1.373,1.064,1.095,1.384,1.589,1.792,6.682*73



2025-07-31 18:04:35:323 ==>> $GBGGA,100435.000,2301.2574520,N,11421.9415289,E,1,11,1.41,72.377,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,40,8,81,181,40,26,65,31,41,16,65,309,39,1*70

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,42,38,64,166,41,39,63,338,40,5*4B

$GBGSV,2,2,07,24,60,228,42,21,47,107,40,33,16,322,30,5*4E

$GBRMC,100435.000,A,2301.2574520,N,11421.9415289,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100435.000,1.069,0.219,0.223,0.301,1.190,1.364,5.912*75



2025-07-31 18:04:35:879 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:04:36:018 ==>> [W][05:19:14][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 18:04:36:333 ==>> [D][05:19:14][COMM]read battery soc:255
$GBGGA,100436.000,2301.2574717,N,11421.9415432,E,1,11,1.41,72.613,M,-1.770,M,,*59

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,41,8,81,181,40,26,65,31,41,16,65,309,39,1*71

$GBGSV,6,2,21,42,64,1,40,38,64,166,42,39,63,338,39,3,62,191,40,1*49

$GBGSV,6,3,21,24,60,228,43,9,58,275,37,6,54,7,37,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,36,1*75

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*4A

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100436.000,A,2301.2574717,N,11421.9415432,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100436.000,1.156,0.243,0.248,0.330,1.150,1.295,5.438*7D



2025-07-31 18:04:37:029 ==>> [D][05:19:15][CAT1]SEND RAW data timeout
[D][05:19:15][CAT1]exec over: func id: 12, ret: -52
[D][05:19:15][CAT1]gsm read msg sub id: 12
[D][05:19:15][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:15][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:15][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:04:37:265 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 18:04:37:272 ==>> 检测【关闭GSM联网】
2025-07-31 18:04:37:278 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:04:37:516 ==>> $GBGGA,100437.000,2301.2575065,N,11421.9415660,E,1,11,1.41,72.840,M,-1.770,M,,*56

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,263,41,8,81,181,40,26,65,31,41,16,65,309,39,1*71

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,41,1*4B

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,37,59,52,130,40,1*77

$GBGSV,6,4,21,1,48,126,38,21,47,107,40,2,46,239,37,60,41,238,40,1*74

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,41,5*4B

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100437.000,A,2301.2575065,N,11421.9415660,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,100437.000,1.185,0.259,0.264,0.350,1.104,1.231,5.065*72

[D][05:19:15][PROT]CLEAN,SEND:2
[D][05:19:15][PROT]CLEAN:2
[D][05:19:15][PROT]index:0 1629955155
[D][05:19:15][PROT]is_send:0
[D][05:19:15][PROT]sequence_num:7
[D][05:19:15][PROT]retry_timeout:0
[D][05:19:15][PROT]retry_times:1
[D][05:19:15][PROT]send_path:0x2
[D][05:19:15][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:15][PROT]===============================

2025-07-31 18:04:37:621 ==>> ============================
[D][05:19:15][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:15][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955155]
[D][05:19:15][PROT]===========================================================
[D][05:19:15][PROT]sending traceid [9999999999900008]
[D][05:19:15][PROT]Send_TO_M2M [1629955155]
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:15][SAL ]sock send credit cnt[6]
[D][05:19:15][SAL ]sock send ind credit cnt[6]
[D][05:19:15][M2M ]m2m send data len[294]
[D][05:19:15][SAL ]Cellular task submsg id[10]
[D][05:19:15][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:15][CAT1]gsm read msg sub id: 15
[D][05:19:15][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:15][CAT1]<<< 
ERROR

[W][05:19:15][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:15][COMM]GSM test
[D][05:19:15][COMM]GSM test disable


2025-07-31 18:04:37:810 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:04:37:818 ==>> 检测【4G联网测试】
2025-07-31 18:04:37:840 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:04:38:646 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:16][COMM]Main Task receive event:14
[D][05:19:16][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955156, allstateRepSeconds = 0
[D][05:19:16][COMM]index:0,power_mode:0xFF
[D][05:19:16][COMM]index:1,sound_mode:0xFF
[D][05:19:16][COMM]index:2,gsensor_mode:0xFF
[D][05:19:16][COMM]index:3,report_freq_mode:0xFF
[D][05:19:16][COMM]index:4,report_period:0xFF
[D][05:19:16][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:16][COMM]index:6,normal_reset_period:0xFF
[D][05:19:16][COMM]index:7,spock_over_speed:0xFF
[D][05:19:16][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:16][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:16][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:16][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:16][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:16][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:16][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:16][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:16][COMM]index:16,imu_config_params:0xFF
[D][05:19:16][COMM]index:17,long_connect_params:0xFF
[D][05:19:16][COMM]index:18,detain_mark:0xFF
[D][05:19:16][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:16][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:16][COMM]index:21,mc_mo

2025-07-31 18:04:38:750 ==>> de:0xFF
[D][05:19:16][COMM]index:22,S_mode:0xFF
[D][05:19:16][COMM]index:23,overweight:0xFF
[D][05:19:16][COMM]index:24,standstill_mode:0xFF
[D][05:19:16][COMM]index:25,night_mode:0xFF
[D][05:19:16][COMM]index:26,experiment1:0xFF
[D][05:19:16][COMM]index:27,experiment2:0xFF
[D][05:19:16][COMM]index:28,experiment3:0xFF
[D][05:19:16][COMM]index:29,experiment4:0xFF
[D][05:19:16][COMM]index:30,night_mode_start:0xFF
[D][05:19:16][COMM]index:31,night_mode_end:0xFF
[D][05:19:16][COMM]index:33,park_report_minutes:0xFF
[D][05:19:16][COMM]index:34,park_report_mode:0xFF
[D][05:19:16][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:16][COMM]index:38,charge_battery_para: FF
[D][05:19:16][COMM]index:39,multirider_mode:0xFF
[D][05:19:16][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:16][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:16][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:16][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:16][COMM]index:44,riding_duration_config:0xFF
[D][05:19:16][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:16][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:16][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:16][COMM

2025-07-31 18:04:38:856 ==>> ]index:48,shlmt_sensor_en:0xFF
[D][05:19:16][COMM]index:49,mc_load_startup:0xFF
[D][05:19:16][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:16][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:16][COMM]index:52,traffic_mode:0xFF
[D][05:19:16][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:16][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:16][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:16][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:16][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:16][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:16][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:16][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:16][COMM]index:63,experiment5:0xFF
[D][05:19:16][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:16][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:16][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:16][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:16][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:16][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:16][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:16][COMM]index:72,experiment6:0xFF
[D][05:19:16][COMM]index

2025-07-31 18:04:38:961 ==>> :73,experiment7:0xFF
[D][05:19:16][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:16][COMM]index:75,zero_value_from_server:-1
[D][05:19:16][COMM]index:76,multirider_threshold:255
[D][05:19:16][COMM]index:77,experiment8:255
[D][05:19:16][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:16][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:16][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:16][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:16][COMM]index:83,loc_report_interval:255
[D][05:19:16][COMM]index:84,multirider_threshold_p2:255
[D][05:19:16][COMM]index:85,multirider_strategy:255
[D][05:19:16][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:16][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:16][COMM]index:90,weight_param:0xFF
[D][05:19:16][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:16][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:16][COMM]index:95,current_limit:0xFF
[D][05:19:16][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:16][COMM]index:100,location_mode:0xFF

[W][05:19:16][PROT]remove success[1629955156],send_path[2],type[0000],priority[0],inde

2025-07-31 18:04:39:066 ==>> x[0],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][COMM]read battery soc:255
$GBGGA,100438.000,2301.2575198,N,11421.9415785,E,1,11,1.41,73.014,M,-1.770,M,,*58

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,81,181,40,26,65,31,41,16,65,309,39,1*71

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,41,1*4B

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,41,5*4B

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100438.000,A,2301.2575198,N,11421.9415785,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,100438.000,1.311,0.242,0.246,0.329,1.158,1.267,4.803*75



2025-07-31 18:04:39:321 ==>> $GBGGA,100439.000,2301.2575431,N,11421.9415783,E,1,11,1.41,73.019,M,-1.770,M,,*54

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,81,181,39,26,65,31,41,16,65,309,39,1*7F

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,41,1*4B

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*4A

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100439.000,A,2301.2575431,N,11421.9415783,E,0.000,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,100439.000,1.407,0.210,0.214,0.289,1.199,1.296,4.586*7C



2025-07-31 18:04:40:109 ==>> [D][05:19:18][M2M ]get csq[-1]


2025-07-31 18:04:40:351 ==>> $GBGGA,100440.000,2301.2575758,N,11421.9415926,E,1,11,1.41,73.107,M,-1.770,M,,*59

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,81,181,39,26,65,31,41,16,65,309,39,1*7F

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,41,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*4A

$GBGSV,2,2,07,24,60,228,43,21,47,107,41,33,16,322,30,5*4E

$GBRMC,100440.000,A,2301.2575758,N,11421.9415926,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100440.000,1.180,0.238,0.242,0.323,0.991,1.085,4.288*78

[D][05:19:18][COMM]read battery soc:255


2025-07-31 18:04:40:427 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 18:04:41:332 ==>> $GBGGA,100441.000,2301.2576082,N,11421.9416045,E,1,11,1.41,73.179,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,40,26,65,31,41,16,65,309,39,1*70

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,41,1*7A

$GBGSV,6,5,21,4,32,112,34,45,23,199,33,5,22,258,34,33,16,322,35,1*78

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,41,5*4B

$GBGSV,2,2,07,24,60,228,43,21,47,107,41,33,16,322,30,5*4E

$GBRMC,100441.000,A,2301.2576082,N,11421.9416045,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100441.000,1.192,0.200,0.203,0.275,0.980,1.066,4.111*78



2025-07-31 18:04:42:349 ==>> $GBGGA,100442.000,2301.2576058,N,11421.9416169,E,1,11,1.41,73.298,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,40,2,46,239,36,60,41,238,40,1*75

$GBGSV,6,5,21,4,32,112,34,45,23,199,33,5,22,258,34,33,16,322,35,1*78

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,41,5*4B

$GBGSV,2,2,07,24,60,228,43,21,47,107,41,33,16,322,30,5*4E

$GBRMC,100442.000,A,2301.2576058,N,11421.9416169,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100442.000,1.340,0.261,0.266,0.353,1.085,1.161,4.019*75

[D][05:19:20][COMM]read battery soc:255


2025-07-31 18:04:43:327 ==>> $GBGGA,100443.000,2301.2576096,N,11421.9415937,E,1,11,1.41,73.369,M,-1.770,M,,*56

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,41,5*4B

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100443.000,A,2301.2576096,N,11421.9415937,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,100443.000,1.367,0.242,0.247,0.329,1.093,1.163,3.892*77



2025-07-31 18:04:43:860 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:04:44:016 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 18:04:44:702 ==>> [D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:22][COMM]index:21,mc_mode:0xFF
[D][05

2025-07-31 18:04:44:806 ==>> :19:22][COMM]index:22,S_mode:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:22][COMM]index:48,

2025-07-31 18:04:44:911 ==>> shlmt_sensor_en:0xFF
[D][05:19:22][COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:72,experiment6:0xFF
[D][05:19:22][COMM]index:7

2025-07-31 18:04:45:016 ==>> 3,experiment7:0xFF
[D][05:19:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162],send_path[2],type[0000],priority[0],index[0],

2025-07-31 18:04:45:121 ==>> used[0]
[D][05:19:22][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:22][PROT]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,100444.000,2301.2576189,N,11421.9415894,E,1,11,1.41,73.474,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,26,16,42,38,39,24,09,21,33,,2.36,1.41,1.90,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,40,26,65,31,41,16,65,309,39,1*70

$GBGSV,6,2,21,42,64,1,40,38,64,166,41,39,63,338,39,3,62,191,40,1*4A

$GBGSV,6,3,21,24,60,228,42,9,58,275,37,6,54,7,36,59,52,130,40,1*76

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,34,45,23,199,32,5,22,258,34,33,16,322,35,1*79

$GBGSV,6,6,21,14,,,35,1*76

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*4A

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100444.000,A,2301.2576189,N,11421.9415894,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100444.000,1.371,0.235,0.239,0.320,1.085,1.150,3.768*7A

[D][05:19:22][COMM]read battery soc:255


2025-07-31 18:04:45:376 ==>> $GBGGA,100445.000,2301.2576232,N,11421.9415772,E,1,13,1.27,73.538,M,-1.770,M,,*53

$GBGSA,A,3,13,08,26,16,06,42,38,39,24,09,21,33,1.97,1.27,1.51,4*02

$GBGSA,A,3,45,,,,,,,,,,,,1.97,1.27,1.51,4*07

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,6,65,303,36,42,64,1,40,38,64,166,41,39,63,338,39,1*40

$GBGSV,6,3,21,3,62,191,40,24,60,228,42,9,58,275,37,59,52,130,40,1*79

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,14,41,332,35,4,32,112,34,5,22,258,34,33,16,322,35,1*7D

$GBGSV,6,6,21,45,9,39,32,1*46

$GBGSV,2,1,07,26,65,31,41,42,64,1,43,38,64,166,40,39,63,338,40,5*4B

$GBGSV,2,2,07,24,60,228,42,21,47,107,41,33,16,322,30,5*4F

$GBRMC,100445.000,A,2301.2576232,N,11421.9415772,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,100445.000,1.411,0.193,0.205,0.267,1.108,1.168,3.673*7C



2025-07-31 18:04:46:362 ==>> [D][05:19:24][M2M ]get csq[-1]
$GBGGA,100446.000,2301.2576198,N,11421.9415904,E,1,14,1.23,73.603,M,-1.770,M,,*54

$GBGSA,A,3,13,08,26,16,06,42,38,39,24,09,21,14,1.95,1.23,1.51,4*01

$GBGSA,A,3,33,45,,,,,,,,,,,1.95,1.23,1.51,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,6,65,303,36,42,64,1,40,38,64,166,41,39,63,338,39,1*40

$GBGSV,6,3,21,3,62,191,40,24,60,228,42,9,58,275,37,59,52,130,40,1*79

$GBGSV,6,4,21,1,48,126,38,21,47,107,39,2,46,239,36,60,41,238,40,1*7B

$GBGSV,6,5,21,14,41,332,35,4,32,112,34,5,22,258,34,33,16,322,35,1*7D

$GBGSV,6,6,21,45,9,39,32,1*46

$GBGSV,2,1,08,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*45

$GBGSV,2,2,08,24,60,228,43,21,47,107,41,33,16,322,30,45,9,39,32,5*72

$GBRMC,100446.000,A,2301.2576198,N,11421.9415904,E,0.003,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,100446.000,1.657,0.251,0.267,0.338,1.289,1.341,3.671*7C

[D][05:19:24][COMM]read battery soc:255


2025-07-31 18:04:46:467 ==>>      RESEND ALLSTATE<<<<<
[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:24][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:24][COMM][LOC]wifi scan is already running, error
[D][05:19:24][COMM]Main Task receive event:14 finished processi

2025-07-31 18:04:46:497 ==>> ng
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 18:04:47:524 ==>> $GBGGA,100447.000,2301.2576450,N,11421.9416013,E,1,14,1.23,73.593,M,-1.770,M,,*52

$GBGSA,A,3,13,08,26,16,06,42,38,39,24,09,21,14,1.95,1.23,1.51,4*01

$GBGSA,A,3,33,45,,,,,,,,,,,1.95,1.23,1.51,4*01

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,6,65,303,36,42,64,1,40,38,64,166,41,39,63,338,39,1*40

$GBGSV,6,3,21,3,62,191,40,24,60,228,42,9,58,275,37,59,52,130,40,1*79

$GBGSV,6,4,21,1,48,126,37,21,47,107,39,2,46,239,36,60,41,238,41,1*75

$GBGSV,6,5,21,14,41,332,35,4,32,112,34,5,22,258,34,33,16,322,35,1*7D

$GBGSV,6,6,21,45,9,39,32,1*46

$GBGSV,2,1,08,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*45

$GBGSV,2,2,08,24,60,228,43,21,47,107,41,33,16,322,30,45,9,39,33,5*73

$GBRMC,100447.000,A,2301.2576450,N,11421.9416013,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,100447.000,1.547,0.251,0.267,0.339,1.199,1.250,3.526*7C

[D][05:19:25][CAT1]exec over: func id: 15, ret: -93
[D][05:19:25][CAT1]sub id: 15, ret: -93

[D][05:19:25][SAL ]Cellular task submsg id[68]
[D][05:19:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:25][SAL ]socket send fail. id[4]
[D][05:19:25][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:25][CAT1]gsm read msg sub id: 13
[D][05:19:25][M2M ]m

2025-07-31 18:04:47:629 ==>> 2m select fd[4]
[D][05:19:25][M2M ]socket[4] Link is disconnected
[D][05:19:25][M2M ]tcpclient close[4]
[D][05:19:25][SAL ]socket[4] has closed
[D][05:19:25][PROT]protocol read data ok
[E][05:19:25][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:25][CAT1]tx ret[8] >>> AT+CSQ

[E][05:19:25][PROT]M2M Send Fail [1629955165]
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:25][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:25][CAT1]exec over: func id: 13, ret: 21
[D][05:19:25][CAT1]gsm read msg sub id: 21
[D][05:19:25][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:25][CAT1]<<< 
OK

[D][05:19:25][CAT1]cell info report total[0]
[D][05:19:25][CAT1]exec over: func id: 21, ret: 6
[D][05:19:25][CAT1]gsm read msg sub id: 13
[D][05:19:25][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:25][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:25][CAT1]exec over: func id: 13, ret: 21
[D][05:19:25][CAT1]gsm read msg sub id: 10
[D][05:19:25][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:25][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:25][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 18:04:47:930 ==>> [D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 10, ret: 6
[D][05:19:26][CAT1]sub id: 10, ret: 6

[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:26][M2M ]m2m gsm shut done, ret[0]
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:26][SAL ]open socket ind id[4], rst[0]
[D][05:19:26][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:26][SAL ]Cellular task submsg id[8]
[D][05:19:26][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:26][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:26][CAT1]gsm read msg sub id: 8
[D][05:19:26][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:26][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:26][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:04:48:110 ==>> [D][05:19:26][CAT1]pdpdeact urc len[22]


2025-07-31 18:04:48:380 ==>> $GBGGA,100448.000,2301.2576558,N,11421.9415883,E,1,18,0.93,73.530,M,-1.770,M,,*59

$GBGSA,A,3,13,08,26,16,06,42,38,03,39,24,09,59,1.61,0.93,1.31,4*0F

$GBGSA,A,3,21,01,60,14,33,45,,,,,,,1.61,0.93,1.31,4*07

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,41,16,65,309,39,1*7E

$GBGSV,6,2,21,6,65,303,36,42,64,1,40,38,64,166,41,3,64,190,40,1*70

$GBGSV,6,3,21,39,63,338,39,24,60,228,42,9,58,275,37,59,52,126,40,1*49

$GBGSV,6,4,21,21,47,107,40,1,47,123,38,2,46,239,36,60,44,243,41,1*77

$GBGSV,6,5,21,14,41,332,35,4,32,112,34,5,22,258,34,33,16,322,35,1*7D

$GBGSV,6,6,21,45,9,39,32,1*46

$GBGSV,2,1,08,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*45

$GBGSV,2,2,08,24,60,228,42,21,47,107,41,33,16,322,30,45,9,39,33,5*72

$GBRMC,100448.000,A,2301.2576558,N,11421.9415883,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100448.000,1.484,0.232,0.241,0.321,1.143,1.192,3.392*76

[D][05:19:26][COMM]read battery soc:255


2025-07-31 18:04:49:374 ==>> [D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:27][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:27][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:27][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:27][CAT1]tx ret[11] >>> AT+QIACT?

$GBGGA,100449.000,2301.2576464,N,11421.9415770,E,1,18,0.93,73.486,M,-1.770,M,,*59

$GBGSA,A,3,13,08,26,16,06,42,38,03,39,24,09,59,1.61,0.93,1.31,4*0F

$GBGSA,A,3,21,01,60,14,33,45,,,,,,,1.61,0.93,1.31,4*07

$GBGSV,6,1,21,13,82,262,40,8,80,181,39,26,65,31,40,16,65,309,38,1*7E

$GBGSV,6,2,21,6,65,303,36,42,64,1,40,38,64,166,41,3,64,190,40,1*70

$GBGSV,6,3,21,39,63,338,39,24,60,228,42,9,58,275,37,59,52,126,40,1*49

$GBGSV,6,4,21,21,47,107,39,1,47,123,37,2,46,239,36,60,44,243,40,1*77

$GBGSV,6,5,21,14,41,332,35,4,32,112,34,5,22,258,34,33,16,322,35,1*7D

$GBGSV,6,6,21,45,9,39,32,1*46

$GBGSV,2,1,08,26,65,31,41,42,64,1,43,38,64,166,41,39,63,338,40,5*45

$GBGSV,2,2,08,24,60,228,43,21,47,107,41,33,16,322,30,45,9,39,33,5*73

$GBRMC,100449.000,A,2301.2576464,N,11421.9415770,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,100449.000,1.455,0.250,0.260,0.345,1.115,1.161,3.283*70

[D][05:19:27][CAT1]

2025-07-31 18:04:49:419 ==>> <<< 
OK

[D][05:19:27][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:04:49:737 ==>>                         d : 0, 0
[D][05:19:28][SAL ]Cellular task submsg id[68]
[D][05:19:28][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:28][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:28][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:28][M2M ]g_m2m_is_idle become true
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:28][PROT]index:1 1629955168
[D][05:19:28][PROT]is_send:0
[D][05:19:28][PROT]sequence_num:11
[D][05:19:28][PROT]retry_timeout:0
[D][05:19:28][PROT]retry_times:1
[D][05:19:28][PROT]send_path:0x2
[D][05:19:28][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:28][PROT]===========================================================
[W][05:19:28][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955168]
[D][05:19:28][PROT]===========================================================
[D][05:19:28][PROT]sending traceid [999999999990000C]
[D][05:19:28][PROT]Send_TO_M2M [1629955168]
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:28][SAL ]sock send credit cnt[6]
[D][05:19:28][SAL ]sock send ind credit cnt[6]
[D][05:19:28][M2M ]m2m send data len[166]
[D][05:19:28][SAL ]Cellular task submsg id[10]
[D][05:19:28][SAL ]cellular SEND socket id[0] 

2025-07-31 18:04:49:843 ==>> type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:28][CAT1]gsm read msg sub id: 15
[D][05:19:28][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:28][CAT1]Send Data To Server[166][169] ... ->:
0053B987113311331133113311331B88B0DC7086C0CBDF6CF3E2AB56C178F9B0C5CC4D978CEC19459A605242BEFE4F7F7F72D5C239333755B39748C43B89EAF882A7F8D3F8318D3938F44B770EEC08404F2236
[D][05:19:28][CAT1]<<< 
SEND OK

[D][05:19:28][CAT1]exec over: func id: 15, ret: 11
[D][05:19:28][CAT1]sub id: 15, ret: 11

[D][05:19:28][SAL ]Cellular task submsg id[68]
[D][05:19:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:28][M2M ]g_m2m_is_idle become true
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:28][PROT]M2M Send ok [1629955168]


2025-07-31 18:04:49:941 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:04:49:953 ==>> 检测【关闭GPS】
2025-07-31 18:04:49:981 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:04:50:319 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]stop event:8
[D][05:19:28][GNSS]GPS stop. ret=0
[D][05:19:28][GNSS]all continue location stop
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]all sing location stop
[D][05:19:28][CAT1]gsm read msg sub id: 24
[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:28][COMM]read battery soc:255
[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]exec over: func id: 24, ret: 6
[D][05:19:28][CAT1]sub id: 24, ret: 6



2025-07-31 18:04:50:489 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:04:50:497 ==>> 检测【清空消息队列2】
2025-07-31 18:04:50:523 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:04:50:843 ==>> [D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100

2025-07-31 18:04:50:948 ==>> 025. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100030. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100030. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100031. period:10000. 

2025-07-31 18:04:51:053 ==>> j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100031. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100032. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100032. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100032. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100033. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100033. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100033
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100034
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100034
[D][05:19:29][GNSS]recv submsg id[1]
[D][05:19:29][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:29][GNSS]location stop evt done evt
[W][05:19:29][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:29][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:04:51:285 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:04:51:293 ==>> 检测【轮动检测】
2025-07-31 18:04:51:301 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:04:51:358 ==>> 3A A3 01 00 A3 


2025-07-31 18:04:51:448 ==>> OFF_OUT1
OVER 150


2025-07-31 18:04:51:523 ==>> [D][05:19:30][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:04:51:794 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:04:51:854 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:04:52:098 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:04:52:109 ==>> 检测【关闭小电池】
2025-07-31 18:04:52:125 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:04:52:161 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:04:52:236 ==>> [D][05:19:30][COMM]read battery soc:255


2025-07-31 18:04:52:375 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:04:52:383 ==>> 检测【进入休眠模式】
2025-07-31 18:04:52:400 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:04:52:574 ==>> [W][05:19:31][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:31][COMM]Main Task receive event:28
[D][05:19:31][COMM]main task tmp_sleep_event = 8
[D][05:19:31][COMM]prepare to sleep
[D][05:19:31][CAT1]gsm read msg sub id: 12
[D][05:19:31][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:04:53:379 ==>> [D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 12, ret: 6
[D][05:19:31][M2M ]tcpclient close[4]
[D][05:19:31][SAL ]Cellular task submsg id[12]
[D][05:19:31][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:31][CAT1]gsm read msg sub id: 9
[D][05:19:31][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 9, ret: 6
[D][05:19:31][CAT1]sub id: 9, ret: 6

[D][05:19:31][SAL ]Cellular task submsg id[68]
[D][05:19:31][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:31][SAL ]socket close ind. id[4]
[D][05:19:31][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:31][COMM]1x1 frm_can_tp_send ok
[D][05:19:31][CAT1]pdpdeact urc len[22]


2025-07-31 18:04:53:665 ==>> [E][05:19:32][COMM]1x1 rx timeout
[D][05:19:32][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:04:54:196 ==>> [E][05:19:32][COMM]1x1 rx timeout
[D][05:19:32][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:32][COMM]1x1 tp timeout
[E][05:19:32][COMM]1x1 error -3.
[W][05:19:32][COMM]CAN STOP!
[D][05:19:32][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:32][COMM]------------ready to Power off Acckey 1------------
[D][05:19:32][COMM]------------ready to Power off Acckey 2------------
[D][05:19:32][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:32][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1315
[D][05:19:32][COMM]bat sleep fail, reason:-1
[D][05:19:32][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:32][COMM]accel parse set 0
[D][05:19:32][COMM]imu rest ok. 103641
[D][05:19:32][COMM]imu sleep 0
[W][05:19:32][COMM]now sleep


2025-07-31 18:04:54:523 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:04:54:532 ==>> 检测【检测33V休眠电流】
2025-07-31 18:04:54:540 ==>> 开始33V电流采样
2025-07-31 18:04:54:571 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:04:54:625 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:04:55:628 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:04:55:672 ==>> Current33V:????:16.30

2025-07-31 18:04:56:133 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:04:56:141 ==>> 【检测33V休眠电流】通过,【16.3uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:04:56:164 ==>> 该项需要延时执行
2025-07-31 18:04:58:155 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:04:58:176 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:04:58:192 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:04:58:260 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1651mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 2023mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:04:59:189 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:04:59:202 ==>> 检测【打开小电池2】
2025-07-31 18:04:59:223 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:04:59:263 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:04:59:467 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:04:59:475 ==>> 该项需要延时执行
2025-07-31 18:04:59:973 ==>> 此处延时了:【500】毫秒
2025-07-31 18:04:59:986 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:05:00:009 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:05:00:049 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:05:00:291 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:05:00:305 ==>> 该项需要延时执行
2025-07-31 18:05:00:398 ==>> [D][05:19:38][COMM]------------ready to Power on Acckey 1------------
[D][05:19:38][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:38][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 4,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 5,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 6,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 7,volt = 10
[D][05:19:38][FCTY]get_ext_48v_vol retry i = 8,volt = 10
[D][05:19:38][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 10
[D][05:19:38][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:38][COMM]----- get Acckey 1 and value:1------------
[W][05:19:38][COMM]CAN START!
[D][05:19:38][CAT1]gsm read msg sub id: 12
[D][05:19:38][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:38][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 109772
[D][05:19:38][COMM][Audio]exec status ready.
[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]exec over: fun

2025-07-31 18:05:00:443 ==>> c id: 12, ret: 6
[D][05:19:38][COMM]imu wakeup ok. 109786
[D][05:19:38][COMM]imu wakeup 1
[W][05:19:38][COMM]wake up system, wakeupEvt=0x80
[D][05:19:38][COMM]frm_can_weigth_power_set 1
[D][05:19:38][COMM]Clear Sleep Block Evt
[D][05:19:38][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:38][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:05:00:641 ==>> [E][05:19:39][COMM]1x1 rx timeout
[D][05:19:39][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:05:00:746 ==>> [D][05:19:39][COMM]msg 02A0 loss. last_tick:109757. cur_tick:110266. period:50
[D][05:19:39][COMM]msg 02A4 loss. last_tick:109757. cur_tick:110267. period:50
[D][05:19:39][COMM]msg 02A5 loss. last_tick:109758. cur_tick:110267. period:50
[D][05:19:39][COMM]msg 02A6 loss. last_tick:109758. cur_tick:110267. period:50
[D][05:19:39][COMM]msg 02A7 loss. last_tick:109758. cur_tick:110268. period:50
[D][05:19:39][COMM]CAN me

2025-07-31 18:05:00:776 ==>> ssage fault change: 0x0000000000000000->0x0000E00000220000 110268
[D][05:19:39][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 110268


2025-07-31 18:05:00:806 ==>> 此处延时了:【500】毫秒
2025-07-31 18:05:00:822 ==>> 检测【进入休眠模式2】
2025-07-31 18:05:00:841 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:05:00:911 ==>> [W][05:19:39][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:05:01:121 ==>> [E][05:19:39][COMM]1x1 rx timeout
[E][05:19:39][COMM]1x1 tp timeout
[E][05:19:39][COMM]1x1 error -3.
[D][05:19:39][COMM]Main Task receive event:28 finished processing
[D][05:19:39][COMM]Main Task receive event:28
[D][05:19:39][COMM]prepare to sleep
[D][05:19:39][CAT1]gsm read msg sub id: 12
[D][05:19:39][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 12, ret: 6
[D][05:19:39][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:39][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:05:01:436 ==>> [D][05:19:39][COMM]msg 0220 loss. last_tick:109757. cur_tick:110762. period:100
[D][05:19:39][COMM]msg 0221 loss. last_tick:109757. cur_tick:110762. period:100
[D][05:19:39][COMM]msg 0224 loss. last_tick:109757. cur_tick:110763. period:100
[D][05:19:39][COMM]msg 0260 loss. last_tick:109757. cur_tick:110763. period:100
[D][05:19:39][COMM]msg 0280 loss. last_tick:109757. cur_tick:110763. period:100
[D][05:19:39][COMM]msg 02C0 loss. last_tick:109757. cur_tick:110764. period:100
[D][05:19:39][COMM]msg 02C1 loss. last_tick:109757. cur_tick:110764. period:100
[D][05:19:39][COMM]msg 02C2 loss. last_tick:109757. cur_tick:110765. period:100
[D][05:19:39][COMM]msg 02E0 loss. last_tick:109757. cur_tick:110765. period:100
[D][05:19:39][COMM]msg 02E1 loss. last_tick:109757. cur_tick:110765. period:100
[D][05:19:39][COMM]msg 02E2 loss. last_tick:109757. cur_tick:110766. period:100
[D][05:19:39][COMM]msg 0300 loss. last_tick:109758. cur_tick:110766. period:100
[D][05:19:39][COMM]msg 0301 loss. last_tick:109758. cur_tick:110766. period:100
[D][05:19:39][COMM]bat msg 0240 loss. last_tick:109758. cur_tick:110767. period:100. j,i:1 54
[D][05:19:39][COMM]bat msg 0241 loss. last_tick:109758. cur_tick:110767. period:

2025-07-31 18:05:01:541 ==>> 100. j,i:2 55
[D][05:19:39][COMM]bat msg 0242 loss. last_tick:109758. cur_tick:110768. period:100. j,i:3 56
[D][05:19:39][COMM]bat msg 0244 loss. last_tick:109758. cur_tick:110768. period:100. j,i:5 58
[D][05:19:39][COMM]bat msg 024E loss. last_tick:109758. cur_tick:110768. period:100. j,i:15 68
[D][05:19:39][COMM]bat msg 024F loss. last_tick:109758. cur_tick:110769. period:100. j,i:16 69
[D][05:19:39][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 110769
[D][05:19:39][COMM]CAN message bat fault change: 0x00000000->0x0001802E 110770
[D][05:19:39][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 110770
[D][05:19:39][GNSS]handler GSMGet Base timeout
                                                                              

2025-07-31 18:05:01:801 ==>> [D][05:19:40][COMM]msg 0222 loss. last_tick:109757. cur_tick:111264. period:150
[D][05:19:40][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 111265
[D][05:19:40][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:40][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:40][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:05:01:995 ==>> [E][05:19:40][COMM]1x1 rx timeout
[E][05:19:40][COMM]1x1 tp timeout
[E][05:19:40][COMM]1x1 error -3.
[W][05:19:40][COMM]CAN STOP!
[D][05:19:40][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:40][COMM]------------ready to Power off Acckey 1------------
[D][05:19:40][COMM]------------ready to Power off Acckey 2------------
[D][05:19:40][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:40][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 118
[D][05:19:40][COMM]bat sleep fail, reason:-1
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:40][COMM]accel parse set 0
[D][05:19:40][COMM]imu rest ok. 111444
[D][05:19:40][COMM]imu sleep 0
[W][05:19:40][COMM]now sleep


2025-07-31 18:05:02:118 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:05:02:138 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:05:02:171 ==>> 开始小电池电流采样
2025-07-31 18:05:02:188 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:05:02:222 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:05:03:234 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:05:03:295 ==>> CurrentBattery:ƽ��:1154.71

2025-07-31 18:05:03:738 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:05:03:843 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:05:04:843 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:05:04:918 ==>> CurrentBattery:ƽ��:71.29

2025-07-31 18:05:05:351 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:05:05:361 ==>> 【检测小电池休眠电流】通过,【71.29uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:05:05:390 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:05:05:420 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:05:05:457 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:05:05:688 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:05:05:699 ==>> 该项需要延时执行
2025-07-31 18:05:05:714 ==>> [D][05:19:44][COMM]------------ready to Power on Acckey 1------------
[D][05:19:44][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:44][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:44][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:44][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[W][05:19:44][COMM]CAN START!
[D][05:19:44][CAT1]gsm read msg sub id: 12
[D][05:19:44][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:44][COMM]CAN message bat fault change: 0x0001802E->0x00000000 115103
[D][05:19:44][COMM][Audio]exec status ready.
[D][05:19:44][CAT1]<<< 
OK

[D][05:19:44][CAT1]exec over: func id: 12, ret: 6
[D][05:19:44][COMM]imu wakeup ok. 115117
[D][05:19:44][COMM]imu wakeup 1
[W][05:19:44][COMM]wake up system, wakeupEvt=0x80
[D][05:19:44][COMM]frm_can_weigth_power_set 1
[D][05:19:44][COMM]Clear Sleep Block Evt
[D][05:19:44][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:44][COMM]1x1 frm_can_tp_send ok
[D][05:19:44][COMM]read battery soc:0


2025-07-31 18:05:05:952 ==>> [E][05:19:44][COMM]1x1 rx timeout
[D][05:19:44][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:05:06:057 ==>> [D][05:19:44][COMM]msg 02A0 loss. last_tick:115085. cur_tick:115597. period:50
[D][05:1

2025-07-31 18:05:06:117 ==>> 9:44][COMM]msg 02A4 loss. last_tick:115085. cur_tick:115597. period:50
[D][05:19:44][COMM]msg 02A5 loss. last_tick:115085. cur_tick:115598. period:50
[D][05:19:44][COMM]msg 02A6 loss. last_tick:115085. cur_tick:115598. period:50
[D][05:19:44][COMM]msg 02A7 loss. last_tick:115085. cur_tick:115598. period:50
[D][05:19:44][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 115599
[D][05:19:44][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 115599


2025-07-31 18:05:06:192 ==>> 此处延时了:【500】毫秒
2025-07-31 18:05:06:218 ==>> 检测【检测唤醒】
2025-07-31 18:05:06:232 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:05:06:918 ==>> [W][05:19:44][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:44][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:44][FCTY]==========Modules-nRF5340 ==========
[D][05:19:44][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:44][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:44][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:44][FCTY]DeviceID    = 460130071539006
[D][05:19:44][FCTY]HardwareID  = 867222087567865
[D][05:19:44][FCTY]MoBikeID    = 9999999999
[D][05:19:44][FCTY]LockID      = FFFFFFFFFF
[D][05:19:44][FCTY]BLEFWVersion= 105
[D][05:19:44][FCTY]BLEMacAddr   = D73B1AC7DE82
[D][05:19:44][FCTY]Bat         = 3864 mv
[D][05:19:44][FCTY]Current     = 0 ma
[D][05:19:44][FCTY]VBUS        = 2600 mv
[D][05:19:44][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:19:44][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:44][FCTY]Ext battery vol = 33, adc = 1312
[D][05:19:44][FCTY]Acckey1 vol = 5598 mv, Acckey2 vol = 328 mv
[D][05:19:44][FCTY]Bike Type flag is invalied
[D][05:19:44][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:44][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:44][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:44][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:44][FC

2025-07-31 18:05:07:013 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:05:07:049 ==>> 检测【关机】
2025-07-31 18:05:07:064 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:05:07:096 ==>> TY]CAT1_GNSS_PLATFORM = C4
[D][05:19:44][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:44][FCTY]Bat1         = 3833 mv
[D][05:19:44][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:44][FCTY]==========Modules-nRF5340 ==========
[E][05:19:44][COMM]1x1 rx timeout
[E][05:19:44][COMM]1x1 tp timeout
[E][05:19:44][COMM]1x1 error -3.
[D][05:19:44][COMM]Main Task receive event:28 finished processing
[D][05:19:44][COMM]Main Task receive event:65
[D][05:19:44][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:44][COMM]Main Task receive event:65 finished processing
[D][05:19:44][COMM]Main Task receive event:60
[D][05:19:44][COMM]smart_helmet_vol=255,255
[D][05:19:44][COMM]report elecbike
[W][05:19:44][PROT]remove success[1629955184],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:44][PROT]add success [1629955184],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:44][COMM]Main Task receive event:60 finished processing
[D][05:19:44][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:44][PROT]index:0
[D][05:19:44][PROT]is_send:1
[D][05:19:44][PROT]sequence_num:12
[D][05:19:44][PROT]retry_timeout:0
[D][05:19:44][

2025-07-31 18:05:07:135 ==>> PROT]retry_times:3
[D][05:19:44][PROT]send_path:0x3
[D][05:19:44][PROT]msg_type:0x5d03
[D][05:19:44][PROT]===========================================================
[W][05:19:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955184]
[D][05:19:44][PROT]===========================================================
[D][05:19:44][PROT]Sending traceid[999999999990000D]
[D][05:19:44][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:44][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:44][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:44][SAL ]open socket ind id[4], rst[0]
[D][05:19:44][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:44][SAL ]Cellular task submsg id[8]
[D][05:19:44][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:44][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:44][M2M ]m2m sw

2025-07-31 18:05:07:233 ==>> itch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:44][CAT1]gsm read msg sub id: 8
[D][05:19:44][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:44][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:44][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:45][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:45][CAT1]<<< 
+CME ERROR: 100

[D][05:19:45][COMM]msg 0220 loss. last_tick:115085. cur_tick:116086. period:100
[D][05:19:45][COMM]msg 0221 loss. last_tick:115085. cur_tick:116086. period:100
[D][05:19:45][COMM]msg 0224 loss. last_tick:115085. cur_tick:116087. period:100
[D][05:19:45][COMM]msg 0260 loss. last_tick:115085. cur_tick:116087. period:100
[D][05:19:45][COMM]msg 0280 loss. last_tick:115085. cur_tick:116088. period:100
[D][05:19:45][COMM]msg 02C0 loss. last_tick:115085. cur_tick:116088. period:100
[D][05:19:45][COMM]msg 02C1 loss. last_tick:115085. cur_tick:116088. period:100
[D][05:19:45][COMM]msg 02C2 loss. last_tick:115085. cur_tick:116088. period:100
[D][05:19:45][COMM]msg 02E0 loss. last_tick:115085. cur_tick:116089. period:100
[D][05:19:45][COMM]msg 02E1 loss. last_tick:115085. cur_tick:116089. period:100
[D][05:19:45][COMM]msg 02E2 loss. last_tick:115085. cur_tick:116090. period:1

2025-07-31 18:05:07:338 ==>> 00
[D][05:19:45][COMM]msg 0300 loss. last_tick:115085. cur_tick:116090. period:100
[D][05:19:45][COMM]msg 0301 loss. last_tick:115085. cur_tick:116090. period:100
[D][05:19:45][COMM]bat msg 0240 loss. last_tick:115085. cur_tick:116091. period:100. j,i:1 54
[D][05:19:45][COMM]bat msg 0241 loss. last_tick:115085. cur_tick:116091. period:100. j,i:2 55
[D][05:19:45][COMM]bat msg 0242 loss. last_tick:115085. cur_tick:116092. period:100. j,i:3 56
[D][05:19:45][COMM]bat msg 0244 loss. last_tick:115085. cur_tick:116092. period:100. j,i:5 58
[D][05:19:45][COMM]bat msg 024E loss. last_tick:115085. cur_tick:116092. period:100. j,i:15 68
[D][05:19:45][COMM]bat msg 024F loss. last_tick:115085. cur_tick:116093. period:100. j,i:16 69
[D][05:19:45][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 116093
[D][05:19:45][COMM]CAN message bat fault change: 0x00000000->0x0001802E 116093
[D][05:19:45][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 116094


2025-07-31 18:05:07:443 ==>>                                                                                                                                                               

2025-07-31 18:05:07:548 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 18:05:07:654 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:05:07:759 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   ROT]send_path:0x3
[D][05:19:45][PROT]msg_type:0x5d03
[D][05:19:45][PROT]===========================================================
[W][05:19:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955185]
[D][05:19:45][PROT]===========================================================
[D][05:19:45][PROT]Sending traceid[999999999990000E]
[D][05:19:45][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:45][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:45][PROT]ble is not inited or not connected or cc

2025-07-31 18:05:07:788 ==>> cd not enabled
[D][05:19:45][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:45][M2M ]m2m_task: gpc:[0]

2025-07-31 18:05:07:893 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 18:05:07:998 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 18:05:08:043 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:05:08:103 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 18:05:08:208 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  [D][05:19:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 18:05:08:313 ==>>                               [W][05:19:46][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:46][COMM]arm_hub_enable: hub power: 0
[D][05:19:46][HSDK]hexlog index save 0 3840 43 @ 0 : 0
[D][05:19:46][HSDK]write save hexlog index [0]
[D][05:19:46][FCTY]F:[syncP

2025-07-31 18:05:08:343 ==>> araFromRamToFlash].L:[962] ready to read para flash
[D][05:19:46][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:05:08:793 ==>> [W][05:19:47][COMM]Power Off


2025-07-31 18:05:08:826 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:05:08:844 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:05:08:875 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:05:08:958 ==>> 5A A5 02 5A A5 


2025-07-31 18:05:09:048 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:05:09:125 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:05:09:134 ==>> 检测【检测小电池关机电流】
2025-07-31 18:05:09:151 ==>> 开始小电池电流采样
2025-07-31 18:05:09:176 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:05:09:228 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:05:09:243 ==>> [D][05:19:47][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 18:05:10:229 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:05:10:275 ==>> CurrentBattery:ƽ��:69.10

2025-07-31 18:05:10:736 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:05:10:750 ==>> 【检测小电池关机电流】通过,【69.1uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:05:11:112 ==>> MES过站成功
2025-07-31 18:05:11:131 ==>> #################### 【测试结束】 ####################
2025-07-31 18:05:11:150 ==>> 关闭5V供电
2025-07-31 18:05:11:163 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:05:11:253 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:05:12:155 ==>> 关闭5V供电成功
2025-07-31 18:05:12:170 ==>> 关闭33V供电
2025-07-31 18:05:12:197 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:05:12:262 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:05:13:168 ==>> 关闭33V供电成功
2025-07-31 18:05:13:183 ==>> 关闭3.7V供电
2025-07-31 18:05:13:205 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:05:13:261 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:05:14:022 ==>>  

