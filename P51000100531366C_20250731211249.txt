2025-07-31 21:12:49:391 ==>> MES查站成功:
查站序号:P51000100531366C验证通过
2025-07-31 21:12:49:395 ==>> 扫码结果:P51000100531366C
2025-07-31 21:12:49:397 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:12:49:399 ==>> 测试参数版本:2024.10.11
2025-07-31 21:12:49:401 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:12:49:403 ==>> 检测【打开透传】
2025-07-31 21:12:49:406 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:12:49:463 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:12:49:727 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:12:49:731 ==>> 检测【检测接地电压】
2025-07-31 21:12:49:733 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:12:49:871 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:12:50:064 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:12:50:066 ==>> 检测【打开小电池】
2025-07-31 21:12:50:070 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:12:50:173 ==>> 6A A6 01 A6 6A 


2025-07-31 21:12:50:263 ==>> Battery ON
OVER 150


2025-07-31 21:12:50:358 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:12:50:364 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:12:50:367 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:12:50:473 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 21:12:50:659 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:12:50:661 ==>> 检测【等待设备启动】
2025-07-31 21:12:50:663 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:12:51:020 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:12:51:218 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:12:51:683 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:12:51:854 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:12:51:914 ==>>                                   S Will Not Open


2025-07-31 21:12:52:309 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:12:52:721 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:12:52:782 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:12:53:044 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:12:53:047 ==>> 检测【产品通信】
2025-07-31 21:12:53:049 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:12:53:255 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:12:53:335 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:12:53:337 ==>> 检测【初始化完成检测】
2025-07-31 21:12:53:339 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:12:53:435 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:12:53:601 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:12:53:860 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:53:882 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:12:53:885 ==>> 检测【关闭大灯控制1】
2025-07-31 21:12:53:888 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:12:53:965 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],s

2025-07-31 21:12:54:010 ==>> end_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:12:54:085 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:12:54:181 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:12:54:186 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:12:54:189 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:12:54:373 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:12:54:466 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:12:54:469 ==>> 检测【关闭仪表供电】
2025-07-31 21:12:54:471 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:12:54:662 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:12:54:757 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:12:54:760 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:12:54:763 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:12:54:857 ==>> [D][05:17:52][COMM]3635 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:54:932 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:12:55:059 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:12:55:061 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:12:55:064 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:12:55:249 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:12:55:357 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:12:55:361 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:12:55:364 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:12:55:550 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:12:55:647 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:12:55:650 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:12:55:653 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:12:55:766 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:12:55:871 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:55:950 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:12:55:953 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:12:55:956 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:12:56:066 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:12:56:233 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:12:56:236 ==>> 该项需要延时执行
2025-07-31 21:12:56:396 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5004. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5005. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5005. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5006. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5006. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5007. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5008
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5008


2025-07-31 21:12:56:879 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:57:452 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:12:57:969 ==>>                                                                                                                                                                         value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready


2025-07-31 21:12:58:074 ==>> 
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D]

2025-07-31 21:12:58:179 ==>> [05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][

2025-07-31 21:12:58:254 ==>> 05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6669 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:58:903 ==>> [D][05:17:56][COMM]7680 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:59:912 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:13:00:238 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:13:00:241 ==>> 检测【33V输入电压ADC】
2025-07-31 21:13:00:244 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:00:585 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:17:58][COMM]adc read out 24v adc:1321  volt:33412 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:58][COMM]adc read throttle adc:14  volt:18 mv
[D][05:17:58][COMM]adc read battery ts volt:15 mv
[D][05:17:58][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:17:58][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:13:00:786 ==>> 【33V输入电压ADC】通过,【32627mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:13:00:789 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:13:00:793 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:13:00:873 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1665mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1989mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:13:00:918 ==>> [D][05:17:58][COMM]9703 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:13:01:072 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:075 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:13:01:107 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:109 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:13:01:112 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:13:01:141 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:13:01:144 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:13:01:175 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:13:01:179 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:13:01:214 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:13:01:216 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:13:01:290 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018
1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2775mV
Get AD_V6 1990mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:13:01:510 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:512 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:13:01:542 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:545 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:13:01:548 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 21:13:01:574 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:13:01:576 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:13:01:607 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:13:01:609 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:13:01:644 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:13:01:647 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:13:01:785 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:13:01:942 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:944 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:13:01:973 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:01:976 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:13:01:979 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:13:02:004 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:13:02:006 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:13:02:035 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:13:02:039 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:13:02:075 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:13:02:078 ==>> 检测【打开WIFI(1)】
2025-07-31 21:13:02:082 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:13:02:135 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10714 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg s

2025-07-31 21:13:02:165 ==>> ub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 21:13:02:666 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087912343

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290491

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][0

2025-07-31 21:13:02:697 ==>> 5:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:13:02:846 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:13:02:899 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:13:02:902 ==>> 检测【清空消息队列(1)】
2025-07-31 21:13:02:904 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:13:02:936 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:13:03:086 ==>> [D][05:18:00][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:00][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:13:03:246 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:13:03:250 ==>> 检测【打开GPS(1)】
2025-07-31 21:13:03:253 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:13:03:468 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:13:03:622 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:13:03:625 ==>> 检测【打开GSM联网】
2025-07-31 21:13:03:628 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:13:03:834 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] r

2025-07-31 21:13:03:894 ==>> eady to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:13:04:214 ==>>                                                                                                                                                                                                                                                                                                                                                                                               [D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:13:04:426 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:13:04:429 ==>> 检测【打开仪表供电1】
2025-07-31 21:13:04:432 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:13:04:659 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:13:04:760 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:13:04:764 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:13:04:766 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:13:04:979 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:02][COMM]13727 imu init OK
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:13:05:097 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:13:05:101 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:13:05:104 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:13:05:249 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub read adc[3],val[33015]


2025-07-31 21:13:05:384 ==>> 【读取主控ADC采集的仪表电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:13:05:387 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:13:05:390 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:05:585 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:13:05:676 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:13:05:680 ==>> 检测【AD_V20电压】
2025-07-31 21:13:05:686 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:05:693 ==>>                                                                          

2025-07-31 21:13:05:765 ==>>                                                            36,39,,,35,1*75

$GBGSV,2,2,05,25,,,34,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1492.442,1492.442,47.690,2097152,2097152,2097152*43

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 21:13:05:780 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:13:05:870 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:13:05:930 ==>> 本次取值间隔时间:148ms
2025-07-31 21:13:05:960 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:13:05:964 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:06:065 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:13:06:201 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:13:06:426 ==>> 本次取值间隔时间:357ms
2025-07-31 21:13:06:459 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:06:565 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:13:06:732 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,24,,,39,33,,,38,60,,,38,41,,,37,1*7D

$GBGSV,4,2,14,39,,,36,25,,,36,16,,,35,1,,,34,1*4F

$GBGSV,4,3,14,2,,,33,44,,,32,5,,,31,12,,,38,1*78

$GBGSV,4,4,14,59,,,38,40,,,36,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1466.102,1466.102,46.890,2097152,2097152,2097152*4C

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:13:06:792 ==>> 本次取值间隔时间:218ms
2025-07-31 21:13:06:836 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:06:946 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:13:07:071 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:13:07:423 ==>> 本次取值间隔时间:473ms
2025-07-31 21:13:07:456 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:13:07:558 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:13:07:727 ==>> 本次取值间隔时间:157ms
2025-07-31 21:13:07:742 ==>> 1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,40,24,,,39,60,,,39,3,,,39,1*49

$GBGSV,4,2,16,59,,,38,25,,,38,41,,,37,39,,,37,1*73

$GBGSV,4,3,16,16,,,36,1,,,35,40,,,34,2,,,33,1*72

$GBGSV,4,4,16,44,,,33,4,,,32,12,,,31,5,,,31,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1482.143,1482.143,47.425,2097152,2097152,2097152*4F



2025-07-31 21:13:07:892 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 21:13:08:027 ==>> 本次取值间隔时间:288ms
2025-07-31 21:13:08:059 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:08:063 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:13:08:066 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:13:08:162 ==>> 3A A3 02 00 A3 


2025-07-31 21:13:08:267 ==>> OFF_OUT2
OVER 150


2025-07-31 21:13:08:349 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:13:08:352 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:13:08:356 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:13:08:568 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:13:08:644 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:13:08:649 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:13:08:652 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:13:08:763 ==>> $GBGGA,131312.553,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,41,3,,,40,24,,,39,60,,,39,1*49

$GBGSV,5,2,18,59,,,39,25,,,39,41,,,38,39,,,38,1*7C

$GBGSV,5,3,18,16,,,36,40,,,36,1,,,35,34,,,35,1*4C

$GBGSV,5,4,18,23,,,35,2,,,33,44,,,33,12,,,32,1*49

$GBGSV,5,5,18,4,,,31,5,,,31,1*7E

$GBRMC,131312.553,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131312.553,0.000,1497.119,1497.119,47.908,2097152,2097152,2097152*53

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:13:08:938 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:13:08:941 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:13:08:945 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:13:09:188 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:13:09:236 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:13:09:240 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:13:09:244 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:13:09:464 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:13:09:537 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:13:09:541 ==>> 检测【AD_V21电压】
2025-07-31 21:13:09:543 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:13:09:722 ==>> 本次取值间隔时间:170ms
2025-07-31 21:13:09:737 ==>> 1A A1 20 00 00 
Get AD_V21 1075mV
OVER 150
$GBGGA,131313.533,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,7,2,25,60,,,39,59,,,39,41,,,38,39,,,38,1*71

$GBGSV,7,3,25,40,,,37,16,,,36,42,,,36,1,,,35,1*43

$GBGSV,7,4,25,34,,,35,14,,,35,7,,,35,23,,,34,1*47

$GBGSV,7,5,25,9,,,34,13,,,34,10,,,34,6,,,34,1*7F

$GBGSV,7,6,25,2,,,33,44,,,33,12,,,33,4,,,31,1*77

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,131313.533,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131313.533,0.000,1484.208,1484.208,47.484,2097152,2097152,2097152*5D



2025-07-31 21:13:09:888 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 21:13:10:189 ==>> 本次取值间隔时间:462ms
2025-07-31 21:13:10:231 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:13:10:374 ==>> 1A A1 20 00 00 
Get AD_V21 1651mV
OVER 150


2025-07-31 21:13:10:464 ==>> 本次取值间隔时间:232ms
2025-07-31 21:13:10:501 ==>> 【AD_V21电压】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:10:506 ==>> 检测【关闭仪表供电2】
2025-07-31 21:13:10:510 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:13:10:767 ==>> $GBGGA,131314.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,7,2,25,60,,,39,59,,,39,41,,,38,39,,,38,1*71

$GBGSV,7,3,25,40,,,37,16,,,36,14,,,36,42,,,35,1*77

$GBGSV,7,4,25,1,,,35,34,,,35,7,,,35,6,,,35,1*45

$GBGSV,7,5,25,23,,,34,9,,,34,13,,,34,10,,,33,1*4F

$GBGSV,7,6,25,2,,,33,44,,,33,12,,,33,4,,,31,1*77

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,131314.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131314.513,0.000,1484.209,1484.209,47.484,2097152,2097152,2097152*58

[D][05:18:08][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:13:11:072 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:13:11:076 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:13:11:078 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:13:11:253 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:13:11:426 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:13:11:429 ==>> 检测【打开AccKey2供电】
2025-07-31 21:13:11:432 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:13:11:740 ==>> $GBGGA,131315.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,7,2,25,60,,,39,59,,,39,41,,,38,39,,,38,1*71

$GBGSV,7,3,25,40,,,37,16,,,36,14,,,36,1,,,36,1*43

$GBGSV,7,4,25,7,,,36,42,,,35,34,,,35,6,,,34,1*70

$GBGSV,7,5,25,23,,,34,9,,,34,2,,,34,44,,,34,1*79

$GBGSV,7,6,25,13,,,33,10,,,33,12,,,33,4,,,31,1*46

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,131315.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131315.513,0.000,1487.524,1487.524,47.589,2097152,2097152,2097152*55

[W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:13:11:890 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:13:12:022 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:13:12:026 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:13:12:031 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:12:379 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:10][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:10][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:10][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:10][COMM]adc read battery ts volt:11 mv
[D][05:18:10][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:10][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1422  volt:32969 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:13:12:589 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:13:12:593 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:13:12:596 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:13:12:713 ==>> $GBGGA,131316.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,25,,,40,24,,,39,1*4A

$GBGSV,7,2,25,60,,,39,59,,,39,41,,,38,39,,,38,1*71

$GBGSV,7,3,25,40,,,37,16,,,36,14,,,36,1,,,35,1*40

$GBGSV,7,4,25,7,,,35,42,,,35,34,,,35,6,,,34,1*73

$GBGSV,7,5,25,23,,,34,9,,,34,2,,,34,44,,,34,1*79

$GBGSV,7,6,25,13,,,33,10,,,33,12,,,33,4,,,31,1*46

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,131316.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131316.513,0.000,1482.547,1482.547,47.428,2097152,2097152,2097152*5C



2025-07-31 21:13:12:788 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:13:12:915 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:13:12:918 ==>> 该项需要延时执行
2025-07-31 21:13:13:724 ==>> $GBGGA,131317.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,35,6,,,34,1*76

$GBGSV,7,5,26,23,,,34,9,,,34,2,,,34,44,,,34,1*7A

$GBGSV,7,6,26,13,,,33,10,,,33,12,,,33,4,,,31,1*45

$GBGSV,7,7,26,5,,,31,8,,,30,1*7E

$GBRMC,131317.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131317.513,0.000,1474.966,1474.966,47.194,2097152,2097152,2097152*5F



2025-07-31 21:13:13:889 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:13:14:742 ==>> $GBGGA,131318.513,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,35,6,,,34,1*76

$GBGSV,7,5,26,23,,,34,9,,,34,2,,,34,44,,,34,1*7A

$GBGSV,7,6,26,13,,,33,10,,,33,12,,,33,4,,,31,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131318.513,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131318.513,0.000,1474.963,1474.963,47.191,2097152,2097152,2097152*55



2025-07-31 21:13:15:719 ==>> $GBGGA,131319.513,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,35,6,,,35,1*77

$GBGSV,7,5,26,23,,,34,9,,,34,2,,,34,44,,,34,1*7A

$GBGSV,7,6,26,13,,,33,10,,,33,12,,,33,4,,,31,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131319.513,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131319.513,0.000,1478.151,1478.151,47.292,2097152,2097152,2097152*54



2025-07-31 21:13:15:883 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:13:15:928 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:13:15:932 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:13:15:937 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:16:274 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:13][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:13][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:13][COMM]adc read right brake adc:17  volt:22 mv
[D][05:18:13][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:13][COMM]adc read battery ts volt:13 mv
[D][05:18:13][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:13][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:13:16:471 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:13:16:474 ==>> 检测【打开AccKey1供电】
2025-07-31 21:13:16:477 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:13:16:746 ==>> $GBGGA,131320.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,39,24,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,1,,,36,42,,,35,34,,,35,9,,,35,1*7B

$GBGSV,7,5,26,6,,,34,23,,,34,2,,,34,13,,,34,1*77

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,32,1*44

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131320.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131320.513,0.000,1481.335,1481.335,47.389,2097152,2097152,2097152*55

[W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:13:17:159 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:13:17:166 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:13:17:171 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:13:17:269 ==>> 1A A1 00 40 00 
Get AD_V14 2564mV
OVER 150


2025-07-31 21:13:17:419 ==>> 原始值:【2564】, 乘以分压基数【2】还原值:【5128】
2025-07-31 21:13:17:454 ==>> 【读取AccKey1电压(ADV14)前】通过,【5128mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:13:17:459 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:13:17:464 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:17:835 ==>> $GBGGA,131321.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,39,24,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,35,9,,,35,1*78

$GBGSV,7,5,26,6,,,34,23,,,34,2,,,34,13,,,34,1*77

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131321.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131321.513,0.000,1476.554,1476.554,47.238,2097152,2097152,2097152*5F

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:15][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:15][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:17 mv
[D][05:18:15][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:15][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2396  volt:3860 mv
[D][05:18:15][COMM]arm_hub adc rea

2025-07-31 21:13:17:865 ==>> d led yb adc:1422  volt:32969 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 21:13:17:910 ==>>                                          

2025-07-31 21:13:18:002 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:13:18:007 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:13:18:010 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:13:18:154 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:13:18:289 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:13:18:293 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:13:18:296 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:13:18:366 ==>> 1A A1 00 40 00 
Get AD_V14 2566mV
OVER 150


2025-07-31 21:13:18:549 ==>> 原始值:【2566】, 乘以分压基数【2】还原值:【5132】
2025-07-31 21:13:18:582 ==>> 【读取AccKey1电压(ADV14)后】通过,【5132mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:13:18:585 ==>> 检测【打开WIFI(2)】
2025-07-31 21:13:18:589 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:13:18:719 ==>> $GBGGA,131322.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,39,24,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,38,41,,,38,39,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,35,7,,,35,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,35,9,,,34,1*79

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131322.513,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131322.513,0.000,1465.395,1465.395,46.885,2097152,2097152,2097152*51



2025-07-31 21:13:18:824 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id

2025-07-31 21:13:18:854 ==>> : 12, ret: 6


2025-07-31 21:13:18:943 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:13:18:949 ==>> 检测【转刹把供电】
2025-07-31 21:13:18:962 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:13:19:157 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:13:19:253 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:13:19:257 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:13:19:259 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:13:19:354 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:19:430 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:13:19:475 ==>> 1A A1 00 80 00 
Get AD_V15 2408mV
OVER 150


2025-07-31 21:13:19:505 ==>> 原始值:【2408】, 乘以分压基数【2】还原值:【4816】
2025-07-31 21:13:19:535 ==>> +WIFISCAN:4,0,44A1917CAD81,-76
+WIFISCAN:4,1,44A1917CAD80,-76
+WIFISCAN:4,2,CC057790A7C1,-79
+WIFISCAN:4,3,F86FB0660A82,-87

[D][05:18:17][CAT1]wifi scan report total[4]


2025-07-31 21:13:19:568 ==>> 【读取AD_V15电压(前)】通过,【4816mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:13:19:571 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:13:19:575 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:13:19:670 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:13:19:715 ==>> $GBGGA,131323.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,39,24,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,38,41,,,38,39,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,35,7,,,35,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,34,9,,,34,1*78

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131323.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131323.513,0.000,1463.801,1463.801,46.835,2097152,2097152,2097152*5B



2025-07-31 21:13:19:790 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:13:19:895 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:13:19:984 ==>> [D][05:18:17][GNSS]recv submsg id[3]


2025-07-31 21:13:20:624 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:13:20:716 ==>> $GBGGA,131324.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,39,25,,,39,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,38,41,,,38,39,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,42,,,35,34,,,34,9,,,34,1*78

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131324.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131324.513,0.000,1463.799,1463.799,46.832,2097152,2097152,2097152*5B



2025-07-31 21:13:20:731 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:13:20:791 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:13:20:866 ==>> 1A A1 01 00 00 
Get AD_V16 2444mV
OVER 150


2025-07-31 21:13:20:896 ==>> 原始值:【2444】, 乘以分压基数【2】还原值:【4888】
2025-07-31 21:13:20:934 ==>> 【读取AD_V16电压(前)】通过,【4888mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:13:20:938 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:13:20:942 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:21:286 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3149  volt:5535 mv
[D][05:18:18][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:18][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:18][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:18][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3092  volt:5435 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1422  volt:32969 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:13:21:488 ==>> 【转刹把供电电压(主控ADC)】通过,【5435mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:13:21:492 ==>> 检测【转刹把供电电压】
2025-07-31 21:13:21:498 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:21:834 ==>> $GBGGA,131325.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,34,9,,,34,1*78

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131325.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131325.513,0.000,1468.589,1468.589,46.992,2097152,2097152,2097152*51

[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:19][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:19][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:19][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:19][COMM]adc read battery ts volt:13 mv
[D][05:18:19][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3087  volt:5426 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:19][COMM]arm_hub

2025-07-31 21:13:21:879 ==>>  adc read led yb adc:1423  volt:32992 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:13:21:909 ==>>                                          

2025-07-31 21:13:22:028 ==>> 【转刹把供电电压】通过,【5426mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:13:22:032 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:13:22:037 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:22:257 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:13:22:319 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:13:22:324 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:13:22:328 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:22:422 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:22:452 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:13:22:527 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:22:632 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:22:722 ==>> $GBGGA,131326.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,24,,,40,3,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,35,9,,,34,1*79

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131326.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131326.513,0.000,1471.778,1471.778,47.093,2097152,2097152,2097152*5B



2025-07-31 21:13:22:737 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:22:797 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:13:22:842 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:22:947 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:22:962 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = ?<<<<
00 00 00 00 00 
head err!


2025-07-31 21:13:23:052 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:23:160 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:23:175 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:13:23:265 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:13:23:328 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:13:23:372 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:13:23:403 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:13:23:409 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:13:23:414 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:13:23:507 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:13:23:537 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:13:23:567 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 21:13:23:653 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:13:23:658 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:13:23:666 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:13:23:673 ==>> $GBGGA,131327.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,24,,,39,3,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,35,9,,,34,1*79

$GBGSV,7,5,26,6,,,34,23,,,33,2,,,33,13,,

2025-07-31 21:13:23:717 ==>> ,33,1*70

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131327.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131327.513,0.000,1470.181,1470.181,47.040,2097152,2097152,2097152*54

3A A3 03 01 A3 


2025-07-31 21:13:23:762 ==>> ON_OUT3
OVER 150


2025-07-31 21:13:23:927 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:13:23:945 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:13:23:949 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:13:23:954 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:13:24:077 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:13:24:231 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:13:24:238 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:13:24:244 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:13:24:366 ==>> 3A A3 05 01 A3 


2025-07-31 21:13:24:471 ==>> ON_OUT5
OVER 150


2025-07-31 21:13:24:519 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:13:24:525 ==>> 检测【左刹电压测试1】
2025-07-31 21:13:24:537 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:24:835 ==>> $GBGGA,131328.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,35,9,,,34,1*79

$GBGSV,7,5,26,6,,,34,13,,,34,23,,,33,2,,,33,1*77

$GBGSV,7,6,26,44,,,33,10,,,33,12,,,33,4,,,31,1*47

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131328.513,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131328.513,0.000,1471.776,1471.776,47.092,2097152,2097152,2097152*54

[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:22][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:22][COMM]adc read left brake adc:1727  volt:2276 mv
[D][05:18:22][COMM]adc read right brake adc:1730  volt:2280 mv
[D][05:18:22][COMM]adc read throttle adc:1723  volt:2271 mv
[D][05:18:22][COMM]adc read battery ts volt:13 mv
[D][05:18:22][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:22][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:22][COMM]arm_hu

2025-07-31 21:13:24:880 ==>> b adc read led yb adc:1424  volt:33015 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:13:25:070 ==>> 【左刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 21:13:25:076 ==>> 检测【右刹电压测试1】
2025-07-31 21:13:25:116 ==>> 【右刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 21:13:25:119 ==>> 检测【转把电压测试1】
2025-07-31 21:13:25:149 ==>> 【转把电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 21:13:25:153 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:13:25:159 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:13:25:265 ==>> 3A A3 03 00 A3 


2025-07-31 21:13:25:370 ==>> OFF_OUT3
OVER 150


2025-07-31 21:13:25:434 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:13:25:438 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:13:25:444 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:13:25:565 ==>> 3A A3 04 00 A3 


2025-07-31 21:13:25:670 ==>> $GBGGA,131329.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,35,9,,,34,1*79

$GBGS

2025-07-31 21:13:25:715 ==>> V,7,5,26,6,,,34,13,,,34,2,,,34,44,,,34,1*76

$GBGSV,7,6,26,23,,,33,10,,,33,12,,,33,4,,,32,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131329.513,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131329.513,0.000,1479.746,1479.746,47.343,2097152,2097152,2097152*5A

OFF_OUT4
OVER 150


2025-07-31 21:13:25:929 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:13:25:976 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:13:25:980 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:13:25:986 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:13:26:064 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:13:26:262 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:13:26:268 ==>> 检测【左刹电压测试2】
2025-07-31 21:13:26:273 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:13:26:575 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:24][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:24][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:24][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:24][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:24][COMM]adc read battery ts volt:11 mv
[D][05:18:24][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:24][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:13:26:679 ==>>                                                                                                                                                                        39,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,34,,,35,42,,,34,9,,,34,1*78

$GBGSV,7,5,26,6,,,34,13,,,34,2,,,34,44,,,34,1*76

$GBGSV,7,6,26,23,,,33,10,,,33,12,,,33,4,,,31,1*46

$GBGSV,7,7,2

2025-07-31 21:13:26:724 ==>> 6,5,,,31,8,,,31,1*7F

$GBRMC,131330.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131330.513,0.000,1473.370,1473.370,47.141,2097152,2097152,2097152*52



2025-07-31 21:13:26:807 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 21:13:26:811 ==>> 检测【右刹电压测试2】
2025-07-31 21:13:26:849 ==>> 【右刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 21:13:26:853 ==>> 检测【转把电压测试2】
2025-07-31 21:13:26:896 ==>> 【转把电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 21:13:26:900 ==>> 检测【晶振检测】
2025-07-31 21:13:26:904 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:13:27:059 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]


2025-07-31 21:13:27:182 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:13:27:190 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:13:27:195 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:13:27:284 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1663mV
Get AD_V4 1651mV
Get AD_V5 2772mV
Get AD_V6 2024mV
Get AD_V7 1087mV
OVER 150


2025-07-31 21:13:27:464 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:13:27:471 ==>> 检测【检测BootVer】
2025-07-31 21:13:27:476 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:13:27:884 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130020290491
[D][05:18:25][FCTY]HardwareID  = 867222087912343
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
$GBGGA,131331.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,24,,,40,1*47

$GBGSV,7,2,26,59,,,39,60,,,39,41,,,38,39,,,38,1*72

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,1,,,36,34,,,35,42,,,35,9,,,35,1*7B

$GBGSV,7,5,26,6,,,34,13,,,34,2,,,34,44,,,33,1*71

$GBGSV,7,6,26,23,,,33,10,,,33,12,,,33,4,,,32,1*45

$GBGSV,7,7,26,5,,,31,8,,,

2025-07-31 21:13:27:990 ==>> 31,1*7F

$GBRMC,131331.513,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131331.513,0.000,740.085,740.085,676.826,2097152,2097152,2097152*6F

[D][05:18:25][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1284
[D][05:18:25][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 101 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3699 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
                                         

2025-07-31 21:13:28:291 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:13:28:295 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:13:28:299 ==>> 检测【检测固件版本】
2025-07-31 21:13:28:326 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:13:28:331 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:13:28:334 ==>> 检测【检测蓝牙版本】
2025-07-31 21:13:28:359 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:13:28:366 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:13:28:380 ==>> 检测【检测MoBikeId】
2025-07-31 21:13:28:392 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:13:28:395 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:13:28:401 ==>> 检测【检测蓝牙地址】
2025-07-31 21:13:28:408 ==>> 取到目标值:C897881DBBB8
2025-07-31 21:13:28:424 ==>> 【检测蓝牙地址】通过,【C897881DBBB8】符合目标值【】要求!
2025-07-31 21:13:28:428 ==>> 提取到蓝牙地址:C897881DBBB8
2025-07-31 21:13:28:434 ==>> 检测【BOARD_ID】
2025-07-31 21:13:28:461 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:13:28:468 ==>> 检测【检测充电电压】
2025-07-31 21:13:28:507 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:13:28:511 ==>> 检测【检测VBUS电压1】
2025-07-31 21:13:28:551 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:13:28:556 ==>> 检测【检测充电电流】
2025-07-31 21:13:28:623 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:13:28:628 ==>> 检测【检测IMEI】
2025-07-31 21:13:28:634 ==>> 取到目标值:867222087912343
2025-07-31 21:13:28:669 ==>> 【检测IMEI】通过,【867222087912343】符合目标值【】要求!
2025-07-31 21:13:28:676 ==>> 提取到IMEI:867222087912343
2025-07-31 21:13:28:684 ==>> 检测【检测IMSI】
2025-07-31 21:13:28:715 ==>> 取到目标值:460130020290491
2025-07-31 21:13:28:724 ==>> 【检测IMSI】通过,【460130020290491】符合目标值【】要求!
2025-07-31 21:13:28:727 ==>> 提取到IMSI:460130020290491
2025-07-31 21:13:28:731 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:13:28:737 ==>> 取到目标值:460130020290491
2025-07-31 21:13:28:746 ==>> $GBGGA,131332.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,40,,,38,39,,,38,1*73

$GBGSV,7,3,26,41,,,38,7,,,36,16,,,36,14,,,36,1*48

$GBGSV,7,4,26,1,,,35,9,,,35,6,,,35,34,,,35,1*48

$GBGSV,7,5,26,42,,,35,2,,,34,13,,,34,44,,,34,1*47

$GBGSV,7,6,26,10,,,33,12,,,33,23,,,33,4,,,32,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131332.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131332.513,0.000,740.878,740.878,677.552,2097152,2097152,2097152*63



2025-07-31 21:13:28:803 ==>> 【校验网络运营商(移动)】通过,【460130020290491】符合目标值【】要求!
2025-07-31 21:13:28:807 ==>> 检测【打开CAN通信】
2025-07-31 21:13:28:810 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:13:28:849 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SE

2025-07-31 21:13:28:879 ==>> T SUCCESS


2025-07-31 21:13:29:115 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:13:29:119 ==>> 检测【检测CAN通信】
2025-07-31 21:13:29:125 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:13:29:168 ==>> can send success


2025-07-31 21:13:29:213 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:258 ==>> [D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38041
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:333 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:393 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:425 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:13:29:433 ==>> 检测【关闭CAN通信】
2025-07-31 21:13:29:441 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:13:29:457 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:513 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:13:29:573 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:13:29:678 ==>> $GBGGA,131333.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,59,,,39,60,,,38,40,,,

2025-07-31 21:13:29:717 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:13:29:724 ==>> 检测【打印IMU STATE】
2025-07-31 21:13:29:730 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:13:29:741 ==>> 38,39,,,38,1*72

$GBGSV,7,3,26,41,,,38,7,,,36,1,,,36,16,,,36,1*7C

$GBGSV,7,4,26,14,,,36,9,,,35,6,,,35,34,,,35,1*7F

$GBGSV,7,5,26,42,,,35,2,,,34,13,,,34,44,,,34,1*47

$GBGSV,7,6,26,10,,,33,12,,,33,23,,,33,4,,,32,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131333.513,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131333.513,0.000,740.876,740.876,677.550,2097152,2097152,2097152*60



2025-07-31 21:13:29:843 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:13:29:948 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:13:30:015 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:13:30:020 ==>> 检测【六轴自检】
2025-07-31 21:13:30:026 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:13:30:188 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:13:30:744 ==>> $GBGGA,131334.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,60,,,39,1*49

$GBGSV,7,2,26,59,,,39,24,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,7,,,36,16,,,36,14,,,36,1*46

$GBGSV,7,4,26,1,,,35,9,,,35,6,,,35,34,,,35,1*48

$GBGSV,7,5,26,42,,,35,2,,,34,13,,,34,44,,,34,1*47

$GBGSV,7,6,26,10,,,33,12,,,33,23,,,33,4,,,32,1*45

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,131334.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131334.513,0.000,739.283,739.283,676.093,2097152,2097152,2097152*6C



2025-07-31 21:13:31:745 ==>> $GBGGA,131335.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,60,,,39,1*48

$GBGSV,7,2,26,59,,,39,25,,,39,39,,,38,41,,,38,1*73

$GBGSV,7,3,26,40,,,37,7,,,36,16,,,36,14,,,36,1*46

$GBGSV,7,4,26,1,,,35,9,,,35,6,,,35,34,,,35,1*48

$GBGSV,7,5,26,42,,,35,2,,,34,13,,,34,44,,,34,1*47

$GBGSV,7,6,26,10,,,33,23,,,33,5,,,32,12,,,32,1*45

$GBGSV,7,7,26,4,,,32,8,,,31,1*7D

$GBRMC,131335.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131335.513,0.000,739.282,739.282,676.092,2097152,2097152,2097152*6C



2025-07-31 21:13:31:850 ==>> [D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 

2025-07-31 21:13:31:880 ==>> 12, ret: 6


2025-07-31 21:13:31:986 ==>> [D][05:18:29][COMM]read battery soc:255
[D][05:18:29][COMM]Main Task receive event:142
[D][05:18:29][COMM]###### 40773 imu self test OK ######
[D][05:18:29][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,-4,4036]
[D][05:18:29][COMM]Main Task receive eve

2025-07-31 21:13:32:016 ==>> nt:142 finished processing


2025-07-31 21:13:32:114 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:13:32:118 ==>> 检测【打印IMU STATE2】
2025-07-31 21:13:32:122 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:13:32:259 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:30][COMM]YAW data: 32763[32763]
[D][05:18:30][COMM]pitch:-66 roll:0
[D][05:18:30][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:13:32:413 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:13:32:418 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:13:32:425 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:13:32:471 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:13:32:576 ==>> [D][05:18:30][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:30][FCTY]get_e

2025-07-31 21:13:32:636 ==>> xt_48v_vol retry i = 2,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:13:32:700 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:13:32:709 ==>> 检测【检测VBUS电压2】
2025-07-31 21:13:32:716 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:13:32:741 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      N,0.000,K,N*20

$GBGST,131336.513,0.000,733.709,733.709,670.996,2097152,2097152,2097152*64



2025-07-31 21:13:33:057 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130020290491
[D][05:18:30][FCTY]HardwareID  = 867222087912343
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 11700 mv
[D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 7, adc = 308
[D][05:18:30][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 177 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4

2025-07-31 21:13:33:102 ==>> 
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3699 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:13:33:237 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:13:33:632 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130020290491
[D][05:18:31][FCTY]HardwareID  = 867222087912343
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:31][FCTY]Bat         = 3944 mv
[D][05:18:31][FCTY]Current     = 50 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 4, adc = 177
[D][05:18:31][FCTY]Acckey1 vol = 5528 mv, Acckey2 vol = 202 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465

2025-07-31 21:13:33:737 ==>> b5b1
[D][05:18:31][FCTY]Bat1         = 3699 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:13:33:880 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:13:33:885 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:13:33:891 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:13:33:963 ==>> 5A A5 01 5A A5 


2025-07-31 21:13:34:068 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:13:34:173 ==>> [D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 4
[D][05:18:31][COMM]read battery soc:255


2025-07-31 21:13:34:259 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:13:34:263 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:13:34:270 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:13:34:276 ==>> [D][05:18:32][COMM]msg 0601 loss. last_tick:38024. cur_tick:43039. period:500
[D][05:18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43040


2025-07-31 21:13:34:368 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:13:34:587 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:13:34:592 ==>> 检测【打开WIFI(3)】
2025-07-31 21:13:34:600 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:13:34:836 ==>> $GBGGA,131338.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,39,59,,,39,1*4C

$GBGSV,7,2,26,24,,,39,25,,,39,39,,,38,41,,,38,1*79

$GBGSV,7,3,26,40,,,37,16,,,36,7,,,35,1,,,35,1*72

$GBGSV,7,4,26,34,,,35,42,,,35,14,,,35,2,,,34,1*46

$GBGSV,7,5,26,9,,,34,6,,,34,10,,,33,13,,,33,1*7C

$GBGSV,7,6,26,44,,,33,23,,,33,12,,,32,5,,,31,1*47

$GBGSV,7,7,26,8,,,31,4,,,31,1*7E

$GBRMC,131338.513,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131338.513,0.000,732.128,732.128,669.551,2097152,2097152,2097152*65

[W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:13:34:885 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:13:34:889 ==>> 检测【扩展芯片hw】
2025-07-31 21:13:34:896 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:13:35:064 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:13:35:172 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:13:35:177 ==>> 检测【扩展芯片boot】
2025-07-31 21:13:35:204 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:13:35:208 ==>> 检测【扩展芯片sw】
2025-07-31 21:13:35:235 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:13:35:239 ==>> 检测【检测音频FLASH】
2025-07-31 21:13:35:247 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:13:35:456 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:13:35:789 ==>> $GBGGA,131339.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,59,,,39,24,,,39,1*4D

$GBGSV,7,2,27,25,,,39,60,,,38,39,,,38,41,,,38,1*79

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,7,,,35,1*44

$GBGSV,7,4,27,1,,,35,34,,,35,42,,,35,2,,,34,1*73

$GBGSV,7,5,27,9,,,34,6,,,34,10,,,33,13,,,33,1*7D

$GBGSV,7,6,27,44,,,33,23,,,33,12,,,32,5,,,31,1*46

$GBGSV,7,7,27,8,,,31,4,,,31,38,,,43,1*73

$GBRMC,131339.513,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131339.513,0.000,732.126,732.126,669.549,2097152,2097152,2097152*6D

[D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:13:36:593 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[920].cmd fil

2025-07-31 21:13:36:698 ==>> e 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][COMM

2025-07-31 21:13:36:803 ==>> ]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get soc error
[E][05:18:33][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][PROT]index:0
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:4
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33

2025-07-31 21:13:36:908 ==>> ][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900005]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][PROT]index:0 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:4
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900005]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18

2025-07-31 21:13:37:014 ==>> :33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:33][COMM]Receive Bat Lock cmd 0
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][COMM]Main Task receive event:61
[D][05:18:33][COMM][D301]:type:3, trace id:280
[D][05:18:33][COMM]id[], hw[000
[D][05:18:33][COMM]get mcMaincircuitVolt error
[D][05:18:33][COMM]get mcSubcircuitVolt error
[D][05:18:33][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:3

2025-07-31 21:13:37:119 ==>> 3][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][COMM]get bat work state err
[W][05:18:33][PROT]remove success[1629955113],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:18:33][COMM]Main Task receive event:61 finished processing
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5C41276430188FBACE694F694555F25F4EF07514FAE7D6BBDB8F633BC8E666F02D3971AB4971F9BE2115AFA66ADF0F29348C7AF03B9A7BDA1E32064C819D13E4DDE74A86C036E42A9F3AA9E81558F42DFE23A
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle be

2025-07-31 21:13:37:224 ==>> come true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[

2025-07-31 21:13:37:314 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:13:37:803 ==>> $GBGGA,131337.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,64,235,41,3,62,190,39,6,55,46,34,39,53,15,38,1*72

$GBGSV,7,2,26,59,52,129,39,40,52,177,37,16,52,355,36,7,48,195,36,1*4F

$GBGSV,7,3,26,1,48,126,35,25,46,304,40,2,45,238,34,24,41,29,39,1*41

$GBGSV,7,4,26,60,41,238,38,9,39,324,35,10,37,202,33,41,34,310,38,1*4E

$GBGSV,7,5,26,4,32,112,31,5,22,257,31,44,20,85,33,13,17,207,34,1*4B

$GBGSV,7,6,26,8,14,202,31,42,8,322,35,14,,,36,34,,,35,1*76

$GBGSV,7,7,26,23,,,33,12,,,32,1*71

$GBGSV,2,1,05,33,64,235,42,39,53,15,40,25,46,304,40,24,41,29,40,5*72

$GBGSV,2,2,05,41,34,310,38,5*4C

$GBRMC,131337.520,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,131337.520,0.570,0.208,0.232,0.342,5.652,3.116,18*5E



2025-07-31 21:13:38:198 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 21:13:39:433 ==>> $GBGGA,131338.520,2301.2585224,N,11421.9411092,E,1,07,1.50,75.954,M,-1.770,M,,*58

$GBGSA,A,3,33,39,16,25,24,41,44,,,,,,3.04,1.50,2.64,4*02

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,6,55,46,34,39,53,15,38,1*7D

$GBGSV,7,2,27,59,52,129,39,40,52,177,37,16,52,355,36,7,48,195,35,1*4D

$GBGSV,7,3,27,1,48,126,35,25,46,304,40,2,45,238,34,24,41,29,39,1*40

$GBGSV,7,4,27,60,41,238,38,9,39,324,35,10,37,202,33,41,34,310,38,1*4F

$GBGSV,7,5,27,4,32,112,31,5,22,257,31,12,22,112,32,44,20,85,34,1*4B

$GBGSV,7,6,27,13,17,207,34,38,15,198,38,8,14,202,31,42,8,322,35,1*74

$GBGSV,7,7,27,14,,,36,34,,,35,23,,,34,1*74

$GBGSV,2,1,05,33,64,235,43,39,53,15,41,25,46,304,40,24,41,29,40,5*72

$GBGSV,2,2,05,41,34,310,39,5*4D

$GBRMC,131338.520,A,2301.2585224,N,11421.9411092,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:36][GNSS]HD8040 GPS
[D][05:18:36][GNSS]GPS diff_sec 124012502, report 0x42 frame
$GBGST,131338.520,1.178,0.618,0.714,0.970,2.771,1.835,8.923*79

[D][05:18:36][COMM]Main Task receive event:131
[D][05:18:36][COMM]index:0,power_mode:0xFF
[D][05:18:36][COMM]index:1,sound_mode:0xFF
[D][05:18:36][COMM]index:2,gsensor_mode:0xFF
[D][05:18:36][COMM]index:3,report_freq_mode:0xFF
[D][05:18:

2025-07-31 21:13:39:538 ==>> 36][COMM]index:4,report_period:0xFF
[D][05:18:36][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:36][COMM]index:6,normal_reset_period:0xFF
[D][05:18:36][COMM]index:7,spock_over_speed:0xFF
[D][05:18:36][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:36][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:36][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:36][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:36][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:36][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:36][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:36][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:36][COMM]index:16,imu_config_params:0xFF
[D][05:18:36][COMM]index:17,long_connect_params:0xFF
[D][05:18:36][COMM]index:18,detain_mark:0xFF
[D][05:18:36][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:36][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:36][COMM]index:21,mc_mode:0xFF
[D][05:18:36][COMM]index:22,S_mode:0xFF
[D][05:18:36][COMM]index:23,overweight:0xFF
[D][05:18:36][COMM]index:24,standstill_mode:0xFF
[D][05:18:36][COMM]index:25,night_mode:0xFF
[D][05:18:36][COMM]index:26,experiment1:0xFF
[D][05:18:36][COMM]index:27,experiment2:0xFF
[D][05

2025-07-31 21:13:39:643 ==>> :18:36][COMM]index:28,experiment3:0xFF
[D][05:18:36][COMM]index:29,experiment4:0xFF
[D][05:18:36][COMM]index:30,night_mode_start:0xFF
[D][05:18:36][COMM]index:31,night_mode_end:0xFF
[D][05:18:36][COMM]index:33,park_report_minutes:0xFF
[D][05:18:36][COMM]index:34,park_report_mode:0xFF
[D][05:18:36][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:36][COMM]index:38,charge_battery_para: FF
[D][05:18:36][COMM]index:39,multirider_mode:0xFF
[D][05:18:36][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:36][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:36][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:36][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:36][COMM]index:44,riding_duration_config:0xFF
[D][05:18:36][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:36][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:36][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:36][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:36][COMM]index:49,mc_load_startup:0xFF
[D][05:18:36][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:36][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:36][COMM]index:52,traffic_mode:0xFF
[D][05:18:36][COMM]index:53,traffic_info_collect_freq:0xFF


2025-07-31 21:13:39:748 ==>> [D][05:18:36][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:36][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:36][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:36][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:36][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:36][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:36][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:36][COMM]index:63,experiment5:0xFF
[D][05:18:36][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:36][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:36][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:36][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:36][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:36][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:36][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:36][COMM]index:72,experiment6:0xFF
[D][05:18:36][COMM]index:73,experiment7:0xFF
[D][05:18:36][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:36][COMM]index:75,zero_value_from_server:-1
[D][05:18:36][COMM]index:76,multirider_threshold:255
[D][05:18:36][COMM]index:77,experiment8:255
[D][05:18:36][COMM]index:78,temp_park_audio_p

2025-07-31 21:13:39:853 ==>> lay_duration:255
[D][05:18:36][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:36][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:36][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:36][COMM]index:83,loc_report_interval:255
[D][05:18:36][COMM]index:84,multirider_threshold_p2:255
[D][05:18:36][COMM]index:85,multirider_strategy:255
[D][05:18:36][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:36][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:36][COMM]index:90,weight_param:0xFF
[D][05:18:36][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:36][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:36][COMM]index:95,current_limit:0xFF
[D][05:18:36][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:36][COMM]index:100,location_mode:0xFF

[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[4205],priority[0],index[2],used[1]
[D][05:18:36][COMM]Main Task receive event:131 finished processing
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: g

2025-07-31 21:13:39:959 ==>> pc:[0],gpo:[1]
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
$GBGGA,131339.020,2301.2582553,N,11421.9412099,E,1,12,0.93,75.305,M,-1.770,M,,*50

$GBGSA,A,3,33,03,39,16,59,25,01,60,24,41,12,44,2.19,0.93,1.99,4*0B

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,6,55,46,34,39,53,15,38,1*7D

$GBGSV,7,2,27,40,52,177,37,16,52,355,36,59,50,129,38,7,48,195,36,1*4D

$GBGSV,7,3,27,25,46,304,40,1,46,125,35,2,45,238,34,60,42,240,38,1*72

$GBGSV,7,4,27,24,41,29,39,9,39,324,35,10,37,202,33,41,34,310,38,1*7C

$GBGSV,7,5,27,4,32,112,31,5,22,257,31,12,22,112,32,44,20,85,34,1*4B

$GBGSV,7,6,27,13,17,207,33,38,15,198,36,8,14,202,31,42,8,322,35,1*7D

$GBGSV,7,7,27,14,,,36,34,,,35,23,,,34,1*74

$GBGSV,2,1,06,33,64,235,43,39,53,15,41,25,46,304,40,24,41,29,40,5*71

$GBGSV,2,2,06,41,34,310,39,44,20,85,35,5*47

$GBRMC,131339.020,A,2301.2582553,N,11421.9412099,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,131339.020,1.907,0.247,0.251,0.380,2.125,1.840,5.827*7F

[D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:13:40:048 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 21:13:40:367 ==>> $GBGGA,131340.000,2301.2582858,N,11421.9412317,E,1,16,0.74,75.303,M,-1.770,M,,*54

$GBGSA,A,3,33,03,39,16,59,07,25,01,40,60,24,10,1.89,0.74,1.75,4*0E

$GBGSA,A,3,41,13,12,44,,,,,,,,,1.89,0.74,1.75,4*0C

$GBGSV,7,1,26,33,64,235,41,3,62,190,40,6,55,46,34,39,53,15,38,1*7C

$GBGSV,7,2,26,16,52,355,36,14,50,187,36,59,50,129,39,7,46,179,36,1*4C

$GBGSV,7,3,26,25,46,304,40,1,46,125,35,2,45,238,34,40,44,161,37,1*78

$GBGSV,7,4,26,60,42,240,38,24,41,29,40,9,39,324,35,10,37,191,33,1*7C

$GBGSV,7,5,26,41,34,310,38,4,32,112,31,13,31,217,34,34,23,141,35,1*49

$GBGSV,7,6,26,5,22,257,31,12,22,112,32,44,20,85,34,8,14,202,31,1*43

$GBGSV,7,7,26,23,10,264,34,42,8,322,35,1*4E

$GBGSV,2,1,06,33,64,235,44,39,53,15,41,25,46,304,41,24,41,29,40,5*77

$GBGSV,2,2,06,41,34,310,39,44,20,85,36,5*44

$GBRMC,131340.000,A,2301.2582858,N,11421.9412317,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,131340.000,1.498,0.211,0.207,0.322,1.603,1.454,4.817*7B

[D][05:18:38][COMM]read battery soc:255
                                          

2025-07-31 21:13:40:398 ==>>                                                                                                                                       

2025-07-31 21:13:40:783 ==>> [D][05:18:38][COMM]49597 imu init OK


2025-07-31 21:13:41:445 ==>> [D][05:18:38][GNSS]recv submsg id[3]
[D][05:18:38][PROT]CLEAN,SEND:0
[D][05:18:38][PROT]index:0 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:4
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900005]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:3

2025-07-31 21:13:41:550 ==>> 8][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B546731A0318E15CA6924B4AB958C9170E349001DF579A6A54E14DA9CED346106FD44F8528C07E7C049A37F37B6E2D41E1FD1D0CE4EEA523FD66DA0BF9A40EC52E639235820DC7D964F6FAFD973AF20E698C3A
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,131341.000,2301.2582850,N,11421.9412749,E,1,22,0.60,75.412,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.32,0.60,1.18,4*0B

$GBGSA,A,3,60,24,10,41,13,42,34,12,44,23,,,1.32,0.60,1.18,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,34,1*40

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,31,13,31,217,33,42,

2025-07-31 21:13:41:625 ==>> 26,167,35,1*4F

$GBGSV,7,6,27,34,23,141,35,5,22,257,31,12,22,112,32,44,20,85,33,1*7E

$GBGSV,7,7,27,38,15,198,32,8,14,202,31,23,10,264,34,1*75

$GBGSV,3,1,09,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*4C

$GBGSV,3,2,09,24,41,29,40,41,34,310,39,34,23,141,33,44,20,85,36,5*74

$GBGSV,3,3,09,23,10,264,33,5*4B

$GBRMC,131341.000,A,2301.2582850,N,11421.9412749,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131341.000,2.621,0.229,0.225,0.334,2.103,2.087,4.475*70

[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 21:13:42:394 ==>> $GBGGA,131342.000,2301.2582989,N,11421.9413058,E,1,22,0.60,75.642,M,-1.770,M,,*50

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.32,0.60,1.18,4*0B

$GBGSA,A,3,60,24,10,41,13,42,34,12,44,23,,,1.32,0.60,1.18,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,35,1*46

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,34,1*40

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,31,13,31,217,33,42,26,167,35,1*4F

$GBGSV,7,6,27,34,23,141,35,5,22,257,31,12,22,112,32,44,20,85,33,1*7E

$GBGSV,7,7,27,38,15,198,33,8,14,202,31,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*44

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,33,5*40

$GBGSV,3,3,10,44,20,85,37,23,10,264,33,5*48

$GBRMC,131342.000,A,2301.2582989,N,11421.9413058,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131342.000,3.879,0.217,0.214,0.319,2.687,2.709,4.481*70

[D][05:18:40][COMM]read battery soc:255


2025-07-31 21:13:43:390 ==>> $GBGGA,131343.000,2301.2583037,N,11421.9413081,E,1,22,0.60,75.602,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.32,0.60,1.18,4*0B

$GBGSA,A,3,60,24,10,41,13,42,34,12,44,23,,,1.32,0.60,1.18,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,34,1*40

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,32,13,31,217,34,42,26,167,35,1*4B

$GBGSV,7,6,27,34,23,141,35,5,22,257,31,12,22,112,32,44,20,85,33,1*7E

$GBGSV,7,7,27,38,15,198,33,8,14,202,31,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,33,5*48

$GBRMC,131343.000,A,2301.2583037,N,11421.9413081,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131343.000,3.746,0.165,0.163,0.241,2.585,2.618,4.167*76



2025-07-31 21:13:44:388 ==>> $GBGGA,131344.000,2301.2582993,N,11421.9413541,E,1,23,0.59,75.783,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,35,25,46,304,39,1,46,125,35,2,45,238,34,1*4D

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,32,13,31,217,33,8,27,207,31,1*72

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,112,32,1*44

$GBGSV,7,7,27,44,20,85,33,38,15,198,33,23,10,264,35,1*75

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,36,23,10,264,32,5*48

$GBRMC,131344.000,A,2301.2582993,N,11421.9413541,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,131344.000,3.611,0.194,0.191,0.284,2.495,2.532,3.941*78

[D][05:18:42][COMM]read battery soc:255


2025-07-31 21:13:45:317 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:13:45:392 ==>> $GBGGA,131345.000,2301.2582972,N,11421.9413400,E,1,23,0.59,75.868,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,35,1*46

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,34,1*40

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,40,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,310,38,4,32,112,32,13,31,217,33,8,27,207,32,1*71

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,112,32,1*44

$GBGSV,7,7,27,44,20,85,33,38,15,198,33,23,10,264,35,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,38,5*43

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131345.000,A,2301.2582972,N,11421.9413400,E,0.003,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,131345.000,3.621,0.204,0.201,0.302,2.486,2.522,3.814*77



2025-07-31 21:13:45:467 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:13:46:590 ==>> $GBGGA,131346.000,2301.2583005,N,11421.9413313,E,1,23,0.59,75.827,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,36,2,45,238,34,1*43

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,31,13,31,217,34,8,27,207,31,1*76

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,112,32,1*45

$GBGSV,7,7,27,44,20,85,33,38,15,198,34,23,10,264,35,1*72

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*44

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131346.000,A,2301.2583005,N,11421.9413313,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131346.000,3.722,0.208,0.205,0.307,2.527,2.562,3.754*7E

[D][05:18:44][COMM]read battery soc:255
[D][05:18:44][PROT]CLEAN,SEND:0
[D][05:18:44][PROT]index:0 1629955124
[D][05:

2025-07-31 21:13:46:696 ==>> 18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:4
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:1
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900005]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B5CDFF5CC25FDDB52364EB341929DCFB1A24159E8CAEA99EF40416C330C9B6D61A7B20A15880F1C1513C10

2025-07-31 21:13:46:770 ==>> 0BD790F255DDC06C5F164EC41FE3AB8028DAF78CA6C5DFFF3D2638FC4028DCB351DE1FA20D6874A4
[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][05:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]


2025-07-31 21:13:47:393 ==>> $GBGGA,131347.000,2301.2583053,N,11421.9413266,E,1,23,0.59,75.810,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,34,1*40

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,32,13,31,217,33,8,27,207,31,1*72

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,112,32,1*45

$GBGSV,7,7,27,44,20,85,33,38,15,198,34,23,10,264,35,1*72

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,38,5*43

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131347.000,A,2301.2583053,N,11421.9413266,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131347.000,3.706,0.188,0.186,0.279,2.512,2.545,3.668*7F



2025-07-31 21:13:48:384 ==>> $GBGGA,131348.000,2301.2583065,N,11421.9413160,E,1,23,0.59,75.714,M,-1.770,M,,*53

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

$GBGSV,7,2,27,16,52,355,36,14,50,187,36,59,50,129,39,9,47,329,34,1*47

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,33,1*47

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,32,13,31,217,33,8,27,207,32,1*71

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,112,32,1*45

$GBGSV,7,7,27,44,20,85,33,38,15,198,34,23,10,264,34,1*73

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,38,5*43

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,36,23,10,264,32,5*48

$GBRMC,131348.000,A,2301.2583065,N,11421.9413160,E,0.000,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,131348.000,3.659,0.165,0.164,0.245,2.481,2.513,3.584*72

[D][05:18:46][COMM]read battery soc:255


2025-07-31 21:13:49:372 ==>> $GBGGA,131349.000,2301.2583134,N,11421.9413046,E,1,23,0.59,75.686,M,-1.770,M,,*58

$GBGSA,A,3,33,03,39,06,16,14,59,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

$GBGSV,7,2,27,16,52,355,36,14,50,187,35,59,50,129,38,9,47,329,34,1*45

$GBGSV,7,3,27,7,46,179,36,25,46,304,40,1,46,125,35,2,45,238,33,1*47

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,31,13,31,217,33,8,27,207,31,1*71

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,112,32,1*44

$GBGSV,7,7,27,44,20,85,34,38,15,198,33,23,10,264,34,1*73

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,33,5*40

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131349.000,A,2301.2583134,N,11421.9413046,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,131349.000,2.394,0.178,0.176,0.261,1.757,1.795,2.922*7A

                                                                   

2025-07-31 21:13:49:538 ==>> [D][05:18:47][COMM]crc 108B
[D][05:18:47][COMM]flash test ok


2025-07-31 21:13:50:391 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:13:50:398 ==>> 检测【打开喇叭声音】
2025-07-31 21:13:50:410 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:13:50:436 ==>> $GBGGA,131350.000,2301.2583133,N,11421.9412947,E,1,23,0.59,75.756,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,35,9,47,329,34,1*44

$GBGSV,7,3,27,7,46,179,35,25,46,304,39,1,46,125,35,2,45,238,33,1*4A

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,4,32,112,31,13,31,217,33,8,27,207,31,1*71

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,112,31,1*47

$GBGSV,7,7,27,44,20,85,33,38,15,198,33,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,38,5*43

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,33,5*40

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131350.000,A,2301.2583133,N,11421.9412947,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,131350.000,2.501,0.157,0.155,0.228,1.820,1.854,2.925*73

[D][05:18:48][COMM]read battery soc:255


2025-07-31 21:13:51:045 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:48][COMM]read file, len:15228, num:4
[D][05:18:48][COMM]--->crc16:0x419c
[D][05:18:48][COMM]read file success
[W][05:18:48][COMM][Audio].l:[936].close hexlog save
[D][05:18:48][COMM]accel parse set 1
[D][05:18:48][COMM][Audio]mon:9,05:18:48
[D][05:18:48][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:48][COMM]

2025-07-31 21:13:51:151 ==>> f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, ind

2025-07-31 21:13:51:202 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:13:51:211 ==>> 检测【打开大灯控制】
2025-07-31 21:13:51:222 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:13:51:255 ==>> ex:4, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 21:13:51:360 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 21:13:51:420 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                               [W][05:18:49][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:13:51:503 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:13:51:511 ==>> 检测【关闭仪表供电3】
2025-07-31 21:13:51:518 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:13:51:827 ==>> [D][05:18:49][PROT]CLEAN,SEND:0
[D][05:18:49][PROT]CLEAN:0
[D][05:18:49][PROT]index:1 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:5
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:3
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:18:49][PROT]sending traceid [9999999999900006]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[134]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:1

2025-07-31 21:13:51:917 ==>> 8:49][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:49][CAT1]Send Data To Server[134][137] ... ->:
0043B68C113311331133113311331B88B3BD159D907649683C45F8586C73445A0C365F4928044866C8786F5BC6A7D30DCC980E078461E6CC8B8D189E9653A5911AD67B
[W][05:18:49][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:49][COMM]set POWER 0
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 21:13:52:096 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:13:52:102 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:13:52:111 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:13:52:386 ==>> $GBGGA,131352.000,2301.2583135,N,11421.9412864,E,1,23,0.59,75.629,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,38,14,50,187,35,2,47,239,33,1*49

$GBGSV,7,3,27,9,47,329,34,7,46,179,35,25,46,304,39,1,46,125,35,1*45

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,37,13,31,217,33,4,31,113,32,8,27,207,31,1*7F

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,33,38,15,198,33,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,36,23,10,264,32,5*48

$GBRMC,131352.000,A,2301.2583135,N,11421.9412864,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131352.000,2.438,0.184,0.182,0.271,1.773,1.805,2.809*70

[W][05:18:50][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:18:50][COMM]read ba

2025-07-31 21:13:52:416 ==>> ttery soc:255


2025-07-31 21:13:52:668 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:13:52:674 ==>> 检测【读大灯电压】
2025-07-31 21:13:52:684 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:13:52:859 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:50][COMM]arm_hub read adc[5],val[32992]


2025-07-31 21:13:52:972 ==>> 【读大灯电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:13:52:978 ==>> 检测【关闭大灯控制2】
2025-07-31 21:13:52:988 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:13:53:135 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:13:53:258 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:13:53:268 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:13:53:292 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:13:53:392 ==>> $GBGGA,131353.000,2301.2583138,N,11421.9412924,E,1,23,0.59,75.607,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,35,2,47,239,33,1*48

$GBGSV,7,3,27,9,47,329,35,7,46,179,35,25,46,304,40,1,46,125,35,1*4A

$GBGSV,7,4,27,40,44,161,37,60,42,240,38,24,41,29,39,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,37,13,31,217,34,4,31,113,32,8,27,207,31,1*78

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,33,38,15,198,34,23,10,264,34,1*73

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131353.000,A,2301.2583138,N,11421.9412924,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,131353.000,2.577,0.207,0.203,0.304,1.856,1.886,2.848*7C



2025-07-31 21:13:53:497 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:51][COMM]arm_hub read adc[5],val[92]


2025-07-31 21:13:53:547 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:13:53:554 ==>> 检测【打开WIFI(4)】
2025-07-31 21:13:53:563 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:13:53:574 ==>> [D][05:18:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:13:53:756 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:13:53:895 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:13:53:903 ==>> 检测【EC800M模组版本】
2025-07-31 21:13:53:911 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:13:54:389 ==>> $GBGGA,131354.000,2301.2583108,N,11421.9412867,E,1,23,0.59,75.586,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

$GBGSV,7,2,27,16,52,355,36,59,50,129,38,14,50,187,35,2,47,239,33,1*49

$GBGSV,7,3,27,9,47,329,34,7,46,179,35,25,46,304,40,1,46,125,35,1*4B

$GBGSV,7,4,27,40,44,161,37,60,42,240,38,24,41,29,39,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,37,13,31,217,33,4,31,113,31,8,27,207,31,1*7C

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,33,38,15,198,34,23,10,264,34,1*73

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*44

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131354.000,A,2301.2583108,N,11421.9412867,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131354.000,2.649,0.177,0.175,0.260,1.898,1.927,2.856*77

[D][05:18:52][COMM]read battery soc:255


2025-07-31 21:13:54:573 ==>> [D][05:18:52][COMM]63365 imu init OK
[D][05:18:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:13:54:768 ==>> +WIFISCAN:4,0,F88C21BCF57D,-37
+WIFISCAN:4,1,F42A7D1297A3,-69
+WIFISCAN:4,2,CC057790A741,-76
+WIFISCAN:4,3,44A1917CAD80,-82

[D][05:18:52][CAT1]wifi scan report total[4]


2025-07-31 21:13:54:920 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:13:55:385 ==>> [D][05:18:52][GNSS]recv submsg id[3]
$GBGGA,131355.000,2301.2583084,N,11421.9412949,E,1,23,0.59,75.575,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,39,39,53,15,38,6,52,351,34,1*41

$GBGSV,7,2,27,16,52,355,36,59,50,129,38,14,50,187,35,2,47,239,33,1*49

$GBGSV,7,3,27,9,47,329,34,7,46,179,35,25,46,304,40,1,46,125,35,1*4B

$GBGSV,7,4,27,40,44,161,37,60,42,240,38,24,41,29,39,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,37,13,31,217,33,4,31,113,32,8,27,207,31,1*7F

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,33,38,15,198,33,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131355.000,A,2301.2583084,N,11421.9412949,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,131355.000,2.831,0.160,0.159,0.234,2.005,2.031,2.925*79



2025-07-31 21:13:55:599 ==>> [D][05:18:53][COMM]64377 imu init OK
[D][05:18:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:13:55:796 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:13:55:962 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:13:56:174 ==>> [D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:53][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:53][COMM]accel parse set 0
[D][05:18:53][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:13:56:385 ==>>                                                                                                                                                                                                                                              33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,38,14,50,187,35,2,47,239,33,1*49

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,38,24,41,29,40,10,37,191,33,1*4B

$GBGSV,7,5,27,41,34,310,38,13,31,217,33,4,31,113,32,8,27,207,31,1*70

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*44

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131356.000,A,2301.2583044,N,11421.9413005,E,0.003,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,131356.000,2.801,0.202,0.199,0.298,1.986,2.011,2.887*7E

[D][05:18:54][COMM]read battery soc:255


2025-07-31 21:13:56:598 ==>> [D][05:18:54][COMM]65389 imu init OK


2025-07-31 21:13:56:993 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:13:57:054 ==>> [D][05:18:54][CAT1]SEND RAW data timeout
[D][05:18:54][CAT1]exec over: func id: 12, ret: -52
[D][05:18:54][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:54][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]exec over: func id: 12, ret: 132
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:54][PROT]CLEAN,SEND:1
[D][05:18:54][PROT]index:1 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:5
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:2
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][

2025-07-31 21:13:57:159 ==>> COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:18:54][PROT]sending traceid [9999999999900006]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[134]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052e48] format[0]
[D][05:18:54][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]exec over: func id: 12, ret: 132
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:54][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]exec over: func id: 12, ret: 132
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:54][CAT1

2025-07-31 21:13:57:189 ==>> ]<<< 
ERROR



2025-07-31 21:13:57:282 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 21:13:57:292 ==>> 检测【配置蓝牙地址】
2025-07-31 21:13:57:300 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 21:13:57:312 ==>>                                                                                                                         

2025-07-31 21:13:57:400 ==>>                                                                                                                                                                                                                             187,36,2,47,239,33,1*4B

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,13,31,217,33,4,31,113,32,8,27,207,31,1*70

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,111,32,1*46

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,264,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,38,5*44

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,264,32,5*49

$GBRMC,131357.000,A,2301.2583014,N,11421.9412996,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131357.000,2.517,0.193,0.190,0.284,1.812,1.838,2.713*74

[W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:13:57:490 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C897881DBBB8>】
2025-07-31 21:13:57:674 ==>> recv ble 1
recv ble 2
ble set mac ok :c8,97,88,1d,bb,b8
enable filters ret : 0

2025-07-31 21:13:57:784 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 21:13:57:794 ==>> 检测【BLETEST】
2025-07-31 21:13:57:815 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 21:13:57:871 ==>> 4A A4 01 A4 4A 


2025-07-31 21:13:58:085 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C897881DBBB8*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C897881DBBB899999OVER 150


2025-07-31 21:13:58:388 ==>> $GBGGA,131358.000,2301.2583036,N,11421.9413050,E,1,23,0.59,75.636,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,35,2,47,239,34,1*4F

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,13,31,217,33,4,31,113,32,8,27,207,31,1*70

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,111,32,1*46

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131358.000,A,2301.2583036,N,11421.9413050,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131358.000,2.619,0.162,0.160,0.238,1.873,1.897,2.748*7C

[D][05:18:56][COMM]read battery soc:255


2025-07-31 21:13:58:829 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 21:13:58:837 ==>> 该项需要延时执行
2025-07-31 21:13:59:386 ==>> $GBGGA,131359.000,2301.2582999,N,11421.9413096,E,1,23,0.59,75.696,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,59,50,129,40,14,50,187,36,2,47,239,34,1*42

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,31,8,27,207,31,1*74

$GBGSV,7,6,27,42,26,167,34,34,23,141,35,5,22,257,31,12,22,111,32,1*47

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,35,1*74

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131359.000,A,2301.2582999,N,11421.9413096,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131359.000,2.773,0.272,0.267,0.404,1.965,1.988,2.813*77

[W][05:18:57][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 21:14:00:386 ==>> $GBGGA,131400.000,2301.2582991,N,11421.9413167,E,1,23,0.59,75.722,M,-1.770,M,,*59

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,42,3,62,190,40,39,53,15,38,6,52,351,35,1*4D

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,40,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,34,23,141,35,5,22,257,31,12,22,111,32,1*46

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,35,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131400.000,A,2301.2582991,N,11421.9413167,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131400.000,2.894,0.201,0.198,0.295,2.034,2.056,2.861*71

[D][05:18:58][COMM]read battery soc:255


2025-07-31 21:14:01:387 ==>> $GBGGA,131401.000,2301.2583003,N,11421.9413225,E,1,23,0.59,75.738,M,-1.770,M,,*55

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,42,3,62,190,40,39,53,15,38,6,52,351,35,1*4D

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,36,1*4A

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,40,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,35,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131401.000,A,2301.2583003,N,11421.9413225,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,131401.000,2.882,0.189,0.187,0.277,2.027,2.048,2.841*79



2025-07-31 21:14:02:394 ==>> $GBGGA,131402.000,2301.2583003,N,11421.9413210,E,1,23,0.59,75.736,M,-1.770,M,,*5E

$GBGSA,A,3,33,03,39,06,16,59,14,09,07,25,01,40,1.30,0.59,1.16,4*0D

$GBGSA,A,3,60,24,10,41,13,08,42,34,12,44,23,,1.30,0.59,1.16,4*0D

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,36,1*4A

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,40,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,34,5,24,258,31,34,23,141,35,12,22,111,32,1*4E

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131402.000,A,2301.2583003,N,11421.9413210,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131402.000,2.569,0.163,0.161,0.238,1.837,1.859,2.658*72

[D][05:19:00][COMM]read battery soc:255


2025-07-31 21:14:03:417 ==>> $GBGGA,131403.000,2301.2582975,N,11421.9413217,E,1,24,0.58,75.749,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,36,1*4A

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,40,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131403.000,A,2301.2582975,N,11421.9413217,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131403.000,2.567,0.209,0.207,0.311,1.835,1.857,2.645*7B



2025-07-31 21:14:04:386 ==>> $GBGGA,131404.000,2301.2582904,N,11421.9413245,E,1,24,0.58,75.768,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,39,24,41,29,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131404.000,A,2301.2582904,N,11421.9413245,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,131404.000,3.689,0.201,0.199,0.298,2.467,2.484,3.214*7A

[D][05:19:02][COMM]read battery soc:255


2025-07-31 21:14:05:387 ==>> $GBGGA,131405.000,2301.2582818,N,11421.9413219,E,1,24,0.58,75.776,M,-1.770,M,,*51

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,40,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,32,5*48

$GBRMC,131405.000,A,2301.2582818,N,11421.9413219,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131405.000,2.290,0.209,0.207,0.309,1.658,1.679,2.464*7D



2025-07-31 21:14:06:389 ==>> $GBGGA,131406.000,2301.2582746,N,11421.9413176,E,1,24,0.58,75.792,M,-1.770,M,,*56

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,39,10,37,191,33,1*4B

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131406.000,A,2301.2582746,N,11421.9413176,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131406.000,3.550,0.233,0.230,0.347,2.393,2.410,3.129*71

[D][05:19:04][COMM]read battery soc:255


2025-07-31 21:14:07:109 ==>> [D][05:19:04][CAT1]exec over: func id: 15, ret: -93
[D][05:19:04][CAT1]sub id: 15, ret: -93

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:04][SAL ]socket send fail. id[4]
[D][05:19:04][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:04][CAT1]gsm read msg sub id: 12
[D][05:19:04][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:04][M2M ]m2m select fd[4]
[D][05:19:04][M2M ]socket[4] Link is disconnected
[D][05:19:04][M2M ]tcpclient close[4]
[D][05:19:04][SAL ]socket[4] has closed
[D][05:19:04][PROT]protocol read data ok
[E][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:04][PROT]M2M Send Fail [1629955144]
[D][05:19:04][PROT]CLEAN,SEND:1
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:04][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:19:04][CAT1]exec over: func id: 12, ret: 132
[D][05:19:04][CAT1]gsm read msg sub id: 10
[D][05:19:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:04][CAT1]<<< 
+

2025-07-31 21:14:07:140 ==>> CGATT: 1

OK

[D][05:19:04][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 21:14:07:486 ==>> $GBGGA,131407.000,2301.2582719,N,11421.9413089,E,1,24,0.58,75.776,M,-1.770,M,,*56

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,40,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,35,5,24,258,32,34,23,141,35,12,22,111,32,1*4C

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131407.000,A,2301.2582719,N,11421.9413089,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131407.000,3.683,0.223,0.220,0.332,2.462,2.478,3.185*7E

[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]exec over: func id: 10, 

2025-07-31 21:14:07:576 ==>> ret: 6
[D][05:19:05][CAT1]sub id: 10, ret: 6

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:05][M2M ]m2m gsm shut done, ret[0]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:05][SAL ]open socket ind id[4], rst[0]
[D][05:19:05][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:05][SAL ]Cellular task submsg id[8]
[D][05:19:05][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:05][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:05][CAT1]gsm read msg sub id: 8
[D][05:19:05][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:05][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:05][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:14:07:710 ==>> [D][05:19:05][CAT1]pdpdeact urc len[22]


2025-07-31 21:14:08:415 ==>> $GBGGA,131408.000,2301.2582660,N,11421.9413084,E,1,24,0.58,75.764,M,-1.770,M,,*58

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,36,1*4A

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,39,10,37,191,33,1*4B

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,35,5,24,258,32,34,23,141,35,12,22,111,32,1*4C

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131408.000,A,2301.2582660,N,11421.9413084,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131408.000,3.595,0.188,0.186,0.279,2.416,2.432,3.135*70

[D][05:19:06][COMM]read battery soc:255


2025-07-31 21:14:08:827 ==>> [D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:06][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:06][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:06][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:06][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:14:08:842 ==>> 此处延时了:【10000】毫秒
2025-07-31 21:14:08:849 ==>> 检测【检测WiFi结果】
2025-07-31 21:14:08:860 ==>> WiFi信号:【44A1917CAD81】,信号值:-76
2025-07-31 21:14:08:871 ==>> WiFi信号:【44A1917CAD80】,信号值:-76
2025-07-31 21:14:08:884 ==>> WiFi信号:【CC057790A7C1】,信号值:-79
2025-07-31 21:14:08:910 ==>> WiFi信号:【F86FB0660A82】,信号值:-87
2025-07-31 21:14:08:922 ==>> WiFi信号:【F88C21BCF57D】,信号值:-37
2025-07-31 21:14:08:953 ==>> WiFi信号:【F42A7D1297A3】,信号值:-69
2025-07-31 21:14:08:960 ==>> WiFi信号:【CC057790A741】,信号值:-76
2025-07-31 21:14:08:984 ==>> WiFi数量【7】, 最大信号值:-37
2025-07-31 21:14:08:995 ==>> 检测【检测GPS结果】
2025-07-31 21:14:09:027 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 21:14:09:038 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【59】,信号值:【39】
北斗星号:【40】,信号值:【37】
北斗星号:【16】,信号值:【36】
北斗星号:【7】,信号值:【35】
北斗星号:【1】,信号值:【35】
北斗星号:【25】,信号值:【40】
北斗星号:【24】,信号值:【40】
北斗星号:【60】,信号值:【38】
北斗星号:【9】,信号值:【35】
北斗星号:【41】,信号值:【39】
北斗星号:【38】,信号值:【38】
北斗星号:【42】,信号值:【35】
北斗星号:【14】,信号值:【36】
北斗星号:【34】,信号值:【35】

2025-07-31 21:14:09:047 ==>> 检测【CSQ强度】
2025-07-31 21:14:09:058 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:14:09:394 ==>> [D][05:19:06][CAT1]opened : 0, 0
[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:06][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:19:06][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:06][PROT]index:1 1629955146
[D][05:19:06][PROT]is_send:0
[D][05:19:06][PROT]sequence_num:5
[D][05:19:06][PROT]retry_timeout:0
[D][05:19:06][PROT]retry_times:1
[D][05:19:06][PROT]send_path:0x2
[D][05:19:06][PROT]min_index:1, type:0xD302, priority:0
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:06][PROT]sending traceid [9999999999900006]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND


2025-07-31 21:14:09:499 ==>> 
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[134]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:06][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 21
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:06][CAT1]Send Data To Server[134][137] ... ->:
0043B682113311331133113311331B88B3CE1266F1FE0CE6E161CF8D5BA7D603DC928301A25F980E80FCDF69E24FCCD8262E24B14AEC8E8F79901CEF9734340534F464
[D][05:19:06][CAT1]<<< 
SEND OK

[D][05:19:06][CAT1]exec over: func id: 15, ret: 11
[D][05:19:06][CAT1]sub id: 15, ret: 11

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:06][PROT]M2M Send ok [1629955146]
$GBGGA,131409.000,2301.2582612,N,11421.94

2025-07-31 21:14:09:604 ==>> 13153,E,1,24,0.58,75.788,M,-1.770,M,,*55

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,39,10,37,191,33,1*4B

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,35,38,15,198,34,23,10,265,34,1*74

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131409.000,A,2301.2582612,N,11421.9413153,E,0.003,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,131409.000,3.667,0.217,0.214,0.322,2.453,2.468,3.163*70



2025-07-31 21:14:09:671 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 21:14:09:678 ==>> 检测【关闭GSM联网】
2025-07-31 21:14:09:699 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 21:14:09:861 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:07][COMM]GSM test
[D][05:19:07][COMM]GSM test disable


2025-07-31 21:14:09:990 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 21:14:09:998 ==>> 检测【4G联网测试】
2025-07-31 21:14:10:018 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 21:14:11:039 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:07][COMM]Main Task receive event:14
[D][05:19:07][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955147, allstateRepSeconds = 0
[D][05:19:07][COMM]index:0,power_mode:0xFF
[D][05:19:07][COMM]index:1,sound_mode:0xFF
[D][05:19:07][COMM]index:2,gsensor_mode:0xFF
[D][05:19:07][COMM]index:3,report_freq_mode:0xFF
[D][05:19:07][COMM]index:4,report_period:0xFF
[D][05:19:07][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:07][COMM]index:6,normal_reset_period:0xFF
[D][05:19:07][COMM]index:7,spock_over_speed:0xFF
[D][05:19:07][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:07][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:07][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:07][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:07][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:07][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:07][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:07][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:07][COMM]index:16,imu_config_params:0xFF
[D][05:19:07][COMM]index:17,long_connect_params:0xFF
[D][05:19:07][COMM]index:18,detain_mark:0xFF
[D][05:19:07][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:07][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:07][COMM]inde

2025-07-31 21:14:11:145 ==>> x:21,mc_mode:0xFF
[D][05:19:07][COMM]index:22,S_mode:0xFF
[D][05:19:07][COMM]index:23,overweight:0xFF
[D][05:19:07][COMM]index:24,standstill_mode:0xFF
[D][05:19:07][COMM]index:25,night_mode:0xFF
[D][05:19:07][COMM]index:26,experiment1:0xFF
[D][05:19:07][COMM]index:27,experiment2:0xFF
[D][05:19:07][COMM]index:28,experiment3:0xFF
[D][05:19:07][COMM]index:29,experiment4:0xFF
[D][05:19:07][COMM]index:30,night_mode_start:0xFF
[D][05:19:07][COMM]index:31,night_mode_end:0xFF
[D][05:19:07][COMM]index:33,park_report_minutes:0xFF
[D][05:19:07][COMM]index:34,park_report_mode:0xFF
[D][05:19:07][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:07][COMM]index:38,charge_battery_para: FF
[D][05:19:07][COMM]index:39,multirider_mode:0xFF
[D][05:19:07][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:07][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:07][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:07][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:07][COMM]index:44,riding_duration_config:0xFF
[D][05:19:07][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:07][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:07][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:1

2025-07-31 21:14:11:249 ==>> 9:07][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:07][COMM]index:49,mc_load_startup:0xFF
[D][05:19:07][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:07][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:07][COMM]index:52,traffic_mode:0xFF
[D][05:19:07][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:07][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:07][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:07][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:07][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:07][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:07][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:07][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:07][COMM]index:63,experiment5:0xFF
[D][05:19:07][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:07][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:07][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:07][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:07][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:07][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:07][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:07][COMM]index:72,experiment6:0xFF
[D]

2025-07-31 21:14:11:354 ==>> [05:19:07][COMM]index:73,experiment7:0xFF
[D][05:19:07][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:07][COMM]index:75,zero_value_from_server:-1
[D][05:19:07][COMM]index:76,multirider_threshold:255
[D][05:19:07][COMM]index:77,experiment8:255
[D][05:19:07][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:07][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:07][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:07][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:07][COMM]index:83,loc_report_interval:255
[D][05:19:07][COMM]index:84,multirider_threshold_p2:255
[D][05:19:07][COMM]index:85,multirider_strategy:255
[D][05:19:07][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:07][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:07][COMM]index:90,weight_param:0xFF
[D][05:19:07][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:07][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:07][COMM]index:95,current_limit:0xFF
[D][05:19:07][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:07][COMM]index:100,location_mode:0xFF

[D][05:19:07][HSDK][0] flush to flash addr:[0xE42500] --- write le

2025-07-31 21:14:11:459 ==>> n --- [256]
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:07][PROT]index:0 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:7
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:1
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][PROT]sending traceid [9999999999900008]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[W][05:19:08][PROT]add success [1629955148],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:08][SAL ]sock send credit cnt[6]
[D][05:19:08][SAL ]sock send ind credit cnt[6]
[D][05:19:08][M2M ]m2m send data len[294]
[D][05:19:08][SAL ]Cellular task submsg id[10]
[D][05:19:08][SAL ]cellular SEND socket id[0] type[1

2025-07-31 21:14:11:564 ==>> ], len[294], data[0x20052df0] format[0]
[D][05:19:08][CAT1]gsm read msg sub id: 13
[D][05:19:08][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
$GBGGA,131410.000,2301.2582555,N,11421.9413213,E,1,24,0.58,75.818,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.29,0.58,1.15,4*01

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,23,1.29,0.58,1.15,4*03

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,29,40,10,37,191,33,1*45

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,31,1*77

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,15,198,34,23,10,265,34,1*75

$GBGSV,3,1,10,33,64,235,43,39,53,15,41,25,46,304,41,40,44,161,39,5*42

$GBGSV,3,2,10,24,41,29,41,41,34,310,39,42,26,167,37,34,23,141,34,5*46

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131410.000,A,2301.2582555,N,11421.9413213,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K

2025-07-31 21:14:11:654 ==>> ,A*2C

$GBGST,131410.000,3.695,0.204,0.202,0.303,2.467,2.482,3.171*73

[D][05:19:08][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:08][CAT1]exec over: func id: 13, ret: 21
[D][05:19:08][M2M ]get csq[23]
[D][05:19:08][CAT1]gsm read msg sub id: 15
[D][05:19:08][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:08][CAT1]Send Data To Server[294][294] ... ->:
0093B98E113311331133113311331B88B2CEFB57434B2E2EDE7AE826B04444E0EC4C8BAAF366DF51D2644AFC3BD458DBBBADB43757824C05FAABAD7AADBD40233970C0F7F5642EDBBFEDB6E27A8989CFF513CBF13F8471213E825D6EABA89377E57135ABC479FEEC21346655EEA7CFE9A2A2DD01549A9D15EDE8340ED0BC0DB473102D176FB5E7242B2ED59706179E01B16166
[D][05:19:08][CAT1]<<< 
SEND OK

[D][05:19:08][CAT1]exec over: func id: 15, ret: 11
[D][05:19:08][CAT1]sub id: 15, ret: 11

[D][05:19:08][SAL ]Cellular task submsg id[68

2025-07-31 21:14:11:759 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 21:14:12:036 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 21:14:12:044 ==>> 检测【关闭GPS】
2025-07-31 21:14:12:056 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:14:12:469 ==>> $GBGGA,131412.000,2301.2582535,N,11421.9413291,E,1,25,0.57,75.816,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,59,14,02,09,07,25,01,1.25,0.57,1.12,4*05

$GBGSA,A,3,40,60,24,10,41,13,08,42,34,12,44,38,1.25,0.57,1.12,4*0D

$GBGSA,A,3,23,,,,,,,,,,,,1.25,0.57,1.12,4*0F

$GBGSV,7,1,27,33,64,235,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,59,50,129,39,14,50,187,36,2,47,239,34,1*4C

$GBGSV,7,3,27,9,47,329,35,7,46,179,36,25,46,304,40,1,46,125,35,1*49

$GBGSV,7,4,27,40,44,161,37,60,42,240,40,24,41,29,40,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,310,38,13,31,217,34,4,31,113,32,8,27,207,32,1*74

$GBGSV,7,6,27,42,26,167,35,5,24,258,31,34,23,141,35,12,22,111,32,1*4F

$GBGSV,7,7,27,44,20,85,34,38,20,192,34,23,10,265,34,1*79

$GBGSV,3,1,10,33,64,235,44,39,53,15,41,25,46,304,41,40,44,161,39,5*45

$GBGSV,3,2,10,24,41,29,40,41,34,310,39,42,26,167,37,34,23,141,34,5*47

$GBGSV,3,3,10,44,20,85,37,23,10,265,31,5*4B

$GBRMC,131412.000,A,2301.2582535,N,11421.9413291,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131412.000,3.622,0.192,0.189,0.281,2.429,2.443,3.123*7A

[W][05:19:10][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:

2025-07-31 21:14:12:544 ==>> 19:10][GNSS]stop locating
[D][05:19:10][GNSS]stop event:8
[D][05:19:10][GNSS]GPS stop. ret=0
[D][05:19:10][GNSS]all continue location stop
[W][05:19:10][GNSS]stop locating
[D][05:19:10][GNSS]all sing location stop
[D][05:19:10][CAT1]gsm read msg sub id: 24
[D][05:19:10][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][COMM]read battery soc:255
[D][05:19:10][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 24, ret: 6
[D][05:19:10][CAT1]sub id: 24, ret: 6



2025-07-31 21:14:12:596 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 21:14:12:612 ==>> 检测【清空消息队列2】
2025-07-31 21:14:12:641 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:14:12:740 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:10][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:14:12:901 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:14:12:911 ==>> 检测【轮动检测】
2025-07-31 21:14:12:932 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 21:14:12:965 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 21:14:13:025 ==>> [D][05:19:10][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 21:14:13:296 ==>> [D][05:19:11][GNSS]recv submsg id[1]
[D][05:19:11][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:11][GNSS]location stop evt done evt


2025-07-31 21:14:13:415 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 21:14:13:475 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 21:14:13:708 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 21:14:13:716 ==>> 检测【关闭小电池】
2025-07-31 21:14:13:724 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:14:13:760 ==>> 6A A6 02 A6 6A 


2025-07-31 21:14:13:865 ==>> Battery OFF
OVER 150


2025-07-31 21:14:14:003 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 21:14:14:016 ==>> 检测【进入休眠模式】
2025-07-31 21:14:14:043 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:14:14:159 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:14:14:249 ==>>                                                      :19:11][COMM]main task tmp_sleep_event = 8
[D][05:19:11][COMM]prepare to sleep
[D][05:19:11][CAT1]gsm read msg sub id: 12
[D][05:19:11][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 21:14:14:354 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 21:14:15:112 ==>> [D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][M2M ]tcpclient close[4]
[D][05:19:12][SAL ]Cellular task submsg id[12]
[D][05:19:12][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:12][CAT1]gsm read msg sub id: 9
[D][05:19:12][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 9, ret: 6
[D][05:19:12][CAT1]sub id: 9, ret: 6

[D][05:19:12][SAL ]Cellular task submsg id[68]
[D][05:19:12][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:12][SAL ]socket close ind. id[4]
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok
[D][05:19:12][CAT1]pdpdeact urc len[22]


2025-07-31 21:14:15:420 ==>> [E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:14:15:933 ==>> [E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[W][05:19:13][COMM]CAN STOP!
[D][05:19:13][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:13][COMM]------------ready to Power off Acckey 1------------
[D][05:19:13][COMM]------------ready to Power off Acckey 2------------
[D][05:19:13][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:13][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1287
[D][05:19:13][COMM]bat sleep fail, reason:-1
[D][05:19:13][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:13][COMM]accel parse set 0
[D][05:19:13][COMM]imu rest ok. 84610
[D][05:19:13][COMM]imu sleep 0
[W][05:19:13][COMM]now sleep


2025-07-31 21:14:16:100 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:14:16:108 ==>> 检测【检测33V休眠电流】
2025-07-31 21:14:16:136 ==>> 开始33V电流采样
2025-07-31 21:14:16:148 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:16:206 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 21:14:17:214 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 21:14:17:276 ==>> Current33V:????:19.31

2025-07-31 21:14:17:729 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:17:737 ==>> 【检测33V休眠电流】通过,【19.31uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:14:17:761 ==>> 该项需要延时执行
2025-07-31 21:14:19:739 ==>> 此处延时了:【2000】毫秒
2025-07-31 21:14:19:773 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 21:14:19:796 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:14:19:880 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1665mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1879mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:14:20:774 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:14:20:782 ==>> 检测【打开小电池2】
2025-07-31 21:14:20:790 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:14:20:865 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:14:21:082 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:14:21:094 ==>> 该项需要延时执行
2025-07-31 21:14:21:595 ==>> 此处延时了:【500】毫秒
2025-07-31 21:14:21:608 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 21:14:21:631 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:14:21:671 ==>> 5A A5 02 5A A5 


2025-07-31 21:14:21:776 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:14:21:882 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:14:21:890 ==>> 该项需要延时执行
2025-07-31 21:14:22:394 ==>> 此处延时了:【500】毫秒
2025-07-31 21:14:22:407 ==>> 检测【进入休眠模式2】
2025-07-31 21:14:22:422 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:14:22:454 ==>> [D][05:19:20][COMM]------------ready to Power on Acckey 1------------
[D][05:19:20][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:20][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:20][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:20][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:20][COMM]----- get Acckey 1 and value:1------------
[W][05:19:20][COMM]CAN START!
[D][05:19:20][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:20][COMM]1x1 frm_can_tp_send ok
[D][05:19:20][CAT1]gsm read msg sub id: 12
[D][05:19:20][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:20][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 91065
[D][05:19:20][COMM][Audio]exec status ready.
[D][05

2025-07-31 21:14:22:514 ==>> :19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 12, ret: 6
[D][05:19:20][COMM]imu wakeup ok. 91079
[D][05:19:20][COMM]imu wakeup 1
[W][05:19:20][COMM]wake up system, wakeupEvt=0x80
[D][05:19:20][COMM]frm_can_weigth_power_set 1
[D][05:19:20][COMM]Clear Sleep Block Evt
[D][05:19:20][COMM]Main Task receive event:28 finished processing


2025-07-31 21:14:22:771 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:20][COMM]Main Task receive event:28
[D][05:19:20][COMM]prepare to sleep
[D][05:19:20][CAT1]gsm read msg sub id: 12
[D][05:19:20][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 12, ret: 6
[W][05:19:20][COMM]CAN STOP!
[D][05:19:20][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:20][COMM]------------ready to Power off Acckey 1------------
[D][05:19:20][COMM]------------ready to Power off Acckey 2------------
[D][05:19:20][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:20][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 127
[D][05:19:20][COMM]bat sleep fail, reason:-1
[D][05:19:20][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:20][COMM]accel parse set 0
[D][05:19:20][COMM]imu rest ok. 91433
[D][05:19:20][COMM]imu sleep 0
[W][05:19:20][COMM]now sleep


2025-07-31 21:14:22:978 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:14:22:989 ==>> 检测【检测小电池休眠电流】
2025-07-31 21:14:23:002 ==>> 开始小电池电流采样
2025-07-31 21:14:23:020 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:23:089 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:14:24:090 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:14:24:182 ==>> CurrentBattery:ƽ��:67.57

2025-07-31 21:14:24:605 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:24:619 ==>> 【检测小电池休眠电流】通过,【67.57uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:14:24:646 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 21:14:24:659 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:14:24:688 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:14:24:892 ==>> [D][05:19:22][COMM]------------ready to Power on Acckey 1------------
[D][05:19:22][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:22][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:22][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:22][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:22][COMM]----- get Acckey 1 and value:1------------
[W][05:19:22][COMM]CAN START!
[E][05:19:22][COMM]1x1 rx timeout
[E][05:19:22][COMM]1x1 tp timeout
[E][05:19:22][COMM]1x1 error -3.
[D][05:19:22][COMM]read battery soc:0
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:22][COMM][Audio]exec status ready.
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 12, ret: 6
[D][05:19:22][COMM]imu wakeup ok. 93564
[D][05:19:22][COMM]imu wakeup 1
[W][05:19:22][COMM]wake up system, wakeupEvt=0x80
[D][05:19:22][COMM]frm_can_weigth_power_set 1
[D][05:19:22][COMM]Clear Sleep Block Evt
[D][05:19:22][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:22][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:14:24:971 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:14:24:979 ==>> 该项需要延时执行
2025-07-31 21:14:25:178 ==>> [E][05:19:22][COMM]1x1 rx timeout
[D][05:19:22][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:14:25:283 ==>> [D][05:19:23][COMM]msg 02A0 loss. last_tick:93531. cur_tick:94043. period:50
[D][05:19:23][COMM]msg 02A4 loss. last_tick:93531. cur_tick:94044. period:50
[D][05:19:23][COMM]msg 02A5 loss. last_tick:93531. cur_tick:94044

2025-07-31 21:14:25:328 ==>> . period:50
[D][05:19:23][COMM]msg 02A6 loss. last_tick:93531. cur_tick:94044. period:50
[D][05:19:23][COMM]msg 02A7 loss. last_tick:93531. cur_tick:94045. period:50
[D][05:19:23][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 94045
[D][05:19:23][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 94046


2025-07-31 21:14:25:478 ==>> 此处延时了:【500】毫秒
2025-07-31 21:14:25:497 ==>> 检测【检测唤醒】
2025-07-31 21:14:25:528 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:14:25:996 ==>> [E][05:19:23][COMM]1x1 rx timeout
[E][05:19:23][COMM]1x1 tp timeout
[E][05:19:23][COMM]1x1 error -3.
[D][05:19:23][COMM]Main Task receive event:28 finished processing
[W][05:19:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:23][FCTY]==========Modules-nRF5340 ==========
[D][05:19:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:23][FCTY]DeviceID    = 460130020290491
[D][05:19:23][FCTY]HardwareID  = 867222087912343
[D][05:19:23][FCTY]MoBikeID    = 9999999999
[D][05:19:23][FCTY]LockID      = FFFFFFFFFF
[D][05:19:23][FCTY]BLEFWVersion= 105
[D][05:19:23][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:19:23][FCTY]Bat         = 3824 mv
[D][05:19:23][FCTY]Current     = 0 ma
[D][05:19:23][FCTY]VBUS        = 2600 mv
[D][05:19:23][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:23][FCTY]Ext battery vol = 32, adc = 1287
[D][05:19:23][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 151 mv
[D][05:19:23][FCTY]Bike Type flag is invalied
[D][05:19:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[

2025-07-31 21:14:26:101 ==>> D][05:19:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:23][FCTY]Bat1         = 3699 mv
[D][05:19:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:23][FCTY]==========Modules-nRF5340 ==========
[D][05:19:23][COMM]msg 0220 loss. last_tick:93531. cur_tick:94539. period:100
[D][05:19:23][COMM]msg 0221 loss. last_tick:93531. cur_tick:94540. period:100
[D][05:19:23][COMM]msg 0224 loss. last_tick:93531. cur_tick:94540. period:100
[D][05:19:23][COMM]msg 0260 loss. last_tick:93531. cur_tick:94541. period:100
[D][05:19:23][COMM]msg 0280 loss. last_tick:93531. cur_tick:94541. period:100
[D][05:19:23][COMM]msg 02C0 loss. last_tick:93531. cur_tick:94541. period:100
[D][05:19:23][COMM]msg 02C1 loss. last_tick:93531. cur_tick:94542. period:100
[D][05:19:23][COMM]msg 02C2 loss. last_tick:93531. cur_tick:94542. period:100
[D][05:19:23][COMM]msg 02E0 loss. last_tick:93531. cur_tick:94543. period:100
[D][05:19:23][COMM]msg 02E1 loss. last_tick:93531. cur_tick:94543. period:100
[D][05:19:23][COMM]msg 02E2 loss. last_tick:93531. cur_tick:94543. period:100
[D][05:19:23][COMM]msg 0300 l

2025-07-31 21:14:26:206 ==>> oss. last_tick:93531. cur_tick:94544. period:100
[D][05:19:23][COMM]msg 0301 loss. last_tick:93531. cur_tick:94544. period:100
[D][05:19:23][COMM]bat msg 0240 loss. last_tick:93531. cur_tick:94544. period:100. j,i:1 54
[D][05:19:23][COMM]bat msg 0241 loss. last_tick:93531. cur_tick:94545. period:100. j,i:2 55
[D][05:19:23][COMM]bat msg 0242 loss. last_tick:93531. cur_tick:94545. period:100. j,i:3 56
[D][05:19:23][COMM]bat msg 0244 loss. last_tick:93531. cur_tick:94545. period:100. j,i:5 58
[D][05:19:23][COMM]bat msg 024E loss. last_tick:93531. cur_tick:94546. period:100. j,i:15 68
[D][05:19:23][COMM]bat msg 024F loss. last_tick:93531. cur_tick:94546. period:100. j,i:16 69
[D][05:19:23][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 94547
[D][05:19:23][COMM]CAN message bat fault change: 0x00000000->0x0001802E 94547
[D][05:19:23][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 94547


2025-07-31 21:14:26:300 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 21:14:26:316 ==>> 检测【关机】
2025-07-31 21:14:26:332 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:14:26:352 ==>>                                                                                                                                                                           [D][05:19:24][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:24][COMM]frm_peripheral_device_poweron type 0.... 
[D]

2025-07-31 21:14:26:360 ==>> [05:19:24][COMM]----- get Acckey 1 and value:1------------
[D][05:19:24][COMM]----- get Acckey 2 and value:0------------
[D][05:19:24][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:14:27:258 ==>>                                                                                                                                   05:19:24][COMM]----- get Acckey 2 and value:1------------
[D][05:19:24][COMM]more than the number of battery plugs
[D][05:19:24][COMM]VBUS is 1
[D][05:19:24][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:24][COMM]file:B50 exist
[D][05:19:24][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:24][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:24][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:24][COMM]Bat auth off fail, error:-1
[D][05:19:24][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:24][COMM]----- get Acckey 1 and value:1------------
[D][05:19:24][COMM]----- get Acckey 2 and value:1------------
[D][05:19:24][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:24][COMM]----- get Acckey 1 and value:1------------
[D][05:19:24][COMM]----- get Acckey 2 and value:1------------
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:24][COMM]file:B50 exist
[D][05:19:24][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:24][COMM]read file, len:10800, nu

2025-07-31 21:14:27:333 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:14:27:363 ==>> m:3
[D][05:19:24][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[W][05:19:24][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:24][COMM]--->crc16:0xb8a
[D][05:19:24][COMM]read file success
[W][05:19:24][COMM][Audio].l:[936].close hexlog save
[D][05:19:24][COMM]accel parse set 1
[D][05:19:24][COMM][Audio]mon:9,05:19:24
[D][05:19:24][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:24][COMM]arm_hub_enable: hub power: 0
[D][05:19:24][HSDK]hexlog index save 0 6144 24 @ 0 : 0
[D][05:19:24][HSDK]write save hexlog index [0]
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:1

2025-07-31 21:14:27:468 ==>> 9:24][COMM]Main Task receive event:65
[D][05:19:24][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:24][COMM]Main Task receive event:65 finished processing
[D][05:19:24][COMM]Main Task receive event:66
[D][05:19:24][COMM]Try to Auto Lock Bat
[D][05:19:24][COMM]Main Task receive event:66 finished processing
[D][05:19:24][COMM]Main Task receive event:60
[D][05:19:24][COMM]smart_helmet_vol=255,255
[D][05:19:24][COMM]BAT CAN get state1 Fail 204
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:24][COMM]BAT CAN get soc Fail, 204
[D][05:19:24][COMM]BAT CAN get state2 fail 204
[D][05:19:24][COMM]get soh error
[E][05:19:24][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:24][COMM]report elecbike
[W][05:19:24][PROT]remove success[1629955164],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:24][COMM]Main Task receive event:60 finished processing
[D][05:19:24][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][PROT]index

2025-07-31 21:14:27:573 ==>> :0
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:24][PROT]is_send:1
[D][05:19:24][PROT]sequence_num:9
[D][05:19:24][PROT]retry_timeout:0
[D][05:19:24][PROT]retry_times:3
[D][05:19:24][PROT]send_path:0x3
[D][05:19:24][PROT]msg_type:0x5d03
[D][05:19:24][PROT]===========================================================
[W][05:19:24][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955164]
[D][05:19:24][PROT]===========================================================
[D][05:19:24][PROT]Sending traceid[999999999990000A]
[D][05:19:24][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:24][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:24][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:24][COMM]Receive Bat Lock cmd 0
[D][05:19:24][COMM]VBUS is 1
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:24][COMM]Main Task receive event:61
[D][05:19:24][COMM][D301]:type:3, trace id:280
[D][05:19:24][COM

2025-07-31 21:14:27:678 ==>> M]id[], hw[000
[D][05:19:24][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:24][SAL ]open socket ind id[4], rst[0]
[D][05:19:24][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:24][SAL ]Cellular task submsg id[8]
[D][05:19:24][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:24][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:24][COMM]get mcMaincircuitVolt error
[D][05:19:24][COMM]get mcSubcircuitVolt error
[D][05:19:24][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:24][COMM]BAT CAN get state1 Fail 204
[D][05:19:24][COMM]BAT CAN get soc Fail, 204
[D][05:19:24][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:24][COMM]BAT CAN get state2 fail 204
[D][05:19:24][COMM]get bat work mode err
[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:24][COM

2025-07-31 21:14:27:783 ==>> M]Main Task receive event:61 finished processing
[D][05:19:24][CAT1]gsm read msg sub id: 8
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:24][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:24][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:24][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:24][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:24][CAT1]<<< 
+CME ERROR: 100

[D][05:19:24][COMM]read battery soc:255
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_

2025-07-31 21:14:27:873 ==>> play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:24][COMM]Power Off


2025-07-31 21:14:27:978 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:25][COMM]arm_hub_enable: hub power: 0
[D][05:19:25][HSDK]hexlog index save 0 6144 24 @ 0 : 0
[D][05:19:25][HSDK]write save hexlog index [0]
[D][05:19:25][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:25][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 21:14:28:135 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 21:14:28:145 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 21:14:28:153 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:14:28:278 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:14:28:368 ==>> [D][05:19:26][FCTY]get_ext_4

2025-07-31 21:14:28:431 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:14:28:445 ==>> 检测【检测小电池关机电流】
2025-07-31 21:14:28:468 ==>> 开始小电池电流采样
2025-07-31 21:14:28:482 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:28:533 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:14:29:540 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:14:29:585 ==>> CurrentBattery:ƽ��:68.55

2025-07-31 21:14:30:048 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:14:30:057 ==>> 【检测小电池关机电流】通过,【68.55uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 21:14:30:542 ==>> MES过站成功
2025-07-31 21:14:30:559 ==>> #################### 【测试结束】 ####################
2025-07-31 21:14:30:591 ==>> 关闭5V供电
2025-07-31 21:14:30:608 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:14:30:664 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:14:31:604 ==>> 关闭5V供电成功
2025-07-31 21:14:31:618 ==>> 关闭33V供电
2025-07-31 21:14:31:633 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:14:31:665 ==>> 5A A5 02 5A A5 


2025-07-31 21:14:31:770 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:14:32:606 ==>> 关闭33V供电成功
2025-07-31 21:14:32:619 ==>> 关闭3.7V供电
2025-07-31 21:14:32:632 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:14:32:667 ==>> 6A A6 02 A6 6A 


2025-07-31 21:14:32:772 ==>> Battery OFF
OVER 150


2025-07-31 21:14:33:583 ==>>  

2025-07-31 21:14:33:613 ==>> 关闭3.7V供电成功
