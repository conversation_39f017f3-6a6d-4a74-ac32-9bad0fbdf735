2025-07-31 20:17:20:461 ==>> MES查站成功:
查站序号:P5100010053128F1验证通过
2025-07-31 20:17:20:469 ==>> 扫码结果:P5100010053128F1
2025-07-31 20:17:20:472 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:17:20:475 ==>> 测试参数版本:2024.10.11
2025-07-31 20:17:20:478 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:17:20:482 ==>> 检测【打开透传】
2025-07-31 20:17:20:486 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:17:20:551 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:17:20:822 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:17:20:833 ==>> 检测【检测接地电压】
2025-07-31 20:17:20:836 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:17:20:948 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:17:21:104 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:17:21:107 ==>> 检测【打开小电池】
2025-07-31 20:17:21:109 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:17:21:251 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:17:21:375 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:17:21:377 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:17:21:379 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:17:21:447 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:17:21:662 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:17:21:666 ==>> 检测【等待设备启动】
2025-07-31 20:17:21:672 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:17:21:986 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:17:22:166 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:17:22:703 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:17:22:858 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:17:23:240 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:17:23:713 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:17:23:762 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:17:23:790 ==>> 检测【产品通信】
2025-07-31 20:17:23:792 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:17:23:923 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:17:24:032 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:17:24:035 ==>> 检测【初始化完成检测】
2025-07-31 20:17:24:038 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:17:24:274 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:17:24:309 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:17:24:312 ==>> 检测【关闭大灯控制1】
2025-07-31 20:17:24:314 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:17:24:380 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:17:24:486 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:17:24:578 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:17:24:582 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:17:24:584 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:17:24:743 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:17:24:853 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:17:24:855 ==>> 检测【关闭仪表供电】
2025-07-31 20:17:24:857 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:17:24:924 ==>>                                                                                        oto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:17:25:029 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:17:25:122 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:17:25:126 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:17:25:129 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:17:25:302 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:17:25:400 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:17:25:403 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:17:25:405 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:17:25:623 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:17:25:686 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:17:25:688 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:17:25:689 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:25:821 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:17:25:960 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:17:25:963 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:17:25:964 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:17:26:047 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:17:26:124 ==>> [D][05:17:52][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31


2025-07-31 20:17:26:183 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:17:26:231 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:17:26:233 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:17:26:237 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:17:26:348 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:17:26:510 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:17:26:512 ==>> 该项需要延时执行
2025-07-31 20:17:26:835 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:17:27:371 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020


2025-07-31 20:17:27:736 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:17:28:223 ==>>                                                          ... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is 

2025-07-31 20:17:28:327 ==>> not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][M2M ]m2m_task: c

2025-07-31 20:17:28:433 ==>> ontrol_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][

2025-07-31 20:17:28:523 ==>> COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
                                         

2025-07-31 20:17:28:853 ==>> [D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:17:28:929 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:17:29:855 ==>> D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:17:30:204 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:17:30:524 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:17:30:527 ==>> 检测【33V输入电压ADC】
2025-07-31 20:17:30:530 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:30:862 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:17:57][COMM]adc read out 24v adc:1324  volt:33487 mv
[D][05:17:57][COMM]adc read left brake adc:12  volt:15 mv
[D][05:17:57][COMM]adc read right brake adc:16  volt:21 mv
[D][05:17:57][COMM]adc read throttle adc:10  volt:13 mv
[D][05:17:57][COMM]adc read battery ts volt:13 mv
[D][05:17:57][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:17:57][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2378  volt:3831 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:1  volt:23 mv
                                                                                                 

2025-07-31 20:17:31:062 ==>> 【33V输入电压ADC】通过,【32830mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:17:31:066 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:17:31:069 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:17:31:148 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1672mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:17:31:334 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:31:336 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:17:31:352 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:31:355 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:17:31:358 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:17:31:371 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:17:31:374 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:17:31:408 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:17:31:413 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:17:31:458 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:17:31:460 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:17:31:558 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:17:31:745 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:31:747 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:17:31:793 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:31:795 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:17:31:798 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:17:31:830 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:17:31:836 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:17:31:864 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:17:31:896 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:17:31:898 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:17:31:993 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:17:31:995 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:17:32:058 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1671mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:17:32:238 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10006
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10006
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:17:32:277 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:32:284 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:17:32:296 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:32:301 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:17:32:306 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:17:32:314 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:17:32:319 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:17:32:333 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:17:32:336 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:17:32:358 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:17:32:360 ==>> 检测【打开WIFI(1)】
2025-07-31 20:17:32:362 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:17:32:523 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:17:32:583 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:17:32:631 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:17:32:636 ==>> 检测【清空消息队列(1)】
2025-07-31 20:17:32:639 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:17:33:085 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:1

2025-07-31 20:17:33:145 ==>> 7:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:17:33:165 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:17:33:168 ==>> 检测【打开GPS(1)】
2025-07-31 20:17:33:172 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:17:33:571 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087966166

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541422

OK

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:17:33:694 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:17:33:708 ==>> 检测【打开GSM联网】
2025-07-31 20:17:33:711 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:17:33:826 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 20:17:33:886 ==>>                                          

2025-07-31 20:17:33:969 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:17:33:971 ==>> 检测【打开仪表供电1】
2025-07-31 20:17:33:974 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:17:34:142 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:17:34:232 ==>>                                [D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:17:34:254 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:17:34:279 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:17:34:281 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:17:34:475 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:17:34:523 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:17:34:527 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:17:34:530 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:17:34:730 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33108]


2025-07-31 20:17:34:794 ==>> 【读取主控ADC采集的仪表电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:17:34:797 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:17:34:799 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:34:942 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:17:35:076 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:17:35:079 ==>> 检测【AD_V20电压】
2025-07-31 20:17:35:082 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:35:184 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:35:245 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:17:35:626 ==>> 本次取值间隔时间:433ms
2025-07-31 20:17:35:665 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:35:777 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:35:853 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:17:35:883 ==>>                                       

2025-07-31 20:17:35:988 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:17:36:123 ==>> 本次取值间隔时间:342ms
2025-07-31 20:17:36:141 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:36:248 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:36:253 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:17:36:353 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:17:36:428 ==>> 本次取值间隔时间:179ms
2025-07-31 20:17:36:447 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:36:549 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:36:639 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:17:36:744 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:0

2025-07-31 20:17:36:774 ==>> 3][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 20:17:36:849 ==>> 本次取值间隔时间:291ms
2025-07-31 20:17:36:868 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:36:969 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:37:044 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:17:37:149 ==>>                                                                                                                                                                                                                                                                                                                                                                   t[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 30

2025-07-31 20:17:37:254 ==>> 3E383D3535373F3836313136313107

[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.169.37.195"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

2025-07-31 20:17:37:359 ==>> 

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:17:37:464 ==>> 本次取值间隔时间:480ms
2025-07-31 20:17:37:497 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:37:599 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:37:919 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:17:37:950 ==>> 本次取值间隔时间:346ms
2025-07-31 20:17:38:237 ==>> 本次取值间隔时间:278ms
2025-07-31 20:17:38:241 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:17:38:538 ==>> 本次取值间隔时间:293ms
2025-07-31 20:17:38:569 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:17:38:764 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

[D][05:18:05][CAT1]<<< 
OK

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,44,24,,,43,33,,,43,42,,,42,1*77

$GBGSV,3,2,09,39,,,40,38,,,37,41,,,37,13,,,44,1*7C

$GBGSV,3,3,09,14,,,40,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1693.849,1693.849,54.165,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 20:17:38:854 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:17:38:959 ==>> 本次取值间隔时间:405ms
2025-07-31 20:17:38:964 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:39:064 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:39:109 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:17:39:138 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:17:39:408 ==>> 本次取值间隔时间:337ms
2025-07-31 20:17:39:427 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:17:39:533 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:17:39:669 ==>> 本次取值间隔时间:130ms
2025-07-31 20:17:39:714 ==>> 1A A1 10 00 00 
Get AD_V20 1664mV
OVER 150
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,59,,,42,24,,,42,33,,,42,42,,,41,1*7E

$GBGSV,4,2,13,25,,,41,14,,,40,39,,,40,3,,,40,1*48

$GBGSV,4,3,13,38,,,37,41,,,37,13,,,35,40,,,32,1*7C

$GBGSV,4,4,13,1,,,29,1*4E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1588.224,1588.224,50.857,2097152,2097152,2097152*40



2025-07-31 20:17:39:834 ==>> 本次取值间隔时间:152ms
2025-07-31 20:17:39:852 ==>> 【AD_V20电压】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:39:855 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:17:39:858 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:17:39:942 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:17:40:131 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:17:40:134 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:17:40:137 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:17:40:247 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:17:40:412 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:07][COMM]oneline display read state:255
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:17:40:720 ==>> $GBGGA,121744.534,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,60,,,43,33,,,42,59,,,41,24,,,41,1*76

$GBGSV,5,2,18,42,,,41,25,,,41,14,,,41,3,,,41,1*4F

$GBGSV,5,3,18,39,,,40,4,,,38,38,,,37,41,,,37,1*46

$GBGSV,5,4,18,13,,,36,2,,,36,40,,,34,5,,,33,1*78

$GBGSV,5,5,18,1,,,32,44,,,29,1*44

$GBRMC,121744.534,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121744.534,0.000,1573.159,1573.159,50.371,2097152,2097152,2097152*56



2025-07-31 20:17:41:167 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:17:41:337 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:17:41:446 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:17:41:449 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:17:41:453 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:17:41:536 ==>> 3A A3 02 01 A3 


2025-07-31 20:17:41:641 ==>> ON_OUT2
OVER 150


2025-07-31 20:17:41:719 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:17:41:726 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:17:41:748 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:17:41:751 ==>> $GBGGA,121745.514,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,14,,,42,60,,,41,59,,,41,1*7F

$GBGSV,5,2,20,24,,,41,42,,,41,25,,,41,3,,,41,1*47

$GBGSV,5,3,20,39,,,40,38,,,37,41,,,37,13,,,37,1*74

$GBGSV,5,4,20,2,,,36,40,,,36,4,,,35,1,,,35,1*46

$GBGSV,5,5,20,9,,,35,16,,,34,5,,,33,44,,,31,1*7C

$GBRMC,121745.514,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121745.514,0.000,1567.143,1567.143,50.152,2097152,2097152,2097152*56



2025-07-31 20:17:41:942 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:17:41:991 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:17:41:994 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:17:41:996 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:17:42:140 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:17:42:245 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:17:42:263 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:17:42:266 ==>> 检测【AD_V21电压】
2025-07-31 20:17:42:268 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:17:42:350 ==>> 1A A1 20 00 00 
Get AD_V21 1660mV
OVER 150


2025-07-31 20:17:42:380 ==>> 本次取值间隔时间:105ms
2025-07-31 20:17:42:398 ==>> 【AD_V21电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:42:403 ==>> 检测【关闭仪表供电2】
2025-07-31 20:17:42:413 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:17:42:744 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,121746.514,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,60,,,41,59,,,41,1*78

$GBGSV,7,2,25,24,,,41,42,,,41,25,,,41,3,,,41,1*40

$GBGSV,7,3,25,39,,,40,38,,,38,13,,,38,41,,,37,1*73

$GBGSV,7,4,25,40,,,37,1,,,37,2,,,36,9,,,36,1*4C

$GBGSV,7,5,25,16,,,36,6,,,35,7,,,35,4,,,34,1*43

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,32,8,,,32,1*7C

$GBGSV,7,7,25,12,,,31,1*70

$GBRMC,121746.514,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121746.514,0.000,1545.589,1545.589,49.467,2097152,2097152,2097152*5E



2025-07-31 20:17:42:927 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:17:42:930 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:17:42:935 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:17:43:127 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:17:43:208 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:17:43:211 ==>> 检测【打开AccKey2供电】
2025-07-31 20:17:43:215 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:17:43:402 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:17:43:480 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:17:43:486 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:17:43:494 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:43:799 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3166  volt:5565 mv
[D][05:18:10][COMM]adc read out 24v adc:1325  volt:33513 mv
[D][05:18:10][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:10][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:10][COMM]adc read battery ts volt:10 mv
[D][05:18:10][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:10][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2377  volt:3830 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
$GBGGA,121747.514,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,24,,,42,1*7A

$GBGSV,7,2,26,60,,,41,42,,,41,25,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,38,,,38,13,,,38,1,,,38,1*4B

$GBGSV,7,4,26,41,,,37,40,,,37,16,,,37,2,,,36,1*44

$GBGSV,7,5,26,9,,,36,6,,,36,7,,,36,4,,,34,1*7E

$GBGSV,7,6,26,5,,,34,10,,,34,44,,,33,8,,,33,1*7F

$GBGSV,7,7,26,34,,,33,12,,,31,1*74

$GBRMC,1217

2025-07-31 20:17:43:844 ==>> 47.514,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121747.514,0.000,1553.116,1553.116,49.708,2097152,2097152,2097152*55

[D][05:18:10][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:17:44:026 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33513mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:17:44:030 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:17:44:032 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:17:44:285 ==>> [D][05:18:11][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:18:11][COMM]read battery soc:255


2025-07-31 20:17:44:564 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:17:44:568 ==>> 该项需要延时执行
2025-07-31 20:17:44:726 ==>> $GBGGA,121748.514,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,24,,,42,59,,,41,1*79

$GBGSV,7,2,26,60,,,41,42,,,41,25,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,39,38,,,38,13,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,9,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,36,7,,,36,5,,,34,1*75

$GBGSV,7,6,26,10,,,34,8,,,34,4,,,33,44,,,33,1*7E

$GBGSV,7,7,26,34,,,33,12,,,31,1*74

$GBRMC,121748.514,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121748.514,0.000,1559.491,1559.491,49.909,2097152,2097152,2097152*55



2025-07-31 20:17:45:755 ==>> $GBGGA,121749.514,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,24,,,42,60,,,42,1*70

$GBGSV,7,2,26,59,,,41,42,,,41,25,,,41,3,,,41,1*49

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,9,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,35,1*78

$GBGSV,7,6,26,5,,,34,10,,,34,4,,,34,44,,,34,1*73

$GBGSV,7,7,26,34,,,33,12,,,31,1*74

$GBRMC,121749.514,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121749.514,0.000,1569.053,1569.053,50.209,2097152,2097152,2097152*57



2025-07-31 20:17:46:286 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:17:46:725 ==>> $GBGGA,121750.514,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,24,,,42,60,,,41,1*73

$GBGSV,7,2,26,59,,,41,42,,,41,25,,,41,3,,,41,1*49

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,9,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,36,7,,,36,8,,,35,1*79

$GBGSV,7,6,26,5,,,34,10,,,34,4,,,34,44,,,34,1*73

$GBGSV,7,7,26,34,,,32,12,,,31,1*75

$GBRMC,121750.514,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121750.514,0.000,1564.271,1564.271,50.058,2097152,2097152,2097152*59



2025-07-31 20:17:47:580 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:17:47:585 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:17:47:590 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:47:870 ==>> $GBGGA,121751.514,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,42,60,,,41,1*72

$GBGSV,7,2,26,59,,,41,42,,,41,25,,,41,3,,,41,1*49

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,16,,,38,40,,,37,41,,,37,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,35,1*78

$GBGSV,7,6,26,5,,,34,10,,,34,4,,,34,44,,,34,1*73

$GBGSV,7,7,26,34,,,32,12,,,31,1*75

$GBRMC,121751.514,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121751.514,0.000,1561.078,1561.078,49.953,2097152,2097152,2097152*52

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:18:14][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:14][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:14][COMM]adc read battery ts volt:14 mv
[D][05:18:14][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:14][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2377  volt:3830 mv
[D][05:18:14][COMM]arm_hub adc read led yb

2025-07-31 20:17:47:900 ==>>  adc:1428  volt:33108 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:17:48:131 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:17:48:137 ==>> 检测【打开AccKey1供电】
2025-07-31 20:17:48:142 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:17:48:328 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:17:48:416 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:17:48:420 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:17:48:423 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:17:48:539 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 20:17:48:674 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 20:17:48:694 ==>> 【读取AccKey1电压(ADV14)前】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:17:48:698 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:17:48:701 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:48:734 ==>> $GBGGA,121752.514,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,42,60,,,41,1*72

$GBGSV,7,2,26,59,,,41,42,,,41,25,,,41,3,,,41,1*49

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,9,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,5,,,34,10,,,34,44,,,34,4,,,33,1*74

$GBGSV,7,7,26,34,,,32,12,,,31,1*75

$GBRMC,121752.514,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121752.514,0.000,1562.674,1562.674,50.005,2097152,2097152,2097152*53



2025-07-31 20:17:48:959 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:15][COMM]adc read battery ts volt:9 mv
[D][05:18:15][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:15][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2377  volt:3830 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:17:49:232 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5559mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:17:49:237 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:17:49:241 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:17:49:415 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:17:49:512 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:17:49:516 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:17:49:519 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:17:49:642 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 20:17:49:731 ==>> $GBGGA,121753.514,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,41,60,,,41,1*71

$GBGSV,7,2,26,59,,,41,42,,,41,25,,,41,3,,,41,1*49

$GBGSV,7,3,26,39,,,40,1,,,40,13,,,39,38,,,38,1*45

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,6,,,37,1*41

$GBGSV,7,5,26,2,,,37,9,,,36,7,,,36,8,,,36,1*75

$GBGSV,7,6,26,5,,,34,10,,,34,44,,,34,4,,,34,1*73

$GBGSV,7,7,26,34,,,33,12,,,31,1*74

$GBRMC,121753.514,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121753.514,0.000,1565.857,1565.857,50.100,2097152,2097152,2097152*56



2025-07-31 20:17:49:776 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 20:17:49:794 ==>> 【读取AccKey1电压(ADV14)后】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:17:49:798 ==>> 检测【打开WIFI(2)】
2025-07-31 20:17:49:803 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:17:49:959 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:17:50:084 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:17:50:089 ==>> 检测【转刹把供电】
2025-07-31 20:17:50:092 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:17:50:201 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:17:50:291 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:17:50:354 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:17:50:358 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:17:50:363 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:17:50:459 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:17:50:504 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:17:50:549 ==>> 1A A1 00 80 00 
Get AD_V15 2411mV
OVER 150


2025-07-31 20:17:50:624 ==>> 原始值:【2411】, 乘以分压基数【2】还原值:【4822】
2025-07-31 20:17:50:642 ==>> 【读取AD_V15电压(前)】通过,【4822mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:17:50:646 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:17:50:652 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:17:50:730 ==>> $GBGGA,121754.514,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,42,59,,,42,1*7B

$GBGSV,7,2,26,60,,,41,42,,,41,25,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,6,,,37,1*41

$GBGSV,7,5,26,2,,,37,9,,,37,7,,,36,8,,,36,1*74

$GBGSV,7,6,26,5,,,34,10,,,34,44,,,34,4,,,34,1*73

$GBGSV,7,7,26,34,,,33,12,,,31,1*74

$GBRMC,121754.514,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121754.514,0.000,1567.454,1567.454,50.154,2097152,2097152,2097152*50



2025-07-31 20:17:50:745 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:17:50:836 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 20:17:50:896 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 20:17:50:926 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:17:50:931 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:17:50:937 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:50:945 ==>> +WIFISCAN:4,0,F88C21BCF57D,-33
+WIFISCAN:4,1,F62A7D2297A3,-61
+WIFISCAN:4,2,CC057790A740,-74
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:17][CAT1]wifi scan report total[4]
[D][05:18:17][GNSS]recv submsg id[3]


2025-07-31 20:17:51:258 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:17][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:17][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:17][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:17][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:17][COMM]adc read battery ts volt:13 mv
[D][05:18:17][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3109  volt:5465 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2379  volt:3833 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:17:51:457 ==>> 【转刹把供电电压(主控ADC)】通过,【5465mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:17:51:464 ==>> 检测【转刹把供电电压】
2025-07-31 20:17:51:474 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:51:819 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:18][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:18][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:18][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:18][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:18][COMM]adc read battery ts volt:15 mv
[D][05:18:18][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3111  volt:5468 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2378  volt:3831 mv
$GBGGA,121755.514,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,42,59,,,42,1*7B

$GBGSV,7,2,26,60,,,41,42,,,41,25,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,6,,,37,1*41

$GBGSV,7,5,26,2,,,37,9,,,37,7,,,36,8,,,36,1*74

$GBGSV,7,6,26,5,,,34,10,,,34,44,,,34,4,,,33,1*74

$GBGSV,7,7,26,34,,,33,12,,,

2025-07-31 20:17:51:879 ==>> 31,1*74

$GBRMC,121755.514,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121755.514,0.000,1565.862,1565.862,50.105,2097152,2097152,2097152*55

[D][05:18:18][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:17:52:001 ==>> 【转刹把供电电压】通过,【5468mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:17:52:004 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:17:52:009 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:52:211 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:17:52:284 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:17:52:288 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:17:52:294 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:52:300 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:17:52:391 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:17:52:406 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:17:52:451 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:17:52:514 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:17:52:518 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:17:52:521 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:52:617 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:17:52:724 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:52:739 ==>> $GBGGA,121756.514,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,14,,,42,24,,,42,25,,,42,1*70

$GBGSV,7,2,27,59,,,41,60,,,41,42,,,41,3,,,41,1*49

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,39,38,,,38,1*4A

$GBGSV,7,4,27,16,,,38,40,,,38,41,,,37,6,,,37,1*40

$GBGSV,7,5,27,2,,,37,9,,,37,7,,,36,8,,,36,1*75

$GBGSV,7,6,27,26,,,36,5,,,34,10,,,34,44,,,34,1*40

$GBGSV,7,7,27,4,,,34,34,,,33,12,,,31,1*46

$GBRMC,121756.514,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121756.514,0.000,1567.750,1567.750,50.164,2097152,2097152,2097152*51



2025-07-31 20:17:52:830 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:17:52:836 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:17:52:906 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:17:52:935 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:17:52:950 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:17:53:041 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:17:53:147 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:17:53:172 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:17:53:178 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:17:53:184 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:17:53:237 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:17:53:464 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:17:53:471 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:17:53:477 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:17:53:541 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:17:53:737 ==>> $GBGGA,121757.514,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,14,,,42,24,,,42,25,,,42,1*70

$GBGSV,7,2,27,59,,,41,60,,,41,42,,,41,3,,,41,1*49

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,39,16,,,39,1*47

$GBGSV,7,4,27,38,,,38,40,,,38,41,,,37,6,,,37,1*4C

$GBGSV,7,5,27,2,,,37,9,,,37,7,,,36,8,,,36,1*75

$GBGSV,7,6,27,26,,,36,5,,,34,10,,,34,44,,,34,1*40

$GBGSV,7,7,27,4,,,34,34,,,33,12,,,31,1*46

$GBRMC,121757.514,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121757.514,0.000,1569.286,1569.286,50.214,2097152,2097152,2097152*54



2025-07-31 20:17:53:742 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:17:53:747 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:17:53:751 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:17:53:842 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:17:54:026 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:17:54:030 ==>> 检测【左刹电压测试1】
2025-07-31 20:17:54:037 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:54:374 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:18:21][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:21][COMM]adc read left brake adc:1736  volt:2288 mv
[D][05:18:21][COMM]adc read right brake adc:1734  volt:2286 mv
[D][05:18:21][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:21][COMM]adc read battery ts volt:14 mv
[D][05:18:21][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2377  volt:3830 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:21][COMM]read battery soc:255


2025-07-31 20:17:54:557 ==>> 【左刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 20:17:54:561 ==>> 检测【右刹电压测试1】
2025-07-31 20:17:54:584 ==>> 【右刹电压测试1】通过,【2286】符合目标值【2250】至【2500】要求!
2025-07-31 20:17:54:587 ==>> 检测【转把电压测试1】
2025-07-31 20:17:54:604 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 20:17:54:608 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:17:54:613 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:17:54:751 ==>> $GBGGA,121758.514,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,14,,,42,24,,,42,25,,,41,1*73

$GBGSV,7,2,27,59,,,41,60,,,41,42,,,41,3,,,41,1*49

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,39,16,,,38,1*46

$GBGSV,7,4,27,38,,,38,40,,,38,41,,,37,6,,,37,1*4C

$GBGSV,7,5,27,2,,,37,9,,,37,26,,,37,7,,,36,1*48

$GBGSV,7,6,27,8,,,36,5,,,34,10,,,34,44,,,34,1*7C

$GBGSV,7,7,27,4,,,34,34,,,33,12,,,31,1*46

$GBRMC,121758.514,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121758.514,0.000,1567.747,1567.747,50.161,2097152,2097152,2097152*5A

3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:17:54:875 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:17:54:879 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:17:54:882 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:17:54:949 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:17:55:151 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:17:55:155 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:17:55:159 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:17:55:242 ==>> 3A A3 05 00 A3 


2025-07-31 20:17:55:347 ==>> OFF_OUT5
OVER 150


2025-07-31 20:17:55:425 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:17:55:431 ==>> 检测【左刹电压测试2】
2025-07-31 20:17:55:437 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:17:55:804 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3170  volt:5572 mv
[D][05:18:22][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:22][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:22][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:22][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:22][COMM]adc read battery ts volt:17 mv
[D][05:18:22][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:22][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2378  volt:3831 mv
$GBGGA,121759.514,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,42,24,,,42,25,,,41,1*72

$GBGSV,7,2,27,59,,,41,60,,,41,42,,,41,3,,,41,1*49

$GBGSV,7,3,27,39,,,40,1,,,38,13,,,38,16,,,38,1*46

$GBGSV,7,4,27,38,,,38,40,,,38,41,,,37,6,,,37,1*4C

$GBGSV,7,5,27,2,,,37,9,,,37,26,,,36,7,,,36,1*49

$GBGSV,7,6,27,8,,,35,5,,,34,10,,,34,44,,,34,1*7F

$GBGSV,7,7,27,4,,,33,34,,,33,12,,,31,1*41

$GBRMC,121759.514,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121759.514,0.000,1558.536,1558.536,49.868,

2025-07-31 20:17:55:850 ==>> 2097152,2097152,2097152*53

[D][05:18:22][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:17:55:958 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 20:17:55:964 ==>> 检测【右刹电压测试2】
2025-07-31 20:17:55:977 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 20:17:55:982 ==>> 检测【转把电压测试2】
2025-07-31 20:17:55:996 ==>> 【转把电压测试2】通过,【13】符合目标值【0】至【50】要求!
2025-07-31 20:17:55:999 ==>> 检测【晶振检测】
2025-07-31 20:17:56:005 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:17:56:127 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 20:17:56:266 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:17:56:272 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:17:56:278 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:17:56:308 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:17:56:353 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1672mV
Get AD_V4 1644mV
Get AD_V5 2773mV
Get AD_V6 1992mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:17:56:537 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:17:56:544 ==>> 检测【检测BootVer】
2025-07-31 20:17:56:551 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:17:56:908 ==>> $GBGGA,121800.514,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,42,24,,,41,25,,,41,1*71

$GBGSV,7,2,27,59,,,41,60,,,41,42,,,41,3,,,41,1*49

$GBGSV,7,3,27,39,,,40,1,,,38,13,,,38,16,,,38,1*46

$GBGSV,7,4,27,38,,,38,40,,,38,6,,,37,2,,,37,1*7B

$GBGSV,7,5,27,9,,,37,41,,,36,26,,,36,7,,,36,1*7F

$GBGSV,7,6,27,8,,,36,5,,,34,10,,,34,44,,,34,1*7C

$GBGSV,7,7,27,4,,,33,34,,,33,12,,,31,1*41

$GBRMC,121800.514,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121800.514,0.000,1556.997,1556.997,49.816,2097152,2097152,2097152*59

[W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071541422
[D][05:18:23][FCTY]HardwareID  = 867222087966166
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = F7D603C7426D
[D][05:18:23][FCTY]Bat         = 3924

2025-07-31 20:17:56:998 ==>>  mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1294
[D][05:18:23][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 75 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3773 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:17:57:066 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:17:57:070 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:17:57:076 ==>> 检测【检测固件版本】
2025-07-31 20:17:57:084 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:17:57:092 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:17:57:120 ==>> 检测【检测蓝牙版本】
2025-07-31 20:17:57:124 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:17:57:129 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:17:57:137 ==>> 检测【检测MoBikeId】
2025-07-31 20:17:57:160 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:17:57:165 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:17:57:172 ==>> 检测【检测蓝牙地址】
2025-07-31 20:17:57:178 ==>> 取到目标值:F7D603C7426D
2025-07-31 20:17:57:198 ==>> 【检测蓝牙地址】通过,【F7D603C7426D】符合目标值【】要求!
2025-07-31 20:17:57:205 ==>> 提取到蓝牙地址:F7D603C7426D
2025-07-31 20:17:57:210 ==>> 检测【BOARD_ID】
2025-07-31 20:17:57:218 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:17:57:225 ==>> 检测【检测充电电压】
2025-07-31 20:17:57:243 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:17:57:248 ==>> 检测【检测VBUS电压1】
2025-07-31 20:17:57:262 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:17:57:269 ==>> 检测【检测充电电流】
2025-07-31 20:17:57:287 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:17:57:291 ==>> 检测【检测IMEI】
2025-07-31 20:17:57:295 ==>> 取到目标值:867222087966166
2025-07-31 20:17:57:311 ==>> 【检测IMEI】通过,【867222087966166】符合目标值【】要求!
2025-07-31 20:17:57:316 ==>> 提取到IMEI:867222087966166
2025-07-31 20:17:57:320 ==>> 检测【检测IMSI】
2025-07-31 20:17:57:324 ==>> 取到目标值:460130071541422
2025-07-31 20:17:57:338 ==>> 【检测IMSI】通过,【460130071541422】符合目标值【】要求!
2025-07-31 20:17:57:347 ==>> 提取到IMSI:460130071541422
2025-07-31 20:17:57:351 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:17:57:355 ==>> 取到目标值:460130071541422
2025-07-31 20:17:57:361 ==>> 【校验网络运营商(移动)】通过,【460130071541422】符合目标值【】要求!
2025-07-31 20:17:57:379 ==>> 检测【打开CAN通信】
2025-07-31 20:17:57:386 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:17:57:451 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:17:57:637 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:17:57:642 ==>> 检测【检测CAN通信】
2025-07-31 20:17:57:651 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:17:57:755 ==>> $GBGGA,121801.514,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,42,59,,,42,24,,,41,1*79

$GBGSV,7,2,27,25,,,41,60,,,41,42,,,41,3,,,41,1*42

$GBGSV,7,3,27,39,,,40,1,,,38,13,,,38,16,,,38,1*46

$GBGSV,7,4,27,38,,,38,40,,,38,6,,,37,2,,,37,1*7B

$GBGSV,7,5,27,9,,,37,41,,,37,26,,,36,7,,,36,1*7E

$GBGSV,7,6,27,8,,,35,5,,,34,10,,,34,44,,,34,1*7F

$GBGSV,7,7,27,4,,,33,34,,,33,12,,,31,1*41

$GBRMC,121801.514,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121801.514,0.000,777.816,777.816,711.331,2097152,2097152,2097152*6C

can send success


2025-07-31 20:17:57:785 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:17:57:860 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35682
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:17:57:912 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:17:57:917 ==>> 检测【关闭CAN通信】
2025-07-31 20:17:57:923 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:17:57:928 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:17:57:980 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:17:58:055 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:17:58:194 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:17:58:203 ==>> 检测【打印IMU STATE】
2025-07-31 20:17:58:210 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:17:58:370 ==>> [D][05:18:25][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
[D][05:18:25][COMM]read battery soc:255


2025-07-31 20:17:58:464 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:17:58:471 ==>> 检测【六轴自检】
2025-07-31 20:17:58:478 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:17:58:767 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBGGA,121802.514,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,24,,,42,33,,,42,60,,,41,3,,,41,1*46

$GBGSV,7,2,27,59,,,41,42,,,41,14,,,41,25,,,41,1*7E

$GBGSV,7,3,27,39,,,40,13,,,38,38,,,38,40,,,38,1*7F

$GBGSV,7,4,27,1,,,38,16,,,38,41,,,37,2,,,36,1*70

$GBGSV,7,5,27,26,,,36,8,,,36,7,,,36,9,,,36,1*43

$GBGSV,7,6,27,6,,,36,44,,,35,10,,,34,5,,,34,1*73

$GBGSV,7,7,27,34,,,33,4,,,33,12,,,31,1*41

$GBRMC,121802.514,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121802.514,0.000,776.280,776.280,709.926,2097152,2097152,2097152*6A



2025-07-31 20:17:59:746 ==>> $GBGGA,121803.514,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,14,,,42,33,,,42,60,,,41,3,,,41,1*45

$GBGSV,7,2,27,59,,,41,24,,,41,42,,,41,25,,,41,1*7D

$GBGSV,7,3,27,39,,,40,13,,,38,38,,,38,40,,,38,1*7F

$GBGSV,7,4,27,1,,,38,16,,,38,2,,,37,26,,,36,1*71

$GBGSV,7,5,27,8,,,36,7,,,36,9,,,36,6,,,36,1*71

$GBGSV,7,6,27,41,,,36,44,,,35,10,,,34,5,,,34,1*40

$GBGSV,7,7,27,34,,,33,4,,,33,12,,,31,1*41

$GBRMC,121803.514,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121803.514,0.000,776.280,776.280,709.926,2097152,2097152,2097152*6B



2025-07-31 20:18:00:351 ==>> [D][05:18:27][COMM]read battery soc:255
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:18:00:456 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38276 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-9,-28,4040]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:18:00:535 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:18:00:539 ==>> 检测【打印IMU STATE2】
2025-07-31 20:18:00:542 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:18:00:790 ==>> $GBGGA,121804.514,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,24,,,42,14,,,42,33,,,42,60,,,41,1*7C

$GBGSV,7,2,28,3,,,41,59,,,41,42,,,41,25,,,41,1*47

$GBGSV,7,3,28,39,,,40,1,,,39,13,,,38,38,,,38,1*44

$GBGSV,7,4,28,16,,,38,2,,,37,40,,,37,6,,,37,1*77

$GBGSV,7,5,28,26,,,36,8,,,36,7,,,36,9,,,36,1*4C

$GBGSV,7,6,28,41,,,36,44,,,35,10,,,34,5,,,34,1*4F

$GBGSV,7,7,28,34,,,33,4,,,33,12,,,31,29,,,39,1*4F

$GBRMC,121804.514,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121804.514,0.000,777.815,777.815,711.330,2097152,2097152,2097152*68

[W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:1
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:18:01:071 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:18:01:076 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:18:01:082 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:18:01:147 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:18:01:252 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i

2025-07-31 20:18:01:312 ==>>  = 2,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:18:01:362 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:18:01:367 ==>> 检测【检测VBUS电压2】
2025-07-31 20:18:01:373 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:18:01:787 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541422
[D][05:18:28][FCTY]HardwareID  = 867222087966166
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = F7D603C7426D
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 8, adc = 317
[D][05:18:28][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 151 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNS

2025-07-31 20:18:01:877 ==>> S_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3773 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121805.514,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,24,,,42,14,,,42,33,,,42,60,,,41,1*7C

$GBGSV,7,2,28,3,,,41,59,,,41,42,,,41,25,,,41,1*47

$GBGSV,7,3,28,39,,,40,1,,,39,13,,,38,38,,,38,1*44

$GBGSV,7,4,28,40,,,38,16,,,38,2,,,37,6,,,37,1*78

$GBGSV,7,5,28,26,,,36,8,,,36,7,,,36,9,,,36,1*4C

$GBGSV,7,6,28,41,,,36,10,,,34,5,,,34,44,,,34,1*4E

$GBGSV,7,7,28,4,,,34,34,,,33,12,,,31,29,,,36,1*47

$GBRMC,121805.514,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121805.514,0.000,778.581,778.581,712.030,2097152,2097152,2097152*69



2025-07-31 20:18:01:896 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:18:02:207 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541422
[D][05:18:28][FCTY]HardwareID  = 867222087966166
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = F7D603C7426D
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 176
[D][05:18:28][FCTY]Acckey1 vol = 5563 mv, Acckey2 vol = 177 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D

2025-07-31 20:18:02:252 ==>> ][05:18:28][FCTY]Bat1         = 3773 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:18:02:422 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:18:02:928 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071541422
[D][05:18:29][FCTY]HardwareID  = 867222087966166
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = F7D603C7426D
[D][05:18:29][FCTY]Bat         = 3924 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 5000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 151
[D][05:18:29][FCTY]Acckey1 vol = 5566 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29

2025-07-31 20:18:02:962 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:18:02:968 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:18:02:977 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:18:03:033 ==>> ][FCTY]Bat1         = 3773 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121806.514,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,14,,,42,60,,,41,1*7D

$GBGSV,7,2,28,3,,,41,59,,,41,42,,,41,25,,,41,1*47

$GBGSV,7,3,28,39,,,40,1,,,39,13,,,38,38,,,38,1*44

$GBGSV,7,4,28,40,,,38,16,,,38,2,,,37,9,,,37,1*77

$GBGSV,7,5,28,6,,,37,26,,,36,8,,,36,7,,,36,1*42

$GBGSV,7,6,28,41,,,36,44,,,35,10,,,34,5,,,34,1*4F

$GBGSV,7,7,28,4,,,34,34,,,33,12,,,31,29,,,,1*42

$GBRMC,121806.514,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121806.514,0.000,780.880,780.880,714.132,2097152,2097152,2097152*6F

[D][05:18:29][COMM]msg 0601 loss. last_tick:35663. cur_tick:40684. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40684
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:18:03:240 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:18:03:244 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:18:03:249 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:18:03:339 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:18:03:444 ==>>                                                                                                                                       [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18:29][COMM]read battery soc:255
[D][05:18:29][COMM]Main Task receive

2025-07-31 20:18:03:523 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:18:03:530 ==>> 检测【打开WIFI(3)】
2025-07-31 20:18:03:539 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:18:03:557 ==>>  event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][PROT]index:0
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]======================

2025-07-31 20:18:03:654 ==>> =====================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND so

2025-07-31 20:18:03:759 ==>> cket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5C1C43549149420FE1A6C0CC7FB23E9633B2B43A333D978FCFB67F8D9206747DBF25E61F19374FB50FE2B69FAB48A225F8C56785E327D2A2D26CFC354B5EC7ADC777701F981C6115BED528DF01940DA4C451E
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
 

2025-07-31 20:18:03:834 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 20:18:03:924 ==>>                                                                                                                                                                                                                                                    [D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:18:04:556 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:18:04:601 ==>> +WIFISCAN:4,0,CC057790A741,-73
+WIFISCAN:4,1,CC057790A7C0,-78
+WIFISCAN:4,2,CC057790A6E0,-79
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:31][CAT1]wifi scan report total[4]


2025-07-31 20:18:05:252 ==>> $GBGGA,121808.514,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,24,,,42,14,,,42,33,,,42,60,,,41,1*73

$GBGSV,7,2,27,3,,,41,59,,,41,42,,,41,25,,,41,1*48

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,38,38,,,38,1*4B

$GBGSV,7,4,27,16,,,38,40,,,37,9,,,37,6,,,37,1*73

$GBGSV,7,5,27,41,,,37,2,,,36,26,,,36,8,,,36,1*7B

$GBGSV,7,6,27,7,,,36,44,,,35,10,,,34,5,,,34,1*72

$GBGSV,7,7,27,4,,,33,34,,,32,12,,,30,1*41

$GBRMC,121808.514,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121808.514,0.000,777.056,777.056,710.635,2097152,2097152,2097152*65

[D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------
[W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------


2025-07-31 20:18:05:347 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:18:05:358 ==>> 检测【扩展芯片hw】
2025-07-31 20:18:05:363 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:18:05:373 ==>> 
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, 

2025-07-31 20:18:05:462 ==>> len:10800, num:3
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][M2M ]m2m_task:

2025-07-31 20:18:05:567 ==>>  control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][PROT]index:1
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][PROT]is_send:1
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],i

2025-07-31 20:18:05:657 ==>> ndex[2],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][GNSS]recv submsg id[3]
[D][05:18:31][COMM]read battery soc:255


2025-07-31 20:18:05:762 ==>>                                                                                                                                                                                                                                                                              

2025-07-31 20:18:05:867 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:18:05:972 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:18:06:017 ==>>                                                                                                              [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:18:06:047 ==>>                                       

2025-07-31 20:18:06:141 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:18:06:146 ==>> 检测【扩展芯片boot】
2025-07-31 20:18:06:160 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:18:06:167 ==>> 检测【扩展芯片sw】
2025-07-31 20:18:06:217 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:18:06:222 ==>> 检测【检测音频FLASH】
2025-07-31 20:18:06:229 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:18:06:402 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:18:06:750 ==>> $GBGGA,121810.514,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,24,,,42,14,,,42,33,,,42,60,,,41,1*73

$GBGSV,7,2,27,3,,,41,59,,,41,42,,,41,25,,,41,1*48

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,38,38,,,38,1*4B

$GBGSV,7,4,27,40,,,38,16,,,38,9,,,37,6,,,37,1*7C

$GBGSV,7,5,27,2,,,36,26,,,36,7,,,36,41,,,36,1*75

$GBGSV,7,6,27,8,,,35,44,,,35,10,,,34,5,,,34,1*7E

$GBGSV,7,7,27,34,,,33,4,,,33,12,,,30,1*40

$GBRMC,121810.514,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121810.514,0.000,777.054,777.054,710.634,2097152,2097152,2097152*6D



2025-07-31 20:18:07:158 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 20:18:07:796 ==>> $GBGGA,121811.514,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,24,,,42,14,,,42,33,,,42,60,,,41,1*73

$GBGSV,7,2,27,3,,,41,59,,,41,42,,,41,25,,,41,1*48

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,38,38,,,38,1*4B

$GBGSV,7,4,27,40,,,38,16,,,38,6,,,37,2,,,36,1*76

$GBGSV,7,5,27,26,,,36,7,,,36,9,,,36,41,,,36,1*7E

$GBGSV,7,6,27,8,,,35,44,,,35,10,,,34,5,,,34,1*7E

$GBGSV,7,7,27,34,,,33,4,,,33,12,,,30,1*40

$GBRMC,121811.514,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121811.514,0.000,776.289,776.289,709.934,2097152,2097152,2097152*6B

[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:18:08:633 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B30EEBAAB17CF7A162C6B2DE069BFCF487B6D3A1A028CC263399AFF572A5B0312DDCC7E265E44989C1BC9593ECC29A081C6E5A46AC1743

2025-07-31 20:18:08:693 ==>> B392A18C3924A33061234045BAD240DC60077E80D15BD4F9980AC312
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]


2025-07-31 20:18:08:798 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              ][05:18:35][COMM]imu_task im

2025-07-31 20:18:08:828 ==>> u work error:[-1]. goto init


2025-07-31 20:18:09:267 ==>> [D][05:18:36][COMM]read battery soc:255
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:36][COMM]accel parse set 0
[D][05:18:36][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:18:09:766 ==>> $GBGGA,121809.514,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,42,24,,,42,14,,,42,1*44

$GBGSV,7,2,27,60,,,41,59,,,41,42,,,41,25,,,41,1*7D

$GBGSV,7,3,27,39,,,40,1,,,39,13,,,38,38,,,38,1*4B

$GBGSV,7,4,27,40,,,38,16,,,38,9,,,37,6,,,37,1*7C

$GBGSV,7,5,27,2,,,36,26,,,36,7,,,36,41,,,36,1*75

$GBGSV,7,6,27,8,,,35,44,,,35,10,,,34,5,,,34,1*7E

$GBGSV,7,7,27,34,,,33,4,,,33,12,,,30,1*40

$GBRMC,121809.514,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121809.514,104338,778.592,778.592,712.041,2097152,2097152,2097152*40

                                      

2025-07-31 20:18:10:305 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:18:10:410 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 20:18:11:293 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:18:11:298 ==>> 检测【打开喇叭声音】
2025-07-31 20:18:11:306 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:18:11:409 ==>> $GBGGA,121810.519,2301.2579917,N,11421.9425484,E,1,10,1.18,68.879,M,-1.770,M,,*56

$GBGSA,A,3,14,33,24,39,42,59,13,25,38,41,,,2.24,1.18,1.91,4*05

$GBGSV,7,1,27,14,76,203,42,33,65,301,43,3,61,191,41,24,55,4,42,1*4C

$GBGSV,7,2,27,16,53,20,38,39,52,4,40,6,52,32,37,42,51,161,41,1*46

$GBGSV,7,3,27,59,51,128,41,1,48,126,38,13,46,220,39,2,46,238,36,1*74

$GBGSV,7,4,27,60,41,238,41,25,39,272,41,40,39,170,38,9,38,314,37,1*43

$GBGSV,7,5,27,7,38,186,36,4,32,112,34,38,32,191,38,10,28,194,34,1*7F

$GBGSV,7,6,27,8,26,204,35,5,22,257,34,26,21,230,36,41,17,322,36,1*7A

$GBGSV,7,7,27,44,,,35,34,,,33,12,,,30,1*72

$GBRMC,121810.519,A,2301.2579917,N,11421.9425484,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:18:37][GNSS]HD8040 GPS
[D][05:18:37][GNSS]GPS diff_sec 124009173, report 0x42 frame
$GBGST,121810.519,0.287,0.310,0.267,0.412,1.934,3.621,9.873*74

[D][05:18:37][COMM]Main Task receive event:131
[D][05:18:37][COMM]index:0,power_mode:0xFF
[D][05:18:37][COMM]index:1,sound_mode:0xFF
[D][05:18:37][COMM]index:2,gsensor_mode:0xFF
[D][05:18:37][COMM]index:3,report_freq_mode:0xFF
[D][05:18:37][COMM]index:4,report_period:0xFF
[D][05:18:37][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:37][COMM]index:6,

2025-07-31 20:18:11:514 ==>> normal_reset_period:0xFF
[D][05:18:37][COMM]index:7,spock_over_speed:0xFF
[D][05:18:37][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:37][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:37][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:37][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:37][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:37][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:37][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:37][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:37][COMM]index:16,imu_config_params:0xFF
[D][05:18:37][COMM]index:17,long_connect_params:0xFF
[D][05:18:37][COMM]index:18,detain_mark:0xFF
[D][05:18:37][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:37][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:37][COMM]index:21,mc_mode:0xFF
[D][05:18:37][COMM]index:22,S_mode:0xFF
[D][05:18:37][COMM]index:23,overweight:0xFF
[D][05:18:37][COMM]index:24,standstill_mode:0xFF
[D][05:18:37][COMM]index:25,night_mode:0xFF
[D][05:18:37][COMM]index:26,experiment1:0xFF
[D][05:18:37][COMM]index:27,experiment2:0xFF
[D][05:18:37][COMM]index:28,experiment3:0xFF
[D][05:18:37][COMM]index:29,experiment4:0xFF
[D][05:18:37][COMM]index:30,ni

2025-07-31 20:18:11:620 ==>> ght_mode_start:0xFF
[D][05:18:37][COMM]index:31,night_mode_end:0xFF
[D][05:18:37][COMM]index:33,park_report_minutes:0xFF
[D][05:18:37][COMM]index:34,park_report_mode:0xFF
[D][05:18:37][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:37][COMM]index:38,charge_battery_para: FF
[D][05:18:37][COMM]index:39,multirider_mode:0xFF
[D][05:18:37][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:37][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:37][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:37][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:37][COMM]index:44,riding_duration_config:0xFF
[D][05:18:37][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:37][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:37][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:37][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:37][COMM]index:49,mc_load_startup:0xFF
[D][05:18:37][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:37][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:37][COMM]index:52,traffic_mode:0xFF
[D][05:18:37][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:37][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:37][COMM]index:55,wheel_alarm_play_switch

2025-07-31 20:18:11:724 ==>> :255
[D][05:18:37][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:37][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:37][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:37][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:37][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:37][COMM]index:63,experiment5:0xFF
[D][05:18:37][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:37][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:37][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:37][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:37][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:37][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:37][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:37][COMM]index:72,experiment6:0xFF
[D][05:18:37][COMM]index:73,experiment7:0xFF
[D][05:18:37][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:37][COMM]index:75,zero_value_from_server:-1
[D][05:18:37][COMM]index:76,multirider_threshold:255
[D][05:18:37][COMM]index:77,experiment8:255
[D][05:18:37][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:37][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:37][COMM]index:

2025-07-31 20:18:11:829 ==>> 80,temp_park_reminder_timeout_duration:255
[D][05:18:37][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:37][COMM]index:83,loc_report_interval:255
[D][05:18:37][COMM]index:84,multirider_threshold_p2:255
[D][05:18:37][COMM]index:85,multirider_strategy:255
[D][05:18:37][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:37][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:37][COMM]index:90,weight_param:0xFF
[D][05:18:37][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:37][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:37][COMM]index:95,current_limit:0xFF
[D][05:18:37][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:37][COMM]index:100,location_mode:0xFF

[D][05:18:37][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:37][PROT]remove success[1629955117],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:37][COMM]Main Task receive event:131 finished processing
$GBGGA,121811.019,2301

2025-07-31 20:18:11:934 ==>> .2583192,N,11421.9418792,E,1,11,1.09,71.011,M,-1.770,M,,*57

[D][05:18:38][COMM]read battery soc:255
$GBGSA,A,3,14,33,24,39,42,59,13,25,38,40,41,,2.02,1.09,1.70,4*0A

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,61,191,41,24,55,4,41,1*4E

$GBGSV,7,2,27,16,53,20,38,39,52,4,40,6,52,32,37,42,51,161,41,1*46

$GBGSV,7,3,27,59,51,128,41,1,48,126,38,13,46,220,38,2,46,238,36,1*75

$GBGSV,7,4,27,60,41,238,41,25,39,272,41,9,38,314,36,7,38,186,36,1*77

$GBGSV,7,5,27,4,32,112,33,38,32,191,38,40,31,160,38,10,28,194,34,1*44

$GBGSV,7,6,27,8,26,204,35,5,22,257,34,26,21,230,36,41,17,322,36,1*7A

$GBGSV,7,7,27,44,,,35,34,,,33,12,,,30,1*72

$GBGSV,2,1,06,33,65,301,37,24,55,4,41,39,52,4,38,42,51,161,41,5*76

$GBGSV,2,2,06,25,39,272,38,38,32,191,35,5*70

$GBRMC,121811.019,A,2301.2583192,N,11421.9418792,E,0.000,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,121811.019,1.594,0.875,0.720,1.113,1.770,2.405,6.481*7A



2025-07-31 20:18:12:640 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:38][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[

2025-07-31 20:18:12:745 ==>> ec800m_audio_start].l:[691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

$GBGGA,121812.000,2301.2582922,N,11421.9415798,E,1,15,0.88,72.588,M,-1.770,M,,*53

$GBGSA,A,3,14,33,03,24,39,42,59,02,13,01,60,25,1.82,0.88,1.59,4*0E

$GBGSA,A,3,38,40,41,,,,,,,,,,1.82,0.88,1.59,4*04

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,16,53,20,38,39,52,4,40,6,52,32,37,42,51,161,41,1*46

$GBGSV,7,3,27,59,51,128,41,2,48,240,36,13,46,220,38,1,46,124,39,1*79

$GBGSV,7,4,27,60,43,241,40,25,39,272,41,9,38,314,36,7,38,186,36,1*7A

$GBGSV,7,5,27,4,32,112,33,38,32,191,38,40,31,160,37,10,28,194,34,1*4B

$GBGSV,7,6,27,8,26,204,35,5,22,257,33,26,21,230,36,41,17,322,36,1*7D

$GBGSV,7,7,27,44,13,108,35,34,,,33,12,,,30,1*49

$GBGSV,2,1,07,33,65,301,40,24,55,4,42,39,52,4,40,42,51,161,42,5*78

$GBGSV,2,2,07,25,39,272,39,38,32,191,35,41,17,322,29,5*4B

$GBRMC,121812.000,A,2301.2582922,N,11421.9415798,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
$GBGST,121812.000,1.666,0.281,0.265,0.393,1.586,2.002,5.2

2025-07-31 20:18:12:846 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:18:12:854 ==>> 检测【打开大灯控制】
2025-07-31 20:18:12:859 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:18:12:882 ==>> 80*7D

[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_

2025-07-31 20:18:12:940 ==>> audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:18:13:030 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:18:13:131 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:18:13:137 ==>> 检测【关闭仪表供电3】
2025-07-31 20:18:13:145 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:18:13:424 ==>> [D][05:18:40][COMM]read battery soc:255
$GBGGA,121813.000,2301.2581797,N,11421.9414506,E,1,22,0.69,73.225,M,-1.770,M,,*5F

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.38,0.69,1.20,4*06

$GBGSA,A,3,01,60,08,25,07,38,40,10,41,44,,,1.38,0.69,1.20,4*0A

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,37,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,35,25,39,272,41,1*7F

$GBGSV,7,5,27,7,35,175,36,4,32,112,34,38,32,191,38,40,31,160,37,1*7B

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,21,230,36,41,17,322,36,1*4A

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,,,33,1*78

$GBGSV,3,1,09,33,65,301,42,24,55,4,43,39,52,4,40,42,51,161,42,5*74

$GBGSV,3,2,09,25,39,272,39,38,32,191,35,40,31,160,34,41,17,322,29,5*72

$GBGSV,3,3,09,44,13,108,31,5*42

$GBRMC,121813.000,A,2301.2581797,N,11421.9414506,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,121813.000,1.875,0.264,0.248,0.361,1.603,1.899,4.550*75

[W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0

2025-07-31 20:18:13:454 ==>> 


2025-07-31 20:18:13:668 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:18:13:676 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:18:13:686 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:18:13:882 ==>> [D][05:18:40][PROT]CLEAN,SEND:1
[D][05:18:40][PROT]index:1 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:2
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:40][CAT1]gsm read msg sub id: 15
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113

2025-07-31 20:18:13:958 ==>> 311331B88B3C2BA7BE4E05FDA0A748AD50BF37C901A0E52A4BD794639923273E3A9EA231E8FA7B272BEEEA5D8293688F2B573F6A6909EB5060B0637E26B6BD700AD28CB3CA5CA3A1D484687A61B6144DA27A4562A6DA391
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]
[W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:18:14:252 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:18:14:259 ==>> 检测【读大灯电压】
2025-07-31 20:18:14:283 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:18:14:401 ==>> $GBGGA,121814.000,2301.2581455,N,11421.9414148,E,1,23,0.67,73.488,M,-1.770,M,,*55

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.28,0.67,1.09,4*02

$GBGSA,A,3,01,60,08,25,07,38,40,10,41,44,12,,1.28,0.67,1.09,4*0D

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,42,1*4F

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,37,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,35,25,39,272,41,1*7F

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,37,1*7C

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,21,230,36,41,17,322,36,1*4A

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,,,33,1*78

$GBGSV,3,1,09,33,65,301,43,24,55,4,42,39,52,4,40,42,51,161,42,5*74

$GBGSV,3,2,09,25,39,272,39,38,32,191,35,40,31,160,34,41,17,322,29,5*72

$GBGSV,3,3,09,44,13,108,30,5*43

$GBRMC,121814.000,A,2301.2581455,N,11421.9414148,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121814.000,2.095,0.222,0.213,0.298,1.687,1.910,4.133*71



2025-07-31 20:18:14:506 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[32853]


2025-07-31 20:18:14:838 ==>> 【读大灯电压】通过,【32853mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:18:14:848 ==>> 检测【关闭大灯控制2】
2025-07-31 20:18:14:864 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:18:15:030 ==>> [D][05:18:41][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:18:15:116 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:18:15:126 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:18:15:152 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:18:15:420 ==>> [D][05:18:42][COMM]read battery soc:255
$GBGGA,121815.000,2301.2581748,N,11421.9413950,E,1,24,0.63,73.393,M,-1.770,M,,*53

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.22,0.63,1.05,4*00

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.22,0.63,1.05,4*0B

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,37,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,36,25,39,272,41,1*7C

$GBGSV,7,5,27,7,35,175,36,4,32,112,34,38,32,191,38,40,31,160,37,1*7B

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,09,33,65,301,43,24,55,4,43,39,52,4,40,42,51,161,42,5*75

$GBGSV,3,2,09,25,39,272,39,38,32,191,36,40,31,160,34,41,17,322,29,5*71

$GBGSV,3,3,09,44,13,108,31,5*42

$GBRMC,121815.000,A,2301.2581748,N,11421.9413950,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121815.000,2.053,0.197,0.189,0.268,1.620,1.800,3.719*7C

[W][05:18:42][COMM]>>>>>Input command = 

2025-07-31 20:18:15:450 ==>> AT+ARM_ADC=5<<<<<
[D][05:18:42][COMM]arm_hub read adc[5],val[69]


2025-07-31 20:18:15:648 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:18:15:654 ==>> 检测【打开WIFI(4)】
2025-07-31 20:18:15:659 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:18:15:860 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:18:15:969 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:18:15:978 ==>> 检测【EC800M模组版本】
2025-07-31 20:18:16:000 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:18:16:026 ==>> [D][05:18:42][COMM]53854 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:18:16:132 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:18:16:450 ==>> $GBGGA,121816.000,2301.2581687,N,11421.9413727,E,1,25,0.62,73.383,M,-1.770,M,,*5D

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,43,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,37,13,46,220,39,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,42,8,42,207,35,25,39,272,41,1*7C

$GBGSV,7,5,27,7,35,175,36,4,32,112,34,38,32,191,38,40,31,160,38,1*74

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,35,40,31,160,34,26,17,46,34,5*49

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121816.000,A,2301.2581687,N,11421.9413727,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,121816.000,3.430,0.241,0.228,0.319,2.404,2.522,4.024*7F

[D][05:18:43][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_

2025-07-31 20:18:16:480 ==>> VHD8040B.3465b5b1"

OK

[D][05:18:43][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:18:16:738 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F62A7D2297A3,-63
+WIFISCAN:4,2,CC057790A741,-73
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:43][CAT1]wifi scan report total[4]


2025-07-31 20:18:16:765 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:18:16:775 ==>> 检测【配置蓝牙地址】
2025-07-31 20:18:16:787 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:18:16:920 ==>> [W][05:18:43][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:18:16:980 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:F7D603C7426D>】
2025-07-31 20:18:17:025 ==>> [D][05:18:43][COMM]54865 imu init OK
[D][05:18:43][COMM]imu_task

2025-07-31 20:18:17:055 ==>>  imu work error:[-1]. goto init
[D][05:18:43][GNSS]recv submsg id[3]


2025-07-31 20:18:17:145 ==>> recv ble 1
recv ble 2
ble set mac ok :f7,d6,3,c7,42,6d
enable filters ret : 0

2025-07-31 20:18:17:254 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:18:17:260 ==>> 检测【BLETEST】
2025-07-31 20:18:17:269 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:18:17:402 ==>> [D][05:18:44][COMM]read battery soc:255
$GBGGA,121817.000,2301.2581569,N,11421.9413923,E,1,25,0.62,73.363,M,-1.770,M,,*5B

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,35,25,39,272,41,1*7E

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,37,1*7C

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,39,38,32,191,36,40,31,160,34,26,17,46,34,5*44

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121817.000,A,2301.2581569,N,11421.9413923,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121817.000,3.304,0.225,0.212,0.300,2.322,2.425,3.7

2025-07-31 20:18:17:432 ==>> 86*70

4A A4 01 A4 4A 


2025-07-31 20:18:17:537 ==>> recv ble 1
recv ble 2
<BSJ*MAC:F7D603C7426D*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9F7D603C7426D99999OVER 150


2025-07-31 20:18:17:763 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:18:18:026 ==>> [D][05:18:44][COMM]55877 imu init OK


2025-07-31 20:18:18:274 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:18:18:280 ==>> 该项需要延时执行
2025-07-31 20:18:18:408 ==>> $GBGGA,121818.000,2301.2581572,N,11421.9413480,E,1,25,0.62,73.245,M,-1.770,M,,*5F

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,36,25,39,272,41,1*7D

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,37,1*7C

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,43,5*7C

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121818.000,A,2301.2581572,N,11421.9413480,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121818.000,3.265,0.264,0.247,0.348,2.290,2.380,3.635*79



2025-07-31 20:18:19:072 ==>> [D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:5
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:1
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]sending traceid [9999999999900006]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[198]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B39B1EF775C6BC8708B5EA4845DE9F4768A13A3DF55F25184DD063497E29C0E2E0D174FB835EAAFDFF8DAE00024A5C864CBA

2025-07-31 20:18:19:133 ==>> 0F47A8B9143E533FDF0A960FAD6CA07DAA6E977AE6CD546D0E8CE32806D554B214
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]


2025-07-31 20:18:19:420 ==>> [D][05:18:46][COMM]read battery soc:255
$GBGGA,121819.000,2301.2581539,N,11421.9413073,E,1,25,0.62,73.233,M,-1.770,M,,*58

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,36,25,39,272,41,1*7C

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,40,42,51,161,42,5*7C

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121819.000,A,2301.2581539,N,11421.9413073,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,121819.000,3.314,0.214,0.203,0

2025-07-31 20:18:19:450 ==>> .286,2.308,2.388,3.552*71



2025-07-31 20:18:20:403 ==>> $GBGGA,121820.000,2301.2581526,N,11421.9413115,E,1,25,0.62,73.296,M,-1.770,M,,*52

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,36,25,39,272,41,1*7D

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,37,41,17,322,36,1*7D

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121820.000,A,2301.2581526,N,11421.9413115,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121820.000,3.464,0.252,0.236,0.335,2.381,2.451,3.529*78



2025-07-31 20:18:21:425 ==>> [D][05:18:48][COMM]read battery soc:255
$GBGGA,121821.000,2301.2581552,N,11421.9412952,E,1,25,0.62,73.322,M,-1.770,M,,*54

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,36,25,39,272,41,1*7D

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,40,42,51,161,42,5*7C

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121821.000,A,2301.2581552,N,11421.9412952,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121821.000,3.530,0.219,0.208,0.292,2.411,2.474,3.493*7E



2025-07-31 20:18:22:402 ==>> $GBGGA,121822.000,2301.2581637,N,11421.9412826,E,1,25,0.62,73.402,M,-1.770,M,,*50

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,203,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,36,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,35,25,39,272,41,1*7F

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121822.000,A,2301.2581637,N,11421.9412826,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121822.000,3.447,0.211,0.201,0.281,2.363,2.421,3.401*76



2025-07-31 20:18:23:404 ==>> [D][05:18:50][COMM]read battery soc:255
$GBGGA,121823.000,2301.2581816,N,11421.9412830,E,1,25,0.62,73.524,M,-1.770,M,,*5E

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,36,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,35,25,39,272,41,1*7F

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,37,41,17,322,36,1*7D

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,40,42,51,161,42,5*7C

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121823.000,A,2301.2581816,N,11421.9412830,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121823.000,3.508,0.237,0.223,0.314,2.392,2.445,3.378*71



2025-07-31 20:18:24:411 ==>> [D][05:18:50][PROT]CLEAN,SEND:1
[D][05:18:50][PROT]CLEAN:1
[D][05:18:50][PROT]index:0 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:4
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900005]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[198]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:50][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5060A3FED6809F9F8858A4FEBEB4E115EDFFABDB0EB7DD4C20991

2025-07-31 20:18:24:516 ==>> EC39AC1088B15AD46F989689268E04CE0053F8293F5E5A5DFA812736F7CE47375D9C3A078E83ADDCE226495C338CF873326D9DF130605033
[D][05:18:50][CAT1]<<< 
SEND OK

[D][05:18:50][CAT1]exec over: func id: 15, ret: 11
[D][05:18:50][CAT1]sub id: 15, ret: 11

[D][05:18:50][SAL ]Cellular task submsg id[68]
[D][05:18:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:50][M2M ]g_m2m_is_idle become true
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
$GBGGA,121824.000,2301.2581853,N,11421.9412739,E,1,25,0.62,73.532,M,-1.770,M,,*59

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,36,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,36,25,39,272,41,1*7D

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,

2025-07-31 20:18:24:576 ==>> 186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,34,1*7A

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,31,34,9,158,32,5*7B

$GBRMC,121824.000,A,2301.2581853,N,11421.9412739,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,121824.000,3.575,0.222,0.210,0.297,2.424,2.473,3.372*77



2025-07-31 20:18:25:420 ==>> $GBGGA,121825.000,2301.2581872,N,11421.9412612,E,1,25,0.62,73.534,M,-1.770,M,,*55

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,39,42,51,161,41,1*4F

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,35,25,39,272,41,1*7E

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,37,1*7C

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121825.000,A,2301.2581872,N,11421.9412612,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,121825.000,3.645,0.217,0.206,0.288,2.458,2.504,3.373*72

[D][0

2025-07-31 20:18:25:450 ==>> 5:18:52][COMM]read battery soc:255


2025-07-31 20:18:26:412 ==>> $GBGGA,121826.000,2301.2581837,N,11421.9412700,E,1,25,0.62,73.508,M,-1.770,M,,*5A

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,40,9,48,317,37,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,35,25,39,272,41,1*7E

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,29,34,9,158,33,1*75

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,39,272,40,38,32,191,35,40,31,160,34,26,17,46,35,5*48

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121826.000,A,2301.2581837,N,11421.9412700,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121826.000,3.629,0.234,0.221,0.311,2.448,2.491,3.337*72



2025-07-31 20:18:27:426 ==>> $GBGGA,121827.000,2301.2581890,N,11421.9412867,E,1,25,0.62,73.515,M,-1.770,M,,*54

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,36,2,48,240,36,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,35,25,40,272,41,1*70

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,36,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,40,272,40,38,32,191,36,40,31,160,34,26,17,46,34,5*44

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121827.000,A,2301.2581890,N,11421.9412867,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121827.000,3.775,0.232,0.219,0.308,2.521,2.561,3.380*72

[D][05:18:54][COMM]read battery soc:255


2025-07-31 20:18:28:289 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:18:28:296 ==>> 检测【检测WiFi结果】
2025-07-31 20:18:28:302 ==>> WiFi信号:【F88C21BCF57D】,信号值:-33
2025-07-31 20:18:28:322 ==>> WiFi信号:【F62A7D2297A3】,信号值:-61
2025-07-31 20:18:28:329 ==>> WiFi信号:【CC057790A740】,信号值:-74
2025-07-31 20:18:28:336 ==>> WiFi信号:【CC057790A6E1】,信号值:-80
2025-07-31 20:18:28:343 ==>> WiFi信号:【CC057790A741】,信号值:-73
2025-07-31 20:18:28:387 ==>> WiFi信号:【CC057790A7C0】,信号值:-78
2025-07-31 20:18:28:406 ==>> WiFi信号:【CC057790A6E0】,信号值:-79
2025-07-31 20:18:28:430 ==>> WiFi数量【7】, 最大信号值:-33
2025-07-31 20:18:28:445 ==>> 检测【检测GPS结果】
2025-07-31 20:18:28:457 ==>> 符合定位需求的卫星数量:【22】
2025-07-31 20:18:28:476 ==>> 
北斗星号:【14】,信号值:【42】
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【41】
北斗星号:【24】,信号值:【42】
北斗星号:【16】,信号值:【38】
北斗星号:【39】,信号值:【40】
北斗星号:【6】,信号值:【37】
北斗星号:【42】,信号值:【41】
北斗星号:【59】,信号值:【41】
北斗星号:【1】,信号值:【38】
北斗星号:【13】,信号值:【39】
北斗星号:【2】,信号值:【36】
北斗星号:【60】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【40】,信号值:【38】
北斗星号:【9】,信号值:【37】
北斗星号:【7】,信号值:【36】
北斗星号:【38】,信号值:【38】
北斗星号:【8】,信号值:【35】
北斗星号:【26】,信号值:【36】
北斗星号:【41】,信号值:【36】
北斗星号:【44】,信号值:【35】

2025-07-31 20:18:28:494 ==>> 检测【CSQ强度】
2025-07-31 20:18:28:510 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:18:28:517 ==>> $GBGGA,121828.000,2301.2581913,N,11421.9413021,E,1,25,0.62,73.569,M,-1.770,M,,*51

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,36,13,46,220,38,1*73

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,36,25,40,272,41,1*72

$GBGSV,7,5,27,7,35,175,36,4,32,112,34,38,32,191,38,40,31,160,38,1*74

$GBGSV,7,6,27,10,27,186,34,5,22,257,34,26,17,46,37,41,17,322,36,1*7D

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,42,5*7D

$GBGSV,3,2,11,25,40,272,40,38,32,191,36,40,31,160,34,26,17,46,35,5*45

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121828.000,A,2301.2581913,N,11421.9413021,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121828.000,3.514,0.203,0.193,0.271,2.384,2.423,3.232*72



2025-07-31 20:18:28:540 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:55][CAT1]gsm read msg sub id: 12
[D][05:18:55][CAT1]SEND RAW dat

2025-07-31 20:18:28:561 ==>> a >>> AT+CSQ

[D][05:18:55][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:55][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:18:28:862 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 20:18:28:870 ==>> 检测【关闭GSM联网】
2025-07-31 20:18:28:883 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:18:29:031 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:55][COMM]GSM test
[D][05:18:55][COMM]GSM test disable


2025-07-31 20:18:29:158 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:18:29:165 ==>> 检测【4G联网测试】
2025-07-31 20:18:29:177 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:18:30:401 ==>> $GBGGA,121829.000,2301.2581844,N,11421.9413107,E,1,25,0.62,73.538,M,-1.770,M,,*52

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,43,3,62,190,41,24,55,4,42,1*4F

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,41,42,51,161,41,1*40

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,37,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,39,60,43,241,41,8,42,207,36,25,40,272,41,1*72

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,35,26,17,46,37,41,17,322,36,1*7C

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,41,42,51,161,43,5*7C

$GBGSV,3,2,11,25,40,272,39,38,32,191,36,40,31,160,34,26,17,46,35,5*4B

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121829.000,A,2301.2581844,N,11421.9413107,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,121829.000,3.423,0.248,0.233,0.328,2.334,2.371,3.166*74

[D][05:18:56][COMM]

2025-07-31 20:18:30:506 ==>> read battery soc:255
[D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[W][05:18:56][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][C

2025-07-31 20:18:30:612 ==>> AT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B5C4A332F4D9E295FC90616C15D4D59DF2CDC31E9F20CBF98FB296BFFA76653D62CDEF57722C4EA71241FB0416B3B80439AE6D1CFF9F5145126129AAAADC3C7093A797711068C6DEF4158F0B598F5B0B25175E
[D][05:18:56][COMM]Main Task receive event:14
[D][05:18:56][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955136, allstateRepSeconds = 0
[D][05:18:56][COMM]index:0,power_mode:0xFF
[D][05:18:56][COMM]index:1,sound_mode:0xFF
[D][05:18:56][COMM]index:2,gsensor_mode:0xFF
[D][05:18:56][COMM]index:3,report_freq_mode:0xFF
[D][05:18:56][COMM]index:4,report_period:0xFF
[D][05:18:56][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:56][COMM]index:6,normal_reset_period:0xFF
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][COMM]index:7,spock_over_speed:0xFF
[D][05:18:56][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:56][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:56][COMM]index:10,spock_report_period_unlock_unit:0xFF
[

2025-07-31 20:18:30:717 ==>> D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:56][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:56][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:56][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:56][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:56][COMM]index:16,imu_config_params:0xFF
[D][05:18:56][COMM]index:17,long_connect_params:0xFF
[D][05:18:56][COMM]index:18,detain_mark:0xFF
[D][05:18:56][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:56][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:56][COMM]index:21,mc_mode:0xFF
[D][05:18:56][COMM]index:22,S_mode:0xFF
[D][05:18:56][COMM]index:23,overweight:0xFF
[D][05:18:56][COMM]index:24,standstill_mode:0xFF
[D][05:18:56][COMM]index:25,night_mode:0xFF
[D][05:18:56][COMM]index:26,experiment1:0xFF
[D][05:18:56][COMM]index:27,experiment2:0xFF
[D][05:18:56][COMM]index:28,experiment3:0xFF
[D][05:18:56][COMM]index:29,experiment4:0xFF
[D][05:18:56][COMM]index:30,night_mode_start:0xFF
[D][05:18:56][COMM]index:31,night_mode_end:0xFF
[D][05:18:56][COMM]index:33,park_report_minutes:0xFF
[D][05:18:56][COMM]index:34,park_report_mode:0xFF
[D][05:18:56][COMM]index:35,mc

2025-07-31 20:18:30:821 ==>> _undervoltage_protection:0xFF
[D][05:18:56][COMM]index:38,charge_battery_para: FF
[D][05:18:56][COMM]index:39,multirider_mode:0xFF
[D][05:18:56][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:56][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:56][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:56][COMM]index:44,riding_duration_config:0xFF
[D][05:18:56][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:56][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:56][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:56][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:56][COMM]index:49,mc_load_startup:0xFF
[D][05:18:56][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:56][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:56][COMM]index:52,traffic_mode:0xFF
[D][05:18:56][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:56][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:56][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:56][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:56][COMM]index:58,traffic_light_thresho

2025-07-31 20:18:30:927 ==>> ld:0xFF
[D][05:18:56][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:56][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:56][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:56][COMM]index:63,experiment5:0xFF
[D][05:18:56][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:56][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:56][PROT]M2M Send ok [1629955136]
[D][05:18:56][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:56][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:56][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:56][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:56][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:56][COMM]index:72,experiment6:0xFF
[D][05:18:56][COMM]index:73,experiment7:0xFF
[D][05:18:56][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:56][COMM]index:75,zero_value_from_server:-1
[D][05:18:56][COMM]index:76,multirider_threshold:255
[D][05:18:56][COMM]index:77,experiment8:255
[D][05:18:56][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:56][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:56][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:56][COM

2025-07-31 20:18:31:032 ==>> M]index:82,loc_report_low_speed_thr:255
[D][05:18:56][COMM]index:83,loc_report_interval:255
[D][05:18:56][COMM]index:84,multirider_threshold_p2:255
[D][05:18:56][COMM]index:85,multirider_strategy:255
[D][05:18:56][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:56][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:56][COMM]index:90,weight_param:0xFF
[D][05:18:56][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:56][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:56][COMM]index:95,current_limit:0xFF
[D][05:18:56][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:56][COMM]index:100,location_mode:0xFF

[W][05:18:56][PROT]remove success[1629955136],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:8
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2


2025-07-31 20:18:31:201 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:18:31:210 ==>> 检测【关闭GPS】
2025-07-31 20:18:31:222 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:18:31:410 ==>> $GBGGA,121831.000,2301.2581804,N,11421.9413335,E,1,25,0.62,73.617,M,-1.770,M,,*52

$GBGSA,A,3,14,33,03,24,06,16,39,42,59,09,02,13,1.16,0.62,0.98,4*03

$GBGSA,A,3,01,60,08,25,07,38,40,10,26,41,44,12,1.16,0.62,0.98,4*08

$GBGSA,A,3,34,,,,,,,,,,,,1.16,0.62,0.98,4*0C

$GBGSV,7,1,27,14,76,202,42,33,65,301,42,3,62,190,41,24,55,4,41,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,42,51,161,41,1*41

$GBGSV,7,3,27,59,51,128,41,9,48,317,37,2,48,240,37,13,46,220,38,1*72

$GBGSV,7,4,27,1,46,124,38,60,43,241,41,8,42,207,36,25,40,272,41,1*73

$GBGSV,7,5,27,7,35,175,36,4,32,112,33,38,32,191,38,40,31,160,38,1*73

$GBGSV,7,6,27,10,27,186,34,5,22,257,35,26,17,46,36,41,17,322,36,1*7D

$GBGSV,7,7,27,44,13,108,35,12,10,132,30,34,9,158,33,1*7D

$GBGSV,3,1,11,33,65,301,43,24,55,4,43,39,52,4,40,42,51,161,42,5*7C

$GBGSV,3,2,11,25,40,272,39,38,32,191,36,40,31,160,34,26,17,46,34,5*4A

$GBGSV,3,3,11,41,17,322,30,44,13,108,30,34,9,158,32,5*7A

$GBRMC,121831.000,A,2301.2581804,N,11421.9413335,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,121831.000,3.387,0.206,0.196,0.277,2.312,2.346,3.114*7C

[D][05:18:58][COMM]read battery soc:255


2025-07-31 20:18:31:698 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]stop event:8
[D][05:18:58][GNSS]GPS stop. ret=0
[D][05:18:58][GNSS]all continue location stop
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]all sing location stop
[D][05:18:58][CAT1]gsm read msg sub id: 24
[D][05:18:58][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 24, ret: 6
[D][05:18:58][CAT1]sub id: 24, ret: 6



2025-07-31 20:18:31:732 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:18:31:739 ==>> 检测【清空消息队列2】
2025-07-31 20:18:31:775 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:18:31:939 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:58][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:18:32:003 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:18:32:010 ==>> 检测【轮动检测】
2025-07-31 20:18:32:035 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:18:32:151 ==>> [D][05:18:58][GNSS]recv submsg id[1]
[D][05:18:58][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:58][GNSS]location stop evt done evt
3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:18:32:226 ==>> [D][05:18:59][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:18:32:518 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:18:32:642 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:18:32:808 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:18:32:816 ==>> 检测【关闭小电池】
2025-07-31 20:18:32:827 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:18:32:944 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:18:33:083 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:18:33:091 ==>> 检测【进入休眠模式】
2025-07-31 20:18:33:103 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:18:33:330 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:00][COMM]read battery soc:255
[D][05:19:00][COMM]Main Task receive event:28
[D][05:19:00][COMM]main task tmp_sleep_event = 8
[D][05:19:00][COMM]prepare to sleep
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:18:34:108 ==>> [D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 6
[D][05:19:00][M2M ]tcpclient close[4]
[D][05:19:00][SAL ]Cellular task submsg id[12]
[D][05:19:00][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c98], socket[0]
[D][05:19:00][CAT1]gsm read msg sub id: 9
[D][05:19:00][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]exec over: func id: 9, ret: 6
[D][05:19:00][CAT1]sub id: 9, ret: 6

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:00][SAL ]socket close ind. id[4]
[D][05:19:00][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:00][COMM]1x1 frm_can_tp_send ok
[D][05:19:00][CAT1]pdpdeact urc len[22]


2025-07-31 20:18:34:384 ==>> [E][05:19:01][COMM]1x1 rx timeout
[D][05:19:01][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:18:34:915 ==>> [E][05:19:01][COMM]1x1 rx timeout
[E][05:19:01][COMM]1x1 tp timeout
[E][05:19:01][COMM]1x1 error -3.
[D][05:19:01][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:01][COMM]CAN STOP!
[D][05:19:01][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:01][COMM]------------ready to Power off Acckey 1------------
[D][05:19:01][COMM]------------ready to Power off Acckey 2------------
[D][05:19:01][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:01][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1293
[D][05:19:01][COMM]bat sleep fail, reason:-1
[D][05:19:01][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:01][COMM]accel parse set 0
[D][05:19:01][COMM]imu rest ok. 72642
[D][05:19:01][COMM]imu sleep 0
[W][05:19:01][COMM]now sleep


2025-07-31 20:18:35:187 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:18:35:194 ==>> 检测【检测33V休眠电流】
2025-07-31 20:18:35:206 ==>> 开始33V电流采样
2025-07-31 20:18:35:228 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:35:299 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:18:36:305 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:18:36:368 ==>> Current33V:????:2801.52

2025-07-31 20:18:36:813 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:36:918 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:18:37:933 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:18:37:994 ==>> Current33V:????:15.45

2025-07-31 20:18:38:441 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:38:449 ==>> 【检测33V休眠电流】通过,【15.45uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:18:38:457 ==>> 该项需要延时执行
2025-07-31 20:18:40:455 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:18:40:467 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:18:40:491 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:18:40:548 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1674mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 2025mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:18:41:478 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:18:41:490 ==>> 检测【打开小电池2】
2025-07-31 20:18:41:513 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:18:41:551 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:18:41:757 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:18:41:769 ==>> 该项需要延时执行
2025-07-31 20:18:42:272 ==>> 此处延时了:【500】毫秒
2025-07-31 20:18:42:284 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:18:42:308 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:18:42:348 ==>> 5A A5 02 5A A5 


2025-07-31 20:18:42:438 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:18:42:547 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:18:42:555 ==>> 该项需要延时执行
2025-07-31 20:18:43:055 ==>> 此处延时了:【500】毫秒
2025-07-31 20:18:43:068 ==>> 检测【进入休眠模式2】
2025-07-31 20:18:43:080 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:18:43:145 ==>> [D][05:19:09][COMM]------------ready to Power on Acckey 1------------
[D][05:19:09][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:09][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:09][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:09][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[W][05:19:09][COMM]CAN START!
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:09][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 80790


2025-07-31 20:18:43:205 ==>> [D][05:19:09][COMM][Audio]exec status ready.
[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][COMM]imu wakeup ok. 80804
[D][05:19:09][COMM]imu wakeup 1
[W][05:19:09][COMM]wake up system, wakeupEvt=0x80
[D][05:19:09][COMM]frm_can_weigth_power_set 1
[D][05:19:09][COMM]Clear Sleep Block Evt
[D][05:19:09][COMM]Main Task receive event:28 finished processing


2025-07-31 20:18:43:542 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:10][COMM]1x1 rx timeout
[D][05:19:10][COMM]1x1 frm_can_tp_send ok
[D][05:19:10][COMM]Main Task receive event:28
[D][05:19:10][COMM]prepare to sleep
[D][05:19:10][CAT1]gsm read msg sub id: 12
[D][05:19:10][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[W][05:19:10][COMM]CAN STOP!
[D][05:19:10][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 1------------
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------
[D][05:19:10][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:10][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 136
[D][05:19:10][COMM]bat sleep fail, reason:-1
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:10][COMM]accel parse set 0
[D][05:19:10][COMM]imu rest ok. 81251
[D][05:19:10][COMM]imu sleep 0
[W][05:19:10][COMM]now sleep


2025-07-31 20:18:43:595 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:18:43:603 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:18:43:610 ==>> 开始小电池电流采样
2025-07-31 20:18:43:634 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:43:707 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:18:44:714 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:18:44:744 ==>> CurrentBattery:ƽ��:68.02

2025-07-31 20:18:45:222 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:45:229 ==>> 【检测小电池休眠电流】通过,【68.02uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:18:45:237 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:18:45:255 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:18:45:346 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:18:45:499 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:18:45:515 ==>> 该项需要延时执行
2025-07-31 20:18:45:586 ==>> [D][05:19:12][COMM]------------ready to Power on Acckey 1------------
[D][05:19:12][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 9
[D][05:19:12][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:12][COMM]----- get Acckey 1 and value:1------------
[W][05:19:12][COMM]CAN START!
[E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[E][05:19:12][COMM]1x1 error -3.
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:12][COMM][Audio]exec status ready.
[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]imu wakeup ok. 83286
[D][05:19:12][COMM]imu wakeup 1
[W][05:19:12][COMM]wake up system, wakeupEvt=0x80
[D][05:19:12][COMM]frm_can_weigth_power_set 1
[D][05:19:12][COMM]Clear Sleep Block Evt
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok
[D][05:19:12][COMM]read battery soc:0


2025-07-31 20:18:45:841 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:18:45:946 ==>> [D][05:19:12][COMM]msg 02A0 loss. last_tick:83254. cur_tick:83766. period:50
[D][

2025-07-31 20:18:46:009 ==>> 此处延时了:【500】毫秒
2025-07-31 20:18:46:022 ==>> 检测【检测唤醒】
2025-07-31 20:18:46:048 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:18:46:077 ==>> 05:19:12][COMM]msg 02A4 loss. last_tick:83254. cur_tick:83766. period:50
[D][05:19:12][COMM]msg 02A5 loss. last_tick:83254. cur_tick:83767. period:50
[D][05:19:12][COMM]msg 02A6 loss. last_tick:83254. cur_tick:83767. period:50
[D][05:19:12][COMM]msg 02A7 loss. last_tick:83254. cur_tick:83767. period:50
[D][05:19:12][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 83768
[D][05:19:12][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 83768


2025-07-31 20:18:46:429 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:13][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[D][05:19:13][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:13][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:13][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:13][FCTY]DeviceID    = 460130071541422
[D][05:19:13][FCTY]HardwareID  = 867222087966166
[D][05:19:13][FCTY]MoBikeID    = 9999999999
[D][05:19:13][FCTY]LockID      = FFFFFFFFFF
[D][05:19:13][FCTY]BLEFWVersion= 105
[D][05:19:13][FCTY]BLEMacAddr   = F7D603C7426D
[D][05:19:13][FCTY]Bat         = 3904 mv
[D][05:19:13][FCTY]Current     = 0 ma
[D][05:19:13][FCTY]VBUS        = 2600 mv
[D][05:19:13][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:13][FCTY]Ext battery vol = 32, adc = 1293
[D][05:19:13][FCTY]Acckey1 vol = 5566 mv, Acckey2 vol = 126 mv
[D][05:19:13][FCTY]Bike Type flag is invalied
[D][05:19:13][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:13][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:13][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:13][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:13][FCTY]

2025-07-31 20:18:46:474 ==>> Bat1         = 3773 mv
[D][05:19:13][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[D][05:19:13][COMM]Main Task receive event:28 finished processing


2025-07-31 20:18:46:539 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:18:46:550 ==>> 检测【关机】
2025-07-31 20:18:46:566 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:18:46:587 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:18:46:684 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ][05:19:13][COMM]bat msg 024E loss. last_tick:83255. cur_tick:84274. period:100. j,i:15 68
[D][05:19:13][COMM]bat msg 024F loss. last_tick:83255. cur_tick:84274. period:100. j,i:16 69
[D][05:19:13][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22

2025-07-31 20:18:46:714 ==>> 213 84275
[D][05:19:13][COMM]CAN message bat fault change: 0x00000000->0x0001802E 84275
[D][05:19:13][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 84276


2025-07-31 20:18:46:804 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 20:18:46:909 ==>> [D][05:19:13][COMM]arm_hub_enable: hub power: 0
[D][05:19:13][HSDK]hexlog index save 0 3328 239 @ 0 : 0
[D][05:19:13][HSDK]write save hexlog index [0]
[D][05:19:13][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:13][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:18:47:014 ==>> [D][05:19:13][COMM]msg 0222 loss. last_tick:83254. cur_tick:84844. period:150
[D][05:19:13][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 84846


2025-07-31 20:18:47:194 ==>> [D][05:19:13][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:13][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:13][COMM]----- get Acckey 1 and value:1------------
[D][05:19:13][COMM]----- get Acckey 2 and value:0------------
[D][05:19:13][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:18:47:572 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:18:48:012 ==>>                                                                                                                           --
[D][05:19:14][COMM]----- get Acckey 2 and value:1------------
[D][05:19:14][COMM]more than the number of battery plugs
[D][05:19:14][COMM]VBUS is 1
[D][05:19:14][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:14][COMM]file:B50 exist
[D][05:19:14][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:14][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:14][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:14][COMM]Bat auth off fail, error:-1
[D][05:19:14][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:14][COMM]----- get Acckey 1 and value:1------------
[D][05:19:14][COMM]----- get Acckey 2 and value:1------------
[D][05:19:14][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:14][COMM]----- get Acckey 1 and value:1------------
[D][05:19:14][COMM]----- get Acckey 2 and value:1------------
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:14][COMM]file:B50 exist
[D][05:19:14][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:14][COMM]read file, 

2025-07-31 20:18:48:117 ==>> len:10800, num:3
[D][05:19:14][COMM]--->crc16:0xb8a
[D][05:19:14][COMM]read file success
[D][05:19:14][COMM]accel parse set 1
[D][05:19:14][COMM][Audio]mon:9,05:19:14
[D][05:19:14][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:14][COMM]Main Task receive event:65
[D][05:19:14][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:14][COMM]Main Task receive event:65 finished processing
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:14][COMM]Main Task receive event:66
[D][05:19:14][COMM]Try to Auto Lock Bat
[D][05:19:14][COMM]Main Task receive event:66 finished processing
[D][05:19:14][COMM]Main Task receive event:60
[D][05:19:14][COMM]smart_he

2025-07-31 20:18:48:222 ==>> lmet_vol=255,255
[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]BAT CAN get state2 fail 204
[D][05:19:14][COMM]get soh error
[D][05:19:14][COMM]Receive Bat Lock cmd 0
[D][05:19:14][COMM]VBUS is 1
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[E][05:19:14][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:14][COMM]report elecbike
[W][05:19:14][PROT]remove success[1629955154],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:14][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:14][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:14][PROT]index:0
[D][05:19:14][PROT]is_send:1
[D][05:19:14][PROT]sequence_num:10
[D][05:19:14][PROT]retry_timeout:0
[D][05:19:14][PROT]retry_times:3
[D][05:19:14][PROT]send_path:0x3
[D][05:19:14][PROT]msg_type:0x5d03
[D][05:19:14][PROT]===========================================================
[W][05:19:14][PR

2025-07-31 20:18:48:327 ==>> OT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955154]
[D][05:19:14][PROT]===========================================================
[D][05:19:14][PROT]Sending traceid[999999999990000B]
[D][05:19:14][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:14][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:14][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:14][PROT]add success [1629955154],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:14][COMM]Main Task receive event:60 finished processing
[D][05:19:14][COMM]Main Task receive event:61
[D][05:19:14][COMM][D301]:type:3, trace id:280
[D][05:19:14][COMM]id[], hw[000
[D][05:19:14][COMM]get mcMaincircuitVolt error
[D][05:19:14][COMM]get mcSubcircuitVolt error
[D][05:19:14][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:14][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:14][SAL ]open socket ind id[4], rst[0]
[D][05:19:14][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:14][SAL ]Cellular task submsg id[8]
[D][05:19:14][SAL ]cell

2025-07-31 20:18:48:432 ==>> ular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:14][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:14][COMM]BAT CAN get state2 fail 204
[D][05:19:14][COMM]get bat work mode err
[W][05:19:14][PROT]remove success[1629955154],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:14][PROT]add success [1629955154],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:14][COMM]Main Task receive event:61 finished processing
[D][05:19:14][CAT1]gsm read msg sub id: 8
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:14][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:14][M2M ]m2m_task: control_queue typ

2025-07-31 20:18:48:537 ==>> e:[M2M_GSM_POWER_ON]
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:14][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:14][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:14][CAT1]<<< 
+CME ERROR: 100

[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:14][COMM]Power Off
[D][05:19:14][COMM]read battery soc:255
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:14][COMM]f:[ec800m_audio

2025-07-31 20:18:48:618 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:18:48:628 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:18:48:636 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:18:48:662 ==>> _play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:14][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:14][COMM]arm_hub_enable: hub power: 0
[D][05:19:14][HSDK]hexlog index save 0 3328 239 @ 0 : 0
[D][05:19:14][HSDK]write save hexlog index [0]
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
                              

2025-07-31 20:18:48:732 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:18:48:901 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:18:48:909 ==>> 检测【检测小电池关机电流】
2025-07-31 20:18:48:916 ==>> 开始小电池电流采样
2025-07-31 20:18:48:928 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:48:935 ==>> [D][05:19:15][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 2,volt = 14


2025-07-31 20:18:49:002 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:18:50:013 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:18:50:043 ==>> CurrentBattery:ƽ��:65.45

2025-07-31 20:18:50:523 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:18:50:533 ==>> 【检测小电池关机电流】通过,【65.45uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:18:50:823 ==>> MES过站成功
2025-07-31 20:18:50:834 ==>> #################### 【测试结束】 ####################
2025-07-31 20:18:50:853 ==>> 关闭5V供电
2025-07-31 20:18:50:866 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:18:50:949 ==>> 5A A5 04 5A A5 


2025-07-31 20:18:51:039 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:18:51:853 ==>> 关闭5V供电成功
2025-07-31 20:18:51:866 ==>> 关闭33V供电
2025-07-31 20:18:51:888 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:18:51:944 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:18:52:861 ==>> 关闭33V供电成功
2025-07-31 20:18:52:875 ==>> 关闭3.7V供电
2025-07-31 20:18:52:901 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:18:52:936 ==>> 6A A6 02 A6 6A 


2025-07-31 20:18:53:041 ==>> Battery OFF
OVER 150


