2025-07-31 18:13:43:001 ==>> MES查站成功:
查站序号:P51000100531378D验证通过
2025-07-31 18:13:43:004 ==>> 扫码结果:P51000100531378D
2025-07-31 18:13:43:006 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:13:43:007 ==>> 测试参数版本:2024.10.11
2025-07-31 18:13:43:008 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:13:43:010 ==>> 检测【打开透传】
2025-07-31 18:13:43:012 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:13:43:071 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:13:43:360 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:13:43:363 ==>> 检测【检测接地电压】
2025-07-31 18:13:43:365 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:13:43:470 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:13:43:639 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:13:43:641 ==>> 检测【打开小电池】
2025-07-31 18:13:43:643 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:13:43:758 ==>> 6A A6 01 A6 6A 


2025-07-31 18:13:43:863 ==>> Battery ON
OVER 150


2025-07-31 18:13:43:915 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:13:43:917 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:13:43:919 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:13:43:968 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:13:44:193 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:13:44:196 ==>> 检测【等待设备启动】
2025-07-31 18:13:44:201 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:13:44:593 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:13:44:788 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:13:45:233 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:13:45:437 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:13:45:482 ==>>                                     Will Not Open


2025-07-31 18:13:45:878 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:13:46:275 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:13:46:366 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:13:46:577 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:13:46:579 ==>> 检测【产品通信】
2025-07-31 18:13:46:581 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:13:46:734 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:13:46:883 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:13:46:885 ==>> 检测【初始化完成检测】
2025-07-31 18:13:46:888 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:13:47:103 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:13:47:186 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:13:47:188 ==>> 检测【关闭大灯控制1】
2025-07-31 18:13:47:189 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:13:47:331 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:13:47:436 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:13:47:476 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:13:47:479 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:13:47:481 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:13:47:541 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2]

2025-07-31 18:13:47:586 ==>> ,type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:13:47:676 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:13:47:754 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:13:47:782 ==>> 检测【关闭仪表供电】
2025-07-31 18:13:47:784 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:13:47:952 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:13:48:031 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:13:48:033 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:13:48:034 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:13:48:228 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:13:48:312 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:13:48:314 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:13:48:316 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:13:48:453 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:13:48:646 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:13:48:648 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:13:48:650 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:13:48:853 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:13:48:919 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:13:48:922 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:13:48:945 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:13:49:066 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:13:49:141 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30


2025-07-31 18:13:49:197 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:13:49:201 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:13:49:203 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:13:49:206 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 18:13:49:262 ==>> 5A A5 03 5A A5 


2025-07-31 18:13:49:368 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 18:13:49:457 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 18:13:49:475 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:13:49:478 ==>> 该项需要延时执行
2025-07-31 18:13:49:961 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5005. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5007. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5007. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5008. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5009. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->

2025-07-31 18:13:49:992 ==>> 0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5009


2025-07-31 18:13:50:449 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:13:50:725 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:13:51:220 ==>>                                   device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][

2025-07-31 18:13:51:325 ==>> 05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]

2025-07-31 18:13:51:430 ==>> Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BL

2025-07-31 18:13:51:490 ==>> E ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 18:13:51:535 ==>>                                                                                                                                           

2025-07-31 18:13:52:473 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:13:53:223 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:13:53:482 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:13:53:485 ==>> 检测【33V输入电压ADC】
2025-07-31 18:13:53:488 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:13:53:490 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:13:53:772 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:13:54:018 ==>> 【33V输入电压ADC】通过,【32678mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:13:54:023 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:13:54:048 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:13:54:169 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2778mV
Get AD_V6 1988mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:13:54:293 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:54:295 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:13:54:313 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:54:317 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:13:54:322 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 18:13:54:336 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:13:54:338 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:13:54:355 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:13:54:357 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:13:54:378 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:13:54:381 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:13:54:484 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2777mV
Get AD_V6 1991mV
Get AD_V7 1087mV
OVER 150
[D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:13:54:657 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:54:678 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:13:54:708 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:54:711 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:13:54:714 ==>> 原始值:【2777】, 乘以分压基数【2】还原值:【5554】
2025-07-31 18:13:54:716 ==>> 【TP68_VCC5V5(ADV5)】通过,【5554mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:13:54:719 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:13:54:726 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:13:54:729 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:13:54:748 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:13:54:751 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:13:54:878 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10019
1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2776mV
Get AD_V6 1989mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:13:55:036 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:55:038 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:13:55:055 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:55:057 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:13:55:062 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 18:13:55:075 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:13:55:078 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:13:55:094 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:13:55:096 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:13:55:116 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:13:55:136 ==>> 检测【打开WIFI(1)】
2025-07-31 18:13:55:139 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:13:55:245 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:13:55:350 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:13:55:398 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:13:55:412 ==>> 检测【清空消息队列(1)】
2025-07-31 18:13:55:415 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:13:55:749 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D]

2025-07-31 18:13:55:794 ==>> [05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:13:55:932 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:13:55:945 ==>> 检测【打开GPS(1)】
2025-07-31 18:13:55:949 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:13:56:259 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087876746

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290679

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_

2025-07-31 18:13:56:304 ==>> CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:13:56:457 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:13:56:472 ==>> 检测【打开GSM联网】
2025-07-31 18:13:56:475 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:13:56:517 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:13:56:621 ==>> [W][05:18:00][COMM]>>>>>Input 

2025-07-31 18:13:56:651 ==>> command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:13:56:747 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:13:56:752 ==>> 检测【打开仪表供电1】
2025-07-31 18:13:56:756 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:13:56:951 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:13:57:053 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:13:57:058 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:13:57:062 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:13:57:446 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][COMM]read batter

2025-07-31 18:13:57:536 ==>> y soc:255
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.89.136.194"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:13:57:613 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:13:57:617 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:13:57:620 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:13:57:825 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        [D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33131]


2025-07-31 18:13:57:916 ==>> 【读取主控ADC采集的仪表电压】通过,【33131mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:13:57:919 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:13:57:921 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:13:58:156 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:13:58:198 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:13:58:201 ==>> 检测【AD_V20电压】
2025-07-31 18:13:58:204 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:13:58:307 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:13:58:368 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:13:58:413 ==>> 本次取值间隔时间:99ms
2025-07-31 18:13:58:434 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:13:58:518 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:02][COMM]13729 imu init OK


2025-07-31 18:13:58:548 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:13:58:608 ==>> 本次取值间隔时间:56ms
2025-07-31 18:13:58:698 ==>> [D][05:18:02][COMM]S->M yaw:INVALID
[D][05:18:02][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:02][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 18:13:58:834 ==>> 本次取值间隔时间:212ms
2025-07-31 18:13:58:855 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:13:58:857 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:13:58:861 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:13:58:958 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:13:59:158 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:13:59:161 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:13:59:164 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:13:59:168 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:13:59:429 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,26,,,42,42,,,42,21,,,41,38,,,41,1*7D

$GBGSV,3,2,12,39,,,39,33,,,38,16,,,19,24,,,48,1*7A

$GBGSV,3,3,12,59,,,43,9,,,40,14,,,40,13,,,39,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1551.964,1551.964,49.878,2097152,2097152,2097152*45

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:13:59:504 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:13:59:713 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:13:59:717 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:13:59:731 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:13:59:759 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:14:00:015 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:14:00:018 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:14:00:023 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:14:00:326 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,26,,,42,42,,,42,13,,,42,3,,,42,1*48

$GBGSV,5,2,18,59,,,41,21,,,41,38,,,41,39,,,40,1*77

$GBGSV,5,3,18,8,,,40,16,,,39,1,,,39,24,,,38,1*7E

$GBGSV,5,4,18,9,,,38,14,,,38,6,,,38,33,,,37,1*7B

$GBGSV,5,5,18,5,,,34,4,,,32,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1621.467,1621.467,51.856,2097152,2097152,2097152*40

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:14:00:544 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:14:00:549 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:14:00:553 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:14:00:752 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:14:00:816 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:14:00:820 ==>> 检测【AD_V21电压】
2025-07-31 18:14:00:824 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:14:00:857 ==>> 1A A1 20 00 00 
Get AD_V21 1088mV
OVER 150


2025-07-31 18:14:00:887 ==>> 本次取值间隔时间:61ms
2025-07-31 18:14:00:905 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:14:00:962 ==>> 1A A1 20 00 00 
Get AD_V21 1088mV
OVER 150


2025-07-31 18:14:01:194 ==>> 本次取值间隔时间:284ms
2025-07-31 18:14:01:213 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:14:01:315 ==>> 1A A1 20 00 00 
Get AD_V21 1645mV
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,26,,,42,42,,,42,13,,,42,3,,,41,1*40

$GBGSV,5,2,20,59,,,41,21,,,41,38,,,41,8,,,41,1*4F

$GBGSV,5,3,20,24,,,41,60,,,41,39,,,40,16,,,39,1*71

$GBGSV,5,4,20,1,,,38,9,,,38,14,,,38,6,,,38,1*4E

$GBGSV,5,5,20,2,,,38,33,,,37,5,,,34,4,,,33,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1629.287,1629.287,52.095,2097152,2097152,2097152*44

[D][05:18:05][COMM]read battery soc:255


2025-07-31 18:14:01:483 ==>> 本次取值间隔时间:266ms
2025-07-31 18:14:01:501 ==>> 【AD_V21电压】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:14:01:506 ==>> 检测【关闭仪表供电2】
2025-07-31 18:14:01:513 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:14:01:654 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:14:01:816 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:14:01:819 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:14:01:822 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:14:02:053 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:14:02:240 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:14:02:244 ==>> 检测【打开AccKey2供电】
2025-07-31 18:14:02:249 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:14:02:297 ==>> $GBGGA,101406.105,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,26,,,42,42,,,42,13,,,42,24,,,42,1*76

$GBGSV,5,2,20,3,,,41,59,,,41,21,,,41,38,,,41,1*44

$GBGSV,5,3,20,60,,,41,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,5,4,20,1,,,38,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,5,5,20,2,,,37,33,,,37,5,,,34,4,,,33,1*40

$GBRMC,101406.105,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101406.105,0.000,1625.146,1625.146,51.968,2097152,2097152,2097152*54



2025-07-31 18:14:02:387 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:14:02:577 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:14:02:580 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:14:02:583 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:02:705 ==>> $GBGGA,101406.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,26,,,42,42,,,42,13,,,42,24,,,42,1*76

$GBGSV,5,2,20,3,,,41,59,,,41,21,,,41,38,,,41,1*44

$GBGSV,5,3,20,60,,,41,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,5,4,20,1,,,38,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,5,5,20,2,,,37,33,,,37,5,,,34,4,,,33,1*40

$GBRMC,101406.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101406.505,0.000,1625.146,1625.146,51.968,2097152,2097152,2097152*50



2025-07-31 18:14:02:945 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:06][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:18:06][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:06][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:06][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:06][COMM]adc read battery ts volt:7 mv
[D][05:18:06][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:06][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:14:03:141 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33285mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:14:03:144 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:14:03:148 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:14:03:270 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:14:03:344 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:14:03:429 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:14:03:432 ==>> 该项需要延时执行
2025-07-31 18:14:03:708 ==>> $GBGGA,101407.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,42,,,42,26,,,41,13,,,41,1*77

$GBGSV,5,2,20,3,,,41,59,,,41,21,,,41,38,,,41,1*44

$GBGSV,5,3,20,60,,,41,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,5,4,20,1,,,38,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,5,5,20,33,,,37,2,,,36,5,,,34,4,,,33,1*41

$GBRMC,101407.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101407.505,0.000,1621.001,1621.001,51.837,2097152,2097152,2097152*5A



2025-07-31 18:14:04:706 ==>> $GBGGA,101408.505,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,42,,,42,26,,,41,13,,,41,1*77

$GBGSV,5,2,20,3,,,41,59,,,41,38,,,41,60,,,41,1*41

$GBGSV,5,3,20,21,,,40,8,,,40,39,,,40,16,,,39,1*4A

$GBGSV,5,4,20,1,,,39,14,,,38,6,,,38,9,,,37,1*40

$GBGSV,5,5,20,33,,,37,2,,,37,5,,,34,4,,,33,1*40

$GBRMC,101408.505,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101408.505,0.000,1623.069,1623.069,51.898,2097152,2097152,2097152*50



2025-07-31 18:14:05:264 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:14:05:711 ==>> $GBGGA,101409.505,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,42,,,42,26,,,41,13,,,41,1*75

$GBGSV,6,2,21,3,,,41,59,,,41,38,,,41,60,,,41,1*43

$GBGSV,6,3,21,21,,,41,8,,,41,39,,,40,16,,,39,1*48

$GBGSV,6,4,21,1,,,39,14,,,38,6,,,38,9,,,37,1*42

$GBGSV,6,5,21,33,,,37,2,,,37,5,,,34,7,,,34,1*46

$GBGSV,6,6,21,4,,,33,1*41

$GBRMC,101409.505,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101409.505,0.000,1616.863,1616.863,51.711,2097152,2097152,2097152*5F



2025-07-31 18:14:06:440 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:14:06:445 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:14:06:459 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:06:828 ==>> $GBGGA,101410.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,41,1*75

$GBGSV,6,2,22,3,,,41,59,,,41,38,,,41,60,,,41,1*40

$GBGSV,6,3,22,21,,,41,8,,,41,39,,,40,16,,,39,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,33,1*41

$GBGSV,6,6,22,7,,,32,45,,,28,1*4B

$GBRMC,101410.505,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101410.505,0.000,1596.182,1596.182,51.097,2097152,2097152,2097152*5E

[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:10][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:10][COMM]adc read battery ts volt:11 mv
[D][05:18:10][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05

2025-07-31 18:14:06:858 ==>> :18:10][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:14:07:000 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:14:07:004 ==>> 检测【打开AccKey1供电】
2025-07-31 18:14:07:008 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:14:07:149 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:14:07:254 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:14:07:393 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:14:07:398 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:14:07:403 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:14:07:467 ==>> 1A A1 00 40 00 
Get AD_V14 2640mV
OVER 150


2025-07-31 18:14:07:649 ==>> 原始值:【2640】, 乘以分压基数【2】还原值:【5280】
2025-07-31 18:14:07:693 ==>> 【读取AccKey1电压(ADV14)前】通过,【5280mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:14:07:696 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:14:07:699 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:07:709 ==>> $GBGGA,101411.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,3,,,41,59,,,41,60,,,41,1*43

$GBGSV,6,3,22,21,,,41,8,,,41,39,,,40,16,,,39,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,29,1*4A

$GBRMC,101411.505,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101411.505,0.000,1603.714,1603.714,51.332,2097152,2097152,2097152*53



2025-07-31 18:14:07:982 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:12][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:12][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:12][COMM]adc read battery ts volt:12 mv
[D][05:18:12][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:12][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:14:08:244 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:14:08:247 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:14:08:252 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:14:08:457 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:14:08:520 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:14:08:526 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:14:08:550 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:14:08:714 ==>> $GBGGA,101412.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,41,3,,,41,59,,,41,21,,,41,1*45

$GBGSV,6,3,22,8,,,41,60,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,30,1*42

$GBRMC,101412.505,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101412.505,0.000,1603.702,1603.702,51.320,2097152,2097152,2097152*53

1A A1 00 40 00 
Get AD_V14 2644mV
OVER 150


2025-07-31 18:14:08:774 ==>> 原始值:【2644】, 乘以分压基数【2】还原值:【5288】
2025-07-31 18:14:08:800 ==>> 【读取AccKey1电压(ADV14)后】通过,【5288mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:14:08:803 ==>> 检测【打开WIFI(2)】
2025-07-31 18:14:08:808 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:14:08:987 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:14:09:091 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:14:09:095 ==>> 检测【转刹把供电】
2025-07-31 18:14:09:098 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:14:09:276 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:13][COMM]read battery soc:255


2025-07-31 18:14:09:392 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:14:09:414 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:14:09:418 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:14:09:503 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:14:09:533 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:14:09:563 ==>> 1A A1 00 80 00 
Get AD_V15 2416mV
OVER 150


2025-07-31 18:14:09:669 ==>> 原始值:【2416】, 乘以分压基数【2】还原值:【4832】
2025-07-31 18:14:09:705 ==>> 【读取AD_V15电压(前)】通过,【4832mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:14:09:710 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:14:09:713 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:14:09:759 ==>> $GBGGA,101413.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,42,13,,,41,26,,,41,1*77

$GBGSV,6,2,22,38,,,41,3,,,41,59,,,41,21,,,41,1*45

$GBGSV,6,3,22,60,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,30,1*42

$GBRMC,101413.505,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101413.505,0.000,1596.158,1596.158,51.074,2097152,2097152,2097152*50

+WIFISCAN:4,0,CC057790A741,-73
+WIFISCAN:4,1,44A1917CA62B,-74
+WIFISCAN:4,2,CC057790A7C1,-76
+WIFISCAN:4,3,CC057790A7C0,-77

[D][05:18:13][CAT1]wifi scan report total[4]


2025-07-31 18:14:09:819 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:14:09:849 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:14:09:954 ==>> 1A A1 01 00 00 
Get AD_V16 2445mV
OVER 150


2025-07-31 18:14:09:984 ==>> 原始值:【2445】, 乘以分压基数【2】还原值:【4890】
2025-07-31 18:14:10:038 ==>> 【读取AD_V16电压(前)】通过,【4890mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:14:10:042 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:14:10:046 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:10:383 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:14][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:14][COMM]adc read battery ts volt:6 mv
[D][05:18:14][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3100  volt:5449 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:14:10:548 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 18:14:10:584 ==>> 【转刹把供电电压(主控ADC)】通过,【5449mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:14:10:590 ==>> 检测【转刹把供电电压】
2025-07-31 18:14:10:609 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:10:883 ==>> $GBGGA,101414.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,41,3,,,41,59,,,41,21,,,41,1*45

$GBGSV,6,3,22,60,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101414.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101414.505,0.000,1603.697,1603.697,51.315,2097152,2097152,2097152*53

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:14][COMM]adc read battery ts volt:9 mv
[D][05:18:14][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3095  volt:5440 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:

2025-07-31 18:14:10:928 ==>> 18:14][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:14:11:128 ==>> 【转刹把供电电压】通过,【5440mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:14:11:133 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:14:11:138 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:14:11:344 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:14:11:408 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:14:11:412 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:14:11:417 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:14:11:511 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:14:11:526 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:14:11:571 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 18:14:11:635 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:14:11:640 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:14:11:646 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:14:11:676 ==>> $GBGGA,101415.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,3,,,41,59,,,41,21,,,41,1*46

$GBGSV,6,3,22,60,,,41,8,,,41,39,,,40,16,,,40,1*40

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101415.505,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.

2025-07-31 18:14:11:706 ==>> 00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101415.505,0.000,1609.356,1609.356,51.501,2097152,2097152,2097152*51



2025-07-31 18:14:11:736 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:14:11:781 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:14:11:841 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:14:11:871 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:14:11:946 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:14:12:023 ==>> [W][05:18:16][COMM]>>>>>Input command = <<<<<


2025-07-31 18:14:12:067 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:14:12:186 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:14:12:190 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:14:12:221 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:14:12:266 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:14:12:495 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:14:12:501 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:14:12:532 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:14:12:558 ==>> 3A A3 04 01 A3 


2025-07-31 18:14:12:663 ==>> $GBGGA,101416.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,3,,,41,59,,,41,21,,,41,1*46

$GBGSV,6,3,22,60,,,41,8,,,41,39,,,40,16,,,39,1*4E

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,37

2025-07-31 18:14:12:708 ==>> ,1*41

$GBGSV,6,5,22,33,,,37,2,,,37,4,,,35,5,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101416.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101416.505,0.000,1607.469,1607.469,51.439,2097152,2097152,2097152*58

ON_OUT4
OVER 150


2025-07-31 18:14:12:798 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:14:12:804 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:14:12:830 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:14:12:858 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:14:13:096 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:14:13:101 ==>> 检测【左刹电压测试1】
2025-07-31 18:14:13:105 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:13:379 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:17][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:17][COMM]adc read left brake adc:1729  volt:2279 mv
[D][05:18:17][COMM]adc read right brake adc:1727  volt:2276 mv
[D][05:18:17][COMM]adc read throttle adc:1723  volt:2271 mv
[D][05:18:17][COMM]adc read battery ts volt:14 mv
[D][05:18:17][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:17][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:17][COMM]read battery soc:255
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:14:13:640 ==>> 【左刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 18:14:13:644 ==>> 检测【右刹电压测试1】
2025-07-31 18:14:13:684 ==>> 【右刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 18:14:13:688 ==>> 检测【转把电压测试1】
2025-07-31 18:14:13:714 ==>> $GBGGA,101417.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,42,3,,,41,59,,,41,1*40

$GBGSV,6,3,22,21,,,41,8,,,41,39,,,40,16,,,40,1*45

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,37,1*41

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101417.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101417.505,0.000,1611.240,1611.240,51.561,2097152,2097152,2097152*55



2025-07-31 18:14:13:723 ==>> 【转把电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 18:14:13:727 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:14:13:747 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:14:13:867 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:14:14:056 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:14:14:060 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:14:14:066 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:14:14:158 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:14:14:327 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:14:14:331 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:14:14:337 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:14:14:464 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:14:14:605 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:14:14:610 ==>> 检测【左刹电压测试2】
2025-07-31 18:14:14:615 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:14:14:706 ==>> $GBGGA,101418.505,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,42,3,,,41,59,,,41,1*40

$GBGSV,6,3,22,21,,,41,8,,,41,39,,,41,16,,,40,1*44

$GBGSV,6,4,22,1,,,39,14,,,39,6,,,38,9,,,38,1*4F

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101418.505,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101418.505,0.000,1615.011,1615.011,51.684,2097152,2097152,2097152*52



2025-07-31 18:14:14:917 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:18][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:18][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:18][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:18][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:14:15:145 ==>> 【左刹电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 18:14:15:149 ==>> 检测【右刹电压测试2】
2025-07-31 18:14:15:164 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 18:14:15:170 ==>> 检测【转把电压测试2】
2025-07-31 18:14:15:184 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 18:14:15:187 ==>> 检测【晶振检测】
2025-07-31 18:14:15:192 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:14:15:350 ==>> [D][05:18:19][COMM]read battery soc:255
[W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 18:14:15:458 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:14:15:462 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:14:15:468 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:14:15:578 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1660mV
Get AD_V4 1650mV
Get AD_V5 2776mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:14:15:683 ==>> $GBGGA,101419.505,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,44,42,,,42,13,,,42,26,,,42,1*71

$GBGSV,6,2,22,38,,,42,60,,,42,21,,,42,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,39,6,,,38,9,

2025-07-31 18:14:15:728 ==>> ,,38,1*4F

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101419.505,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101419.505,0.000,1620.666,1620.666,51.866,2097152,2097152,2097152*51



2025-07-31 18:14:15:732 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:14:15:746 ==>> 检测【检测BootVer】
2025-07-31 18:14:15:753 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:14:16:124 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130020290679
[D][05:18:20][FCTY]HardwareID  = 867222087876746
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1292
[D][05:18:20][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][F

2025-07-31 18:14:16:169 ==>> CTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3815 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:14:16:272 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:14:16:276 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:14:16:285 ==>> 检测【检测固件版本】
2025-07-31 18:14:16:306 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:14:16:310 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:14:16:314 ==>> 检测【检测蓝牙版本】
2025-07-31 18:14:16:326 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:14:16:331 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:14:16:335 ==>> 检测【检测MoBikeId】
2025-07-31 18:14:16:348 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:14:16:352 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:14:16:355 ==>> 检测【检测蓝牙地址】
2025-07-31 18:14:16:361 ==>> 取到目标值:D86AEE8898D0
2025-07-31 18:14:16:383 ==>> 【检测蓝牙地址】通过,【D86AEE8898D0】符合目标值【】要求!
2025-07-31 18:14:16:387 ==>> 提取到蓝牙地址:D86AEE8898D0
2025-07-31 18:14:16:392 ==>> 检测【BOARD_ID】
2025-07-31 18:14:16:403 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:14:16:407 ==>> 检测【检测充电电压】
2025-07-31 18:14:16:429 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:14:16:433 ==>> 检测【检测VBUS电压1】
2025-07-31 18:14:16:459 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:14:16:463 ==>> 检测【检测充电电流】
2025-07-31 18:14:16:478 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:14:16:482 ==>> 检测【检测IMEI】
2025-07-31 18:14:16:495 ==>> 取到目标值:867222087876746
2025-07-31 18:14:16:499 ==>> 【检测IMEI】通过,【867222087876746】符合目标值【】要求!
2025-07-31 18:14:16:503 ==>> 提取到IMEI:867222087876746
2025-07-31 18:14:16:506 ==>> 检测【检测IMSI】
2025-07-31 18:14:16:510 ==>> 取到目标值:460130020290679
2025-07-31 18:14:16:523 ==>> 【检测IMSI】通过,【460130020290679】符合目标值【】要求!
2025-07-31 18:14:16:527 ==>> 提取到IMSI:460130020290679
2025-07-31 18:14:16:541 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:14:16:545 ==>> 取到目标值:460130020290679
2025-07-31 18:14:16:549 ==>> 【校验网络运营商(移动)】通过,【460130020290679】符合目标值【】要求!
2025-07-31 18:14:16:552 ==>> 检测【打开CAN通信】
2025-07-31 18:14:16:555 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:14:16:710 ==>> $GBGGA,101420.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,41,21,,,41,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101420.505,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101420.505,0.000,1613.123,1613.123,51.620,2097152,2097152,2097152*57

[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:14:16:819 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:14:16:825 ==>> 检测【检测CAN通信】
2025-07-31 18:14:16:835 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:14:16:971 ==>> can send success


2025-07-31 18:14:17:001 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:14:17:061 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32260
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:14:17:121 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:14:17:181 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:14:17:233 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:14:17:237 ==>> 检测【关闭CAN通信】
2025-07-31 18:14:17:240 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:14:17:244 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:14:17:316 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:21][COMM]read battery soc:255


2025-07-31 18:14:17:361 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:14:17:558 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:14:17:562 ==>> 检测【打印IMU STATE】
2025-07-31 18:14:17:568 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:14:17:715 ==>> $GBGGA,101421.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,41,21,,,41,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,36,5,,,35,4,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101421.505,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101421.505,0.000,1611.240,1611.240,51.561,2097152,2097152,2097152*50



2025-07-31 18:14:17:820 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:0
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:14:18:149 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:14:18:153 ==>> 检测【六轴自检】
2025-07-31 18:14:18:159 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:14:18:360 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:14:18:808 ==>> $GBGGA,101422.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,42,21,,,41,3,,,41,1*4F

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,39,14,,,39,6,,,38,9,,,38,1*4F

$GBGSV,6,5,22,33,,,37,2,,,37,4,,,35,5,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101422.505,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101422.505,0.000,1615.007,1615.007,51.680,2097152,2097152,2097152*5F



2025-07-31 18:14:19:300 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:14:19:715 ==>> $GBGGA,101423.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,41,21,,,41,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,40,39,,,40,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101423.505,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101423.505,0.000,1609.351,1609.351,51.497,2097152,2097152,2097152*5A



2025-07-31 18:14:20:069 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:14:20:235 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35438 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-14,-3,4061]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 18:14:20:553 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:14:20:557 ==>> 检测【打印IMU STATE2】
2025-07-31 18:14:20:560 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:14:20:709 ==>> $GBGGA,101424.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,41,21,,,41,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101424.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101424.505,0.000,1611.241,1611.241,51.562,2097152,2097152,2097152*56



2025-07-31 18:14:20:799 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:14:20:888 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:14:20:893 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:14:20:897 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:14:20:967 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:14:21:072 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol

2025-07-31 18:14:21:132 ==>>  retry i = 2,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 18:14:21:372 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:14:21:377 ==>> 检测【检测VBUS电压2】
2025-07-31 18:14:21:409 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:14:21:413 ==>> [D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:14:21:772 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130020290679
[D][05:18:25][FCTY]HardwareID  = 867222087876746
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 6, adc = 240
[D][05:18:25][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 50 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3815 mv
[D][05:18:25]

2025-07-31 18:14:21:848 ==>> [FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,101425.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,60,,,41,21,,,41,3,,,41,1*4C

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,4,,,35,5,,,34,1*47

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101425.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101425.505,0.000,1611.237,1611.237,51.558,2097152,2097152,2097152*5E



2025-07-31 18:14:21:914 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:14:22:243 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][COMM]msg 0601 loss. last_tick:32245. cur_tick:37260. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 37261
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130020290679
[D][05:18:26][FCTY]HardwareID  = 867222087876746
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 164
[D][05:18:26][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:

2025-07-31 18:14:22:303 ==>> 26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3815 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:14:22:447 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:14:22:887 ==>> $GBGGA,101426.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,3,,,42,60,,,41,21,,,41,1*4F

$GBGSV,6,3,22,59,,,41,8,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,39,14,,,39,6,,,38,9,,,38,1*4F

$GBGSV,6,5,22,33,,,37,2,,,37,4,,,34,5,,,34,1*46

$GBGSV,6,6,22,7,,,32,45,,,31,1*43

$GBRMC,101426.505,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101426.505,0.000,1613.126,1613.126,51.623,2097152,2097152,2097152*52

[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130020290679
[D][05:18:26][FCTY]HardwareID  = 867222087876746
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma

2025-07-31 18:14:22:977 ==>> 
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 138
[D][05:18:26][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 50 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3815 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:14:23:270 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:14:23:328 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 18:14:23:433 ==>>                                                                                                                                                               DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][CAT1]gsm read msg sub id: 24


2025-07-31 18:14:23:538 ==>> 
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5C271FA51DBDD02DDE563FD6D088AC3B4C43D26E52A2F0BF8838BC0465380E02E230436C2D101D7ECF03F3AFA025B217CE4F177E623D135A13E89EF7FD4C1B493113F95107F2FA0A4454BF26239056A2F24DE
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 1

2025-07-31 18:14:23:583 ==>> 1

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]


2025-07-31 18:14:23:870 ==>>                                                                                                                                            [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130020290679
[D][05:18:27][FCTY]HardwareID  = 867222087876746
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:18:27][FCTY]Bat         = 3864 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:27][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 50 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK 

2025-07-31 18:14:23:915 ==>> = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3815 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:14:24:083 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:14:24:088 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:14:24:092 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:14:24:161 ==>> 5A A5 01 5A A5 


2025-07-31 18:14:24:265 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:14:24:355 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32


2025-07-31 18:14:24:378 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:14:24:383 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:14:24:390 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:14:24:430 ==>> [D][05:18:28][COMM]read battery soc:255


2025-07-31 18:14:24:460 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:14:24:676 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:14:24:684 ==>> 检测【打开WIFI(3)】
2025-07-31 18:14:24:693 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:14:24:890 ==>> [D][05:18:29][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:14:24:957 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:14:24:965 ==>> 检测【扩展芯片hw】
2025-07-31 18:14:24:983 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:14:25:165 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:14:25:244 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:14:25:249 ==>> 检测【扩展芯片boot】
2025-07-31 18:14:25:274 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:14:25:278 ==>> 检测【扩展芯片sw】
2025-07-31 18:14:25:305 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:14:25:309 ==>> 检测【检测音频FLASH】
2025-07-31 18:14:25:316 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:14:25:442 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:14:25:640 ==>> +WIFISCAN:4,0,44A1917CA62B,-74
+WIFISCAN:4,1,CC057790A740,-76
+WIFISCAN:4,2,CC057790A741,-76
+WIFISCAN:4,3,646E97BD0450,-86

[D][05:18:29][CAT1]wifi scan report total[4]
[D][05:18:29][GNSS]recv submsg id[3]


2025-07-31 18:14:25:931 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:14:26:625 ==>>                                                    pe 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m

2025-07-31 18:14:26:730 ==>> _audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing co

2025-07-31 18:14:26:835 ==>> mm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][PROT]index:1
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]


2025-07-31 18:14:26:940 ==>> 
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][M2M ]m2m_t

2025-07-31 18:14:27:045 ==>> ask: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_aud

2025-07-31 18:14:27:120 ==>> io_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255


2025-07-31 18:14:28:528 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3DA97EEB4FA867B7BEDDF0A5877780AB082B0BE56A4F

2025-07-31 18:14:28:603 ==>> 111415CC43867832456F19B224EF9E6E6174D9F9F68C232334681A3793E9792FF5B5420DD2C6E42E1A2D825A2792D89297A1088111AE791A389FFC91E
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]
[D][05:18:32][COMM]read battery soc:255


2025-07-31 18:14:29:049 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:14:29:561 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 18:14:30:116 ==>> [D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]45263 imu init OK
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:14:30:388 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:14:30:394 ==>> 检测【打开喇叭声音】
2025-07-31 18:14:30:401 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:14:30:439 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 18:14:31:121 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[e

2025-07-31 18:14:31:226 ==>> c800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4

2025-07-31 18:14:31:331 ==>> , len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:35][COMM]46274 imu init OK


2025-07-31 18:14:31:381 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:14:31:387 ==>> 检测【打开大灯控制】
2025-07-31 18:14:31:393 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:14:31:529 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:14:31:665 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:14:31:671 ==>> 检测【关闭仪表供电3】
2025-07-31 18:14:31:685 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:14:31:851 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 18:14:31:955 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:14:31:961 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:14:31:978 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:14:32:127 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:14:32:272 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:14:32:277 ==>> 检测【读大灯电压】
2025-07-31 18:14:32:299 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:14:32:449 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[33479]
[D][05:18:36][COMM]read battery soc:255


2025-07-31 18:14:32:546 ==>> 【读大灯电压】通过,【33479mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:14:32:551 ==>> 检测【关闭大灯控制2】
2025-07-31 18:14:32:559 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:14:32:724 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:14:32:818 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:14:32:823 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:14:32:828 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:14:33:048 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:14:33:112 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:14:33:117 ==>> 检测【打开WIFI(4)】
2025-07-31 18:14:33:122 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:14:33:290 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:14:33:454 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:14:33:460 ==>> 检测【EC800M模组版本】
2025-07-31 18:14:33:477 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:14:33:733 ==>> [D][05:18:37][PROT]CLEAN,SEND:1
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 18:14:33:838 ==>>                                                                                                                                                                                                                      

2025-07-31 18:14:33:928 ==>>                                                                                                                                                                                                                                                                                   ket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]
[D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:14:33:994 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:14:33:999 ==>> 检测【配置蓝牙地址】
2025-07-31 18:14:34:008 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:14:34:126 ==>> [W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:14:34:201 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D86AEE8898D0>】
2025-07-31 18:14:34:371 ==>> recv ble 1
recv ble 2
ble set mac ok :d8,6a,ee,88,98,d0
enable filters ret : 0

2025-07-31 18:14:34:461 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 18:14:34:504 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:14:34:510 ==>> 检测【BLETEST】
2025-07-31 18:14:34:515 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:14:34:566 ==>> [D][05:18:38][COMM]49757 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
4A A4 01 A4 4A 


2025-07-31 18:14:34:778 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D86AEE8898D0*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D86AEE8898D099999OVER 150


2025-07-31 18:14:35:552 ==>> [D][05:18:39][COMM]50767 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:14:35:564 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:14:35:570 ==>> 该项需要延时执行
2025-07-31 18:14:36:200 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:14:36:473 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 18:14:36:548 ==>> [D][05:18:40][COMM]51778 imu init OK


2025-07-31 18:14:37:520 ==>> [D][05:18:41][COMM]IMU: [0,-3,-947] ret=21 AWAKE!


2025-07-31 18:14:37:687 ==>> [D][05:18:41][COMM]IMU: [9,0,-992] ret=21 AWAKE!


2025-07-31 18:14:37:993 ==>> +WIFISCAN:4,0,CC057790A7C0,-75
+WIFISCAN:4,1,CC057790A741,-76
+WIFISCAN:4,2,CC057790A7C1,-76
+WIFISCAN:4,3,CC057790A5C1,-80

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 18:14:38:473 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 18:14:38:687 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 18:14:39:191 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3626A0A107769A921D9BD459A9A2E822999F2A8E442A48F42F3DDB9E392A1F9156F96600313863BD0E75002

2025-07-31 18:14:39:266 ==>> 9EC895B11F352D587C750350E25FC65252A34FDE79B37CCCA8399AB753C85D071C676E55AB84DB
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 18:14:40:482 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 18:14:42:489 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 18:14:44:415 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5749DBD33CDC331366C09F1AAA15E5A126D19251C5FC72FF288BC3050DBF827590A2F9534091775E3D3040B851329D91

2025-07-31 18:14:44:475 ==>> 3F170EEDDE497FE2BC79BC1D22750EA38866784D4AF329938679A2DC6FE79F8A79CBF
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 18:14:44:505 ==>>                                          

2025-07-31 18:14:45:579 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:14:45:585 ==>> 检测【检测WiFi结果】
2025-07-31 18:14:45:601 ==>> WiFi信号:【CC057790A741】,信号值:-73
2025-07-31 18:14:45:606 ==>> WiFi信号:【44A1917CA62B】,信号值:-74
2025-07-31 18:14:45:614 ==>> WiFi信号:【CC057790A7C1】,信号值:-76
2025-07-31 18:14:45:629 ==>> WiFi信号:【CC057790A7C0】,信号值:-77
2025-07-31 18:14:45:639 ==>> WiFi信号:【CC057790A740】,信号值:-76
2025-07-31 18:14:45:663 ==>> WiFi信号:【646E97BD0450】,信号值:-86
2025-07-31 18:14:45:672 ==>> WiFi信号:【CC057790A5C1】,信号值:-80
2025-07-31 18:14:45:695 ==>> WiFi数量【7】, 最大信号值:-73
2025-07-31 18:14:45:708 ==>> 检测【检测GPS结果】
2025-07-31 18:14:45:717 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:14:45:765 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all continue location stop
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all sing location stop


2025-07-31 18:14:46:507 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 18:14:46:597 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:14:46:606 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:46:618 ==>> 定位已等待【1】秒.
2025-07-31 18:14:46:967 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:50][COMM]Open GPS Module...
[D][05:18:50][COMM]LOC_MODEL_CONT
[D][05:18:50][GNSS]start event:8
[D][05:18:50][GNSS]GPS start. ret=0
[W][05:18:50][GNSS]start cont locating
[D][05:18:50][CAT1]gsm read msg sub id: 23
[D][05:18:50][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:50][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:14:47:601 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:47:610 ==>> 定位已等待【2】秒.
2025-07-31 18:14:47:679 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:14:48:327 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:14:48:569 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,59,,,57,24,,,44,42,,,42,26,,,41,1*7B

$GBGSV,2,2,07,39,,,40,38,,,37,13,,,37,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1803.597,1803.597,57.845,2097152,2097152,2097152*44

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:52][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 23, ret: 6
[D][05:18:52][CAT1]sub id: 23, ret: 6

[D][05:18:52][COMM]read battery soc:255


2025-07-31 18:14:48:614 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:48:623 ==>> 定位已等待【3】秒.
2025-07-31 18:14:48:751 ==>> [D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:14:49:622 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:49:633 ==>> 定位已等待【4】秒.
2025-07-31 18:14:49:682 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[D][05:18:53][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,59,,,43,24,,,43,3,,,43,42,,,42,1*4A

$GBGSV,5,2,17

2025-07-31 18:14:49:787 ==>> ,13,,,42,26,,,41,21,,,41,8,,,41,1*49

$GBGSV,5,3,17,39,,,40,38,,,39,16,,,39,1,,,39,1*4F

$GBGSV,5,4,17,14,,,38,5,,,36,2,,,35,6,,,34,1*4A

$GBGSV,5,5,17,41,,,37,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1647.955,1647.955,52.703,2097152,2097152,2097152*4C

[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B51BB952E5E8FF4BF622C51AC54EF59D14F817AC851F3A3A9737441D9E5746A29CFAC1AC48CFC3E6A174D70BE8385C8EDF3074C3BCD44DD46278C8FC9C73F0AF2B9A60BB391FFC026FDB8948C0C431F7F9CA0A
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 18:14:50:544 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,59,,,42,3,,,42,42,,,42,1*44

$GBGSV,5,2,19,13,,,42,26,,,42,21,,,41,8,,,41,1*44

$GBGSV,5,3,19,39,,,40,38,,,40,16,,,40,1,,,39,1*41

$GBGSV,5,4,19,60,,,39,14,,,38,2,,,36,6,,,36,1*79

$GBGSV,5,5,19,5,,,35,4,,,34,40,,,32,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1623.425,1623.425,51.937,2097152,2097152,2097152*46

[D][05:18:54][COMM]read battery soc:255


2025-07-31 18:14:50:634 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:50:644 ==>> 定位已等待【5】秒.
2025-07-31 18:14:51:503 ==>> $GBGGA,101455.303,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,42,,,42,13,,,42,26,,,42,1*77

$GBGSV,5,2,20,59,,,41,3,,,41,21,,,41,8,,,41,1*77

$GBGSV,5,3,20,39,,,41,38,,,41,60,,,41,16,,,40,1*73

$GBGSV,5,4,20,1,,,39,14,,,39,2,,,37,6,,,37,1*45

$GBGSV,5,5,20,33,,,37,5,,,35,4,,,34,40,,,32,1*75

$GBRMC,101455.303,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101455.303,0.000,1629.309,1629.309,52.117,2097152,2097152,2097152*55



2025-07-31 18:14:51:639 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:51:648 ==>> 定位已等待【6】秒.
2025-07-31 18:14:51:699 ==>> $GBGGA,101455.503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,42,,,42,13,,,42,26,,,42,1*77

$GBGSV,5,2,20,59,,,41,3,,,41,21,,,41,8,,,41,1*77

$GBGSV,5,3,20,39,,,41,38,,,41,60,,,41,16,,,40,1*73

$GBGSV,5,4,20,1,,,39,14,,,38,2,,,37,6,,,37,1*44

$GBGSV,5,5,20,33,,,37,5,,,35,4,,,34,40,,,32,1*75

$GBRMC,101455.503,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101455.503,0.000,1627.237,1627.237,52.052,2097152,2097152,2097152*53



2025-07-31 18:14:52:518 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 18:14:52:653 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:52:665 ==>> 定位已等待【7】秒.
2025-07-31 18:14:52:713 ==>> $GBGGA,101456.503,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,3,,,42,59,,,41,21,,,41,8,,,41,1*75

$GBGSV,6,3,22,38,,,41,60,,,41,39,,,40,16,,,40,1*73

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,2,,,37,33,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,40,,,32,7,,,31,1*46

$GBRMC,101456.503,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101456.503,0.000,1611.237,1611.237,51.558,2097152,2097152,2097152*5C



2025-07-31 18:14:53:656 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:53:668 ==>> 定位已等待【8】秒.
2025-07-31 18:14:53:716 ==>> $GBGGA,101457.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,3,,,41,59,,,41,21,,,41,8,,,41,1*76

$GBGSV,6,3,22,38,,,41,60,,,41,39,,,40,16,,,40,1*73

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,36,5,,,35,4,,,34,1*46

$GBGSV,6,6,22,40,,,32,7,,,31,1*46

$GBRMC,101457.503,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101457.503,0.000,1607.468,1607.468,51.438,2097152,2097152,2097152*5A



2025-07-31 18:14:54:523 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 18:14:54:659 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:54:668 ==>> 定位已等待【9】秒.
2025-07-31 18:14:54:905 ==>> $GBGGA,101458.503,,,,,0,00,,,M,,M,,*65

[D][05:18:58][PROT]CLEAN,SEND:0
$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,26,,,42,1*76

$GBGSV,6,2,22,38,,,42,3,,,41,59,,,41,21,,,41,1*46

$GBGSV,6,3,22,8,,,41,60,,,41,39,,,40,16,,,40,1*40

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,40,,,32,7,,,32,1*45

$GBRMC,101458.503,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101458.503,0.000,1613.116,1613.116,51.613,2097152,2097152,2097152*5E

[D][05:18:58][PROT]CLEAN:0
[D][05:18:58][PROT]index:2 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:3
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:18:58][PROT]s

2025-07-31 18:14:54:923 ==>> ending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock sen

2025-07-31 18:14:55:025 ==>> d ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[134]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:58][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE46C8A2DD68D84CA8593137FE58355808A17A4B4958319BC6CD4549D0EE31C7975254124A3C02FF118816CF176CA6B264BCF2
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 18:14:55:674 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:55:686 ==>> 定位已等待【10】秒.
2025-07-31 18:14:55:710 ==>> $GBGGA,101459.503,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,42,13,,,42,38,,,42,1*78

$GBGSV,6,2,22,26,,,41,3,,,41,21,,,41,60,,,41,1*40

$GBGSV,6,3,22,59,,,40,8,,,40,39,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,14,,,38,6,,,38,9,,,38,1*4F

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,40,,,32,7,,,32,1*45

$GBRMC,101459.503,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101459.503,0.000,1601.802,1601.802,51.245,2097152,2097152,2097152*58



2025-07-31 18:14:56:528 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 18:14:56:678 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:56:688 ==>> 定位已等待【11】秒.
2025-07-31 18:14:56:711 ==>> $GBGGA,101500.503,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,38,,,42,1*79

$GBGSV,6,2,22,26,,,42,3,,,41,21,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,40,39,,,40,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,40,,,33,7,,,32,1*44

$GBRMC,101500.503,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101500.503,0.000,1611.229,1611.229,51.551,2097152,2097152,2097152*57



2025-07-31 18:14:57:685 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:57:694 ==>> 定位已等待【12】秒.
2025-07-31 18:14:57:730 ==>> $GBGGA,101501.503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,38,,,42,1*79

$GBGSV,6,2,22,26,,,41,3,,,41,21,,,41,60,,,41,1*40

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,39,14,,,38,6,,,38,9,,,38,1*4E

$GBGSV,6,5,22,33,,,37,2,,,36,5,,,34,4,,,34,1*47

$GBGSV,6,6,22,40,,,33,7,,,31,1*47

$GBRMC,101501.503,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101501.503,0.000,802.239,802.239,733.664,2097152,2097152,2097152*60



2025-07-31 18:14:58:542 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 18:14:58:693 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:58:703 ==>> 定位已等待【13】秒.
2025-07-31 18:14:58:723 ==>> $GBGGA,101502.503,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,42,,,42,8,,,41,1*49

$GBGSV,6,2,22,38,,,41,3,,,41,60,,,41,59,,,41,1*40

$GBGSV,6,3,22,26,,,41,21,,,41,39,,,40,1,,,39,1*41

$GBGSV,6,4,22,16,,,39,6,,,38,14,,,38,2,,,37,1*7C

$GBGSV,6,5,22,9,,,37,33,,,37,5,,,34,4,,,34,1*4D

$GBGSV,6,6,22,40,,,33,7,,,32,1*44

$GBRMC,101502.503,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101502.503,0.000,801.290,801.290,732.796,2097152,2097152,2097152*6E



2025-07-31 18:14:59:699 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:14:59:709 ==>> 定位已等待【14】秒.
2025-07-31 18:14:59:733 ==>> $GBGGA,101503.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,42,,,42,1*7F

$GBGSV,6,2,22,13,,,41,8,,,41,60,,,41,59,,,41,1*42

$GBGSV,6,3,22,21,,,41,3,,,40,39,,,40,1,,,39,1*77

$GBGSV,6,4,22,16,,,39,9,,,38,6,,,38,14,,,38,1*78

$GBGSV,6,5,22,2,,,37,33,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,40,,,33,7,,,32,1*44

$GBRMC,101503.503,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101503.503,0.000,802.230,802.230,733.656,2097152,2097152,2097152*63



2025-07-31 18:15:00:111 ==>> [D][05:19:04][PROT]CLEAN,SEND:2
[D][05:19:04][PROT]index:2 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:6
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:2
[D][05:19:04][PROT]send_path:0x2
[D][05:19:04][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:04][PROT]===========================================================
[W][05:19:04][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955144]
[D][05:19:04][PROT]===========================================================
[D][05:19:04][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:04][PROT]sending traceid [9999999999900007]
[D][05:19:04][PROT]Send_TO_M2M [1629955144]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:04][SAL ]sock send credit cnt[6]
[D][05:19:04][SAL ]sock send ind credit cnt[6]
[D][05:19:04][M2M ]m2m send data len[134]
[D][05:19:04][SAL ]Cellular task submsg id[10]
[D][05:19:04][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:04][CAT1]gsm read msg sub id: 15
[D][05:19:04][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]Send Data To Server[134][137] ... ->:
0043B68511331

2025-07-31 18:15:00:186 ==>> 1331133113311331B88BE7364179E8367486495651E655CF3DC41B009076F32583F4ACD338EF00988E963589B7995698C27FC4F1DC53A46CCFB561971
[D][05:19:04][CAT1]<<< 
SEND OK

[D][05:19:04][CAT1]exec over: func id: 15, ret: 11
[D][05:19:04][CAT1]sub id: 15, ret: 11

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:04][M2M ]g_m2m_is_idle become true
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:04][PROT]M2M Send ok [1629955144]


2025-07-31 18:15:00:553 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 18:15:00:703 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:15:00:713 ==>> 定位已等待【15】秒.
2025-07-31 18:15:00:737 ==>> $GBGGA,101504.503,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,26,,,42,42,,,42,13,,,41,1*74

$GBGSV,6,2,23,8,,,41,38,,,41,3,,,41,60,,,41,1*75

$GBGSV,6,3,23,59,,,41,21,,,41,39,,,40,1,,,39,1*48

$GBGSV,6,4,23,16,,,39,9,,,38,6,,,38,14,,,38,1*79

$GBGSV,6,5,23,2,,,37,33,,,37,5,,,35,4,,,34,1*46

$GBGSV,6,6,23,40,,,33,7,,,32,12,,,36,1*43

$GBRMC,101504.503,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101504.503,0.000,803.166,803.166,734.512,2097152,2097152,2097152*60



2025-07-31 18:15:01:707 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:15:01:717 ==>> 定位已等待【16】秒.
2025-07-31 18:15:01:744 ==>> $GBGGA,101505.503,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,38,,,42,42,,,42,1*79

$GBGSV,6,2,22,8,,,41,3,,,41,60,,,41,59,,,41,1*73

$GBGSV,6,3,22,26,,,41,21,,,41,39,,,40,1,,,39,1*41

$GBGSV,6,4,22,16,,,39,9,,,38,6,,,38,14,,,38,1*78

$GBGSV,6,5,22,2,,,37,33,,,37,5,,,35,4,,,34,1*47

$GBGSV,6,6,22,40,,,33,7,,,32,1*44

$GBRMC,101505.503,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101505.503,0.000,804.109,804.109,735.374,2097152,2097152,2097152*66



2025-07-31 18:15:02:544 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 18:15:02:711 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:15:02:722 ==>> 定位已等待【17】秒.
2025-07-31 18:15:03:409 ==>> $GBGGA,101502.508,2301.2566485,N,11421.9415422,E,1,13,1.34,73.995,M,-1.770,M,,*59

$GBGSA,A,3,13,08,42,16,06,24,38,39,26,09,21,14,2.37,1.34,1.96,4*07

$GBGSA,A,3,33,,,,,,,,,,,,2.37,1.34,1.96,4*06

$GBGSV,6,1,22,13,80,249,42,8,78,188,41,42,68,7,42,16,63,313,40,1*4F

$GBGSV,6,2,22,6,63,307,38,24,63,235,43,3,62,191,41,38,62,170,42,1*7A

$GBGSV,6,3,22,39,61,340,41,26,61,29,42,9,57,279,38,59,52,130,41,1*72

$GBGSV,6,4,22,1,48,126,39,2,46,239,37,21,44,113,42,14,44,334,38,1*76

$GBGSV,6,5,22,60,41,238,41,4,32,112,34,5,22,258,35,33,20,323,37,1*72

$GBGSV,6,6,22,7,16,181,32,40,13,168,33,1*46

$GBRMC,101502.508,A,2301.2566485,N,11421.9415422,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:06][GNSS]HD8040 GPS
[D][05:19:06][GNSS]GPS diff_sec 124001756, report 0x42 frame
$GBGST,101502.508,0.622,0.190,0.190,0.265,1.886,2.039,6.927*7D

[D][05:19:06][COMM]Main Task receive event:131
[D][05:19:06][COMM]index:0,power_mode:0xFF
[D][05:19:06][COMM]index:1,sound_mode:0xFF
[D][05:19:06][COMM]index:2,gsensor_mode:0xFF
[D][05:19:06][COMM]index:3,report_freq_mode:0xFF
[D][05:19:06][COMM]index:4,report_period:0xFF
[D][05:19:06][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:06][COMM]index:6

2025-07-31 18:15:03:514 ==>> ,normal_reset_period:0xFF
[D][05:19:06][COMM]index:7,spock_over_speed:0xFF
[D][05:19:06][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:06][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:06][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:06][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:06][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:06][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:06][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:06][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:06][COMM]index:16,imu_config_params:0xFF
[D][05:19:06][COMM]index:17,long_connect_params:0xFF
[D][05:19:06][COMM]index:18,detain_mark:0xFF
[D][05:19:06][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:06][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:06][COMM]index:21,mc_mode:0xFF
[D][05:19:06][COMM]index:22,S_mode:0xFF
[D][05:19:06][COMM]index:23,overweight:0xFF
[D][05:19:06][COMM]index:24,standstill_mode:0xFF
[D][05:19:06][COMM]index:25,night_mode:0xFF
[D][05:19:07][COMM]index:26,experiment1:0xFF
[D][05:19:07][COMM]index:27,experiment2:0xFF
[D][05:19:07][COMM]index:28,experiment3:0xFF
[D][05:19:07][COMM]index:29,experiment4:0xFF
[D][05:19:07][COMM]index:30,

2025-07-31 18:15:03:619 ==>> night_mode_start:0xFF
[D][05:19:07][COMM]index:31,night_mode_end:0xFF
[D][05:19:07][COMM]index:33,park_report_minutes:0xFF
[D][05:19:07][COMM]index:34,park_report_mode:0xFF
[D][05:19:07][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:07][COMM]index:38,charge_battery_para: FF
[D][05:19:07][COMM]index:39,multirider_mode:0xFF
[D][05:19:07][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:07][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:07][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:07][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:07][COMM]index:44,riding_duration_config:0xFF
[D][05:19:07][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:07][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:07][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:07][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:07][COMM]index:49,mc_load_startup:0xFF
[D][05:19:07][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:07][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:07][COMM]index:52,traffic_mode:0xFF
[D][05:19:07][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:07][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:07][COMM]index:55,wheel_alarm_play_switch

2025-07-31 18:15:03:724 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 18:15:03:731 ==>> 
北斗星号:【13】,信号值:【42】
北斗星号:【8】,信号值:【41】
北斗星号:【42】,信号值:【42】
北斗星号:【16】,信号值:【40】
北斗星号:【6】,信号值:【38】
北斗星号:【24】,信号值:【43】
北斗星号:【3】,信号值:【41】
北斗星号:【38】,信号值:【42】
北斗星号:【39】,信号值:【41】
北斗星号:【26】,信号值:【42】
北斗星号:【9】,信号值:【38】
北斗星号:【59】,信号值:【41】
北斗星号:【1】,信号值:【39】
北斗星号:【2】,信号值:【37】
北斗星号:【21】,信号值:【42】
北斗星号:【14】,信号值:【38】
北斗星号:【60】,信号值:【41】
北斗星号:【5】,信号值:【35】
北斗星号:【33】,信号值:【37】

2025-07-31 18:15:03:738 ==>> :255
[D][05:19:07][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:07][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:07][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:07][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:07][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:07][COMM]index:63,experiment5:0xFF
[D][05:19:07][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:07][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:07][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:07][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:07][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:07][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:07][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:07][COMM]index:72,experiment6:0xFF
[D][05:19:07][COMM]index:73,experiment7:0xFF
[D][05:19:07][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:07][COMM]index:75,zero_value_from_server:-1
[D][05:19:07][COMM]index:76,multirider_threshold:255
[D][05:19:07][COMM]index:77,experiment8:255
[D][05:19:07][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:07][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:07][COMM]index:

2025-07-31 18:15:03:759 ==>> 检测【CSQ强度】
2025-07-31 18:15:03:771 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:15:03:829 ==>> 80,temp_park_reminder_timeout_duration:255
[D][05:19:07][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:07][COMM]index:83,loc_report_interval:255
[D][05:19:07][COMM]index:84,multirider_threshold_p2:255
[D][05:19:07][COMM]index:85,multirider_strategy:255
[D][05:19:07][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:07][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:07][COMM]index:90,weight_param:0xFF
[D][05:19:07][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:07][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:07][COMM]index:95,current_limit:0xFF
[D][05:19:07][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:07][COMM]index:100,location_mode:0xFF

[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:07][COMM]Main Task receive event:131 finished processing
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,101503.008,2301.2571161,N,11421.9415644,E,1,13,1.34,74.035,M,-1.770,M,,*52

$GBGSA,A,3,

2025-07-31 18:15:03:919 ==>> 13,08,42,16,06,24,38,39,26,09,21,14,2.37,1.34,1.95,4*04

$GBGSA,A,3,33,,,,,,,,,,,,2.37,1.34,1.95,4*05

$GBGSV,6,1,22,13,80,249,42,8,78,188,41,42,68,7,42,16,63,313,40,1*4F

$GBGSV,6,2,22,6,63,307,38,24,63,235,43,3,62,191,41,38,62,170,42,1*7A

$GBGSV,6,3,22,39,61,340,41,26,61,29,42,9,57,279,38,59,52,130,41,1*72

$GBGSV,6,4,22,1,48,126,39,2,46,239,37,21,44,113,42,14,44,334,38,1*76

$GBGSV,6,5,22,60,41,238,42,4,32,112,34,5,22,258,35,33,20,323,37,1*71

$GBGSV,6,6,22,7,16,181,32,40,13,168,33,1*46

$GBGSV,2,1,05,42,68,7,36,24,63,235,37,38,62,170,39,39,61,340,40,5*70

$GBGSV,2,2,05,21,44,113,39,5*4D

$GBRMC,101503.008,A,2301.2571161,N,11421.9415644,E,0.000,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,101503.008,1.003,1.002,0.999,1.334,1.505,1.650,5.386*7D



2025-07-31 18:15:04:024 ==>> [W][05:19:08][COMM]>>>>>Input command 

2025-07-31 18:15:04:069 ==>> = AT+CSQ<<<<<
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:08][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:15:04:341 ==>> $GBGGA,101504.000,2301.2573450,N,11421.9416189,E,1,13,1.34,73.996,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,16,06,24,38,39,26,09,21,14,2.37,1.34,1.95,4*04

$GBGSA,A,3,33,,,,,,,,,,,,2.37,1.34,1.95,4*05

$GBGSV,6,1,22,13,80,249,42,8,78,188,41,42,68,7,42,16,63,313,39,1*41

$GBGSV,6,2,22,6,63,307,38,24,63,235,43,3,62,191,41,38,62,170,42,1*7A

$GBGSV,6,3,22,39,61,340,40,26,61,29,41,9,57,279,37,59,52,130,41,1*7F

$GBGSV,6,4,22,1,48,126,39,2,46,239,37,21,44,113,41,14,44,334,38,1*75

$GBGSV,6,5,22,60,41,238,41,4,32,112,34,5,22,258,35,33,20,323,37,1*72

$GBGSV,6,6,22,7,16,181,32,40,13,168,33,1*46

$GBGSV,2,1,07,42,68,7,39,24,63,235,40,38,62,170,40,39,61,340,40,5*73

$GBGSV,2,2,07,26,61,29,40,21,44,113,40,33,20,323,32,5*7C

$GBRMC,101504.000,A,2301.2573450,N,11421.9416189,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,101504.000,1.805,0.550,0.548,0.740,1.766,1.897,4.995*72



2025-07-31 18:15:04:361 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 18:15:04:367 ==>> 检测【关闭GSM联网】
2025-07-31 18:15:04:377 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:15:04:570 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:08][COMM]GSM test
[D][05:19:08][COMM]GSM test disable
[D][05:19:08][COMM]read battery soc:255


2025-07-31 18:15:04:635 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:15:04:644 ==>> 检测【4G联网测试】
2025-07-31 18:15:04:651 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:15:04:830 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 18:15:05:771 ==>> [D][05:19:09][COMM]Main Task receive event:14
[D][05:19:09][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955149, allstateRepSeconds = 0
[D][05:19:09][COMM]index:0,power_mode:0xFF
[D][05:19:09][COMM]index:1,sound_mode:0xFF
[D][05:19:09][COMM]index:2,gsensor_mode:0xFF
[D][05:19:09][COMM]index:3,report_freq_mode:0xFF
[D][05:19:09][COMM]index:4,report_period:0xFF
[D][05:19:09][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:09][COMM]index:6,normal_reset_period:0xFF
[D][05:19:09][COMM]index:7,spock_over_speed:0xFF
[D][05:19:09][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:09][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:09][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:09][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:09][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:09][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:09][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:09][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:09][COMM]index:16,imu_config_params:0xFF
[D][05:19:09][COMM]index:17,long_connect_params:0xFF
[D][05:19:09][COMM]index:18,detain_mark:0xFF
[D][05:19:09][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:09][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:09][COMM]index:2

2025-07-31 18:15:05:876 ==>> 1,mc_mode:0xFF
[D][05:19:09][COMM]index:22,S_mode:0xFF
[D][05:19:09][COMM]index:23,overweight:0xFF
[D][05:19:09][COMM]index:24,standstill_mode:0xFF
[D][05:19:09][COMM]index:25,night_mode:0xFF
[D][05:19:09][COMM]index:26,experiment1:0xFF
[D][05:19:09][COMM]index:27,experiment2:0xFF
[D][05:19:09][COMM]index:28,experiment3:0xFF
[D][05:19:09][COMM]index:29,experiment4:0xFF
[D][05:19:09][COMM]index:30,night_mode_start:0xFF
[D][05:19:09][COMM]index:31,night_mode_end:0xFF
[D][05:19:09][COMM]index:33,park_report_minutes:0xFF
[D][05:19:09][COMM]index:34,park_report_mode:0xFF
[D][05:19:09][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:09][COMM]index:38,charge_battery_para: FF
[D][05:19:09][COMM]index:39,multirider_mode:0xFF
[D][05:19:09][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:09][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:09][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:09][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:09][COMM]index:44,riding_duration_config:0xFF
[D][05:19:09][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:09][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:09][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:09][

2025-07-31 18:15:05:981 ==>> COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:09][COMM]index:49,mc_load_startup:0xFF
[D][05:19:09][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:09][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:09][COMM]index:52,traffic_mode:0xFF
[D][05:19:09][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:09][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:09][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:09][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:09][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:09][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:09][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:09][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:09][COMM]index:63,experiment5:0xFF
[D][05:19:09][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:09][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:09][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:09][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:09][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:09][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:09][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:09][COMM]index:72,experiment6:0xFF
[D][05:

2025-07-31 18:15:06:086 ==>> 19:09][COMM]index:73,experiment7:0xFF
[D][05:19:09][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:09][COMM]index:75,zero_value_from_server:-1
[D][05:19:09][COMM]index:76,multirider_threshold:255
[D][05:19:09][COMM]index:77,experiment8:255
[D][05:19:09][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:09][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:09][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:09][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:09][COMM]index:83,loc_report_interval:255
[D][05:19:09][COMM]index:84,multirider_threshold_p2:255
[D][05:19:09][COMM]index:85,multirider_strategy:255
[D][05:19:09][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:09][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:09][COMM]index:90,weight_param:0xFF
[D][05:19:09][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:09][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:09][COMM]index:95,current_limit:0xFF
[D][05:19:09][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:09][COMM]index:100,location_mode:0xFF

[W][05:19:09][PROT]remove success[1629955149],send_path[2],type[0000],

2025-07-31 18:15:06:191 ==>> priority[0],index[0],used[0]
[D][05:19:09][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:19:09][PROT]add success [1629955149],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:09][PROT]index:0 1629955149
[D][05:19:09][PROT]is_send:0
[D][05:19:09][PROT]sequence_num:8
[D][05:19:09][PROT]retry_timeout:0
[D][05:19:09][PROT]retry_times:1
[D][05:19:09][PROT]send_path:0x2
[D][05:19:09][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:09][PROT]===========================================================
[W][05:19:09][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955149]
[D][05:19:09][PROT]===========================================================
[D][05:19:09][PROT]sending traceid [9999999999900009]
[D][05:19:09][PROT]Send_TO_M2M [1629955149]
[D][05:19:09][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:09][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:09][SAL ]sock send credit cnt[6]
[D][05:19:09][SAL ]sock send ind credit cnt[6]
[D][05:19:09][M2M ]m2m send data len[294]
[D][05:19:09][CAT1]gsm read msg sub id: 13
[D][05:19:09][SAL ]Cellular task submsg id[10]
[D][05:19:0

2025-07-31 18:15:06:296 ==>> 9][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:09][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:09][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:09][CAT1]exec over: func id: 13, ret: 21
[D][05:19:09][M2M ]get csq[20]
[D][05:19:09][CAT1]gsm read msg sub id: 15
[D][05:19:09][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:09][CAT1]Send Data To Server[294][297] ... ->:
0093B986113311331133113311331B88B13161C84D52EA6425E14974CDA91625EDD20698D3324054349C1071C521C7FB0AC1B729100F22DB592931BA93BF9CF22B9137CF9320A3AF399BE0F5CD919BCF010E5EB11449CD82B32A4ADD163F02988CD6C6487FF42BA6EF2C5E2C3F3DC6E2F7C52894405647EBC0CA12EDC973BF363B13DA309DABAA90F7EA71A1E78A1635FE5AC0
[D][05:19:09][CAT1]<<< 
SEND OK

[D][05:19:09][CAT1]exec over: func id: 15, ret: 11
[D][05:19:09][CAT1]sub id: 15, ret: 11

[D][05:19:09][SAL ]Cellular task submsg id[68]
[D][05:19:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:09][M2M ]g_m2m_is_idle become true
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:09][PROT]M2M Send ok [1629955149]
$GB

2025-07-31 18:15:06:401 ==>> GGA,101505.000,2301.2573442,N,11421.9415487,E,1,13,1.34,73.576,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,24,38,39,26,09,14,21,2.37,1.34,1.95,4*04

$GBGSA,A,3,33,,,,,,,,,,,,2.37,1.34,1.95,4*05

$GBGSV,6,1,22,13,80,249,42,8,78,188,41,42,68,7,42,16,63,313,40,1*4F

$GBGSV,6,2,22,6,63,307,38,24,63,235,43,3,62,191,41,38,62,170,42,1*7A

$GBGSV,6,3,22,39,61,340,40,26,61,29,42,9,57,279,38,59,52,130,41,1*73

$GBGSV,6,4,22,1,48,126,39,2,46,239,37,14,45,334,38,21,44,113,41,1*74

$GBGSV,6,5,22,60,41,238,41,4,32,112,34,5,22,258,34,33,20,323,37,1*73

$GBGSV,6,6,22,7,16,181,32,40,13,168,33,1*46

$GBGSV,2,1,07,42,68,7,41,24,63,235,41,38,62,170,40,39,61,340,41,5*7C

$GBGSV,2,2,07,26,61,29,41,21,44,113,41,33,20,323,33,5*7D

$GBRMC,101505.000,A,2301.2573442,N,11421.9415487,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,101505.000,1.513,0.2 

2025-07-31 18:15:06:506 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:15:06:566 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 18:15:06:678 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:15:06:685 ==>> 检测【关闭GPS】
2025-07-31 18:15:06:692 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:15:07:042 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:11][GNSS]stop locating
[D][05:19:11][GNSS]stop event:8
[D][05:19:11][GNSS]GPS stop. ret=0
[D][05:19:11][GNSS]all continue location stop
[W][05:19:11][GNSS]stop locating
[D][05:19:11][GNSS]all sing location stop
[D][05:19:11][CAT1]gsm read msg sub id: 24
[D][05:19:11][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 24, ret: 6
[D][05:19:11][CAT1]sub id: 24, ret: 6



2025-07-31 18:15:07:215 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:15:07:222 ==>> 检测【清空消息队列2】
2025-07-31 18:15:07:232 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:15:07:350 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:11][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:15:07:490 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:15:07:497 ==>> 检测【轮动检测】
2025-07-31 18:15:07:504 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:15:07:562 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 18:15:07:652 ==>> [D][05:19:11][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:15:07:849 ==>> [D][05:19:12][GNSS]recv submsg id[1]
[D][05:19:12][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:12][GNSS]location stop evt done evt


2025-07-31 18:15:08:001 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:15:08:061 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:15:08:279 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:15:08:287 ==>> 检测【关闭小电池】
2025-07-31 18:15:08:295 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:15:08:367 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:15:08:560 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:15:08:567 ==>> 检测【进入休眠模式】
2025-07-31 18:15:08:584 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:15:08:591 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 18:15:08:810 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:12][COMM]Main Task receive event:28
[D][05:19:12][COMM]main task tmp_sleep_event = 8
[D][05:19:12][COMM]prepare to sleep
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:15:08:915 ==>> [D][05:19:13][COMM]IMU: [-7,-20,-935] ret=41 AWAKE!


2025-07-31 18:15:09:206 ==>> [D][05:19:13][COMM]IMU: [5,1,-991] ret=28 AWAKE!


2025-07-31 18:15:09:432 ==>> [D][05:19:13][CAT1]<<< 
OK

[D][05:19:13][CAT1]exec over: func id: 12, ret: 6
[D][05:19:13][M2M ]tcpclient close[4]
[D][05:19:13][SAL ]Cellular task submsg id[12]
[D][05:19:13][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:13][CAT1]gsm read msg sub id: 9
[D][05:19:13][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:13][CAT1]<<< 
OK

[D][05:19:13][CAT1]exec over: func id: 9, ret: 6
[D][05:19:13][CAT1]sub id: 9, ret: 6

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:13][SAL ]socket close ind. id[4]
[D][05:19:13][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:13][COMM]1x1 frm_can_tp_send ok
[D][05:19:13][CAT1]pdpdeact urc len[22]


2025-07-31 18:15:09:710 ==>> [E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:10:226 ==>> [E][05:19:14][COMM]1x1 rx timeout
[E][05:19:14][COMM]1x1 tp timeout
[E][05:19:14][COMM]1x1 error -3.
[W][05:19:14][COMM]CAN STOP!
[D][05:19:14][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:14][COMM]------------ready to Power off Acckey 1------------
[D][05:19:14][COMM]------------ready to Power off Acckey 2------------
[D][05:19:14][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:14][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1292
[D][05:19:14][COMM]bat sleep fail, reason:-1
[D][05:19:14][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:14][COMM]accel parse set 0
[D][05:19:14][COMM]imu rest ok. 85347
[D][05:19:14][COMM]imu sleep 0
[W][05:19:14][COMM]now sleep


2025-07-31 18:15:10:422 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:15:10:429 ==>> 检测【检测33V休眠电流】
2025-07-31 18:15:10:440 ==>> 开始33V电流采样
2025-07-31 18:15:10:462 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:10:528 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:15:11:536 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:15:11:597 ==>> Current33V:????:18.84

2025-07-31 18:15:12:047 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:12:056 ==>> 【检测33V休眠电流】通过,【18.84uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:15:12:079 ==>> 该项需要延时执行
2025-07-31 18:15:14:070 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:15:14:083 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:15:14:106 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:15:14:178 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 2038mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:15:15:123 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:15:15:137 ==>> 检测【打开小电池2】
2025-07-31 18:15:15:151 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:15:15:267 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:15:15:438 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:15:15:445 ==>> 该项需要延时执行
2025-07-31 18:15:15:952 ==>> 此处延时了:【500】毫秒
2025-07-31 18:15:15:963 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:15:15:988 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:15:16:061 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:15:16:249 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:15:16:261 ==>> 该项需要延时执行
2025-07-31 18:15:16:752 ==>> 此处延时了:【500】毫秒
2025-07-31 18:15:16:771 ==>> 检测【进入休眠模式2】
2025-07-31 18:15:16:790 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:15:16:802 ==>> [D][05:19:20][COMM]------------ready to Power on Acckey 1------------
[D][05:19:20][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:20][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:20][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:20][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:20][COMM]----- get Acckey 1 and value:1------------
[D][05:19:20][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:19:20][COMM]CAN START!
[D][05:19:20][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 91785
[D][05:19:20][CAT1]gsm read msg sub id: 12
[D][05:19:20][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:20][COMM][Audio]exec status ready.
[D][05:19:20][CAT1]<<< 
OK


2025-07-31 18:15:16:831 ==>> 
[D][05:19:20][CAT1]exec over: func id: 12, ret: 6
[D][05:19:20][COMM]imu wakeup ok. 91811
[D][05:19:20][COMM]imu wakeup 1
[W][05:19:20][COMM]wake up system, wakeupEvt=0x80
[D][05:19:20][COMM]frm_can_weigth_power_set 1
[D][05:19:20][COMM]Clear Sleep Block Evt
[D][05:19:20][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:20][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:16:888 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:15:16:993 ==>> [E][05:19:21][COMM]1x1 rx timeout
[D][05:19:21][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:17:099 ==>> [D][05:19:21][COMM]msg 02A0 loss. last_tick:91771. cur_tick:92279. period:50
[D][05:19:21][COMM]msg 02A4 loss. last_tick:91771. cur_tick:92279. period:50
[D][05:19:21][COMM]msg 02A5 loss. last_tick:91771. cur_ti

2025-07-31 18:15:17:144 ==>> ck:92280. period:50
[D][05:19:21][COMM]msg 02A6 loss. last_tick:91771. cur_tick:92280. period:50
[D][05:19:21][COMM]msg 02A7 loss. last_tick:91771. cur_tick:92280. period:50
[D][05:19:21][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 92281
[D][05:19:21][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 92281


2025-07-31 18:15:17:480 ==>> [E][05:19:21][COMM]1x1 rx timeout
[E][05:19:21][COMM]1x1 tp timeout
[E][05:19:21][COMM]1x1 error -3.
[D][05:19:21][COMM]Main Task receive event:28 finished processing
[D][05:19:21][COMM]Main Task receive event:28
[D][05:19:21][COMM]prepare to sleep
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 6
[D][05:19:21][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:21][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:17:784 ==>> [D][05:19:21][COMM]msg 0220 loss. last_tick:91771. cur_tick:92774. period:100
[D][05:19:21][COMM]msg 0221 loss. last_tick:91771. cur_tick:92775. period:100
[D][05:19:21][COMM]msg 0224 loss. last_tick:91771. cur_tick:92775. period:100
[D][05:19:21][COMM]msg 0260 loss. last_tick:91771. cur_tick:92775. period:100
[D][05:19:21][COMM]msg 0280 loss. last_tick:91771. cur_tick:92776. period:100
[D][05:19:21][COMM]msg 02C0 loss. last_tick:91771. cur_tick:92776. period:100
[D][05:19:21][COMM]msg 02C1 loss. last_tick:91771. cur_tick:92777. period:100
[D][05:19:21][COMM]msg 02C2 loss. last_tick:91771. cur_tick:92777. period:100
[D][05:19:21][COMM]msg 02E0 loss. last_tick:91771. cur_tick:92777. period:100
[D][05:19:21][COMM]msg 02E1 loss. last_tick:91771. cur_tick:92777. period:100
[D][05:19:21][COMM]msg 02E2 loss. last_tick:91771. cur_tick:92778. period:100
[D][05:19:21][COMM]msg 0300 loss. last_tick:91771. cur_tick:92778. period:100
[D][05:19:21][COMM]msg 0301 loss. last_tick:91771. cur_tick:92779. period:100
[D][05:19:21][COMM]bat msg 0240 loss. last_tick:91771. cur_tick:92779. period:100. j,i:1 54
[D][05:19:21][COMM]bat msg 0241 loss. last_tick:91771. cur_tick:92780. period:1

2025-07-31 18:15:17:859 ==>> 00. j,i:2 55
[D][05:19:21][COMM]bat msg 0242 loss. last_tick:91771. cur_tick:92780. period:100. j,i:3 56
[D][05:19:21][COMM]bat msg 0244 loss. last_tick:91771. cur_tick:92780. period:100. j,i:5 58
[D][05:19:21][COMM]bat msg 024E loss. last_tick:91771. cur_tick:92781. period:100. j,i:15 68
[D][05:19:21][COMM]bat msg 024F loss. last_tick:91771. cur_tick:92781. period:100. j,i:16 69
[D][05:19:21][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 92781
[D][05:19:21][COMM]CAN message bat fault change: 0x00000000->0x0001802E 92782
[D][05:19:21][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 92782


2025-07-31 18:15:17:889 ==>>                                                                               

2025-07-31 18:15:18:070 ==>> [D][05:19:22][COMM]msg 0222 loss. last_tick:91771. cur_tick:93277. period:150
[D][05:19:22][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 93277


2025-07-31 18:15:18:160 ==>>                                                                                                                                                   ][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:15:18:265 ==>> [E][05:19:22][COMM]1x1 rx timeout
[E][05:19:22][CO

2025-07-31 18:15:18:339 ==>> MM]1x1 tp timeout
[E][05:19:22][COMM]1x1 error -3.
[W][05:19:22][COMM]CAN STOP!
[D][05:19:22][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:22][COMM]------------ready to Power off Acckey 1------------
[D][05:19:22][COMM]------------ready to Power off Acckey 2------------
[D][05:19:22][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:22][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 101
[D][05:19:22][COMM]bat sleep fail, reason:-1
[D][05:19:22][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:22][COMM]accel parse set 0
[D][05:19:22][COMM]imu rest ok. 93470
[D][05:19:22][COMM]imu sleep 0
[W][05:19:22][COMM]now sleep


2025-07-31 18:15:18:573 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:15:18:586 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:15:18:598 ==>> 开始小电池电流采样
2025-07-31 18:15:18:620 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:18:675 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:15:19:681 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:15:19:742 ==>> CurrentBattery:ƽ��:67.38

2025-07-31 18:15:20:193 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:20:200 ==>> 【检测小电池休眠电流】通过,【67.38uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:15:20:208 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:15:20:227 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:15:20:271 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:15:20:474 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:15:20:481 ==>> 该项需要延时执行
2025-07-31 18:15:20:494 ==>> [D][05:19:24][COMM]------------ready to Power on Acckey 1------------
[D][05:19:24][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:24][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:24][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:24][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:24][COMM]----- get Acckey 1 and value:1------------
[W][05:19:24][COMM]CAN START!
[D][05:19:24][COMM]read battery soc:0
[D][05:19:24][CAT1]gsm read msg sub id: 12
[D][05:19:24][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:24][COMM]CAN message bat fault change: 0x0001802E->0x00000000 95572
[D][05:19:24][COMM][Audio]exec status ready.
[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]exec over: func id: 12, ret: 6
[D][05:19:24][COMM]imu wakeup ok. 95586
[D][05:19:24][COMM]imu wakeup 1
[W][05:19:24][COMM]wake up system, wakeupEvt=0x80
[D][05:19:24][COMM]frm_can_weigth_power_set 1
[D][05:19:24][COMM]Clear Sleep Block Evt
[D][05:19:24][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:24][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:20:774 ==>> [E][05:19:24][COMM]1x1 rx timeout
[D][05:19:24][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:15:20:970 ==>> [D][05:19:25][COMM]msg 02A0 loss. last_tick:95554. cur_tick:96066. period:50
[D][05:19:25][COMM]msg 02A4 loss. last_tick:95554. cur_tick:96067. period:50
[D][05:19:25][COMM]msg 02A5 loss. last_tick:95554. cur_tick:96067. period:50
[D][05:19:25][COMM]msg 02A6 loss. last_tick:95554. cur_tick:96067. period:50
[D][05:19:25][COMM]msg 02A7 loss. last_tick:95554. cur_tick:96068. period:50
[D][05:19:25][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 96068
[D][05:19:25][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 96069
[D][05:19:25][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 18:15:20:985 ==>> 此处延时了:【500】毫秒
2025-07-31 18:15:20:996 ==>> 检测【检测唤醒】
2025-07-31 18:15:21:024 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:15:21:708 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:25][FCTY]==========Modules-nRF5340 ==========
[D][05:19:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:25][FCTY]DeviceID    = 460130020290679
[D][05:19:25][FCTY]HardwareID  = 867222087876746
[D][05:19:25][FCTY]MoBikeID    = 9999999999
[D][05:19:25][FCTY]LockID      = FFFFFFFFFF
[D][05:19:25][FCTY]BLEFWVersion= 105
[D][05:19:25][FCTY]BLEMacAddr   = D86AEE8898D0
[D][05:19:25][FCTY]Bat         = 3884 mv
[D][05:19:25][FCTY]Current     = 0 ma
[D][05:19:25][FCTY]VBUS        = 2600 mv
[D][05:19:25][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:25][FCTY]Ext battery vol = 32, adc = 1291
[D][05:19:25][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 126 mv
[D][05:19:25][FCTY]Bike Type flag is invalied
[D][05:19:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:25][FCTY]CAT1_GNSS_VERSIO

2025-07-31 18:15:21:783 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:15:21:794 ==>> 检测【关机】
2025-07-31 18:15:21:803 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:15:21:817 ==>> N = V3465b5b1
[D][05:19:25][FCTY]Bat1         = 3815 mv
[D][05:19:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:25][FCTY]==========Modules-nRF5340 ==========
[E][05:19:25][COMM]1x1 rx timeout
[E][05:19:25][COMM]1x1 tp timeout
[E][05:19:25][COMM]1x1 error -3.
[D][05:19:25][COMM]Main Task receive event:28 finished processing
[D][05:19:25][COMM]Main Task receive event:65
[D][05:19:25][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:25][COMM]Main Task receive event:65 finished processing
[D][05:19:25][COMM]Main Task receive event:60
[D][05:19:25][COMM]smart_helmet_vol=255,255
[D][05:19:25][COMM]report elecbike
[D][05:19:25][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:19:25][PROT]remove success[1629955165],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:25][PROT]add success [1629955165],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:25][COMM]Main Task receive event:60 finished processing
[D][05:19:25][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:25][PROT]index:0
[D][05:19:25][PROT]is_send:1
[D][05:19:25][PROT]sequence_num:10
[D][05:19:25][PROT]retry_timeout

2025-07-31 18:15:21:918 ==>> :0
[D][05:19:25][PROT]retry_times:3
[D][05:19:25][PROT]send_path:0x3
[D][05:19:25][PROT]msg_type:0x5d03
[D][05:19:25][PROT]===========================================================
[W][05:19:25][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955165]
[D][05:19:25][PROT]===========================================================
[D][05:19:25][PROT]Sending traceid[999999999990000B]
[D][05:19:25][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:25][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:25][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:25][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:25][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:25][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:25][SAL ]open socket ind id[4], rst[0]
[D][05:19:25][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:25][SAL ]Cellular task submsg id[8]
[D][05:19:25][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:25][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:25

2025-07-31 18:15:22:023 ==>> ][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:25][CAT1]gsm read msg sub id: 8
[D][05:19:25][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:25][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:25][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:25][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:25][CAT1]<<< 
+CME ERROR: 100

[D][05:19:25][COMM]msg 0220 loss. last_tick:95554. cur_tick:96569. period:100
[D][05:19:25][COMM]msg 0221 loss. last_tick:95554. cur_tick:96570. period:100
[D][05:19:25][COMM]msg 0224 loss. last_tick:95554. cur_tick:96570. period:100
[D][05:19:25][COMM]msg 0260 loss. last_tick:95554. cur_tick:96571. period:100
[D][05:19:25][COMM]msg 0280 loss. last_tick:95554. cur_tick:96571. period:100
[D][05:19:25][COMM]msg 02C0 loss. last_tick:95554. cur_tick:96571. period:100
[D][05:19:25][COMM]msg 02C1 loss. last_tick:95554. cur_tick:96572. period:100
[D][05:19:25][COMM]msg 02C2 loss. last_tick:95554. cur_tick:96572. period:100
[D][05:19:25][COMM]msg 02E0 loss. last_tick:95554. cur_tick:96572. period:100
[D][05:19:25][COMM]msg 02E1 loss. last_tick:95554. cur_tick:96573. period:100
[D][05:19:25][COMM]msg 02E2 loss. last_tick:95554. cur_tick:96573. period:100
[D][05:

2025-07-31 18:15:22:128 ==>> 19:25][COMM]msg 0300 loss. last_tick:95554. cur_tick:96573. period:100
[D][05:19:25][COMM]msg 0301 loss. last_tick:95554. cur_tick:96574. period:100
[D][05:19:25][COMM]bat msg 0240 loss. last_tick:95554. cur_tick:96574. period:100. j,i:1 54
[D][05:19:25][COMM]bat msg 0241 loss. last_tick:95554. cur_tick:96574. period:100. j,i:2 55
[D][05:19:25][COMM]bat msg 0242 loss. last_tick:95554. cur_tick:96575. period:100. j,i:3 56
[D][05:19:25][COMM]bat msg 0244 loss. last_tick:95554. cur_tick:96575. period:100. j,i:5 58
[D][05:19:25][COMM]bat msg 024E loss. last_tick:95554. cur_tick:96576. period:100. j,i:15 68
[D][05:19:25][COMM]bat msg 024F loss. last_tick:95554. cur_tick:96576. period:100. j,i:16 69
[D][05:19:25][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 96577
[D][05:19:25][COMM]CAN message bat fault change: 0x00000000->0x0001802E 96577
[D][05:19:25][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 96577


2025-07-31 18:15:22:727 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 18:15:22:817 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:15:22:833 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 18:15:22:936 ==>>                                                                                                                                                                                                                                                                                                                                                        e_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:26][COMM]Main Task receive event:66
[D][05:19:26][COMM]Try to Auto Lock Bat
[D][05:19:26][COMM]Main Task receive event:66 finished processing
[D][05:19:26][COMM]Main Task receive event:60
[D][05:19:26][COMM]smart_helmet_vol=255,255
[D][05:19:26][COMM]Receive Bat Lock cmd 0
[D][05:19:26][COMM]VBUS is 1
[D][05:19:26][COMM]BAT CAN get state1 Fail 204
[D][05:19:26][COMM]BAT CAN get soc Fail, 204
[D][05:19:26][COMM]BAT CAN get state2 fail 204
[D][05:19:26][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:26][COMM]get soh error
[E][05:19:26][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:26][COMM]

2025-07-31 18:15:23:041 ==>> report elecbike
[W][05:19:26][PROT]remove success[1629955166],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:26][PROT]add success [1629955166],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:26][COMM]Main Task receive event:60 finished processing
[D][05:19:26][COMM]Main Task receive event:61
[D][05:19:26][COMM][D301]:type:3, trace id:280
[D][05:19:26][COMM]id[], hw[000
[D][05:19:26][COMM]get mcMaincircuitVolt error
[D][05:19:26][COMM]get mcSubcircuitVolt error
[D][05:19:26][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:26][COMM]BAT CAN get state1 Fail 204
[D][05:19:26][COMM]BAT CAN get soc Fail, 204
[D][05:19:26][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:26][COMM]BAT CAN get state2 fail 204
[D][05:19:26][COMM]get bat work mode err
[W][05:19:26][PROT]remove success[1629955166],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:26][PROT]add success [1629955166],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]


2025-07-31 18:15:23:147 ==>> 
[D][05:19:26][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:26][PROT]index:1
[D][05:19:26][PROT]is_send:1
[D][05:19:26][PROT]sequence_num:11
[D][05:19:26][PROT]retry_timeout:0
[D][05:19:26][PROT]retry_times:3
[D][05:19:26][PROT]send_path:0x3
[D][05:19:26][PROT]msg_type:0x5d03
[D][05:19:26][PROT]===========================================================
[W][05:19:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955166]
[D][05:19:26][PROT]===========================================================
[D][05:19:26][PROT]Sending traceid[999999999990000C]
[D][05:19:26][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:26][COMM]Main Task receive event:61 finished processing
[D][05:19:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:26][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:26][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D]

2025-07-31 18:15:23:252 ==>> [05:19:26][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:26][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:26][COMM]arm_hub_enable: hub power: 0
[D][05:19:26][HSDK]hexlog index save 0 5888 109 @ 0 : 0
[D][05:19:26][HSDK]write save hexlog index [0]
[D][05:19:26][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:26][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:26][COMM]read battery soc:255
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:26][CO

2025-07-31 18:15:23:327 ==>> MM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:26][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:26][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:26][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:26][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 18:15:23:432 ==>> [W][05:19:27][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:27][COMM]arm_hub_enable: hub power: 0
[D][05:19:27][HSDK]hexlog index save 0 5888 109 @ 0 : 0
[D][05:19:27][HSDK]write save hexlog index [0]
[D][05:19:27][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:27][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to wri

2025-07-31 18:15:23:462 ==>> te para flash


2025-07-31 18:15:23:783 ==>> [D][05:19:28][COMM]exit wheel stolen mode.


2025-07-31 18:15:23:843 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:15:23:888 ==>> [D][05:19:28][COMM]Main Task receive event:68
[D][05:19:28][COMM]handlerWheelStolen evt type = 2.
[E][05:19:28][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]all continue location stop
[D][05:19:28][COMM]Main Task receive event:68 finished processing
[W][05:19:2

2025-07-31 18:15:23:918 ==>> 8][COMM]Power Off


2025-07-31 18:15:24:099 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:28][COMM]arm_hub_enable: hub power: 0
[D][05:19:28][HSDK]hexlog index save 0 5888 109 @ 0 : 0
[D][05:19:28][HSDK]write save hexlog index [0]
[D][05:19:28][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:28][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:15:24:156 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:15:24:164 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:15:24:172 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:15:24:266 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:15:24:341 ==>> [D][05:19:28][FCTY]get_ext_48v_vol retry i = 0,vo

2025-07-31 18:15:24:463 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:15:24:472 ==>> 检测【检测小电池关机电流】
2025-07-31 18:15:24:484 ==>> 开始小电池电流采样
2025-07-31 18:15:24:512 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:24:570 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:15:25:574 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:15:25:665 ==>> CurrentBattery:ƽ��:69.57

2025-07-31 18:15:26:083 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:15:26:091 ==>> 【检测小电池关机电流】通过,【69.57uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:15:35:462 ==>> MES过站成功
2025-07-31 18:15:35:476 ==>> #################### 【测试结束】 ####################
2025-07-31 18:15:35:541 ==>> 关闭5V供电
2025-07-31 18:15:35:556 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:15:35:670 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:15:36:011 ==>>  

2025-07-31 18:15:36:556 ==>> 关闭5V供电成功
2025-07-31 18:15:36:568 ==>> 关闭33V供电
2025-07-31 18:15:36:592 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:15:36:664 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:15:37:565 ==>> 关闭33V供电成功
2025-07-31 18:15:37:577 ==>> 关闭3.7V供电
2025-07-31 18:15:37:589 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:15:37:673 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:15:38:481 ==>>  

2025-07-31 18:15:38:571 ==>> 关闭3.7V供电成功
