2025-07-31 18:23:12:687 ==>> MES查站成功:
查站序号:P51000100531268C验证通过
2025-07-31 18:23:12:698 ==>> 扫码结果:P51000100531268C
2025-07-31 18:23:12:726 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:23:12:727 ==>> 测试参数版本:2024.10.11
2025-07-31 18:23:12:729 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:23:12:730 ==>> 检测【打开透传】
2025-07-31 18:23:12:732 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:23:12:782 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:23:13:212 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:23:13:217 ==>> 检测【检测接地电压】
2025-07-31 18:23:13:218 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:23:13:270 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 18:23:13:525 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:23:13:527 ==>> 检测【打开小电池】
2025-07-31 18:23:13:529 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:23:13:571 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:23:13:819 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:23:13:822 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:23:13:824 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:23:13:871 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 18:23:14:117 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:23:14:119 ==>> 检测【等待设备启动】
2025-07-31 18:23:14:121 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:14:331 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:14:514 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 18:23:15:016 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:15:153 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:15:198 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 18:23:15:716 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:15:897 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:16:183 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:16:409 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:16:593 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:17:105 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:17:210 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:17:300 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:17:812 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:17:978 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:18:237 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:18:513 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:18:678 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:19:183 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:19:273 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:19:379 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:23:19:883 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:20:079 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 18:23:20:307 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:20:583 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:20:781 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time?

2025-07-31 18:23:21:283 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:21:342 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:21:480 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 18:23:21:985 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:22:182 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 18:23:22:381 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:22:691 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:22:856 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 18:23:23:390 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:23:420 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:23:23:556 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 18:23:24:074 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:24:254 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 18:23:24:454 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 18:23:24:458 ==>> #################### 【测试结束】 ####################
2025-07-31 18:23:24:519 ==>> 关闭5V供电
2025-07-31 18:23:24:522 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:23:24:579 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:23:24:775 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:24:956 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 18:23:25:478 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:25:523 ==>> 关闭5V供电成功
2025-07-31 18:23:25:525 ==>> 关闭33V供电
2025-07-31 18:23:25:528 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:23:25:568 ==>> 5A A5 02 5A A5 


2025-07-31 18:23:25:673 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]TimeCLOSE_POWER_OUT1
OVER 150


2025-07-31 18:23:26:165 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:23:26:361 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 18:23:26:529 ==>> 关闭33V供电成功
2025-07-31 18:23:26:531 ==>> 关闭3.7V供电
2025-07-31 18:23:26:534 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:23:26:670 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:23:27:040 ==>>  

