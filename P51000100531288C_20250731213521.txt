2025-07-31 21:35:21:137 ==>> MES查站成功:
查站序号:P51000100531288C验证通过
2025-07-31 21:35:21:143 ==>> 扫码结果:P51000100531288C
2025-07-31 21:35:21:144 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:35:21:146 ==>> 测试参数版本:2024.10.11
2025-07-31 21:35:21:147 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:35:21:148 ==>> 检测【打开透传】
2025-07-31 21:35:21:150 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:35:21:275 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:35:21:496 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:35:21:503 ==>> 检测【检测接地电压】
2025-07-31 21:35:21:505 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:35:21:576 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:35:21:996 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:35:21:998 ==>> 检测【打开小电池】
2025-07-31 21:35:22:000 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:35:22:074 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:35:22:289 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:35:22:292 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:35:22:296 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:35:22:381 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:35:22:583 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:35:22:587 ==>> 检测【等待设备启动】
2025-07-31 21:35:22:591 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:23:629 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:24:653 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:25:679 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:26:722 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:27:760 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:28:799 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:29:842 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:30:875 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:31:908 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:32:947 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:35:32:950 ==>> #################### 【测试结束】 ####################
2025-07-31 21:35:33:014 ==>> 关闭5V供电
2025-07-31 21:35:33:017 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:35:33:082 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:35:34:019 ==>> 关闭5V供电成功
2025-07-31 21:35:34:021 ==>> 关闭33V供电
2025-07-31 21:35:34:027 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:35:34:081 ==>> 5A A5 02 5A A5 


2025-07-31 21:35:34:171 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:35:35:025 ==>> 关闭33V供电成功
2025-07-31 21:35:35:028 ==>> 关闭3.7V供电
2025-07-31 21:35:35:031 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:35:35:087 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


