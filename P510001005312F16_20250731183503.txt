2025-07-31 18:35:03:275 ==>> MES查站成功:
查站序号:P510001005312F16验证通过
2025-07-31 18:35:03:282 ==>> 扫码结果:P510001005312F16
2025-07-31 18:35:03:283 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:35:03:285 ==>> 测试参数版本:2024.10.11
2025-07-31 18:35:03:286 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:35:03:287 ==>> 检测【打开透传】
2025-07-31 18:35:03:290 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:35:03:381 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:35:03:745 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:35:03:753 ==>> 检测【检测接地电压】
2025-07-31 18:35:03:755 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:35:03:888 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:35:04:044 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:35:04:046 ==>> 检测【打开小电池】
2025-07-31 18:35:04:049 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:35:04:181 ==>> 6A A6 01 A6 6A 


2025-07-31 18:35:04:286 ==>> Battery ON
OVER 150


2025-07-31 18:35:04:329 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:35:04:331 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:35:04:332 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:35:04:391 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:35:04:622 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:35:04:624 ==>> 检测【等待设备启动】
2025-07-31 18:35:04:628 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:35:05:005 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:35:05:186 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:35:05:655 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:35:05:886 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:35:05:931 ==>>                                         

2025-07-31 18:35:06:333 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:35:06:701 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:35:06:807 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:35:07:018 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:35:07:022 ==>> 检测【产品通信】
2025-07-31 18:35:07:024 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:35:07:145 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:35:07:361 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:35:07:363 ==>> 检测【初始化完成检测】
2025-07-31 18:35:07:365 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:35:07:436 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:35:07:616 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE43D00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:35:07:830 ==>> [D][05:17:51][COMM]2647 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:07:921 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:35:07:924 ==>> 检测【关闭大灯控制1】
2025-07-31 18:35:07:927 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:35:07:996 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:35:08:071 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:35:08:221 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:35:08:224 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:35:08:226 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:35:08:376 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:35:08:503 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:35:08:505 ==>> 检测【关闭仪表供电】
2025-07-31 18:35:08:507 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:35:08:685 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:35:08:779 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:35:08:781 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:35:08:784 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:35:08:851 ==>> [D][05:17:52][COMM]3657 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:08:956 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:35:09:062 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:35:09:064 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:35:09:067 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:35:09:245 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:35:09:347 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:35:09:349 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:35:09:351 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:35:09:599 ==>> [D][05:17:53][HSDK]need to erase for write: is[0x0] ie[0x2E00]
[D][05:17:53][HSDK][0] flush to flash addr:[0xE43E00] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:35:09:844 ==>> [D][05:17:53][COMM]4669 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:09:895 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:35:09:897 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:35:09:898 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:35:09:995 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:35:10:054 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 17


2025-07-31 18:35:10:193 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:35:10:195 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:35:10:197 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:35:10:374 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5004. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5004. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5005. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5006. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5006. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5007
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E

2025-07-31 18:35:10:404 ==>> ->0x01B987FE 5008
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:35:10:489 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:35:10:491 ==>> 该项需要延时执行
2025-07-31 18:35:10:648 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]


2025-07-31 18:35:10:877 ==>> [D][05:17:54][COMM]5679 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:11:667 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:35:12:155 ==>>                                                                                                                                               MM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80


2025-07-31 18:35:12:260 ==>> 
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequen

2025-07-31 18:35:12:365 ==>> ce_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][

2025-07-31 18:35:12:425 ==>> 05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6690 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                         

2025-07-31 18:35:12:886 ==>> [D][05:17:56][COMM]7701 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:13:662 ==>> [D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 18:35:13:907 ==>> [D][05:17:57][COMM]8712 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:14:150 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:35:14:505 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:35:14:508 ==>> 检测【33V输入电压ADC】
2025-07-31 18:35:14:511 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:35:14:795 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:17:58][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:58][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:58][COMM]adc read right brake adc:8  volt:10 mv
[D][05:17:58][COMM]adc read throttle adc:13  volt:17 mv
[D][05:17:58][COMM]adc read battery ts volt:16 mv
[D][05:17:58][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:17:58][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:35:14:900 ==>> [D][05:17:58][COMM]9723 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:15:041 ==>> 【33V输入电压ADC】通过,【32653mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:35:15:069 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:35:15:070 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:35:15:191 ==>> 1A A1 00 00 FC 
Get AD_V2 1684mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1084mV
OVER 150


2025-07-31 18:35:15:251 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10017


2025-07-31 18:35:15:379 ==>> 【TP7_VCC3V3(ADV2)】通过,【1684mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:15:382 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:35:15:446 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:15:451 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:35:15:467 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 18:35:15:482 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:35:15:485 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:35:15:529 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:35:15:532 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:35:15:573 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:35:15:577 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:35:15:699 ==>> 1A A1 00 00 FC 
Get AD_V2 1683mV
Get AD_V3 1658mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1993mV
Get AD_V7 1084mV
OVER 150


2025-07-31 18:35:15:907 ==>> 【TP7_VCC3V3(ADV2)】通过,【1683mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:15:909 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:35:15:930 ==>> [D][05:17:59][COMM]10733 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:35:15:959 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:15:962 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:35:15:964 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 18:35:16:011 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:35:16:013 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:35:16:115 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:35:16:118 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:35:16:144 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:35:16:194 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:35:16:204 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:35:16:293 ==>> 1A A1 00 00 FC 
Get AD_V2 1684mV
Get AD_V3 1659mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1084mV
OVER 150


2025-07-31 18:35:16:513 ==>> 【TP7_VCC3V3(ADV2)】通过,【1684mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:16:515 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:35:16:560 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:16:562 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:35:16:566 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 18:35:16:606 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:35:16:609 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:35:16:653 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:35:16:656 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:35:16:701 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:35:16:726 ==>> 检测【打开WIFI(1)】
2025-07-31 18:35:16:728 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:35:16:954 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_AC

2025-07-31 18:35:16:984 ==>> K
[D][05:18:00][HSDK][0] flush to flash addr:[0xE43F00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:35:17:241 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:35:17:244 ==>> 检测【清空消息队列(1)】
2025-07-31 18:35:17:247 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:35:17:475 ==>>                                                                                                                                                        msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087504207

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071536353

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:35:17:519 ==>>                                                                                                                        

2025-07-31 18:35:17:718 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:35:18:148 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 18:35:18:285 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:35:18:563 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc]

2025-07-31 18:35:18:653 ==>> .L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[W][05:18:02][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:02][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:35:18:758 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind.

2025-07-31 18:35:18:788 ==>>  id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 18:35:18:823 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:35:18:827 ==>> 检测【打开GPS(1)】
2025-07-31 18:35:18:829 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:35:19:186 ==>> [D][05:18:02][COMM]13746 imu init OK
[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[W][05:18:02][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:02][COMM]Open GPS Module...
[D][05:18:02][COMM]LOC_MODEL_CONT
[W][05:18:02][GNSS]gps already running
[D][05:18:02][GNSS]start event:8
[W][05:18:02][GNSS]start cont locating
[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][COMM]S->M yaw:INVALID
[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:35:19:357 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:35:19:361 ==>> 检测【打开GSM联网】
2025-07-31 18:35:19:364 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:35:19:555 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:03][COMM]GSM test
[D][05:18:03][COMM]GSM test enable


2025-07-31 18:35:19:635 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:35:19:640 ==>> 检测【打开仪表供电1】
2025-07-31 18:35:19:643 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:35:19:784 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:03][COMM]set POWER 1
[D][05:18:03][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:35:19:874 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:35:19:907 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:35:19:912 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:35:19:916 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:35:20:085 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:03][COMM][oneline_display]: command mode, ON!
[D][05:18:03][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:35:20:160 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 18:35:20:178 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:35:20:181 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:35:20:185 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:35:20:372 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:04][COMM]arm_hub read adc[3],val[33224]


2025-07-31 18:35:20:451 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:35:20:478 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:35:20:480 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:35:20:554 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:35:20:795 ==>> [D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,24,,,42,26,,,42,39,,,40,33,,,38,1*72

$GBGSV,3,2,10,38,,,36,13,,,41,16,,,40,21,,,37,1*7B

$GBGSV,3,3,10,59,,,37,14,,,36,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1641.715,1641.715,52.484,2097152,2097152,2097152*40

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][HSDK][0] flush to flash addr:[0xE44000] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 18:35:20:946 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:35:20:988 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:35:20:992 ==>> 检测【AD_V20电压】
2025-07-31 18:35:20:996 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:35:21:098 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:35:21:191 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 18:35:21:576 ==>> 本次取值间隔时间:465ms
2025-07-31 18:35:21:659 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:35:21:662 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:35:21:664 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:35:21:684 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,24,,,43,13,,,43,59,,,42,26,,,41,1*78

$GBGSV,4,2,15,21,,,41,8,,,41,39,,,40,60,,,40,1*43

$GBGSV,4,3,15,16,,,39,33,,,39,14,,,39,38,,,38,1*7D

$GBGSV,4,4,15,6,,,38,5,,,37,3,,,41,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1661.232,1661.232,53.080,2097152,2097152,2097152*41



2025-07-31 18:35:21:789 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:35:21:947 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:35:21:950 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:35:21:952 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:35:22:206 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][COMM]read battery soc:255


2025-07-31 18:35:22:684 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,43,42,,,43,13,,,42,3,,,42,1*4A

$GBGSV,5,2,18,59,,,41,26,,,41,21,,,41,8,,,41,1*4B

$GBGSV,5,3,18,39,,,40,60,,,40,14,,,40,38,,,40,1*7B

$GBGSV,5,4,18,16,,,39,33,,,39,6,,,38,2,,,38,1*7D

$GBGSV,5,5,18,5,,,36,40,,,31,1*49

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1646.803,1646.803,52.666,2097152,2097152,2097152*4E



2025-07-31 18:35:22:995 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:35:23:181 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:35:23:704 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,44,42,,,43,13,,,42,3,,,42,1*46

$GBGSV,5,2,20,59,,,41,26,,,41,21,,,41,8,,,41,1*40

$GBGSV,5,3,20,39,,,41,60,,,41,38,,,41,1,,,40,1*45

$GBGSV,5,4,20,14,,,39,16,,,39,33,,,39,6,,,38,1*40

$GBGSV,5,5,20,2,,,37,5,,,36,40,,,31,9,,,39,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1651.777,1651.777,52.829,2097152,2097152,2097152*4B



2025-07-31 18:35:24:040 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:35:24:165 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:35:24:270 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:35:24:729 ==>> $GBGGA,103528.497,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,44,42,,,43,13,,,42,3,,,42,1*47

$GBGSV,6,2,22,59,,,41,26,,,41,21,,,41,8,,,41,1*41

$GBGSV,6,3,22,60,,,41,38,,,41,39,,,40,1,,,40,1*45

$GBGSV,6,4,22,14,,,39,16,,,39,33,,,39,9,,,38,1*4E

$GBGSV,6,5,22,6,,,38,2,,,37,5,,,35,7,,,32,1*7B

$GBGSV,6,6,22,40,,,31,4,,,36,1*41

$GBRMC,103528.497,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103528.497,0.000,1628.731,1628.731,52.113,2097152,2097152,2097152*52



2025-07-31 18:35:25:084 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:35:25:088 ==>> #################### 【测试结束】 ####################
2025-07-31 18:35:25:132 ==>> 关闭5V供电
2025-07-31 18:35:25:137 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:35:25:194 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:35:25:715 ==>> $GBGGA,103529.497,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,44,42,,,43,13,,,42,38,,,42,1*7F

$GBGSV,6,2,22,3,,,41,59,,,41,26,,,41,21,,,41,1*4A

$GBGSV,6,3,22,8,,,41,60,,,41,39,,,40,14,,,40,1*42

$GBGSV,6,4,22,16,,,40,1,,,39,33,,,39,9,,,38,1*74

$GBGSV,6,5,22,6,,,38,2,,,37,5,,,35,4,,,33,1*79

$GBGSV,6,6,22,7,,,32,40,,,32,1*45

$GBRMC,103529.497,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103529.497,0.000,1620.660,1620.660,51.860,2097152,2097152,2097152*5D



2025-07-31 18:35:26:145 ==>> 关闭5V供电成功
2025-07-31 18:35:26:149 ==>> 关闭33V供电
2025-07-31 18:35:26:153 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:35:26:191 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:35:26:280 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:35:26:475 ==>> [D][05:18:10][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:35:26:735 ==>> $GBGGA,103530.497,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,44,42,,,43,13,,,42,38,,,41,1*7C

$GBGSV,6,2,22,3,,,41,59,,,41,26,,,41,21,,,41,1*4A

$GBGSV,6,3,22,8,,,41,60,,,40,39,,,40,14,,,40,1*43

$GBGSV,6,4,22,16,,,40,1,,,39,33,,,39,9,,,38,1*74

$GBGSV,6,5,22,6,,,38,2,,,37,5,,,35,4,,,33,1*79

$GBGSV,6,6,22,7,,,32,40,,,32,1*45

$GBRMC,103530.497,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103530.497,0.000,1616.889,1616.889,51.737,2097152,2097152,2097152*58

[D][05:18:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:35:27:149 ==>> 关闭33V供电成功
2025-07-31 18:35:27:153 ==>> 关闭3.7V供电
2025-07-31 18:35:27:181 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:35:27:288 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:35:27:904 ==>>  

