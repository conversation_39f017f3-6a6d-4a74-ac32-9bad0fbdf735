2025-07-31 20:39:00:737 ==>> MES查站成功:
查站序号:P5100010053134BA验证通过
2025-07-31 20:39:00:746 ==>> 扫码结果:P5100010053134BA
2025-07-31 20:39:00:749 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:39:00:754 ==>> 测试参数版本:2024.10.11
2025-07-31 20:39:00:757 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:39:00:761 ==>> 检测【打开透传】
2025-07-31 20:39:00:765 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:39:00:856 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:39:01:173 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:39:01:184 ==>> 检测【检测接地电压】
2025-07-31 20:39:01:187 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:39:01:255 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:39:01:478 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:39:01:480 ==>> 检测【打开小电池】
2025-07-31 20:39:01:483 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:39:01:560 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:39:01:776 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:39:01:778 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:39:01:780 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:39:01:850 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:39:02:072 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:39:02:075 ==>> 检测【等待设备启动】
2025-07-31 20:39:02:077 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:39:02:342 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:39:02:524 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:39:03:112 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:39:03:188 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:39:03:233 ==>>                                                    

2025-07-31 20:39:03:632 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:39:04:107 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:39:04:184 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:39:04:186 ==>> 检测【产品通信】
2025-07-31 20:39:04:188 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:39:04:336 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:39:04:484 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:39:04:487 ==>> 检测【初始化完成检测】
2025-07-31 20:39:04:490 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:39:04:688 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:39:04:763 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:39:04:813 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:39:04:816 ==>> 检测【关闭大灯控制1】
2025-07-31 20:39:04:820 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:39:05:037 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:39:05:105 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:39:05:108 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:39:05:110 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:39:05:142 ==>> [D][05:17:51][COMM]2635 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:05:384 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:39:05:661 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:39:05:665 ==>> 检测【关闭仪表供电】
2025-07-31 20:39:05:667 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:39:05:854 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:39:05:946 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:39:05:948 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:39:05:951 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:39:06:177 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3647 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:06:248 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:39:06:251 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:39:06:253 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:39:06:419 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:39:06:545 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:39:06:548 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:39:06:550 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:06:722 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:39:06:833 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:39:06:836 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:39:06:837 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:39:06:947 ==>> 5A A5 01 5A A5 


2025-07-31 20:39:07:052 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:39:07:130 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:39:07:132 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:39:07:134 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:39:07:202 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4658 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:07:262 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:39:07:412 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:39:07:432 ==>> 该项需要延时执行
2025-07-31 20:39:07:720 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 20:39:07:735 ==>> 


2025-07-31 20:39:08:196 ==>> [D][05:17:54][COMM]5669 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:08:715 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:39:09:301 ==>>                                                    pe 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status

2025-07-31 20:39:09:406 ==>>  fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mc

2025-07-31 20:39:09:512 ==>> MaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][

2025-07-31 20:39:09:602 ==>> BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6680 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 20:39:10:220 ==>> [D][05:17:56][COMM]7691 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:11:242 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8702 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:11:425 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:39:11:428 ==>> 检测【33V输入电压ADC】
2025-07-31 20:39:11:432 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:11:772 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:17:58][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:0 mv
[D][05:17:58][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 20:39:11:972 ==>> 【33V输入电压ADC】通过,【32349mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:39:11:975 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:39:11:987 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:39:12:061 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2778mV
Get AD_V6 1990mV
Get AD_V7 1096mV
OVER 150


2025-07-31 20:39:12:241 ==>> [D][05:17:58][COMM]9713 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:12:259 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:12:261 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:39:12:292 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:12:295 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:39:12:296 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 20:39:12:326 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:39:12:329 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:39:12:359 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:39:12:362 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:39:12:396 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:39:12:398 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:39:12:467 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2780mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:39:12:572 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10016
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10017


2025-07-31 20:39:12:684 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:12:687 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:39:12:717 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:12:720 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:39:12:722 ==>> 原始值:【2780】, 乘以分压基数【2】还原值:【5560】
2025-07-31 20:39:12:747 ==>> 【TP68_VCC5V5(ADV5)】通过,【5560mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:39:12:750 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:39:12:778 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:39:12:780 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:39:12:812 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:39:12:815 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:39:12:963 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1660mV
Get AD_V4 2mV
Get AD_V5 2778mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:39:13:054 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:39:13:118 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:13:129 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:39:13:153 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:13:156 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:39:13:158 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 20:39:13:190 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:39:13:194 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:39:13:226 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:39:13:229 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:39:13:266 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:39:13:268 ==>> 检测【打开WIFI(1)】
2025-07-31 20:39:13:272 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:13:497 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10724 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,

2025-07-31 20:39:13:542 ==>> 1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:39:13:820 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:39:13:825 ==>> 检测【清空消息队列(1)】
2025-07-31 20:39:13:828 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:39:13:919 ==>>                                                                                                                                                                                                                                          1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087691566

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539202

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:39:14:024 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:39:14:113 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:39:14:117 ==>> 检测【打开GPS(1)】
2025-07-31 20:39:14:120 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:39:14:250 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:39:14:355 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:39:14:414 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:39:14:418 ==>> 检测【打开GSM联网】
2025-07-31 20:39:14:420 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:39:14:658 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:39:14:705 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:39:14:708 ==>> 检测【打开仪表供电1】
2025-07-31 20:39:14:720 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:39:14:947 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:39:14:996 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:39:14:998 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:39:15:000 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:39:15:190 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:39:15:284 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:39:15:287 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:39:15:290 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:39:15:433 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33270]


2025-07-31 20:39:15:571 ==>> 【读取主控ADC采集的仪表电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:39:15:574 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:39:15:576 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:39:15:751 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:39:15:856 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:39:15:859 ==>> 检测【AD_V20电压】
2025-07-31 20:39:15:861 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:39:15:962 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:39:16:053 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:39:16:158 ==>> 本次取值间隔时间:184ms
2025-07-31 20:39:16:202 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:39:16:248 ==>> [D][05:18:02][COMM]13736 imu init OK


2025-07-31 20:39:16:308 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:39:16:353 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:

2025-07-31 20:39:16:383 ==>> 1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 20:39:16:581 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:39:16:655 ==>> 本次取值间隔时间:334ms
2025-07-31 20:39:16:699 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:16:702 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:39:16:705 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:39:16:760 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:39:16:821 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:39:16:997 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:39:17:001 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:39:17:004 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:39:17:409 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03

2025-07-31 20:39:17:440 ==>> ][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:39:17:545 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:39:17:548 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:39:17:552 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:39:17:555 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:39:17:606 ==>>                                                                                                                                                                                                      [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:39:17:787 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:39:17:837 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:39:17:840 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:39:17:842 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:39:18:045 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:39:18:126 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:39:18:130 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:39:18:133 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:39:18:498 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:39:18:683 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:39:18:686 ==>> 检测【AD_V21电压】
2025-07-31 20:39:18:688 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:39:18:758 ==>> 1A A1 20 00 00 
Get AD_V21 1646mV
OVER 150


2025-07-31 20:39:18:983 ==>> 本次取值间隔时间:294ms
2025-07-31 20:39:19:017 ==>> 【AD_V21电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:19:022 ==>> 检测【关闭仪表供电2】
2025-07-31 20:39:19:027 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:39:19:271 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
[D][05:18:05][COMM]read battery soc:255


2025-07-31 20:39:19:564 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:39:19:568 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:39:19:572 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:39:19:743 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:39:19:848 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:39:19:853 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:39:19:856 ==>> 检测【打开AccKey2供电】
2025-07-31 20:39:19:859 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:39:20:074 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,59,,,44,33,,,42,25,,,39,42,,,38,1*78

$GBGSV,2,2,07,39,,,30,24,,,40,14,,,37,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1600.383,1600.383,51.285,2097152,2097152,2097152*44

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:39:20:138 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:39:20:142 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:39:20:144 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:20:256 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:39:20:496 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:06][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:06][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:06][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:06][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:06][COMM]adc read battery ts volt:0 mv
[D][05:18:06][COMM]adc read in 24v adc:1275  volt:32248 mv
[D][05:18:06][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:39:20:678 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33184mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:39:20:682 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:39:20:685 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:39:20:817 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:39:20:977 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:39:20:981 ==>> 该项需要延时执行
2025-07-31 20:39:20:985 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,42,24,,,41,59,,,40,60,,,40,1*72

$GBGSV,3,2,09,25,,,39,14,,,39,42,,,38,39,,,34,1*7C

$GBGSV,3,3,09,44,,,31,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1584.648,1584.648,50.712,2097152,2097152,2097152*4E



2025-07-31 20:39:21:256 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:39:21:994 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,41,24,,,40,3,,,40,59,,,39,1*40

$GBGSV,4,2,14,60,,,39,25,,,39,14,,,39,42,,,38,1*76

$GBGSV,4,3,14,40,,,37,39,,,36,1,,,36,2,,,33,1*7D

$GBGSV,4,4,14,44,,,32,38,,,15,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1492.676,1492.676,47.924,2097152,2097152,2097152*43



2025-07-31 20:39:22:989 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,41,24,,,40,3,,,40,25,,,40,1*47

$GBGSV,4,2,16,59,,,39,60,,,39,14,,,39,42,,,38,1*7F

$GBGSV,4,3,16,40,,,37,39,,,37,1,,,36,38,,,35,1*41

$GBGSV,4,4,16,2,,,33,44,,,32,23,,,30,16,,,37,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1536.736,1536.736,49.176,2097152,2097152,2097152*42



2025-07-31 20:39:23:250 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:39:23:983 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:39:23:989 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:39:23:993 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:24:013 ==>> $GBGGA,123927.826,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,41,24,,,40,3,,,40,25,,,40,1*43

$GBGSV,5,2,20,59,,,39,60,,,39,14,,,39,42,,,38,1*7B

$GBGSV,5,3,20,39,,,38,40,,,37,16,,,36,13,,,36,1*76

$GBGSV,5,4,20,1,,,35,38,,,35,9,,,34,2,,,33,1*43

$GBGSV,5,5,20,10,,,33,44,,,32,6,,,31,23,,,30,1*42

$GBRMC,123927.826,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123927.826,0.000,1504.956,1504.956,48.165,2097152,2097152,2097152*5F



2025-07-31 20:39:24:269 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:10][COMM]adc read battery ts volt:0 mv
[D][05:18:10][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:39:24:533 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:39:24:537 ==>> 检测【打开AccKey1供电】
2025-07-31 20:39:24:539 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:39:24:782 ==>> $GBGGA,123928.526,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,24,,,40,3,,,40,25,,,40,1*41

$GBGSV,6,2,22,59,,,39,60,,,39,14,,,39,39,,,38,1*76

$GBGSV,6,3,22,42,,,37,40,,,37,16,,,36,13,,,36,1*74

$GBGSV,6,4,22,1,,,35,38,,,35,9,,,34,7,,,34,1*40

$GBGSV,6,5,22,2,,,33,10,,,33,44,,,32,6,,,32,1*70

$GBGSV,6,6,22,23,,,30,4,,,30,1*43

$GBRMC,123928.526,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123928.526,0.000,1490.642,1490.642,47.715,2097152,2097152,2097152*53

[W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:39:24:832 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:39:24:837 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:39:24:841 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:39:24:948 ==>> 1A A1 00 40 00 
Get AD_V14 2672mV
OVER 150


2025-07-31 20:39:25:083 ==>> 原始值:【2672】, 乘以分压基数【2】还原值:【5344】
2025-07-31 20:39:25:121 ==>> 【读取AccKey1电压(ADV14)前】通过,【5344mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:39:25:124 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:39:25:129 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:25:251 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:39:25:461 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3141  volt:5521 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:0 mv
[D][05:18:11][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 20:39:25:684 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5521mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:39:25:690 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:39:25:694 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:39:25:701 ==>> $GBGGA,123929.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,24,,,40,3,,,40,25,,,39,1*4C

$GBGSV,6,2,22,59,,,39,60,,,39,14,,,39,39,,,38,1*76

$GBGSV,6,3,22,42,,,37,40,,,37,16,,,36,13,,,35,1*77

$GBGSV,6,4,22,1,,,35,38,,,35,9,,,35,7,,,34,1*41

$GBGSV,6,5,22,2,,,33,6,,,33,10,,,32,44,,,32,1*70

$GBGSV,6,6,22,23,,,30,4,,,30,1*43

$GBRMC,123929.506,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123929.506,0.000,1486.866,1486.866,47.588,2097152,2097152,2097152*56



2025-07-31 20:39:25:806 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:39:25:982 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:39:25:986 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:39:25:989 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:39:26:051 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 20:39:26:235 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 20:39:26:279 ==>> 【读取AccKey1电压(ADV14)后】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:39:26:286 ==>> 检测【打开WIFI(2)】
2025-07-31 20:39:26:291 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:26:479 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:39:26:572 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:39:26:575 ==>> 检测【转刹把供电】
2025-07-31 20:39:26:578 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:39:26:704 ==>> $GBGGA,123930.506,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,24,,,40,3,,,40,25,,,40,1*42

$GBGSV,6,2,22,59,,,39,60,,,39,14,,,39,39,,,38,1*76

$GBGSV,6,3,22,42,,,37,40,,,37,16,,,36,13,,,35,1*77

$GBGSV,6,4,22,1,,,35,38,,,35,9,,,35,7,,,35,1*40

$GBGSV,6,5,22,6,,,34,2,,,33,10,,,32,44,,,32,1*77

$GBGSV,6,6,22,23,,,30,4,,,30,1*43

$GBRMC,123930.506,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123930.506,0.000,1492.519,1492.519,47.768,2097152,2097152,2097152*52



2025-07-31 20:39:26:779 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:39:26:855 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:39:26:860 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:39:26:863 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:39:26:959 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:39:27:049 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2417mV
OVER 150


2025-07-31 20:39:27:124 ==>> 原始值:【2417】, 乘以分压基数【2】还原值:【4834】
2025-07-31 20:39:27:154 ==>> +WIFISCAN:4,0,CC057790A640,-77
+WIFISCAN:4,1,44A1917CAD81

2025-07-31 20:39:27:159 ==>> System.IndexOutOfRangeException: 索引超出了数组界限。
   在 AppSe5x.FormMain.<ProcessMessagesAsync>d__105.MoveNext()
2025-07-31 20:39:27:181 ==>> 【读取AD_V15电压(前)】通过,【4834mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:39:27:185 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:39:27:188 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:39:27:194 ==>> ,-77
+WIFISCAN:4,2,44A1917CAD80,-78
+WIFISCAN:4,3,CC057790A7C0,-81

[D][05:18:13][CAT1]wifi scan report total[4]


2025-07-31 20:39:27:289 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:39:27:297 ==>> [D

2025-07-31 20:39:27:320 ==>> ][05:18:13][COMM]read battery soc:255
[D][05:18:13][GNSS]recv submsg id[3]
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:39:27:349 ==>> 1A A1 01 00 00 
Get AD_V16 2430mV
OVER 150


2025-07-31 20:39:27:454 ==>> 原始值:【2430】, 乘以分压基数【2】还原值:【4860】
2025-07-31 20:39:27:519 ==>> 【读取AD_V16电压(前)】通过,【4860mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:39:27:523 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:39:27:526 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:27:883 ==>> $GBGGA,123931.506,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,24,,,40,3,,,40,25,,,39,1*4E

$GBGSV,6,2,23,59,,,39,60,,,39,14,,,39,39,,,38,1*77

$GBGSV,6,3,23,42,,,38,40,,,37,16,,,36,13,,,35,1*79

$GBGSV,6,4,23,1,,,35,38,,,35,9,,,35,7,,,35,1*41

$GBGSV,6,5,23,6,,,34,2,,,33,10,,,33,44,,,32,1*77

$GBGSV,6,6,23,26,,,32,23,,,30,4,,,30,1*47

$GBRMC,123931.506,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123931.506,0.000,743.078,743.078,679.565,2097152,2097152,2097152*67

[D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:0 mv
[D][05:18:14][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3087  volt:5426 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:14][COMM]arm_hub adc read

2025-07-31 20:39:27:913 ==>>  led yb adc:1436  volt:33293 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:39:28:088 ==>> 【转刹把供电电压(主控ADC)】通过,【5426mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:39:28:091 ==>> 检测【转刹把供电电压】
2025-07-31 20:39:28:096 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:28:371 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:0 mv
[D][05:18:14][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3108  volt:5463 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:39:28:643 ==>> 【转刹把供电电压】通过,【5463mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:39:28:647 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:39:28:653 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:28:725 ==>> $GBGGA,123932.506,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,6,2,24,60,,,39,14,,,39,59,,,38,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,40,,,37,16,,,36,13,,,35,1*7E

$GBGSV,6,4,24,38,,,35,7,,,35,9,,,35,1,,,35,1*46

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,44,,,32,4,,,30,23,,,30,5,,,29,1*7A

$GBRMC,123932.506,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123932.506,0.000,737.992,737.992,674.915,2097152,2097152,2097152*62



2025-07-31 20:39:28:815 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:39:28:942 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:39:28:946 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:39:28:949 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:29:055 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:39:29:115 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:39:29:160 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:29:165 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:39:29:265 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:39:29:325 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:39:29:356 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:39:29:412 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:39:29:415 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:39:29:419 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:29:521 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:39:29:630 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:29:737 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:39:29:767 ==>> $GBGGA,123933.506,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,41,60,,,40,25,,,40,1*73

$GBGSV,6,2,24,3,,,39,59,,,39,14,,,39,39,,,38,1*45

$GBGSV,6,3,24,40,,,37,42,,,37,16,,,36,13,,,35,1*71

$GBGSV,6,4,24,38,,,35,7,,,35,9,,,35,1,,,35,1*46

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,44,,,32,5,,,30,4,,,30,23,,,30,1*72

$GBRMC,123933.506,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123933.506,0.000,739.715,739.715,676.491,2097152,2097152,2097152*60

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:39:29:842 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:29:947 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:39:29:952 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:39:30:052 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:39:30:058 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:39:30:157 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:39:30:262 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:39:30:322 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:39:30:326 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:39:30:331 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:39:30:459 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:39:30:626 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:39:30:632 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:39:30:638 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:39:30:714 ==>> $GBGGA,123934.506,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,41,60,,,40,3,,,40,1*47

$GBGSV,6,2,24,25,,,40,59,,,39,14,,,39,39,,,38,1*7F

$GBGSV,6,3,24,42,,,38,40,,,37,13,,,36,38,,,36,1*71

$GBGSV,6,4,24,16,,,36,7,,,35,9,,,35,1,,,35,1*49

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,44,,,32,5,,,30,4,,,30,23,,,30,1*72

$GBRMC,123934.506,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123934.506,0.000,743.166,743.166,679.647,2097152,2097152,2097152*61



2025-07-31 20:39:30:759 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:39:30:926 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:39:30:932 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:39:30:938 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:39:31:062 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:39:31:219 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:39:31:222 ==>> 检测【左刹电压测试1】
2025-07-31 20:39:31:228 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:31:276 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:39:31:578 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:17][COMM]adc read right brake adc:1722  volt:2270 mv
[D][05:18:17][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:17][COMM]adc read battery ts volt:4 mv
[D][05:18:17][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:17][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 20:39:31:683 ==>> $GBGGA,123935.506,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,40,24,,,40,25,,,40,1*71

$GBGSV,6,2,24,3,,,39,59,,,39,14,,,39,39,,,38,1*45

$GBGSV,6,3,24,40,,,37,42,,,37,16,,,36,7,,,35,1*44

$GBGSV,6,4,24,13,,,35,38,,,35,9,,,35,1,,,35,1*73

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,44,,,32,5,,,30,4,,,30,23,,,30,1*72



2025-07-31 20:39:31:713 ==>> $GBRMC,123935.506,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123935.506,0.000,737.985,737.985,674.908,2097152,2097152,2097152*69



2025-07-31 20:39:31:765 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:39:31:768 ==>> 检测【右刹电压测试1】
2025-07-31 20:39:31:800 ==>> 【右刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 20:39:31:805 ==>> 检测【转把电压测试1】
2025-07-31 20:39:31:834 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:39:31:840 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:39:31:845 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:39:31:956 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:39:32:130 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:39:32:137 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:39:32:144 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:39:32:256 ==>> 3A A3 04 00 A3 


2025-07-31 20:39:32:361 ==>> OFF_OUT4
OVER 150


2025-07-31 20:39:32:430 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:39:32:437 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:39:32:443 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:39:32:558 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:39:32:734 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:39:32:744 ==>> 检测【左刹电压测试2】
2025-07-31 20:39:32:752 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:39:32:764 ==>> $GBGGA,123936.506,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,6,2,24,60,,,39,59,,,39,14,,,39,39,,,38,1*70

$GBGSV,6,3,24,40,,,37,42,,,37,16,,,36,7,,,35,1*44

$GBGSV,6,4,24,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,44,,,32,4,,,31,5,,,30,23,,,30,1*73

$GBRMC,123936.506,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123936.506,0.000,738.843,738.843,675.693,2097152,2097152,2097152*66



2025-07-31 20:39:33:070 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:0 mv
[D][05:18:19][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:39:33:286 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:39:33:290 ==>> 检测【右刹电压测试2】
2025-07-31 20:39:33:299 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:39:33:320 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:39:33:326 ==>> 检测【转把电压测试2】
2025-07-31 20:39:33:354 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:39:33:358 ==>> 检测【晶振检测】
2025-07-31 20:39:33:364 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:39:33:523 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 20:39:33:651 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:39:33:656 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:39:33:663 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:39:33:764 ==>> $GBGGA,123937.506,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,59,,,40,24,,,40,1*4C

$GBGSV,6,2,24,25,,,40,60,,,39,39,,,39,14,,,39,1*74

$GBGSV,6,3,24,42,,,38,40,,,37,13,,,36,1,,,36,1*4B

$GBGSV,6,4,24,16,,,36,7,,,35,38,,,35,9,,,35,1*73

$GBGSV,6,5,24,6,,,35,2,,,34,10,,,33,26,,,32,1*72

$GBGSV,6,6,24,44,,,32,5,,,31,4,,,31,23,,,30,1*72

$GBRMC,123937.506,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123937.506,0.000,745.743,745.743,682.002,2097152,2097152,2097152*61

1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1660mV
Get AD_V4 1652mV
Get AD_V5 2780mV
Get AD_V6 1992mV
Get AD_V7 1096mV
OVER 150


2025-07-31 20:39:33:949 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:39:33:956 ==>> 检测【检测BootVer】
2025-07-31 20:39:33:961 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:39:34:316 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071539202
[D][05:18:20][FCTY]HardwareID  = 867222087691566
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = DA955A6833A9
[D][05:18:20][FCTY]Bat         = 3924 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1288
[D][05:18:20][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION =

2025-07-31 20:39:34:361 ==>>  V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3812 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:39:34:503 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:39:34:507 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:39:34:512 ==>> 检测【检测固件版本】
2025-07-31 20:39:34:536 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:39:34:540 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:39:34:546 ==>> 检测【检测蓝牙版本】
2025-07-31 20:39:34:579 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:39:34:585 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:39:34:591 ==>> 检测【检测MoBikeId】
2025-07-31 20:39:34:613 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:39:34:617 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:39:34:621 ==>> 检测【检测蓝牙地址】
2025-07-31 20:39:34:624 ==>> 取到目标值:DA955A6833A9
2025-07-31 20:39:34:646 ==>> 【检测蓝牙地址】通过,【DA955A6833A9】符合目标值【】要求!
2025-07-31 20:39:34:650 ==>> 提取到蓝牙地址:DA955A6833A9
2025-07-31 20:39:34:656 ==>> 检测【BOARD_ID】
2025-07-31 20:39:34:679 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:39:34:686 ==>> 检测【检测充电电压】
2025-07-31 20:39:34:712 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:39:34:718 ==>> 检测【检测VBUS电压1】
2025-07-31 20:39:34:725 ==>> $GBGGA,123938.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,6,2,24,60,,,39,59,,,39,14,,,39,39,,,38,1*70

$GBGSV,6,3,24,42,,,38,40,,,37,13,,,36,16,,,36,1*7D

$GBGSV,6,4,24,7,,,35,38,,,35,1,,,35,9,,,35,1*46

$GBGSV,6,5,24,6,,,35,2,,,34,10,,,33,26,,,32,1*72

$GBGSV,6,6,24,44,,,32,5,,,31,4,,,31,23,,,30,1*72

$GBRMC,123938.506,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123938.506,0.000,743.153,743.153,679.634,2097152,2097152,2097152*69



2025-07-31 20:39:34:744 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:39:34:749 ==>> 检测【检测充电电流】
2025-07-31 20:39:34:778 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:39:34:782 ==>> 检测【检测IMEI】
2025-07-31 20:39:34:785 ==>> 取到目标值:867222087691566
2025-07-31 20:39:34:812 ==>> 【检测IMEI】通过,【867222087691566】符合目标值【】要求!
2025-07-31 20:39:34:816 ==>> 提取到IMEI:867222087691566
2025-07-31 20:39:34:821 ==>> 检测【检测IMSI】
2025-07-31 20:39:34:827 ==>> 取到目标值:460130071539202
2025-07-31 20:39:34:850 ==>> 【检测IMSI】通过,【460130071539202】符合目标值【】要求!
2025-07-31 20:39:34:854 ==>> 提取到IMSI:460130071539202
2025-07-31 20:39:34:858 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:39:34:863 ==>> 取到目标值:460130071539202
2025-07-31 20:39:34:884 ==>> 【校验网络运营商(移动)】通过,【460130071539202】符合目标值【】要求!
2025-07-31 20:39:34:888 ==>> 检测【打开CAN通信】
2025-07-31 20:39:34:892 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:39:34:963 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:39:35:173 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:39:35:177 ==>> 检测【检测CAN通信】
2025-07-31 20:39:35:180 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:39:35:248 ==>> can send success


2025-07-31 20:39:35:278 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:39:35:338 ==>> [D][05:18:21][COMM]read battery soc:255
[D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32803
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:39:35:398 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:39:35:458 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:39:35:462 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:39:35:466 ==>> 检测【关闭CAN通信】
2025-07-31 20:39:35:490 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:39:35:518 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:39:35:563 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:39:35:668 ==>> $GBGGA,123939.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,6,2,24,60,,,39,59,,,39,14,,,39,39,,,38,1*70

$GBGSV,6,3,24,42,,,38,40,,,37,1

2025-07-31 20:39:35:713 ==>> 6,,,36,7,,,35,1*4B

$GBGSV,6,4,24,13,,,35,38,,,35,1,,,35,6,,,35,1*7C

$GBGSV,6,5,24,9,,,34,2,,,33,10,,,33,26,,,32,1*7B

$GBGSV,6,6,24,44,,,32,5,,,31,4,,,31,23,,,30,1*72

$GBRMC,123939.506,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123939.506,0.000,740.570,740.570,677.272,2097152,2097152,2097152*60



2025-07-31 20:39:35:749 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:39:35:753 ==>> 检测【打印IMU STATE】
2025-07-31 20:39:35:756 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:39:35:953 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:39:36:043 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:39:36:048 ==>> 检测【六轴自检】
2025-07-31 20:39:36:053 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:39:36:243 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:39:36:734 ==>> $GBGGA,123940.506,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,40,60,,,39,3,,,39,1*46

$GBGSV,6,2,24,59,,,39,14,,,39,25,,,39,39,,,38,1*71

$GBGSV,6,3,24,40,,,37,42,,,37,16,,,36,7,,,35,1*44

$GBGSV,6,4,24,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,6,5,24,6,,,35,2,,,33,10,,,33,26,,,32,1*75

$GBGSV,6,6,24,5,,,31,44,,,31,4,,,31,23,,,30,1*71

$GBRMC,123940.506,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123940.506,0.000,737.980,737.980,674.903,2097152,2097152,2097152*60



2025-07-31 20:39:37:296 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:39:37:727 ==>> $GBGGA,123941.506,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,40,25,,,40,60,,,39,1*7C

$GBGSV,6,2,24,3,,,39,59,,,39,14,,,39,39,,,38,1*45

$GBGSV,6,3,24,40,,,37,42,,,37,16,,,36,7,,,35,1*44

$GBGSV,6,4,24,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,6,5,24,6,,,34,2,,,33,10,,,33,26,,,32,1*74

$GBGSV,6,6,24,5,,,31,44,,,31,4,,,31,23,,,30,1*71

$GBRMC,123941.506,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123941.506,0.000,737.983,737.983,674.906,2097152,2097152,2097152*64



2025-07-31 20:39:37:952 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:39:38:133 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35584 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,0,4063]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 20:39:38:407 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:39:38:417 ==>> 检测【打印IMU STATE2】
2025-07-31 20:39:38:424 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:39:38:753 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,123942.506,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,6,2,24,60,,,39,59,,,39,14,,,39,39,,,38,1*70

$GBGSV,6,3,24,42,,,38,40,,,37,16,,,36,7,,,35,1*4B

$GBGSV,6,4,24,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,6,5,24,2,,,34,6,,,34,10,,,33,26,,,32,1*73

$GBGSV,6,6,24,5,,,31,44,,,31,4,,,31,23,,,30,1*71

$GBRMC,123942.506,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123942.506,0.000,740.571,740.571,677.273,2097152,2097152,2097152*6D



2025-07-31 20:39:38:967 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:39:38:977 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:39:38:982 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:39:39:054 ==>> 5A A5 02 5A A5 


2025-07-31 20:39:39:159 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:39:39:266 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:39:39:271 ==>> 检测【检测VBUS电压2】
2025-07-31 20:39:39:282 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:39:39:339 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 10


2025-07-31 20:39:39:639 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539202
[D][05:18:25][FCTY]HardwareID  = 867222087691566
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = DA955A6833A9
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11900 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 8, adc = 332
[D][05:18:25][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1       

2025-07-31 20:39:39:744 ==>>   = 3812 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:39:39:809 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:39:40:209 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539202
[D][05:18:26][FCTY]HardwareID  = 867222087691566
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = DA955A6833A9
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11900 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 158
[D][05:18:26][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18

2025-07-31 20:39:40:254 ==>> :26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3812 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:39:40:344 ==>> [D][05:18:26][COMM]msg 0601 loss. last_tick:32797. cur_tick:37800. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 37801


2025-07-31 20:39:40:364 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:39:40:773 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539202
[D][05:18:26][FCTY]HardwareID  = 867222087691566
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = DA955A6833A9
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 131
[D][05:18:27][FCTY]Acckey1 vol = 5528 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C

2025-07-31 20:39:40:862 ==>> 4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3812 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,123944.506,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,59,,,40,24,,,40,1*4F

$GBGSV,7,2,25,25,,,40,60,,,39,14,,,39,39,,,38,1*75

$GBGSV,7,3,25,42,,,38,40,,,37,16,,,36,7,,,35,1*4B

$GBGSV,7,4,25,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,7,5,25,6,,,35,2,,,34,26,,,33,10,,,33,1*73

$GBGSV,7,6,25,44,,,32,5,,,31,4,,,31,34,,,31,1*75

$GBGSV,7,7,25,23,,,30,1*73

$GBRMC,123944.506,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123944.506,0.000,739.085,739.085,675.914,2097152,2097152,2097152*63



2025-07-31 20:39:40:910 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:39:40:920 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:39:40:941 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:39:41:057 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:39:41:199 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:39:41:203 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:39:41:207 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:39:41:252 ==>> 5A A5 04 5A A5 


2025-07-31 20:39:41:357 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:39:41:462 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[D][05:18:27][COMM]report elecbike
[W][05:18:27][P

2025-07-31 20:39:41:495 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:39:41:502 ==>> 检测【打开WIFI(3)】
2025-07-31 20:39:41:508 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:41:567 ==>> ROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm

2025-07-31 20:39:41:672 ==>> _ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D]

2025-07-31 20:39:41:777 ==>> [05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B560ED952A0DBA9E4E0800C10086B55501ABD20ECECB14C3A5F878532804598729AE60227EF6B9A81161CEE23C2099C67BC6E5DDB3602D802688E81C005F6D0D3CF96C2BD6CC7E112DAABC3394C183A0B66A09
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][COMM]read battery soc:255
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:39:41:822 ==>>                                                                                                                                                                                                                                                                       

2025-07-31 20:39:41:867 ==>>                                                                                                                                                                                                                                                       

2025-07-31 20:39:42:523 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:43:560 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:43:650 ==>> $GBGGA,123946.506,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,39,59,,,39,14,,,39,39,,,38,1*70

$GBGSV,7,3,25,42,,,38,40,,,37,16,,,36,7,,,35,1*4B

$GBGSV,7,4,25,13,,,35,38,,,35,1,,,35,9,,,35,1*73

$GBGSV,7,5,25,6,,,35,2,,,34,26,,,33,10,,,33,1*73

$GBGSV,7,6,25,44,,,32,5,,,31,4,,,31,34,,,31,1*75

$GBGSV,7,7,25,23,,,31,1*72

$GBRMC,123946.506,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123946.506,0.000,739.911,739.911,676.669,2097152,2097152,2097152*67

[D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------
[W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
+WIFISCAN:4,0,F42A7D1297A3,-65
+WIFISCAN:4,1,CC057790A640,-74
+WIFISCAN:4,2,CC057790A641,-75
+WIFISCAN:4,3,CC057790A7C1,-79

[D][05:18:29][CAT1]wifi scan report total[4]
[

2025-07-31 20:39:43:755 ==>> D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_

2025-07-31 20:39:43:846 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:39:43:853 ==>> 检测【扩展芯片hw】
2025-07-31 20:39:43:860 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:39:43:865 ==>> audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D]

2025-07-31 20:39:43:966 ==>> [05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][PROT]index:1
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29

2025-07-31 20:39:44:071 ==>> ][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]read battery soc:255
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A3230

2025-07-31 20:39:44:176 ==>> 34380D0A0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:29][GNSS]recv submsg id[3]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. 

2025-07-31 20:39:44:281 ==>> send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
+WIFISCAN:4,0,CC057790A641,-74
+WIFISCAN:4,1,CC057790A640,-74
+WIFISCAN:4,2,CC057790A7C0,-79
+WIFISCAN:4,3,44A1917CAD80,-81

[D][05:18:30][CAT1]wifi scan report total[4]
                                                                                                                                            

2025-07-31 20:39:44:341 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 20:39:44:446 ==>>                                                                                                                                                                                                                                                                              d: 12, ret: 6
[W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:39:44:647 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:39:44:657 ==>> 检测【扩展芯片boot】
2025-07-31 20:39:44:695 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:39:44:699 ==>> 检测【扩展芯片sw】
2025-07-31 20:39:44:732 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:39:44:737 ==>> 检测【检测音频FLASH】
2025-07-31 20:39:44:745 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:39:45:440 ==>> $GBGGA,123944.513,2301.2570530,N,11421.9417856,E,1,17,0.69,77.053,M,-1.770,M,,*52

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.28,0.69,1.08,4*04

$GBGSA,A,3,10,38,44,26,23,,,,,,,,1.28,0.69,1.08,4*0A

$GBGSV,7,1,25,33,69,276,42,14,66,192,39,3,61,190,40,59,52,129,39,1*4B

$GBGSV,7,2,25,6,52,344,35,39,52,8,38,16,52,348,36,24,50,15,41,1*76

$GBGSV,7,3,25,1,48,126,36,9,47,322,35,2,46,238,34,25,43,284,40,1*43

$GBGSV,7,4,25,42,41,164,38,60,41,238,40,13,40,219,36,7,39,176,35,1*45

$GBGSV,7,5,25,40,36,160,37,4,32,112,31,10,31,188,33,38,27,192,35,1*44

$GBGSV,7,6,25,5,22,257,31,44,16,99,32,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,123944.513,A,2301.2570530,N,11421.9417856,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:31][GNSS]HD8040 GPS
[D][05:18:31][GNSS]GPS diff_sec 124010473, report 0x42 frame
$GBGST,123944.513,1.696,0.359,0.334,0.480,1.816,2.607,6.491*73

[D][05:18:31][COMM]Main Task receive event:131
[D][05:18:31][COMM]index:0,power_mode:0xFF
[D][05:18:31][COMM]index:1,sound_mode:0xFF
[D][05:18:31][COMM]index:2,gsensor_mode:0xFF
[D][05:18:31][COMM]index:3,report_freq_mode:0xFF
[D][05:18:31][COMM]index:4,report_period:0xFF
[D][05:18:31][COMM]index:5,normal_reset_mode

2025-07-31 20:39:45:545 ==>> :0xFF
[D][05:18:31][COMM]index:6,normal_reset_period:0xFF
[D][05:18:31][COMM]index:7,spock_over_speed:0xFF
[D][05:18:31][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:31][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:31][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:31][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:31][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:31][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:31][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:31][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:31][COMM]index:16,imu_config_params:0xFF
[D][05:18:31][COMM]index:17,long_connect_params:0xFF
[D][05:18:31][COMM]index:18,detain_mark:0xFF
[D][05:18:31][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:31][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:31][COMM]index:21,mc_mode:0xFF
[D][05:18:31][COMM]index:22,S_mode:0xFF
[D][05:18:31][COMM]index:23,overweight:0xFF
[D][05:18:31][COMM]index:24,standstill_mode:0xFF
[D][05:18:31][COMM]index:25,night_mode:0xFF
[D][05:18:31][COMM]index:26,experiment1:0xFF
[D][05:18:31][COMM]index:27,experiment2:0xFF
[D][05:18:31][COMM]index:28,experiment3:0xFF
[D][05:18:31][COMM]index:29,experiment4:

2025-07-31 20:39:45:650 ==>> 0xFF
[D][05:18:31][COMM]index:30,night_mode_start:0xFF
[D][05:18:31][COMM]index:31,night_mode_end:0xFF
[D][05:18:31][COMM]index:33,park_report_minutes:0xFF
[D][05:18:31][COMM]index:34,park_report_mode:0xFF
[D][05:18:31][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:31][COMM]index:38,charge_battery_para: FF
[D][05:18:31][COMM]index:39,multirider_mode:0xFF
[D][05:18:31][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:31][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:31][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:31][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:31][COMM]index:44,riding_duration_config:0xFF
[D][05:18:31][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:31][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:31][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:31][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:31][COMM]index:49,mc_load_startup:0xFF
[D][05:18:31][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:31][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:31][COMM]index:52,traffic_mode:0xFF
[D][05:18:31][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:31][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:31][CO

2025-07-31 20:39:45:755 ==>> MM]index:55,wheel_alarm_play_switch:255
[D][05:18:31][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:31][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:31][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:31][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:31][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:31][COMM]index:63,experiment5:0xFF
[D][05:18:31][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:31][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:31][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:31][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:31][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:31][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:31][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:31][COMM]index:72,experiment6:0xFF
[D][05:18:31][COMM]index:73,experiment7:0xFF
[D][05:18:31][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:31][COMM]index:75,zero_value_from_server:-1
[D][05:18:31][COMM]index:76,multirider_threshold:255
[D][05:18:31][COMM]index:77,experiment8:255
[D][05:18:31][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:31][COMM]index:79,temp_park_tail_light_twinkle_durati

2025-07-31 20:39:45:860 ==>> on:255
[D][05:18:31][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:31][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:31][COMM]index:83,loc_report_interval:255
[D][05:18:31][COMM]index:84,multirider_threshold_p2:255
[D][05:18:31][COMM]index:85,multirider_strategy:255
[D][05:18:31][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:31][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:31][COMM]index:90,weight_param:0xFF
[D][05:18:31][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:31][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:31][COMM]index:95,current_limit:0xFF
[D][05:18:31][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:31][COMM]index:100,location_mode:0xFF

[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:31][COMM]Main Task receive event:131 finished processing
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235

2025-07-31 20:39:45:965 ==>> ,2<<<<<
$GBGGA,123945.013,2301.2574193,N,11421.9414500,E,1,16,0.92,76.309,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,41,14,66,192,39,3,61,190,40,59,52,129,39,1*48

$GBGSV,7,2,25,6,52,344,34,39,52,8,38,16,52,348,36,24,50,15,40,1*76

$GBGSV,7,3,25,1,48,126,36,9,47,322,35,2,46,238,34,25,43,284,40,1*43

$GBGSV,7,4,25,42,41,164,38,60,41,238,39,13,40,219,36,7,39,176,35,1*4B

$GBGSV,7,5,25,40,36,160,37,4,32,112,31,10,31,188,33,38,27,192,35,1*44

$GBGSV,7,6,25,5,22,257,31,44,16,99,32,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,2,1,06,33,69,276,37,39,52,8,39,24,50,15,40,25,43,284,41,5*48

$GBGSV,2,2,06,42,41,164,39,40,36,160,38,5*73

$GBRMC,123945.013,A,2301.2574193,N,11421.9414500,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123945.013,1.573,0.776,0.601,1.056,1.504,1.979,5.144*7E

[D][05:18:31][COMM]read battery soc:255


2025-07-31 20:39:45:995 ==>>                                                             

2025-07-31 20:39:46:594 ==>> +WIFISCAN:4,0,603A7CF67DD4,-79
+WIFISCAN:4,1,44A1917CAD81,-81
+WIFISCAN:4,2,CC057790A7C0,-81
+WIFISCAN:4,3,44A1917CAD80,-81

[D][05:18:32][CAT1]wifi scan report total[4]
$GBGGA,123946.000,2301.2577125,N,11421.9412998,E,1,16,0.92,76.353,M,-1.770,M,,*51

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,39,3,61,190,40,59,52,129,39,1*4B

$GBGSV,7,2,25,6,52,344,35,39,52,8,38,16,52,348,36,24,50,15,40,1*77

$GBGSV,7,3,25,1,48,126,36,9,47,322,35,2,46,238,34,25,43,284,40,1*43

$GBGSV,7,4,25,42,41,164,38,60,41,238,40,13,40,219,35,7,39,176,35,1*46

$GBGSV,7,5,25,40,36,160,37,4,32,112,32,10,31,188,33,38,27,192,35,1*47

$GBGSV,7,6,25,5,22,257,31,44,16,99,32,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,69,276,40,39,52,8,40,24,50,15,41,25,43,284,40,5*40

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123946.000,A,2301.2577125,N,11421.9412998,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123946.000,2.183,0.474,0.381,0.645,1.800,2.108,4.700*7

2025-07-31 20:39:46:699 ==>> D

[D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]Send Data To Server[198][198] ... ->:
0063B98D113311331133113311331B88

2025-07-31 20:39:46:774 ==>> B3B84D5CFABEB249D2A26675023FA3E1B63F60A84384D453AF6923D3D9126FB2B758BC615ACBC84493CBCC6D94F568F683D98A5D3ED4EFF554A78E9BDA1DD2D28E82D03029F309272275708556EF92EF1E7740
[D][05:18:32][GNSS]recv submsg id[3]
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 20:39:46:819 ==>>                                                                                                   

2025-07-31 20:39:47:421 ==>> $GBGGA,123947.000,2301.2578438,N,11421.9411930,E,1,16,0.92,76.511,M,-1.770,M,,*57

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,39,3,61,190,40,59,52,129,39,1*4B

$GBGSV,7,2,25,6,52,344,35,39,52,8,38,16,52,348,36,24,50,15,40,1*77

$GBGSV,7,3,25,1,48,126,36,9,47,322,35,2,46,238,34,25,43,284,40,1*43

$GBGSV,7,4,25,42,41,164,38,60,41,238,39,13,40,219,35,7,39,176,35,1*48

$GBGSV,7,5,25,40,36,160,37,4,32,112,32,10,31,188,33,38,27,192,35,1*47

$GBGSV,7,6,25,5,22,257,31,44,16,99,31,26,12,51,33,23,3,253,31,1*75

$GBGSV,7,7,25,34,,,31,1*74

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
$GBGSV,3,1,10,33,69,276,41,39,52,8,40,24,50,15,41,25,43,284,40,5*41

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,35,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123947.000,A,2301.2578438,N,11421.9411930,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123947.000,2.236,0.296,0.251,0.406,1.778,2.015,4.273*7E

[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[

2025-07-31 20:39:47:466 ==>> 856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:33][COMM]accel parse set 0
[D][05:18:33][COMM][Audio].l:[1012].open hexlog save
[D][05:18:33][COMM]read battery soc:255


2025-07-31 20:39:47:812 ==>> [D][05:18:34][COMM]45310 imu init OK


2025-07-31 20:39:48:377 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
$GBGGA,123948.000,2301.2579319,N,11421.9411603,E,1,16,0.92,76.507,M,-1.770,M,,*55

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,39,3,61,190,40,59,52,129,39,1*4B

$GBGSV,7,2,25,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*77

$GBGSV,7,3,25,1,48,126,35,9,47,322,35,2,46,238,34,25,43,284,40,1*40

$GBGSV,7,4,25,42,41,164,38,60,41,238,39,13,40,219,36,7,39,176,35,1*4B

$GBGSV,7,5,25,40,36,160,37,4,32,112,31,10,31,188,33,38,27,192,35,1*44

$GBGSV,7,6,25,5,22,257,31,44,16,99,31,26,12,51,33,23,3,253,31,1*75

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,35,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123948.000,A,2301.2579319,N,11421.9411603,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,123948.000,1.980,0.280,0.240,0.381,1.575,1.780,3.858*78



2025-07-31 20:39:48:940 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 20:39:49:377 ==>> $GBGGA,123949.000,2301.2579997,N,11421.9411855,E,1,16,0.92,76.548,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,40,3,61,190,40,59,52,129,39,1*45

$GBGSV,7,2,25,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*77

$GBGSV,7,3,25,1,48,126,35,9,47,322,35,2,46,238,34,25,43,284,40,1*40

$GBGSV,7,4,25,42,41,164,38,60,41,238,40,13,40,219,36,7,39,176,35,1*45

$GBGSV,7,5,25,40,36,160,37,4,32,112,31,10,31,188,33,38,27,192,35,1*44

$GBGSV,7,6,25,5,22,257,31,44,16,99,32,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,31,23,,,30,5*70

$GBRMC,123949.000,A,2301.2579997,N,11421.9411855,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123949.000,1.927,0.332,0.280,0.450,1.514,1.691,3.600*7E

[D][05:18:35][COMM]read battery soc:255


2025-07-31 20:39:49:811 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:39:49:825 ==>> 检测【打开喇叭声音】
2025-07-31 20:39:49:856 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:39:50:584 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:36][COMM]read file, len:15228, num:4
[D][05:18:36][COMM]--->crc16:0x419c
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]f:[ec800m_audi

2025-07-31 20:39:50:618 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:39:50:625 ==>> 检测【打开大灯控制】
2025-07-31 20:39:50:635 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:39:50:690 ==>> o_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
$GBGGA,123950.000,2301.2580536,N,11421.9411593,E,1,16,0.92,76.555,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,40,3,61,190,40,59,52,129,39,1*45

$GBGSV,7,2,25,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*77

$GBGSV,7,3,25,1,48,126,35,9,47,322,35,2,46,238,34,25,43,284,40,1*40

$GBGSV,7,4,25,42,41,164,38,60,41,238,39,13,40,219,36,7,39,176,35,1*4B

$GBGSV,7,5,25,40,36,160,37,4,32,112,32,10,31,188,33,38,27,192,35,1*47

$GBGSV,7,6,25,5,22,257,31,44,16,99,3

2025-07-31 20:39:50:794 ==>> 2,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,32,1*77

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,31,23,,,30,5*70

$GBRMC,123950.000,A,2301.2580536,N,11421.9411593,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123950.000,1.872,0.296,0.252,0.402,1.458,1.613,3.390*7F

[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6,

2025-07-31 20:39:50:869 ==>>  len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:39:50:959 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:39:51:157 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:39:51:165 ==>> 检测【关闭仪表供电3】
2025-07-31 20:39:51:175 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:39:51:409 ==>> $GBGGA,123951.000,2301.2580742,N,11421.9411363,E,1,16,0.92,76.614,M,-1.770,M,,*53

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,25,33,69,276,42,14,66,192,39,3,61,190,40,59,52,129,39,1*4B

$GBGSV,7,2,25,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*77

$GBGSV,7,3,25,1,48,126,35,9,47,322,35,2,46,238,34,25,43,284,40,1*40

$GBGSV,7,4,25,42,41,164,38,60,41,238,40,13,40,219,36,7,39,176,35,1*45

$GBGSV,7,5,25,40,36,160,37,4,32,112,31,10,31,188,33,38,27,192,35,1*44

$GBGSV,7,6,25,5,22,257,31,44,16,99,32,26,12,51,33,23,3,253,31,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

$GBGSV,3,2,10,42,41,164,41,40,36,160,37,38,27,192,34,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,31,23,,,30,5*70

$GBRMC,123951.000,A,2301.2580742,N,11421.9411363,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,123951.000,1.849,0.340,0.286,0.460,1.427,1.565,3.232*72

[D][05:18:37][COMM]read battery soc:255
[W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<

2025-07-31 20:39:51:439 ==>> <<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 20:39:51:701 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:39:51:710 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:39:51:720 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:39:51:817 ==>> [D][05:18:38][PROT]CLEAN,SEND:1
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3F9C0D9AA5053D73587F6057101E596096CA78FD

2025-07-31 20:39:51:892 ==>> E7D63B849F11935BCAAC4110ED6897E7A4566ED90913278D6062D94D55700502CF5ACF965F8D60B2FC7E316DEC486E2FE84BDFA85AD98E416E33444B3EA77
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]


2025-07-31 20:39:51:952 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:39:52:250 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:39:52:256 ==>> 检测【读大灯电压】
2025-07-31 20:39:52:265 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:39:52:390 ==>> $GBGGA,123952.000,2301.2580903,N,11421.9411134,E,1,16,0.92,76.527,M,-1.770,M,,*58

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,26,33,69,276,42,14,66,192,40,3,61,190,40,59,52,129,39,1*46

$GBGSV,7,2,26,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*74

$GBGSV,7,3,26,1,48,126,35,9,47,322,35,2,46,238,34,25,43,284,40,1*43

$GBGSV,7,4,26,42,41,164,38,60,41,238,40,13,40,219,36,7,39,176,35,1*46

$GBGSV,7,5,26,8,36,207,,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,36,5,22,257,31,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123952.000,A,2301.2580903,N,11421.9411134,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123952.000,1.873,0.302,0.257,0.410,1.433,1.556,3.117*74



2025-07-31 20:39:52:495 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[33340]


2025-07-31 20:39:52:800 ==>> 【读大灯电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:39:52:810 ==>> 检测【关闭大灯控制2】
2025-07-31 20:39:52:834 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:39:52:941 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:39:53:076 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:53:098 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:39:53:104 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:39:53:113 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:39:53:380 ==>> $GBGGA,123953.000,2301.2581050,N,11421.9410853,E,1,16,0.92,76.455,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,06,39,16,24,09,25,42,13,07,40,1.97,0.92,1.75,4*0E

$GBGSA,A,3,10,38,44,26,,,,,,,,,1.97,0.92,1.75,4*01

$GBGSV,7,1,26,33,69,276,42,14,66,192,40,3,61,190,40,59,52,129,40,1*48

$GBGSV,7,2,26,6,52,344,35,39,52,8,39,16,52,348,36,24,50,15,41,1*74

$GBGSV,7,3,26,1,48,126,36,9,47,322,35,2,46,238,34,25,43,284,40,1*40

$GBGSV,7,4,26,42,41,164,38,60,41,238,40,13,40,219,36,7,39,176,35,1*46

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,36,5,22,257,31,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,41,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123953.000,A,2301.2581050,N,11421.9410853,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123953.000,1.784,0.255,0.223,0.346,1.361,1.475,2.967*78

[W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[139]
[D][05:18:39][COMM]read battery soc:255


2025-07-31 20:39:53:646 ==>> 【关大灯控制后读大灯电压】通过,【139mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:39:53:653 ==>> 检测【打开WIFI(4)】
2025-07-31 20:39:53:661 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:39:53:874 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:39:53:984 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:39:53:995 ==>> 检测【EC800M模组版本】
2025-07-31 20:39:54:026 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:39:54:159 ==>> [D][05:18:40][COMM]51569 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:39:54:459 ==>> $GBGGA,123954.000,2301.2581197,N,11421.9410863,E,1,21,0.71,76.489,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,42,14,66,192,39,3,62,190,40,6,52,344,35,1*74

$GBGSV,7,2,26,39,52,8,39,16,52,348,36,59,50,128,39,24,50,15,40,1*49

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,39,1*47

$GBGSV,7,4,26,25,43,284,40,42,41,164,38,13,40,219,36,7,39,176,35,1*42

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,32,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123954.000,A,2301.2581197,N,11421.9410863,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,123954.000,1.746,0.218,0.204,0.308,1.326,1.430,2.854*74

[D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132

2025-07-31 20:39:54:489 ==>> 


2025-07-31 20:39:54:542 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:39:54:552 ==>> 检测【配置蓝牙地址】
2025-07-31 20:39:54:569 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:39:54:594 ==>> +WIFISCAN:4,0,CC057790A641,-75
+WIFISCAN:4,1,CC057790A7C0,-78
+WIFISCAN:4,2,CC057790A7C1,-78
+WIFISCAN:4,3,44A1917CAD81,-80

[D][05:18:41][CAT1]wifi scan report total[4]


2025-07-31 20:39:54:700 ==>> [W][05:18:41][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:39:54:744 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:DA955A6833A9>】
2025-07-31 20:39:54:960 ==>> recv ble 1
recv ble 2
ble set mac ok :da,95,5a,68,33,a9
enable filters ret : 0

2025-07-31 20:39:55:090 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:39:55:101 ==>> 检测【BLETEST】
2025-07-31 20:39:55:115 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:39:55:126 ==>> [D][05:18:41][COMM]52580 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:39:55:156 ==>> 4A A4 01 A4 4A 


2025-07-31 20:39:55:261 ==>> recv ble 1
recv ble 2
<BSJ*MAC:DA955A6833A9*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9DA955A6833A999999OVER 150


2025-07-31 20:39:55:367 ==>> $GBGGA,123955.000,2301.2581338,N,11421.9410727,E,1,21,0.71,76.452,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,42,14,66,192,39,3,62,190,40,6,52,344,35,1*74

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,36,60,43,241,40,1*4A

$GBGSV,7,4,26,25,43,284,40,42,41,164,38,13,40,219,36,7,39,176,35,1*42

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,32,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,30,5*73

$GBRMC,123955.000,A,2301.2581338,N,11421.9410727,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123955.000,1.751,0.201,0.189,0.284,1.323,1.419,2.778*77

[D][05:18:41][COMM]read battery soc:255


2025-07-31 20:39:55:412 ==>>                                       

2025-07-31 20:39:55:702 ==>> [D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:39:56:110 ==>> [D][05:18:42][COMM]53592 imu init OK


2025-07-31 20:39:56:189 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:39:56:197 ==>> 该项需要延时执行
2025-07-31 20:39:56:380 ==>> $GBGGA,123956.000,2301.2581482,N,11421.9410742,E,1,21,0.71,76.461,M,-1.770,M,,*55

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,344,35,1*77

$GBGSV,7,2,26,39,52,8,39,16,52,348,36,59,50,128,39,24,50,15,40,1*49

$GBGSV,7,3,26,2,48,239,33,9,47,322,35,1,46,125,35,60,43,241,39,1*40

$GBGSV,7,4,26,25,43,284,40,42,41,164,37,13,40,219,36,7,39,176,35,1*4D

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,32,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,43,39,52,8,40,24,50,15,41,25,43,284,40,5*43

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,31,5*72

$GBRMC,123956.000,A,2301.2581482,N,11421.9410742,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123956.000,1.678,0.235,0.217,0.333,1.264,1.354,2.673*76



2025-07-31 20:39:57:030 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[D][05:18:43][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[

2025-07-31 20:39:57:120 ==>> 17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B35EEE4FF302A11F6DD294D8C8F9B6B65862F437D22AC06CC622B81BA66230F6F33E6A8BAB76E78FD0D6EACEF6E18C53EAFC6B8EF3E6F37F80A799360ADC8638D5CBB1D7D6ED8F430452BA5C83E1AEB610B828
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 20:39:57:377 ==>> $GBGGA,123957.000,2301.2581582,N,11421.9410870,E,1,21,0.71,76.507,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,39,16,52,348,36,59,50,128,39,24,50,15,40,1*49

$GBGSV,7,3,26,2,48,239,34,9,47,322,34,1,46,125,35,60,43,241,39,1*46

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,51,32,1*42

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,32,23,,,31,5*72

$GBRMC,123957.000,A,2301.2581582,N,11421.9410870,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123957.000,1.655,0.238,0.220,0.338,1.242,1.327,2.604*7A

[D][05:18:43][COMM]read battery soc:255


2025-07-31 20:39:58:377 ==>> $GBGGA,123958.000,2301.2581633,N,11421.9410912,E,1,21,0.71,76.575,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,34,1,46,125,35,60,43,241,39,1*46

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,31,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,31,5*72

$GBRMC,123958.000,A,2301.2581633,N,11421.9410912,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123958.000,1.590,0.245,0.225,0.348,1.188,1.269,2.512*7D



2025-07-31 20:39:59:396 ==>> $GBGGA,123959.000,2301.2581597,N,11421.9410966,E,1,21,0.71,76.595,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,39,1*47

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,32,44,16,99,32,26,12,51,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,31,5*72

$GBRMC,123959.000,A,2301.2581597,N,11421.9410966,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123959.000,1.751,0.230,0.212,0.329,1.307,1.380,2.560*74

[D][05:18:45][COMM]read battery soc:255


2025-07-31 20:40:00:381 ==>> $GBGGA,124000.000,2301.2581537,N,11421.9410950,E,1,21,0.71,76.610,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,39,1*47

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,33,1*7D

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,51,33,1*43

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,51,32,23,,,31,5*72

$GBRMC,124000.000,A,2301.2581537,N,11421.9410950,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124000.000,1.935,0.272,0.248,0.387,1.437,1.503,2.625*7C



2025-07-31 20:40:01:397 ==>> $GBGGA,124001.000,2301.2581442,N,11421.9410842,E,1,21,0.71,76.657,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,34,1*77

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,33,9,47,322,34,1,46,125,35,60,43,241,40,1*4F

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,33,1*7D

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,51,33,1*43

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,51,31,23,,,31,5*71

$GBRMC,124001.000,A,2301.2581442,N,11421.9410842,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124001.000,1.962,0.275,0.252,0.390,1.453,1.515,2.607*70

[D][05:18:47][COMM]read battery soc:255


2025-07-31 20:40:02:373 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063

2025-07-31 20:40:02:478 ==>> B982113311331133113311331B88B5D8A7488AD66D3118B5794B979726B5B427837F3F3957ABFD5B749ECCD03A55C7CBC143235E9F825DE21C14E1826BD795A29FC272C029BCB2C5AFF44A19A3772240694E719CAB1A66794F413609ECAD69CDE2
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
$GBGGA,124002.000,2301.2581493,N,11421.9410892,E,1,21,0.71,76.681,M,-1.770,M,,*54

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,34,1*77

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,40,1*49

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,34,1*41

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,33,

2025-07-31 20:40:02:538 ==>> 1*7D

$GBGSV,7,6,26,38,27,192,35,5,22,257,32,44,16,99,32,26,12,52,33,1*43

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,52,31,23,,,30,5*73

$GBRMC,124002.000,A,2301.2581493,N,11421.9410892,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,124002.000,2.106,0.242,0.224,0.342,1.551,1.608,2.659*77



2025-07-31 20:40:03:401 ==>> $GBGGA,124003.000,2301.2581556,N,11421.9410928,E,1,21,0.71,76.724,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,39,1*47

$GBGSV,7,4,26,25,43,284,40,42,41,164,37,13,40,219,35,7,39,176,35,1*4E

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,33,1*7D

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,52,34,1*47

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,34,44,16,99,34,5*47

$GBGSV,3,3,10,26,12,52,31,23,,,30,5*73

$GBRMC,124003.000,A,2301.2581556,N,11421.9410928,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,124003.000,1.931,0.269,0.246,0.380,1.428,1.485,2.533*7D

[D][05:18:49][COMM]read battery soc:255


2025-07-31 20:40:04:399 ==>> $GBGGA,124004.000,2301.2581671,N,11421.9410943,E,1,21,0.71,76.714,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,50,15,40,1*48

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,36,60,43,241,39,1*44

$GBGSV,7,4,26,25,43,284,40,42,41,164,37,13,40,219,35,7,39,176,35,1*4E

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,52,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,50,15,41,25,43,284,40,5*42

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,52,31,23,,,30,5*73

$GBRMC,124004.000,A,2301.2581671,N,11421.9410943,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124004.000,2.013,0.259,0.238,0.368,1.483,1.536,2.554*75



2025-07-31 20:40:05:394 ==>> $GBGGA,124005.000,2301.2581781,N,11421.9410976,E,1,21,0.71,76.714,M,-1.770,M,,*55

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,39,6,52,345,35,1*78

$GBGSV,7,2,26,39,52,8,39,16,52,348,36,59,50,128,39,24,49,15,40,1*41

$GBGSV,7,3,26,2,48,239,34,9,47,322,35,1,46,125,35,60,43,241,40,1*49

$GBGSV,7,4,26,25,43,284,39,42,41,164,38,13,40,219,36,7,39,176,35,1*4C

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,32,10,31,188,33,1*7E

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,32,26,12,52,33,1*40

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,49,15,41,25,43,284,40,5*4A

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,52,31,23,,,30,5*73

$GBRMC,124005.000,A,2301.2581781,N,11421.9410976,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124005.000,1.753,0.241,0.223,0.342,1.295,1.350,2.377*7F

[D][05:18:51][COMM]read battery soc:255


2025-07-31 20:40:06:193 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:40:06:205 ==>> 检测【检测WiFi结果】
2025-07-31 20:40:06:219 ==>> WiFi信号:【CC057790A640】,信号值:-77
2025-07-31 20:40:06:244 ==>> WiFi信号:【44A1917CAD80】,信号值:-78
2025-07-31 20:40:06:252 ==>> WiFi信号:【CC057790A7C0】,信号值:-81
2025-07-31 20:40:06:273 ==>> WiFi信号:【F42A7D1297A3】,信号值:-65
2025-07-31 20:40:06:285 ==>> WiFi信号:【CC057790A641】,信号值:-75
2025-07-31 20:40:06:303 ==>> WiFi信号:【CC057790A7C1】,信号值:-79
2025-07-31 20:40:06:314 ==>> WiFi信号:【603A7CF67DD4】,信号值:-79
2025-07-31 20:40:06:326 ==>> WiFi信号:【44A1917CAD81】,信号值:-81
2025-07-31 20:40:06:355 ==>> WiFi数量【8】, 最大信号值:-65
2025-07-31 20:40:06:380 ==>> 检测【检测GPS结果】
2025-07-31 20:40:06:393 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 20:40:06:406 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【14】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【59】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【39】,信号值:【38】
北斗星号:【16】,信号值:【36】
北斗星号:【24】,信号值:【41】
北斗星号:【1】,信号值:【36】
北斗星号:【9】,信号值:【35】
北斗星号:【25】,信号值:【40】
北斗星号:【42】,信号值:【38】
北斗星号:【60】,信号值:【40】
北斗星号:【13】,信号值:【36】
北斗星号:【7】,信号值:【35】
北斗星号:【40】,信号值:【37】
北斗星号:【38】,信号值:【35】

2025-07-31 20:40:06:429 ==>> 检测【CSQ强度】
2025-07-31 20:40:06:442 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:40:06:454 ==>> $GBGGA,124006.000,2301.2581905,N,11421.9410970,E,1,21,0.71,76.706,M,-1.770,M,,*51

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,34,1*77

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,49,15,40,1*40

$GBGSV,7,3,26,2,48,239,34,9,47,322,34,1,46,125,35,60,43,241,39,1*46

$GBGSV,7,4,26,25,43,284,40,42,41,164,37,13,40,219,35,7,39,176,35,1*4E

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,33,1*7D

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,31,26,12,52,33,1*43

$GBGSV,7,7,26,23,3,253,31,34,,,31,1*73

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,49,15,41,25,43,284,40,5*4A

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,52,31,23,,,30,5*73

$GBRMC,124006.000,A,2301.2581905,N,11421.9410970,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124006.000,1.719,0.284,0.260,0.401,1.268,1.321,2.333*78

[W][05:18:52][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 12
[D][05:18:52][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:

2025-07-31 20:40:06:467 ==>> 52][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:52][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:40:06:513 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 20:40:06:520 ==>> 检测【关闭GSM联网】
2025-07-31 20:40:06:531 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:40:06:738 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:53][COMM]GSM test
[D][05:18:53][COMM]GSM test disable


2025-07-31 20:40:06:805 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:40:06:812 ==>> 检测【4G联网测试】
2025-07-31 20:40:06:822 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:40:07:810 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:53][COMM]Main Task receive event:14
[D][05:18:53][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955133, allstateRepSeconds = 0
[D][05:18:53][COMM]index:0,power_mode:0xFF
[D][05:18:53][COMM]index:1,sound_mode:0xFF
[D][05:18:53][COMM]index:2,gsensor_mode:0xFF
[D][05:18:53][COMM]index:3,report_freq_mode:0xFF
[D][05:18:53][COMM]index:4,report_period:0xFF
[D][05:18:53][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:53][COMM]index:6,normal_reset_period:0xFF
[D][05:18:53][COMM]index:7,spock_over_speed:0xFF
[D][05:18:53][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:53][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:53][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:53][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:53][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:53][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:53][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:53][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:53][COMM]index:16,imu_config_params:0xFF
[D][05:18:53][COMM]index:17,long_connect_params:0xFF
[D][05:18:53][COMM]index:18,

2025-07-31 20:40:07:916 ==>> detain_mark:0xFF
[D][05:18:53][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:53][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:53][COMM]index:21,mc_mode:0xFF
[D][05:18:53][COMM]index:22,S_mode:0xFF
[D][05:18:53][COMM]index:23,overweight:0xFF
[D][05:18:53][COMM]index:24,standstill_mode:0xFF
[D][05:18:53][COMM]index:25,night_mode:0xFF
[D][05:18:53][COMM]index:26,experiment1:0xFF
[D][05:18:53][COMM]index:27,experiment2:0xFF
[D][05:18:53][COMM]index:28,experiment3:0xFF
[D][05:18:53][COMM]index:29,experiment4:0xFF
[D][05:18:53][COMM]index:30,night_mode_start:0xFF
[D][05:18:53][COMM]index:31,night_mode_end:0xFF
[D][05:18:53][COMM]index:33,park_report_minutes:0xFF
[D][05:18:53][COMM]index:34,park_report_mode:0xFF
[D][05:18:53][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:53][COMM]index:38,charge_battery_para: FF
[D][05:18:53][COMM]index:39,multirider_mode:0xFF
[D][05:18:53][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:53][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:53][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:53][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:53][COMM]index:44,riding_duration_config:0xFF
[D][05:18:53

2025-07-31 20:40:08:021 ==>> ][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:53][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:53][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:53][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:53][COMM]index:49,mc_load_startup:0xFF
[D][05:18:53][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:53][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:53][COMM]index:52,traffic_mode:0xFF
[D][05:18:53][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:53][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:53][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:53][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:53][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:53][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:53][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:53][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:53][COMM]index:63,experiment5:0xFF
[D][05:18:53][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:53][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:53][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:53][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:53][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:53]

2025-07-31 20:40:08:126 ==>> [COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:53][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:53][COMM]index:72,experiment6:0xFF
[D][05:18:53][COMM]index:73,experiment7:0xFF
[D][05:18:53][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:53][COMM]index:75,zero_value_from_server:-1
[D][05:18:53][COMM]index:76,multirider_threshold:255
[D][05:18:53][COMM]index:77,experiment8:255
[D][05:18:53][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:53][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:53][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:53][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:53][COMM]index:83,loc_report_interval:255
[D][05:18:53][COMM]index:84,multirider_threshold_p2:255
[D][05:18:53][COMM]index:85,multirider_strategy:255
[D][05:18:53][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:53][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:53][COMM]index:90,weight_param:0xFF
[D][05:18:53][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:53][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:53][COMM]index:95,current_limit:0xFF
[D][05:18:53][COMM]index:97,panel display th

2025-07-31 20:40:08:231 ==>> reshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:53][COMM]index:100,location_mode:0xFF

[W][05:18:53][PROT]remove success[1629955133],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:53][PROT]add success [1629955133],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:8
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900009]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[294]
[D][05:18:53][CAT1]gsm read msg

2025-07-31 20:40:08:336 ==>>  sub id: 13
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:53][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:53][CAT1]exec over: func id: 13, ret: 21
[D][05:18:53][M2M ]get csq[24]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:53][CAT1]Send Data To Server[294][297] ... ->:
0093B98E113311331133113311331B88B17976E2A62C4E9AED7B9AB16B3CD6CE49B3CC1AD29FC2B9A8A20D1E0161C7209C331876B4593060227F59C7DB92E9E9DC5A02EB313804B9FECA1BF60161975D2CC28E239B87819EE841F3F4F6C15C59194FB9D7026D807EA52A550726DDB7EAFF0346F4C1CF929E26188A92E6D3707CACCF609DED8FFC3EDA7B96EACA8909B227AA1C
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch 

2025-07-31 20:40:08:441 ==>> to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]
$GBGGA,124007.000,2301.2581926,N,11421.9410912,E,1,21,0.71,76.700,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,60,1.57,0.71,1.40,4*03

$GBGSA,A,3,25,42,13,07,40,10,38,44,26,,,,1.57,0.71,1.40,4*06

$GBGSV,7,1,26,33,69,276,41,14,66,192,39,3,62,190,40,6,52,345,35,1*76

$GBGSV,7,2,26,39,52,8,38,16,52,348,36,59,50,128,39,24,49,15,40,1*40

$GBGSV,7,3,26,2,48,239,33,9,47,322,35,1,46,125,35,60,43,241,39,1*40

$GBGSV,7,4,26,25,43,284,39,42,41,164,37,13,40,219,35,7,39,176,35,1*40

$GBGSV,7,5,26,8,36,207,33,40,36,160,37,4,32,112,31,10,31,188,32,1*7C

$GBGSV,7,6,26,38,27,192,35,5,22,257,31,44,16,99,31,26,12,52,33,1*43

$GBGSV,7,7,26,23,3,253,30,34,,,31,1*72

$GBGSV,3,1,10,33,69,276,42,39,52,8,40,24,49,15,41,25,43,284,40,5*4A

$GBGSV,3,2,10,42,41,164,40,40,36,160,37,38,27,192,35,44,16,99,34,5*46

$GBGSV,3,3,10,26,12,52,31,23,,,29,5*7B

$GBRMC,124007.000,A,2301.2581926,N

2025-07-31 20:40:08:546 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:40:08:858 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:40:08:871 ==>> 检测【关闭GPS】
2025-07-31 20:40:08:897 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:40:09:211 ==>> [D][05:18:55][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]stop event:8
[D][05:18:55][GNSS]GPS stop. ret=0
[D][05:18:55][GNSS]all continue location stop
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all sing location stop
[D][05:18:55][CAT1]gsm read msg sub id: 24
[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 24, ret: 6
[D][05:18:55][CAT1]sub id: 24, ret: 6



2025-07-31 20:40:09:301 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:40:09:409 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:40:09:416 ==>> 检测【清空消息队列2】
2025-07-31 20:40:09:428 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:40:09:516 ==>> [D][05:18:55][GNSS]recv submsg id[1]
[D][05:18:55][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:55][GNSS]location stop evt done evt


2025-07-31 20:40:09:621 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:56][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:40:09:753 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:40:09:761 ==>> 检测【轮动检测】
2025-07-31 20:40:09:775 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:40:09:846 ==>> 3A A3 01 00 A3 


2025-07-31 20:40:09:951 ==>> OFF_OUT1
OVER 150


2025-07-31 20:40:10:011 ==>> [D][05:18:56][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:40:10:255 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:40:10:347 ==>> 3A A3 01 01 A3 


2025-07-31 20:40:10:452 ==>> ON_OUT1
OVER 150


2025-07-31 20:40:10:573 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:40:10:580 ==>> 检测【关闭小电池】
2025-07-31 20:40:10:590 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:40:10:649 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:40:10:888 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:40:10:901 ==>> 检测【进入休眠模式】
2025-07-31 20:40:10:927 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:40:11:015 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:40:11:120 ==>> [D][05:18:57][COMM]Main Task receive event:28
[D][05:18:57][COMM]main task tmp_sleep_event = 8
[D][05:18:57][COMM]prepare to sleep
[D][05:18:57][CAT1]gsm read msg sub id: 12
[D][05:18:57][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:40:11:319 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:40:11:936 ==>> [D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 6
[D][05:18:58][M2M ]tcpclient close[4]
[D][05:18:58][SAL ]Cellular task submsg id[12]
[D][05:18:58][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:18:58][CAT1]gsm read msg sub id: 9
[D][05:18:58][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 9, ret: 6
[D][05:18:58][CAT1]sub id: 9, ret: 6

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:58][SAL ]socket close ind. id[4]
[D][05:18:58][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:58][COMM]1x1 frm_can_tp_send ok
[D][05:18:58][CAT1]pdpdeact urc len[22]


2025-07-31 20:40:12:236 ==>> [E][05:18:58][COMM]1x1 rx timeout
[D][05:18:58][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:40:12:761 ==>> [E][05:18:59][COMM]1x1 rx timeout
[E][05:18:59][COMM]1x1 tp timeout
[E][05:18:59][COMM]1x1 error -3.
[W][05:18:59][COMM]CAN STOP!
[D][05:18:59][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:18:59][COMM]------------ready to Power off Acckey 1------------
[D][05:18:59][COMM]------------ready to Power off Acckey 2------------
[D][05:18:59][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:18:59][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1285
[D][05:18:59][COMM]bat sleep fail, reason:-1
[D][05:18:59][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:18:59][COMM]accel parse set 0
[D][05:18:59][COMM]imu rest ok. 70125
[D][05:18:59][COMM]imu sleep 0
[W][05:18:59][COMM]now sleep


2025-07-31 20:40:13:010 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:40:13:021 ==>> 检测【检测33V休眠电流】
2025-07-31 20:40:13:045 ==>> 开始33V电流采样
2025-07-31 20:40:13:057 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:13:112 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:40:14:116 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:40:14:162 ==>> Current33V:????:18.13

2025-07-31 20:40:14:623 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:14:630 ==>> 【检测33V休眠电流】通过,【18.13uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:40:14:641 ==>> 该项需要延时执行
2025-07-31 20:40:16:633 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:40:16:645 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:40:16:669 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:40:16:769 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1913mV
Get AD_V7 1097mV
OVER 150


2025-07-31 20:40:17:676 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:40:17:685 ==>> 检测【打开小电池2】
2025-07-31 20:40:17:693 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:40:17:762 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:40:17:967 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:40:17:975 ==>> 该项需要延时执行
2025-07-31 20:40:18:470 ==>> 此处延时了:【500】毫秒
2025-07-31 20:40:18:483 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:40:18:497 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:40:18:561 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:40:18:762 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:40:18:771 ==>> 该项需要延时执行
2025-07-31 20:40:19:249 ==>> [D][05:19:05][COMM]------------ready to Power on Acckey 1------------
[D][05:19:05][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:05][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:05][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:05][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:05][COMM]----- get Acckey 1 and value:1------------
[W][05:19:05][COMM]CAN START!
[D][05:19:05][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:05][COMM]1x1 frm_can_tp_send ok
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:0

2025-07-31 20:40:19:264 ==>> 此处延时了:【500】毫秒
2025-07-31 20:40:19:273 ==>> 检测【进入休眠模式2】
2025-07-31 20:40:19:288 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:40:19:309 ==>> 5][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 76549
[D][05:19:05][COMM][Audio]exec status ready.
[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]exec over: func id: 12, ret: 6
[D][05:19:05][COMM]imu wakeup ok. 76563
[D][05:19:05][COMM]imu wakeup 1
[W][05:19:05][COMM]wake up system, wakeupEvt=0x80
[D][05:19:05][COMM]frm_can_weigth_power_set 1
[D][05:19:05][COMM]Clear Sleep Block Evt
[D][05:19:05][COMM]Main Task receive event:28 finished processing


2025-07-31 20:40:19:550 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:05][COMM]Main Task receive event:28
[D][05:19:05][COMM]prepare to sleep
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]exec over: func id: 12, ret: 6
[W][05:19:05][COMM]CAN STOP!
[D][05:19:05][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:05][COMM]------------ready to Power off Acckey 1------------
[D][05:19:05][COMM]------------ready to Power off Acckey 2------------
[D][05:19:05][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:05][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 122
[D][05:19:05][COMM]bat sleep fail, reason:-1
[D][05:19:05][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:05][COMM]accel parse set 0
[D][05:19:05][COMM]imu rest ok. 76916
[D][05:19:05][COMM]imu sleep 0
[W][05:19:05][COMM]now sleep


2025-07-31 20:40:19:808 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:40:19:816 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:40:19:840 ==>> 开始小电池电流采样
2025-07-31 20:40:19:853 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:19:911 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:40:20:923 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:40:21:001 ==>> CurrentBattery:ƽ��:71.02

2025-07-31 20:40:21:436 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:21:444 ==>> 【检测小电池休眠电流】通过,【71.02uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:40:21:452 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:40:21:473 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:40:21:559 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:40:21:727 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:40:21:742 ==>> 该项需要延时执行
2025-07-31 20:40:21:801 ==>> [D][05:19:08][COMM]------------ready to Power on Acckey 1------------
[D][05:19:08][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 1
[D][05:19:08][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:08][COMM]----- get Acckey 1 and value:1------------
[W][05:19:08][COMM]CAN START!
[E][05:19:08][COMM]1x1 rx timeout
[E][05:19:08][COMM]1x1 tp timeout
[D][05:19:08][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[E][05:19:08][COMM]1x1 error -3.
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:08][COMM][Audio]exec status ready.
[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:19:08][COMM]imu wakeup ok. 79135
[D][05:19:08][COMM]imu wakeup 1
[W][05:19:08][COMM]wake up system, wakeupEvt=0x80
[D][05:19:08][COMM]frm_can_weigth_power_set 1
[D][05:19:08][COMM]Clear Sleep Block Evt
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok
[D][05:19:08][COMM]read battery soc:0


2025-07-31 20:40:22:059 ==>> [E][05:19:08][COMM]1x1 rx timeout
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:40:22:164 ==>> [D][05:19:08][COMM]msg 02A0 loss. last_tick:79103. cur_tick:79614. period:50
[D][05:19:08][COMM]msg 02A4 loss. last_tick:79103. cur_tick:79614. period:50
[D][05:19:08][COMM]msg 02A

2025-07-31 20:40:22:224 ==>> 5 loss. last_tick:79103. cur_tick:79615. period:50
[D][05:19:08][COMM]msg 02A6 loss. last_tick:79103. cur_tick:79615. period:50
[D][05:19:08][COMM]msg 02A7 loss. last_tick:79103. cur_tick:79615. period:50
[D][05:19:08][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 79616
[D][05:19:08][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 79616


2025-07-31 20:40:22:239 ==>> 此处延时了:【500】毫秒
2025-07-31 20:40:22:251 ==>> 检测【检测唤醒】
2025-07-31 20:40:22:274 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:40:22:636 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:08][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:08][FCTY]==========Modules-nRF5340 ==========
[D][05:19:08][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:08][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:08][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:08][FCTY]DeviceID    = 460130071539202
[D][05:19:08][FCTY]HardwareID  = 867222087691566
[D][05:19:08][FCTY]MoBikeID    = 9999999999
[D][05:19:08][FCTY]LockID      = FFFFFFFFFF
[D][05:19:08][FCTY]BLEFWVersion= 105
[D][05:19:08][FCTY]BLEMacAddr   = DA955A6833A9
[D][05:19:08][FCTY]Bat         = 3904 mv
[D][05:19:08][FCTY]Current     = 0 ma
[D][05:19:08][FCTY]VBUS        = 2600 mv
[D][05:19:08][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:08][FCTY]Ext battery vol = 32, adc = 1279
[D][05:19:08][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
[D][05:19:08][FCTY]Bike Type flag is invalied
[D][05:19:08][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:08][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:08][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:08][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:08][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:08][FCTY]CAT1_GNSS_VERSION = V34

2025-07-31 20:40:22:696 ==>> 65b5b1
[D][05:19:08][FCTY]Bat1         = 3812 mv
[D][05:19:08][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:08][FCTY]==========Modules-nRF5340 ==========
[E][05:19:08][COMM]1x1 rx timeout
[E][05:19:08][COMM]1x1 tp timeout
[E][05:19:08][COMM]1x1 error -3.
[D][05:19:08][COMM]Main Task receive event:28 finished processing


2025-07-31 20:40:22:790 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:40:22:805 ==>> 检测【关机】
2025-07-31 20:40:22:817 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:40:22:840 ==>>                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:40:22:906 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            . cur_tick:80125. period:100. j,i:5 58
[D][05:19:09][COMM]bat msg 024E loss. last_tick:79103. cur_tick:80126. period:100. j,i:15 68
[D][05:19:09][COMM]bat msg 024F loss. last_tick:79103. cur_tick:80126. period:100. j,i:16 6

2025-07-31 20:40:22:951 ==>> 9
[D][05:19:09][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 80126
[D][05:19:09][COMM]CAN message bat fault change: 0x00000000->0x0001802E 80127
[D][05:19:09][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 80127


2025-07-31 20:40:23:011 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 20:40:23:116 ==>> [D][05:19:09][COMM]arm_hub_enable: hub power: 0
[D][05:19:09][HSDK]hexlog index

2025-07-31 20:40:23:161 ==>>  save 0 3072 115 @ 0 : 0
[D][05:19:09][HSDK]write save hexlog index [0]
[D][05:19:09][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:09][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:40:23:251 ==>>                    msg 0222 loss. last_tick:79103. cur_tick:80608. period:150
[D][05:19:09][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 80610


2025-07-31 20:40:23:356 ==>> [D][05:19:09][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:09][

2025-07-31 20:40:23:401 ==>> COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[D][05:19:09][COMM]----- get Acckey 2 and value:0------------
[D][05:19:09][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:40:23:826 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:40:24:239 ==>> [D][05:19:09][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[D][05:19:09][COMM]----- get Acckey 2 and value:1------------
[D][05:19:09][COMM]more than the number of battery plugs
[D][05:19:09][COMM]VBUS is 1
[D][05:19:09][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:09][COMM]file:B50 exist
[D][05:19:09][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:09][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:09][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:09][COMM]Bat auth off fail, error:-1
[D][05:19:09][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[D][05:19:09][COMM]----- get Acckey 2 and value:1------------
[D][05:19:09][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[D][05:19:09][COMM]----- get Acckey 2 and value:1------------
[D][05:19:09][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:09][COMM]file:B50 exist
[D][05:19:09][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:09][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][

2025-07-31 20:40:24:345 ==>> 05:19:09][COMM]read file, len:10800, num:3
[D][05:19:09][COMM]--->crc16:0xb8a
[D][05:19:09][COMM]read file success
[D][05:19:09][COMM]accel parse set 1
[D][05:19:09][COMM][Audio]mon:9,05:19:09
[D][05:19:09][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:09][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:09][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:10][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:10][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:10][COMM]Main Task receive event:65
[D][05:19:10][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:10][COMM]Main Task receive event:65 finished processing
[D][05:19:10][COMM]Main Task receive event:66
[D][05:19:10][COMM]Try to Auto Lock Bat
[D][05:19:10][COMM]Main Task receive event:66 finished processing
[D][05:19:10][COMM]Main Task receive event:60
[D][05:19:10][COMM]smart_helmet_vol=255,255
[D][05:19:10][COMM]BAT CAN get state1 Fail 204


2025-07-31 20:40:24:450 ==>> 
[D][05:19:10][COMM]BAT CAN get soc Fail, 204
[D][05:19:10][COMM]BAT CAN get state2 fail 204
[D][05:19:10][COMM]get soh error
[E][05:19:10][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:10][COMM]report elecbike
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[W][05:19:10][PROT]remove success[1629955150],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:10][COMM]Main Task receive event:60 finished processing
[D][05:19:10][COMM]Main Task receive event:61
[D][05:19:10][COMM][D301]:type:3, trace id:280
[D][05:19:10][COMM]id[], hw[000
[D][05:19:10][COMM]get mcMaincircuitVolt error
[D][05:19:10][COMM]get mcSubcircuitVolt error
[D][05:19:10][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:10][COMM]BAT CAN get state1 Fail 204
[D][05:19:10][COMM]Receive Bat Lock cmd 0
[D][05:19:10][COMM]VBUS is 1
[D][05:19:10][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODAT

2025-07-31 20:40:24:556 ==>> AHEX=0,10800,0

[D][05:19:10][COMM]BAT CAN get soc Fail, 204
[D][05:19:10][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:10][COMM]BAT CAN get state2 fail 204
[D][05:19:10][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][PROT]index:0
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:10][PROT]is_send:1
[D][05:19:10][PROT]sequence_num:10
[D][05:19:10][PROT]retry_timeout:0
[D][05:19:10][PROT]retry_times:3
[D][05:19:10][PROT]send_path:0x3
[D][05:19:10][PROT]msg_type:0x5d03
[D][05:19:10][PROT]===========================================================
[W][05:19:10][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955150]
[D][05:19:10][PROT]===========================================================
[D][05:19:10][PROT]Sending traceid[999999999990000B]
[D][05:19:10][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:10][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:10][PROT]ble is not inited or not connected or cccd

2025-07-31 20:40:24:661 ==>>  not enabled
[D][05:19:10][COMM]get bat work mode err
[W][05:19:10][PROT]remove success[1629955150],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:10][COMM]Main Task receive event:61 finished processing
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:10][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:10][SAL ]open socket ind id[4], rst[0]
[D][05:19:10][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:10][SAL ]Cellular task submsg id[8]
[D][05:19:10][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:10][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:10][CAT1]gsm read msg sub id: 8
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hex

2025-07-31 20:40:24:766 ==>> send, index:1, len:2048
[D][05:19:10][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:10][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:10][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:10][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:10][CAT1]<<< 
+CME ERROR: 100

[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:10][COMM]Power Off
[D][05:19:10][COMM]read battery soc:255
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:10][COMM]f:[

2025-07-31 20:40:24:840 ==>> ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:10][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:10][COMM]arm_hub_enable: hub power: 0
[D][05:19:10][HSDK]hexlog index save 0 3072 115 @ 0 : 0
[D][05:19:10][HSDK]write save hexlog index [0]
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:40:24:870 ==>>                               

2025-07-31 20:40:24:960 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:40:24:969 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:40:24:981 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:40:25:052 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:40:25:157 ==>> [D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 15
[D][05:19:11][FCTY]get_

2025-07-31 20:40:25:217 ==>> ext_48v_vol retry i = 1,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 15
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 15


2025-07-31 20:40:25:248 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:40:25:256 ==>> 检测【检测小电池关机电流】
2025-07-31 20:40:25:264 ==>> 开始小电池电流采样
2025-07-31 20:40:25:283 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:25:352 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:40:26:363 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:40:26:409 ==>> CurrentBattery:ƽ��:71.60

2025-07-31 20:40:26:865 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:40:26:873 ==>> 【检测小电池关机电流】通过,【71.6uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:40:27:215 ==>> MES过站成功
2025-07-31 20:40:27:229 ==>> #################### 【测试结束】 ####################
2025-07-31 20:40:27:265 ==>> 关闭5V供电
2025-07-31 20:40:27:278 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:40:27:355 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:40:28:268 ==>> 关闭5V供电成功
2025-07-31 20:40:28:281 ==>> 关闭33V供电
2025-07-31 20:40:28:289 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:40:28:360 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:40:29:281 ==>> 关闭33V供电成功
2025-07-31 20:40:29:295 ==>> 关闭3.7V供电
2025-07-31 20:40:29:316 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:40:29:356 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:40:30:168 ==>>  

2025-07-31 20:40:30:288 ==>> 关闭3.7V供电成功
