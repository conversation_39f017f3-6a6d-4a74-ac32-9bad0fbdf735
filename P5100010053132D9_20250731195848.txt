2025-07-31 19:58:48:465 ==>> MES查站成功:
查站序号:P5100010053132D9验证通过
2025-07-31 19:58:48:502 ==>> 扫码结果:P5100010053132D9
2025-07-31 19:58:48:503 ==>> 当前测试项目:SE51_PCBA
2025-07-31 19:58:48:505 ==>> 测试参数版本:2024.10.11
2025-07-31 19:58:48:506 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 19:58:48:508 ==>> 检测【打开透传】
2025-07-31 19:58:48:509 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 19:58:48:632 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 19:58:49:138 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 19:58:49:153 ==>> 检测【检测接地电压】
2025-07-31 19:58:49:155 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 19:58:49:229 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 19:58:49:431 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 19:58:49:433 ==>> 检测【打开小电池】
2025-07-31 19:58:49:435 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 19:58:49:532 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 19:58:49:702 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 19:58:49:703 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 19:58:49:706 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 19:58:49:838 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 19:58:49:977 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 19:58:49:980 ==>> 检测【等待设备启动】
2025-07-31 19:58:49:984 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:50:325 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 19:58:50:521 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti 

2025-07-31 19:58:51:011 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:52:061 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:53:098 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:54:136 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:55:174 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:56:215 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:57:004 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 19:58:57:201 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 19:58:57:246 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:57:696 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 19:58:57:879 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 19:58:58:294 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:58:530 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 19:58:58:575 ==>>                                                    

2025-07-31 19:58:58:971 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 19:58:59:316 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 19:58:59:454 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 19:58:59:594 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 19:58:59:596 ==>> 检测【产品通信】
2025-07-31 19:58:59:597 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 19:59:00:153 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 19:59:00:348 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 19:59:00:620 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 19:59:01:033 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 19:59:01:413 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 19:59:01:654 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 19:59:02:397 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[D][05:17:50][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[D][05:17:50][COMM][MC]get MC real state err,rt:-3
[D][05:17:50][COMM]frm_can_weigth_power_set 1
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[D][05:17:50][COMM]index:0,power_mode:0xFF
[D][05:17:50][COMM]index:1,sound_mode:0xFF
[D][05:17:50][COMM]index:2,gsensor_mode:0xFF
[D][05:17:50][COMM]index:3,report_freq_mode:0xFF
[D][05:17:50][COMM]index:4,report_period:0xFF
[D][05:17:50][COMM]index:5,normal_reset_mode:0xFF
[D][05:17:50][COMM]index:6,normal_reset_period:0xFF
[D][05:17:50][COMM]index:7,spock_over_speed:0xFF
[D][05:17:50][COMM]index:8,spock_limit_speed:0xFF
[D][05:17:50][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:17:50][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:17:50][COMM]index:11,ble_scan_mode:0xFF
[D][05:17:50][COMM]index:12,ble_adv_mode:0xFF
[D][0

2025-07-31 19:59:02:445 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 19:59:02:448 ==>> 检测【初始化完成检测】
2025-07-31 19:59:02:452 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 19:59:02:502 ==>> 5:17:50][COMM]index:13,spock_audio_volumn:0xFF
[D][05:17:50][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:17:50][COMM]index:15,bat_auth_mode:0xFF
[D][05:17:50][COMM]index:16,imu_config_params:0xFF
[D][05:17:50][COMM]index:17,long_connect_params:0xFF
[D][05:17:50][COMM]index:18,detain_mark:0xFF
[D][05:17:50][COMM]index:19,lock_pos_report_count:0xFF
[D][05:17:50][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:17:50][COMM]index:21,mc_mode:0xFF
[D][05:17:50][COMM]index:22,S_mode:0xFF
[D][05:17:50][COMM]index:23,overweight:0xFF
[D][05:17:50][COMM]index:24,standstill_mode:0xFF
[D][05:17:50][COMM]index:25,night_mode:0xFF
[D][05:17:50][COMM]index:26,experiment1:0xFF
[D][05:17:50][COMM]index:27,experiment2:0xFF
[D][05:17:50][COMM]index:28,experiment3:0xFF
[D][05:17:50][COMM]index:29,experiment4:0xFF
[D][05:17:50][COMM]index:30,night_mode_start:0xFF
[D][05:17:50][COMM]index:31,night_mode_end:0xFF
[D][05:17:50][COMM]index:33,park_report_minutes:0xFF
[D][05:17:50][COMM]index:34,park_report_mode:0xFF
[D][05:17:50][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:17:50][COMM]index:38,charge_battery_para: FF
[D][05:17:50][COMM]index:39,multirider_mode:0xFF
[D][05:17:50]

2025-07-31 19:59:02:608 ==>> [COMM]index:40,mc_launch_mode:0xFF
[D][05:17:50][COMM]index:41,head_light_enable_mode:0xFF
[D][05:17:50][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:17:50][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:17:50][COMM]index:44,riding_duration_config:0xFF
[D][05:17:50][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:17:50][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:17:50][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:17:50][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:17:50][COMM]index:49,mc_load_startup:0xFF
[D][05:17:50][COMM]index:50,mc_tcs_mode:0xFF
[D][05:17:50][COMM]index:51,traffic_audio_play:0xFF
[D][05:17:50][COMM]index:52,traffic_mode:0xFF
[D][05:17:50][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:17:50][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:17:50][COMM]index:55,wheel_alarm_play_switch:255
[D][05:17:50][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:17:50][COMM]index:58,traffic_light_threshold:0xFF
[D][05:17:50][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:17:50][COMM]index:60,traffic_road_threshold:0xFF
[D][05:17:50][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:17:50][COMM]index:63,experiment5:0xFF
[D][05:17:50][CO

2025-07-31 19:59:02:712 ==>> MM]index:64,camera_park_markline_cfg:0xFF
[D][05:17:50][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:17:50][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:17:50][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:17:50][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:17:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:17:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86

2025-07-31 19:59:02:817 ==>> ,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]msg 0222 loss. last_tick:0. cur_tick:1516. period:150
[D][05:17:50][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 1518
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
                                                             

2025-07-31 19:59:03:013 ==>>                              to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:03:118 ==>>                                                                OMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 19:59:03:226 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 19:59:03:229 ==>> 检测【关闭大灯控制1】
2025-07-31 19:59:03:230 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 19:59:03:404 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 19:59:03:499 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 19:59:03:501 ==>> 检测【打开仪表指令模式1】
2025-07-31 19:59:03:503 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 19:59:03:735 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 19:59:03:780 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 19:59:03:782 ==>> 检测【关闭仪表供电】
2025-07-31 19:59:03:784 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 19:59:03:932 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 19:59:04:007 ==>>                                                          mu_task imu work error:[-1]. goto init


2025-07-31 19:59:04:056 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 19:59:04:059 ==>> 检测【关闭AccKey2供电1】
2025-07-31 19:59:04:060 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 19:59:04:204 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 19:59:04:333 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 19:59:04:335 ==>> 检测【关闭AccKey1供电1】
2025-07-31 19:59:04:336 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 19:59:04:495 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 19:59:04:606 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 19:59:04:608 ==>> 检测【关闭转刹把供电1】
2025-07-31 19:59:04:610 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:04:827 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 19:59:04:875 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 19:59:04:878 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 19:59:04:887 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 19:59:04:932 ==>> 5A A5 01 5A A5 


2025-07-31 19:59:05:007 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:05:037 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 19:59:05:112 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28


2025-07-31 19:59:05:145 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 19:59:05:148 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 19:59:05:149 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 19:59:05:172 ==>>                    read battery soc:255


2025-07-31 19:59:05:232 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 19:59:05:415 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 19:59:05:417 ==>> 该项需要延时执行
2025-07-31 19:59:05:532 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5018. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E222

2025-07-31 19:59:05:562 ==>> 17->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019


2025-07-31 19:59:06:020 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:06:714 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 19:59:07:276 ==>>                                                                                                                                            [COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail

2025-07-31 19:59:07:381 ==>> 
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send

2025-07-31 19:59:07:486 ==>> :1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W

2025-07-31 19:59:07:561 ==>> ][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][CAT1]power_urc_cb ret[5]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 19:59:08:043 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:09:045 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:09:194 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 19:59:09:423 ==>> 此处延时了:【4000】毫秒
2025-07-31 19:59:09:426 ==>> 检测【33V输入电压ADC】
2025-07-31 19:59:09:430 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:09:740 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:17:58][COMM]adc read out 24v adc:1328  volt:33589 mv
[D][05:17:58][COMM]adc read left brake adc:8  volt:10 mv
[D][05:17:58][COMM]adc read right brake adc:6  volt:7 mv
[D][05:17:58][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:58][COMM]adc read battery ts volt:11 mv
[D][05:17:58][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:17:58][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 19:59:09:957 ==>> 【33V输入电压ADC】通过,【32779mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 19:59:09:960 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 19:59:09:963 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 19:59:10:059 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1988mV
Get AD_V7 1089mV
OVER 150
[D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:10:378 ==>> 【TP7_VCC3V3(ADV2)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:10:384 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 19:59:10:399 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:10:402 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 19:59:10:406 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 19:59:10:423 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 19:59:10:509 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 19:59:10:512 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 19:59:10:545 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 19:59:10:550 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 19:59:10:568 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 19:59:10:570 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 19:59:10:646 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2769mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 19:59:10:856 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 19:59:10:869 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:10:872 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 19:59:10:887 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:10:890 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 19:59:10:893 ==>> 原始值:【2769】, 乘以分压基数【2】还原值:【5538】
2025-07-31 19:59:10:906 ==>> 【TP68_VCC5V5(ADV5)】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 19:59:10:910 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 19:59:10:926 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 19:59:10:928 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 19:59:10:949 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 19:59:10:951 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 19:59:11:038 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1988mV
Get AD_V7 1090mV
OVER 150


2025-07-31 19:59:11:231 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:11:234 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 19:59:11:250 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:11:253 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 19:59:11:257 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 19:59:11:268 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 19:59:11:271 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 19:59:11:280 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:

2025-07-31 19:59:11:288 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 19:59:11:290 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 19:59:11:311 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 19:59:11:314 ==>> 检测【打开WIFI(1)】
2025-07-31 19:59:11:318 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 19:59:11:325 ==>> 17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]read battery soc:255


2025-07-31 19:59:11:737 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087739746

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539083

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 19:59:11:846 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 19:59:11:849 ==>> 检测【清空消息队列(1)】
2025-07-31 19:59:11:853 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 19:59:12:061 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 19:59:12:124 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 19:59:12:127 ==>> 检测【打开GPS(1)】
2025-07-31 19:59:12:131 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 19:59:12:332 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 19:59:12:395 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 19:59:12:399 ==>> 检测【打开GSM联网】
2025-07-31 19:59:12:402 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 19:59:12:700 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 19:59:12:929 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 19:59:12:933 ==>> 检测【打开仪表供电1】
2025-07-31 19:59:12:936 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 19:59:13:130 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 19:59:13:190 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 19:59:13:211 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 19:59:13:214 ==>> 检测【打开仪表指令模式2】
2025-07-31 19:59:13:216 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 19:59:13:430 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 19:59:13:494 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 19:59:13:497 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 19:59:13:499 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 19:59:13:715 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33433]


2025-07-31 19:59:13:763 ==>> 【读取主控ADC采集的仪表电压】通过,【33433mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 19:59:13:768 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 19:59:13:777 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:13:925 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 19:59:14:030 ==>> [

2025-07-31 19:59:14:035 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 19:59:14:038 ==>> 检测【AD_V20电压】
2025-07-31 19:59:14:040 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:14:060 ==>> D][05:18:02][COMM]13728 imu init OK


2025-07-31 19:59:14:150 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:14:241 ==>> 本次取值间隔时间:90ms
2025-07-31 19:59:14:244 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 19:59:14:261 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:14:362 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:14:437 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 19:59:14:618 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 19:59:14:799 ==>> 本次取值间隔时间:429ms
2025-07-31 19:59:14:817 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:14:860 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 19:59:14:919 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:15:039 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 19:59:15:359 ==>> 本次取值间隔时间:425ms
2025-07-31 19:59:15:378 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:15:390 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 19:59:15:480 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:15:540 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 19:59:15:646 ==>>                                                                                                                                                                                                                                                                                                                                                                                  05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][COMM]init key as 
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:04][COMM]Main Task receive event:4 finished processing
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[

2025-07-31 19:59:15:722 ==>> 8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 21,99

OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:04][CAT1]<<< 
+QIACT: 1,1,1,"10.249.1.233"

OK

[D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 19:59:15:752 ==>> 本次取值间隔时间:270ms
2025-07-31 19:59:15:771 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:15:828 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 19:59:15:874 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:15:934 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 19:59:16:070 ==>> 本次取值间隔时间:196ms
2025-07-31 19:59:16:088 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:16:193 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:16:269 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 19:59:16:374 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 19:59:16:495 ==>> 本次取值间隔时间:299ms
2025-07-31 19:59:16:694 ==>> 本次取值间隔时间:197ms
2025-07-31 19:59:17:009 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 19:59:17:100 ==>> 本次取值间隔时间:395ms
2025-07-31 19:59:17:280 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 19:59:17:587 ==>> 本次取值间隔时间:485ms
2025-07-31 19:59:17:592 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:17:664 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 19:59:17:694 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:17:875 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,42,,,41,33,,,39,60,,,39,39,,,38,1*76

$GBGSV,2,2,07,38,,,37,24,,,42,14,,,36,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.500,1608.500,51.376,2097152,2097152,2097152*49

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 19:59:17:879 ==>> 本次取值间隔时间:180ms
2025-07-31 19:59:17:950 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 19:59:18:056 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 19:59:18:313 ==>> 本次取值间隔时间:431ms
2025-07-31 19:59:18:698 ==>> 本次取值间隔时间:375ms
2025-07-31 19:59:18:788 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,41,42,,,41,33,,,40,14,,,40,1*71

$GBGSV,3,2,11,60,,,39,39,,,38,38,,,37,16,,,37,1*76

$GBGSV,3,3,11,1,,,37,25,,,36,59,,,36,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1590.435,1590.435,50.824,2097152,2097152,2097152*44



2025-07-31 19:59:18:923 ==>> 本次取值间隔时间:220ms
2025-07-31 19:59:18:928 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 19:59:19:030 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 19:59:19:092 ==>> [W][05:18:07][COMM]>>>>>Input command = ?<<<<<


2025-07-31 19:59:19:137 ==>> 1A A1 10 00 00 
Get AD_V20 1646mV
OVER 150


2025-07-31 19:59:19:272 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 19:59:19:277 ==>> 本次取值间隔时间:229ms
2025-07-31 19:59:19:292 ==>> 【AD_V20电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:19:296 ==>> 检测【拉低OUTPUT2】
2025-07-31 19:59:19:300 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 19:59:19:437 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 19:59:19:585 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 19:59:19:592 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 19:59:19:603 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 19:59:19:827 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,41,42,,,41,33,,,41,3,,,41,1*47

$GBGSV,5,2,17,14,,,40,60,,,39,39,,,39,38,,,37,1*75

$GBGSV,5,3,17,16,,,37,1,,,37,25,,,37,59,,,37,1*4B

$GBGSV,5,4,17,5,,,34,2,,,33,9,,,33,4,,,30,1*7F

$GBGSV,5,5,17,40,,,30,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1529.117,1529.117,48.953,2097152,2097152,2097152*4C



2025-07-31 19:59:19:861 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 19:59:19:864 ==>> 检测【拉高OUTPUT2】
2025-07-31 19:59:19:867 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 19:59:19:932 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 19:59:20:131 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 19:59:20:134 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 19:59:20:137 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 19:59:20:358 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 19:59:20:406 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 19:59:20:409 ==>> 检测【预留IO LED功能输出】
2025-07-31 19:59:20:414 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 19:59:20:627 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 19:59:20:675 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 19:59:20:679 ==>> 检测【AD_V21电压】
2025-07-31 19:59:20:682 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 19:59:20:732 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 19:59:20:837 ==>> $GBGGA,115924.630,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,41,42,,,41,33,,,41,3,,,40,1*49

$GBGSV,5,2,18,14,,,40,60,,,39,39,,,39,25,,,38,1*79

$GBGSV,5,3,18,59,,,38,38,,,37,16,,,37,1,,,37,1*47

$GBGSV,5,4,18,2,,,34,40,,,34,5,,,33,9,,,33,1*44

$GBGSV,5,5,18,4,,,30,7,,,37,1*7B

$GBRMC,115924.630,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115924.630,0.000,1541.290,1541.290,49.323,2097152,2097152,2097152*51



2025-07-31 19:59:20:897 ==>> 本次取值间隔时间:213ms
2025-07-31 19:59:20:915 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:20:918 ==>> 检测【关闭仪表供电2】
2025-07-31 19:59:20:921 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 19:59:21:122 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 19:59:21:187 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 19:59:21:192 ==>> 检测【关闭仪表指令模式】
2025-07-31 19:59:21:196 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 19:59:21:273 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 19:59:21:424 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 19:59:21:457 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 19:59:21:461 ==>> 检测【打开AccKey2供电】
2025-07-31 19:59:21:465 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 19:59:21:605 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 19:59:21:709 ==>>                                                         ,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,41,42,,,41,33,,,41,3,,,40,1*48

$GBGSV,5,2,19,14,,,40,60,,,40,39,,,39,25,,,39,1*77

$GBGSV,5,3,19,59,,,38,38,,,37,16,,,37,1,,,37,1*46

$GBGSV,5,4,19,13,,,37,2,,,35,6,,,35,40,,,34,1*7E

$GBGSV,5,5,19,5,,,33,9,,,33,4,,,29,1*4D

$GBRMC,115925.530,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115925.530,0.000,1540.520,1540.520

2025-07-31 19:59:21:728 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 19:59:21:734 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 19:59:21:739 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:21:744 ==>> ,49.299,2097152,2097152,2097152*53



2025-07-31 19:59:22:043 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:10][COMM]adc read out 24v adc:1325  volt:33513 mv
[D][05:18:10][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:10][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:10][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:10][COMM]adc read battery ts volt:6 mv
[D][05:18:10][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 19:59:22:257 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33513mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 19:59:22:262 ==>> 检测【关闭AccKey2供电2】
2025-07-31 19:59:22:267 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 19:59:22:404 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 19:59:22:533 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 19:59:22:537 ==>> 该项需要延时执行
2025-07-31 19:59:22:720 ==>> $GBGGA,115926.510,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,41,42,,,41,33,,,41,3,,,40,1*42

$GBGSV,5,2,20,14,,,40,60,,,39,39,,,39,25,,,39,1*73

$GBGSV,5,3,20,59,,,39,1,,,38,38,,,37,16,,,37,1*42

$GBGSV,5,4,20,13,,,37,2,,,35,6,,,35,40,,,35,1*75

$GBGSV,5,5,20,5,,,33,9,,,33,7,,,32,4,,,30,1*79

$GBRMC,115926.510,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115926.510,0.000,1536.045,1536.045,49.154,2097152,2097152,2097152*50



2025-07-31 19:59:23:298 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 19:59:23:725 ==>> $GBGGA,115927.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,42,,,41,33,,,41,24,,,40,3,,,40,1*41

$GBGSV,6,2,21,14,,,40,60,,,39,25,,,39,59,,,39,1*77

$GBGSV,6,3,21,39,,,38,1,,,38,13,,,38,38,,,37,1*4D

$GBGSV,6,4,21,16,,,37,2,,,35,6,,,35,40,,,35,1*72

$GBGSV,6,5,21,5,,,33,9,,,33,7,,,32,4,,,30,1*7B

$GBGSV,6,6,21,41,,,28,1*7A

$GBRMC,115927.510,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115927.510,0.000,1516.226,1516.226,48.541,2097152,2097152,2097152*50



2025-07-31 19:59:24:712 ==>> $GBGGA,115928.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,42,,,41,33,,,41,24,,,41,3,,,40,1*43

$GBGSV,6,2,22,14,,,40,60,,,39,25,,,39,59,,,39,1*74

$GBGSV,6,3,22,39,,,39,13,,,38,1,,,37,38,,,37,1*40

$GBGSV,6,4,22,16,,,37,2,,,36,40,,,36,6,,,35,1*71

$GBGSV,6,5,22,26,,,34,5,,,33,9,,,33,7,,,33,1*4D

$GBGSV,6,6,22,4,,,31,41,,,31,1*47

$GBRMC,115928.510,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115928.510,0.000,1526.434,1526.434,48.846,2097152,2097152,2097152*55



2025-07-31 19:59:25:291 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 19:59:25:535 ==>> 此处延时了:【3000】毫秒
2025-07-31 19:59:25:541 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 19:59:25:568 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:25:856 ==>> $GBGGA,115929.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,42,,,41,33,,,41,24,,,41,3,,,40,1*43

$GBGSV,6,2,22,14,,,40,60,,,39,25,,,39,59,,,39,1*74

$GBGSV,6,3,22,39,,,39,13,,,38,1,,,38,38,,,38,1*40

$GBGSV,6,4,22,16,,,37,2,,,36,40,,,36,6,,,35,1*71

$GBGSV,6,5,22,26,,,35,9,,,34,5,,,33,7,,,33,1*4B

$GBGSV,6,6,22,41,,,32,4,,,31,1*44

$GBRMC,115929.510,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115929.510,0.000,1535.848,1535.848,49.139,2097152,2097152,2097152*54

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:18:14][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:6 mv
[D][05:18:14][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:14][COMM]arm_hub adc read led yb 

2025-07-31 19:59:25:886 ==>> adc:1443  volt:33456 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 19:59:26:071 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【75mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 19:59:26:077 ==>> 检测【打开AccKey1供电】
2025-07-31 19:59:26:082 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 19:59:26:222 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 19:59:26:345 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 19:59:26:349 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 19:59:26:355 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 19:59:26:432 ==>> 1A A1 00 40 00 
Get AD_V14 2586mV
OVER 150


2025-07-31 19:59:26:598 ==>> 原始值:【2586】, 乘以分压基数【2】还原值:【5172】
2025-07-31 19:59:26:616 ==>> 【读取AccKey1电压(ADV14)前】通过,【5172mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 19:59:26:620 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 19:59:26:623 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:26:718 ==>> $GBGGA,115930.510,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,41,33,,,41,24,,,40,3,,,40,1*43

$GBGSV,6,2,23,14,,,40,60,,,40,25,,,39,59,,,39,1*7B

$GBGSV,6,3,23,39,,,38,13,,,38,1,,,38,38,,,37,1*4F

$GBGSV,6,4,23,16,,,36,40,,,36,2,,,35,6,,,35,1*72

$GBGSV,6,5,23,26,,,35,8,,,35,9,,,34,5,,,33,1*43

$GBGSV,6,6,23,7,,,33,41,,,33,4,,,32,1*70

$GBRMC,115930.510,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115930.510,0.000,1528.546,1528.546,48.896,2097152,2097152,2097152*51



2025-07-31 19:59:26:958 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:15][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:15][COMM]adc read battery ts volt:11 mv
[D][05:18:15][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:15][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 19:59:27:165 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 19:59:27:169 ==>> 检测【关闭AccKey1供电2】
2025-07-31 19:59:27:174 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 19:59:27:322 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 19:59:27:447 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 19:59:27:453 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 19:59:27:459 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 19:59:27:534 ==>> 1A A1 00 40 00 
Get AD_V14 2569mV
OVER 150


2025-07-31 19:59:27:700 ==>> 原始值:【2569】, 乘以分压基数【2】还原值:【5138】
2025-07-31 19:59:27:718 ==>> 【读取AccKey1电压(ADV14)后】通过,【5138mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 19:59:27:722 ==>> 检测【打开WIFI(2)】
2025-07-31 19:59:27:726 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 19:59:27:745 ==>> $GBGGA,115931.510,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,42,,,40,24,,,40,3,,,40,1*42

$GBGSV,6,2,23,60,,,40,59,,,40,14,,,39,25,,,39,1*7B

$GBGSV,6,3,23,39,,,39,13,,,38,1,,,38,38,,,37,1*4E

$GBGSV,6,4,23,16,,,37,40,,,36,2,,,36,8,,,36,1*7D

$GBGSV,6,5,23,6,,,35,26,,,35,9,,,34,5,,,33,1*4D

$GBGSV,6,6,23,7,,,33,41,,,33,4,,,32,1*70

$GBRMC,115931.510,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115931.510,0.000,765.537,765.537,700.100,2097152,2097152,2097152*6D



2025-07-31 19:59:27:955 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 19:59:27:988 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 19:59:27:994 ==>> 检测【转刹把供电】
2025-07-31 19:59:28:017 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 19:59:28:214 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 19:59:28:262 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 19:59:28:267 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 19:59:28:275 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 19:59:28:365 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 19:59:28:441 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2405mV
OVER 150


2025-07-31 19:59:28:516 ==>> 原始值:【2405】, 乘以分压基数【2】还原值:【4810】
2025-07-31 19:59:28:535 ==>> 【读取AD_V15电压(前)】通过,【4810mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 19:59:28:542 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 19:59:28:552 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 19:59:28:636 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 19:59:28:817 ==>> 1A A1 01 00 00 
Get AD_V16 2438mV
OVER 150
$GBGGA,115932.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,3,,,40,59,,,40,1*48

$GBGSV,6,2,23,24,,,40,42,,,40,14,,,40,39,,,39,1*72

$GBGSV,6,3,23,25,,,39,13,,,38,1,,,38,38,,,37,1*43

$GBGSV,6,4,23,2,,,36,40,,,36,16,,,36,8,,,35,1*7F

$GBGSV,6,5,23,26,,,35,6,,,35,9,,,34,7,,,33,1*4F

$GBGSV,6,6,23,5,,,33,41,,,33,4,,,32,1*72

$GBRMC,115932.510,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115932.510,0.000,764.641,764.641,699.281,2097152,2097152,2097152*65

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,CC057790A7C0,-78
+WIFISCAN:4,1,CC057790A6E1,-80
+WIFISCAN:4,2,F86FB0660A82,-80
+WIFISCAN:4,3,CC057790A5C0,-81

[D][05:18:17][CAT1]wifi scan report total[4]


2025-07-31 19:59:28:952 ==>> 原始值:【2438】, 乘以分压基数【2】还原值:【4876】
2025-07-31 19:59:28:976 ==>> 【读取AD_V16电压(前)】通过,【4876mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 19:59:28:980 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 19:59:28:983 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:29:243 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:17][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:17][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:17][COMM]adc read battery ts volt:7 mv
[D][05:18:17][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3101  volt:5450 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][GNSS]recv submsg id[3]
[D][05:18:17][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 19:59:29:318 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 19:59:29:502 ==>> 【转刹把供电电压(主控ADC)】通过,【5450mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 19:59:29:508 ==>> 检测【转刹把供电电压】
2025-07-31 19:59:29:530 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:29:858 ==>> $GBGGA,115933.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,41,33,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,23,59,,,40,42,,,40,39,,,39,14,,,39,1*76

$GBGSV,6,3,23,25,,,39,13,,,38,1,,,38,38,,,37,1*43

$GBGSV,6,4,23,16,,,37,2,,,36,8,,,36,40,,,36,1*7D

$GBGSV,6,5,23,26,,,35,6,,,35,9,,,34,41,,,34,1*7A

$GBGSV,6,6,23,7,,,33,5,,,33,4,,,32,1*40

$GBRMC,115933.510,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115933.510,0.000,767.335,767.336,701.745,2097152,2097152,2097152*6A

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:18][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:18][COMM]adc read battery ts volt:8 mv
[D][05:18:18][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3106  volt:5459 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:18][COMM]arm_hub adc read led yb ad

2025-07-31 19:59:29:888 ==>> c:1442  volt:33433 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 19:59:30:026 ==>> 【转刹把供电电压】通过,【5459mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 19:59:30:031 ==>> 检测【关闭转刹把供电2】
2025-07-31 19:59:30:034 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:30:220 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 19:59:30:299 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 19:59:30:303 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 19:59:30:309 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:30:401 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 19:59:30:506 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:30:521 ==>> [D][05:18:19][COMM]IMU: [13,1,-938] ret=21 AWAKE!


2025-07-31 19:59:30:611 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 19:59:30:716 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:30:776 ==>> $GBGGA,115934.510,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,41,33,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,23,59,,,40,24,,,40,25,,,40,39,,,39,1*7A

$GBGSV,6,3,23,14,,,39,13,,,38,1,,,38,38,,,37,1*41

$GBGSV,6,4,23,16,,,37,2,,,36,40,,,36,8,,,35,1*7E

$GBGSV,6,5,23,26,,,35,6,,,35,9,,,34,41,,,34,1*7A

$GBGSV,6,6,23,7,,,33,5,,,33,4,,,33,1*41

$GBRMC,115934.510,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115934.510,0.000,768.235,768.235,702.567,2097152,2097152,2097152*6F

[D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:19][COMM]>>>>>Input command = ?<<<<


2025-07-31 19:59:30:821 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 19:59:30:851 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<


2025-07-31 19:59:30:926 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:30:941 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 19:59:31:031 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 19:59:31:091 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 19:59:31:136 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 19:59:31:165 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 19:59:31:170 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 19:59:31:174 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 19:59:31:272 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 19:59:31:333 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:19][COMM]read battery soc:255
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 19:59:31:395 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 19:59:31:402 ==>> 检测【拉高OUTPUT3】
2025-07-31 19:59:31:407 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 19:59:31:542 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 19:59:31:665 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 19:59:31:669 ==>> 检测【拉高OUTPUT4】
2025-07-31 19:59:31:675 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 19:59:31:753 ==>> $GBGGA,115935.510,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,41,33,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,23,59,,,40,24,,,40,39,,,39,14,,,39,1*76

$GBGSV,6,3,23,25,,,39,13,,,38,1,,,38,38,,,37,1*43

$GBGSV,6,4,23,16,,,37,40,,,36,2,,,35,8,,,35,1*7D

$GBGSV,6,5,23,26,,,35,6,,,35,7,,,34,9,,,34,1*48

$GBGSV,6,6,23,41,,,34,5,,,33,4,,,32,1*75

$GBRMC,115935.510,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115935.510,0.000,766.436,766.436,700.922,2097152,2097152,2097152*61

3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 19:59:31:935 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 19:59:31:940 ==>> 检测【拉高OUTPUT5】
2025-07-31 19:59:31:943 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 19:59:32:040 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 19:59:32:209 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 19:59:32:213 ==>> 检测【左刹电压测试1】
2025-07-31 19:59:32:220 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:32:540 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:1730  volt:2280 mv
[D][05:18:21][COMM]adc read right brake adc:1738  volt:2291 mv
[D][05:18:21][COMM]adc read throttle adc:1737  volt:2289 mv
[D][05:18:21][COMM]adc read battery ts volt:9 mv
[D][05:18:21][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:21][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 19:59:32:736 ==>> $GBGGA,115936.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,41,42,,,41,33,,,41,60,,,40,1*77

$GBGSV,6,2,23,3,,,40,59,,,40,39,,,39,14,,,39,1*43

$GBGSV,6,3,23,25,,,39,13,,,38,1,,,38,38,,,37,1*43

$GBGSV,6,4,23,16,,,37,8,,,36,40,,,36,2,,,35,1*7E

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,9,,,34,1*7B

$GBGSV,6,6,23,7,,,33,5,,,33,4,,,32,1*40

$GBRMC,115936.510,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115936.510,0.000,768.237,768.237,702.569,2097152,2097152,2097152*63



2025-07-31 19:59:32:740 ==>> 【左刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 19:59:32:744 ==>> 检测【右刹电压测试1】
2025-07-31 19:59:32:763 ==>> 【右刹电压测试1】通过,【2291】符合目标值【2250】至【2500】要求!
2025-07-31 19:59:32:768 ==>> 检测【转把电压测试1】
2025-07-31 19:59:32:782 ==>> 【转把电压测试1】通过,【2289】符合目标值【2250】至【2500】要求!
2025-07-31 19:59:32:788 ==>> 检测【拉低OUTPUT3】
2025-07-31 19:59:32:813 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 19:59:32:841 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 19:59:33:055 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 19:59:33:063 ==>> 检测【拉低OUTPUT4】
2025-07-31 19:59:33:070 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 19:59:33:141 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 19:59:33:321 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 19:59:33:326 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 19:59:33:330 ==>> 检测【拉低OUTPUT5】
2025-07-31 19:59:33:334 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 19:59:33:427 ==>> 3A A3 05 00 A3 


2025-07-31 19:59:33:531 ==>> OFF_OUT5
OVER 150


2025-07-31 19:59:33:609 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 19:59:33:613 ==>> 检测【左刹电压测试2】
2025-07-31 19:59:33:619 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 19:59:33:744 ==>> $GBGGA,115937.510,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,59,,,41,42,,,41,33,,,41,60,,,40,1*7A

$GBGSV,6,2,24,3,,,40,24,,,40,14,,,40,25,,,40,1*43

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,6,4,24,16,,,37,2,,,36,8,,,36,40,,,36,1*7A

$GBGSV,6,5,24,26,,,35,6,,,35,41,,,35,9,,,34,1*7C

$GBGSV,6,6,24,7,,,33,5,,,33,4,,,33,45,,,36,1*42

$GBRMC,115937.510,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115937.510,0.000,771.833,771.833,705.858,2097152,2097152,2097152*6A



2025-07-31 19:59:33:939 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:22][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:22][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:22][COMM]adc read battery ts volt:10 mv
[D][05:18:22][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 19:59:34:140 ==>> 【左刹电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 19:59:34:144 ==>> 检测【右刹电压测试2】
2025-07-31 19:59:34:161 ==>> 【右刹电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 19:59:34:180 ==>> 检测【转把电压测试2】
2025-07-31 19:59:34:184 ==>> 【转把电压测试2】通过,【2】符合目标值【0】至【50】要求!
2025-07-31 19:59:34:188 ==>> 检测【晶振检测】
2025-07-31 19:59:34:192 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 19:59:34:323 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 19:59:34:462 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 19:59:34:467 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 19:59:34:471 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 19:59:34:548 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1657mV
Get AD_V4 1650mV
Get AD_V5 2768mV
Get AD_V6 1993mV
Get AD_V7 1089mV
OVER 150


2025-07-31 19:59:34:732 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 19:59:34:779 ==>> 检测【检测BootVer】
2025-07-31 19:59:34:785 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 19:59:34:807 ==>> $GBGGA,115938.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,59,,,41,60,,,40,3,,,40,24,,,40,1*4E

$GBGSV,6,2,23,42,,,40,25,,,40,33,,,40,39,,,39,1*76

$GBGSV,6,3,23,14,,,39,13,,,38,1,,,38,38,,,37,1*41

$GBGSV,6,4,23,2,,,36,8,,,36,40,,,36,16,,,36,1*7C

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,9,,,34,1*7B

$GBGSV,6,6,23,7,,,33,5,,,33,4,,,33,1*41

$GBRMC,115938.510,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115938.510,0.000,768.229,768.229,702.561,2097152,2097152,2097152*65



2025-07-31 19:59:35:122 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539083
[D][05:18:23][FCTY]HardwareID  = 867222087739746
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = C5E2BF5121A0
[D][05:18:23][FCTY]Bat         = 3924 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1298
[D][05:18:23][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3718 mv
[D]

2025-07-31 19:59:35:152 ==>> [05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 19:59:35:265 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 19:59:35:269 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 19:59:35:273 ==>> 检测【检测固件版本】
2025-07-31 19:59:35:290 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 19:59:35:295 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 19:59:35:299 ==>> 检测【检测蓝牙版本】
2025-07-31 19:59:35:310 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 19:59:35:314 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 19:59:35:321 ==>> 检测【检测MoBikeId】
2025-07-31 19:59:35:338 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 19:59:35:355 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 19:59:35:360 ==>> 提取到MoBikeId:9999999999
2025-07-31 19:59:35:379 ==>> 检测【检测蓝牙地址】
2025-07-31 19:59:35:382 ==>> 取到目标值:C5E2BF5121A0
2025-07-31 19:59:35:386 ==>> 【检测蓝牙地址】通过,【C5E2BF5121A0】符合目标值【】要求!
2025-07-31 19:59:35:392 ==>> 提取到蓝牙地址:C5E2BF5121A0
2025-07-31 19:59:35:404 ==>> 检测【BOARD_ID】
2025-07-31 19:59:35:408 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 19:59:35:412 ==>> 检测【检测充电电压】
2025-07-31 19:59:35:425 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 19:59:35:429 ==>> 检测【检测VBUS电压1】
2025-07-31 19:59:35:450 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 19:59:35:457 ==>> 检测【检测充电电流】
2025-07-31 19:59:35:486 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 19:59:35:490 ==>> 检测【检测IMEI】
2025-07-31 19:59:35:494 ==>> 取到目标值:867222087739746
2025-07-31 19:59:35:506 ==>> 【检测IMEI】通过,【867222087739746】符合目标值【】要求!
2025-07-31 19:59:35:511 ==>> 提取到IMEI:867222087739746
2025-07-31 19:59:35:519 ==>> 检测【检测IMSI】
2025-07-31 19:59:35:529 ==>> 取到目标值:460130071539083
2025-07-31 19:59:35:549 ==>> 【检测IMSI】通过,【460130071539083】符合目标值【】要求!
2025-07-31 19:59:35:553 ==>> 提取到IMSI:460130071539083
2025-07-31 19:59:35:557 ==>> 检测【校验网络运营商(移动)】
2025-07-31 19:59:35:577 ==>> 取到目标值:460130071539083
2025-07-31 19:59:35:584 ==>> 【校验网络运营商(移动)】通过,【460130071539083】符合目标值【】要求!
2025-07-31 19:59:35:610 ==>> 检测【打开CAN通信】
2025-07-31 19:59:35:623 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 19:59:35:637 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 19:59:35:742 ==>> $GBGGA,115939.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,3,,,40,59,,,40,1*48

$GBGSV,6,2,23,24,,,40,42,,,40,25,,,40,39,,,39,1*70

$GBGSV,6,3,23,14,,,39,13,,,38,38,,,37,1,,,37,1*4E

$GBGSV,6,4,23,2,,,36,8,,,36,40,,,36,16,,,36,1*7C

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,9,,,34,1*7B

$GBGSV,6,6,23,7,,,33,5,,,33,4,,,33,1*41

$GBRMC,115939.510,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115939.510,0.000,767.329,767.329,701.738,2097152,2097152,2097152*69



2025-07-31 19:59:35:821 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 19:59:35:825 ==>> 检测【检测CAN通信】
2025-07-31 19:59:35:830 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 19:59:35:938 ==>> can send success


2025-07-31 19:59:35:983 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 19:59:36:028 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35677
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 19:59:36:092 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 19:59:36:097 ==>> 检测【关闭CAN通信】
2025-07-31 19:59:36:104 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 19:59:36:123 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 19:59:36:163 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 19:59:36:238 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 19:59:36:362 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 19:59:36:366 ==>> 检测【打印IMU STATE】
2025-07-31 19:59:36:370 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 19:59:36:538 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 19:59:36:632 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 19:59:36:639 ==>> 检测【六轴自检】
2025-07-31 19:59:36:661 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 19:59:36:733 ==>> $GBGGA,115940.510,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,59,,,40,24,,,40,1*7D

$GBGSV,6,2,23,42,,,40,3,,,39,14,,,39,25,,,39,1*4A

$GBGSV,6,3,23,13,,,38,39,,,38,38,,,37,1,,,37,1*40

$GBGSV,6,4,23,16,,,37,2,,,36,8,,,36,40,,,36,1*7D

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,7,,,34,1*75

$GBGSV,6,6,23,9,,,34,5,,,33,4,,,32,1*49

$GBRMC,115940.510,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115940.510,0.000,765.525,765.525,700.088,2097152,2097152,2097152*6A



2025-07-31 19:59:36:838 ==>> [D][05:18:25][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 19:59:37:329 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 19:59:37:734 ==>> $GBGGA,115941.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,3,,,40,59,,,40,1*48

$GBGSV,6,2,23,24,,,40,42,,,40,25,,,40,39,,,39,1*70

$GBGSV,6,3,23,14,,,39,13,,,38,1,,,38,38,,,37,1*41

$GBGSV,6,4,23,16,,,37,2,,,36,8,,,36,40,,,36,1*7D

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,7,,,34,1*75

$GBGSV,6,6,23,9,,,34,5,,,33,4,,,32,1*49

$GBRMC,115941.510,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115941.510,0.000,769.129,769.129,703.384,2097152,2097152,2097152*67



2025-07-31 19:59:38:555 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 19:59:38:824 ==>> $GBGGA,115942.510,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,3,,,40,59,,,40,1*48

$GBGSV,6,2,23,24,,,40,42,,,40,25,,,40,39,,,39,1*70

$GBGSV,6,3,23,14,,,39,13,,,38,1,,,38,38,,,37,1*41

$GBGSV,6,4,23,16,,,37,40,,,36,2,,,35,8,,,35,1*7D

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,7,,,34,1*75

$GBGSV,6,6,23,9,,,34,5,,,33,4,,,32,1*49

$GBRMC,115942.510,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115942.510,0.000,767.332,767.332,701.742,2097152,2097152,2097152*68

[D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38405 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,-11,4054]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 19:59:38:962 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 19:59:38:967 ==>> 检测【打印IMU STATE2】
2025-07-31 19:59:38:971 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 19:59:39:125 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 19:59:39:241 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 19:59:39:250 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 19:59:39:265 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 19:59:39:338 ==>> [D][05:18:27][COMM]read battery soc:255
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 19:59:39:518 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 19:59:39:528 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 19:59:39:538 ==>> 检测【检测VBUS电压2】
2025-07-31 19:59:39:566 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 19:59:39:915 ==>> $GBGGA,115943.510,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,59,,,40,24,,,40,42,,,40,1*7D

$GBGSV,6,2,23,25,,,40,60,,,39,3,,,39,13,,,38,1*4C

$GBGSV,6,3,23,39,,,38,14,,,38,38,,,37,1,,,37,1*47

$GBGSV,6,4,23,40,,,36,16,,,36,2,,,35,8,,,35,1*7C

$GBGSV,6,5,23,6,,,35,41,,,35,26,,,34,9,,,34,1*7A

$GBGSV,6,6,23,7,,,33,5,,,32,4,,,32,1*41

$GBRMC,115943.510,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115943.510,0.000,759.238,759.238,694.340,2097152,2097152,2097152*62

[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539083
[D][05:18:28][FCTY]HardwareID  = 867222087739746
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C5E2BF5121A0
[D][05:18:28][FCTY]Bat         = 3924 mv
[D

2025-07-31 19:59:40:005 ==>> ][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:28][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3718 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 19:59:40:054 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 19:59:40:401 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539083
[D][05:18:28][FCTY]HardwareID  = 867222087739746
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C5E2BF5121A0
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 50 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 174
[D][05:18:28][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 371

2025-07-31 19:59:40:431 ==>> 8 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 19:59:40:590 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 19:59:40:734 ==>> $GBGGA,115944.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,59,,,40,24,,,40,42,,,40,1*7D

$GBGSV,6,2,23,25,,,40,60,,,39,3,,,39,13,,,38,1*4C

$GBGSV,6,3,23,39,,,38,14,,,38,38,,,37,1,,,37,1*47

$GBGSV,6,4,23,40,,,36,16,,,36,2,,,35,8,,,35,1*7C

$GBGSV,6,5,23,26,,,35,6,,,35,41,,,35,9,,,34,1*7B

$GBGSV,6,6,23,7,,,33,5,,,32,4,,,32,1*41

$GBRMC,115944.510,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,115944.510,0.000,760.136,760.136,695.161,2097152,2097152,2097152*65



2025-07-31 19:59:41:019 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539083
[D][05:18:29][FCTY]HardwareID  = 867222087739746
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C5E2BF5121A0
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 50 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 145
[D][05:18:29][FCTY]Acckey1 vol = 5559 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][

2025-07-31 19:59:41:049 ==>> FCTY]Bat1         = 3718 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 19:59:41:094 ==>>                                                                                                                                                                           

2025-07-31 19:59:41:127 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 19:59:41:803 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539083
[D][05:18:29][FCTY]HardwareID  = 867222087739746
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C5E2BF5121A0
[D][05:18:29][FCTY]Bat         = 3704 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 5000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 123
[D][05:18:29][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 101 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][

2025-07-31 19:59:41:908 ==>> FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3718 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]all continue location stop
[W][05:18:30][GNSS]sing locating running
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30]

2025-07-31 19:59:41:934 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 19:59:41:944 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 19:59:41:970 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 19:59:42:013 ==>> [PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================

2025-07-31 19:59:42:118 ==>> ================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B51E3E391998BFD5FB81380ED6FFDB729F76589B2B86B36085D3A30C0FFBCE2974E0F794AB5A7D3C2D7696AD2FEF15024F620D4C256AB0B7AA4677F10C1248445AB281C2755F696A0D82DD8171BBDDD16558B5
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle be

2025-07-31 19:59:42:163 ==>> come true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 19:59:42:226 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 19:59:42:234 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 19:59:42:256 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 19:59:42:268 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:18:30][COMM]read battery soc:255


2025-07-31 19:59:42:328 ==>> 5A A5 04 5A A5 


2025-07-31 19:59:42:433 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 19:59:42:496 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 19:59:42:503 ==>> 检测【打开WIFI(3)】
2025-07-31 19:59:42:526 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 19:59:42:738 ==>> [D][05:18:31][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 19:59:42:773 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 19:59:42:780 ==>> 检测【扩展芯片hw】
2025-07-31 19:59:42:801 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 19:59:43:819 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 19:59:43:828 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 19:59:44:364 ==>>                       _peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:

2025-07-31 19:59:44:469 ==>> 32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1

2025-07-31 19:59:44:574 ==>> 629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][PROT]index:1
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is 

2025-07-31 19:59:44:679 ==>> not inited or not connected or cccd not enabled
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][COMM]read battery soc:255
                                                            

2025-07-31 19:59:44:784 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1

2025-07-31 19:59:44:829 ==>> ], build_time:[GD  May 24 2024]
[W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 19:59:44:862 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 19:59:44:871 ==>> 检测【扩展芯片boot】
2025-07-31 19:59:44:885 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 19:59:44:891 ==>> 检测【扩展芯片sw】
2025-07-31 19:59:44:907 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 19:59:44:912 ==>> 检测【检测音频FLASH】
2025-07-31 19:59:44:917 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 19:59:44:995 ==>> [D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44622]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 19:59:45:101 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 19:59:45:400 ==>> [D][05:18:34][COMM]45056 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:45:722 ==>> [D][05:18:34][CAT1]SEND RAW data timeout
[D][05:18:34][CAT1]exec over: func id: 12, ret: -52


2025-07-31 19:59:46:014 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45649]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 19:59:46:273 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 19:59:46:423 ==>> [D][05:18:35][COMM]46068 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:46:774 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 19:59:47:050 ==>> [D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46680]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 19:59:47:434 ==>> [D][05:18:36][COMM]47081 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:48:280 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 19:59:48:492 ==>> [D][05:18:37][COMM]48092 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 19:59:49:012 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 19:59:49:459 ==>> [D][05:18:38][COMM]49103 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:49:972 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 19:59:49:982 ==>> 检测【打开喇叭声音】
2025-07-31 19:59:50:003 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 19:59:50:138 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 19:59:50:247 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 19:59:50:253 ==>> 检测【打开大灯控制】
2025-07-31 19:59:50:277 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 19:59:50:290 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 19:59:50:470 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<
[D][05:18:39][COMM]50114 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:50:519 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 19:59:50:547 ==>> 检测【关闭仪表供电3】
2025-07-31 19:59:50:555 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 19:59:50:726 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 19:59:50:794 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 19:59:50:800 ==>> 检测【关闭AccKey2供电3】
2025-07-31 19:59:50:821 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 19:59:51:019 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 19:59:51:098 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 19:59:51:104 ==>> 检测【读大灯电压】
2025-07-31 19:59:51:124 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 19:59:51:244 ==>> [E][05:18:39][GNSS]GPS module no nmea data!
[D][05:18:39][GNSS]GPS reload stop. ret=0
[D][05:18:39][GNSS]GPS reload start. ret=0


2025-07-31 19:59:51:319 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[33131]


2025-07-31 19:59:51:399 ==>> 【读大灯电压】通过,【33131mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 19:59:51:409 ==>> 检测【关闭大灯控制2】
2025-07-31 19:59:51:441 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 19:59:51:484 ==>> [D][05:18:40][COMM]51126 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:51:589 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 19:59:51:707 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 19:59:51:714 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 19:59:51:719 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 19:59:51:925 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[69]


2025-07-31 19:59:52:010 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 19:59:52:021 ==>> 检测【打开WIFI(4)】
2025-07-31 19:59:52:049 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 19:59:52:199 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 19:59:52:289 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 19:59:52:452 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 19:59:52:459 ==>> 检测【EC800M模组版本】
2025-07-31 19:59:52:468 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:52:486 ==>> [D][05:18:41][COMM]52136 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:53:501 ==>> [D][05:18:42][COMM]53147 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:53:510 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:54:309 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][COMM]read battery soc:255


2025-07-31 19:59:54:522 ==>> [D][05:18:43][COMM]54160 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 19:59:54:537 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:55:503 ==>> [D][05:18:44][COMM]imu error,enter wait


2025-07-31 19:59:55:577 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:55:984 ==>> [D][05:18:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 19:59:56:460 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55927]
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1032].open hexlog save
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:44][COMM]file:A20 exist
[D][05:18:44][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:44][COMM]read file, len:15228, num:4
[W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:44][COMM]read battery soc:255
[D][05:18:44][COMM]--->crc16:0x419c
[D][05:18:44][COMM]read file success
[W][05:18:44][COMM][Audio].l:[936].close hexlog save
[D][05:18:44][COMM]accel parse set 1
[D][05:18:44][COMM][Audio]mon:9,05:18:44
[D][05:18:44][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 19:59:56:612 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:56:794 ==>> [D][05:18:45][CAT1]exec over: func id: 15, ret: -93
[D][05:18:45][CAT1]sub id: 15, ret: -93

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:45][SAL ]socket send fail. id[4]
[D][05:18:45][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:45][CAT1]gsm read msg sub id: 24
[D][05:18:45][M2M ]m2m select fd[4]
[D][05:18:45][M2M ]socket[4] Link is disconnected
[D][05:18:45][M2M ]tcpclient close[4]
[D][05:18:45][SAL ]socket[4] has closed
[D][05:18:45][PROT]protocol read data ok
[E][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:45][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:45][PROT]M2M Send Fail [1629955125]
[D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 19:59:57:390 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57025]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 19:59:57:652 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:58:418 ==>> [D][05:18:46][COMM]read battery soc:255
[W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58053]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 19:59:58:676 ==>> [D][05:18:47][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 19:59:58:697 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 19:59:59:453 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59081]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 19:59:59:726 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:00:312 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 20:00:00:402 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:00:706 ==>> [D][05:18:49][CAT1]exec over: func id: 24, ret: -181
[D][05:18:49][CAT1]sub id: 24, ret: -181

[D][05:18:49][CAT1]gsm read msg sub id: 23
[D][05:18:49][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 20:00:00:766 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:00:976 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:01:216 ==>> [D][05:18:49][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 20:00:01:321 ==>> [D][05:18:49][GNSS]recv submsg id[1]
[D][05:18:49][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:49][GNSS]stop gps fail
[E][05:18:49][GNSS]GPS module no nmea data!
[D][05:18:49][GNSS]GPS reload stop. ret=0
[D][05:18:49][GNSS]GPS reload start. ret=0


2025-07-31 20:00:01:742 ==>> [D][05:18:50][CAT1]exec over: func id: 23, ret: -151
[D][05:18:50][CAT1]sub id: 23, ret: -151

[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 20:00:01:787 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:02:345 ==>> [D][05:18:50][GNSS]recv submsg id[1]
[D][05:18:50][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:50][GNSS]start gps fail
[D][05:18:50][COMM]read battery soc:255


2025-07-31 20:00:02:450 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:02:826 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:03:481 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:03:873 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:04:337 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 20:00:04:503 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:04:778 ==>> [D][05:18:53][CAT1]SEND RAW data timeout
[D][05:18:53][CAT1]exec over: func id: 12, ret: -52
[W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:00:04:913 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:05:965 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:05:980 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:06:362 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:00:06:805 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:07:004 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:07:790 ==>> [D][05:18:56][CAT1]SEND RAW data timeout
[D][05:18:56][CAT1]exec over: func id: 12, ret: -52
[W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:56][CAT1]gsm read msg sub id: 12
[D][05:18:56][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:00:08:051 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:08:362 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:00:08:467 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:09:094 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:09:821 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:10:140 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:10:356 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:00:10:814 ==>> [D][05:18:59][CAT1]SEND RAW data timeout
[D][05:18:59][CAT1]exec over: func id: 12, ret: -52
[W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:59][CAT1]gsm read msg sub id: 10
[D][05:18:59][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:00:10:981 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:11:179 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:11:383 ==>> [E][05:19:00][GNSS]GPS module no nmea data!
[D][05:19:00][GNSS]GPS reload stop. ret=0
[D][05:19:00][GNSS]GPS reload start. ret=0


2025-07-31 20:00:11:795 ==>> [D][05:19:00][COMM]f:[drv_audio_ack_receive].wait ack timeout!![71424]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM][Audio].l:[1032].open hexlog save
[D][05:19:00][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 20:00:12:213 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:12:353 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 20:00:12:825 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:13:257 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:13:473 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:14:299 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:14:377 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 20:00:14:882 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:15:344 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:15:976 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:16:373 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:16:382 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 20:00:16:915 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:17:402 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:18:400 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 20:00:18:445 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:18:505 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:00:18:792 ==>> [D][05:19:07][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:00:18:974 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:19:484 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:20:400 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 20:00:20:505 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:21:024 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:21:465 ==>> [E][05:19:10][GNSS]GPS module no nmea data!
[D][05:19:10][GNSS]GPS reload stop. ret=0
[D][05:19:10][GNSS]GPS reload start. ret=0


2025-07-31 20:00:21:540 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:22:400 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 20:00:22:583 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:00:23:232 ==>> [D][05:19:11][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:11][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:00:23:613 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 20:00:23:622 ==>> #################### 【测试结束】 ####################
2025-07-31 20:00:23:720 ==>> 关闭5V供电
2025-07-31 20:00:23:730 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:00:23:844 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:00:24:414 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 20:00:24:734 ==>> 关闭5V供电成功
2025-07-31 20:00:24:743 ==>> 关闭33V供电
2025-07-31 20:00:24:755 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:00:24:841 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:00:25:022 ==>> [D][05:19:13][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:13][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:13][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 20:00:25:744 ==>> 关闭33V供电成功
2025-07-31 20:00:25:755 ==>> 关闭3.7V供电
2025-07-31 20:00:25:771 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:00:25:834 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:00:26:135 ==>>  

2025-07-31 20:00:26:211 ==>>   

