2025-07-31 22:24:05:437 ==>> MES查站成功:
查站序号:P5100010053126B1验证通过
2025-07-31 22:24:05:451 ==>> 扫码结果:P5100010053126B1
2025-07-31 22:24:05:452 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:24:05:454 ==>> 测试参数版本:2024.10.11
2025-07-31 22:24:05:455 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:24:05:456 ==>> 检测【打开透传】
2025-07-31 22:24:05:458 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:24:05:493 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:24:05:726 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:24:05:797 ==>> 检测【检测接地电压】
2025-07-31 22:24:05:799 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:24:05:898 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:24:06:085 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:24:06:087 ==>> 检测【打开小电池】
2025-07-31 22:24:06:089 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:24:06:201 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:24:06:371 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:24:06:373 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:24:06:375 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:24:06:508 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:24:06:647 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:24:06:650 ==>> 检测【等待设备启动】
2025-07-31 22:24:06:653 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:24:06:998 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:24:07:180 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:24:07:681 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:24:07:771 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:24:07:967 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:24:08:632 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:24:08:677 ==>>                                                    

2025-07-31 22:24:08:707 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:24:09:082 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:24:09:551 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:24:09:748 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:24:09:751 ==>> 检测【产品通信】
2025-07-31 22:24:09:753 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:24:10:278 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:24:10:460 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:24:10:784 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:24:11:144 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 22:24:11:538 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:24:11:812 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:24:12:029 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:24:12:089 ==>>                                                                                                  

2025-07-31 22:24:12:252 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:24:12:255 ==>> 检测【初始化完成检测】
2025-07-31 22:24:12:259 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:24:12:534 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:24:12:684 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:24:12:780 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:24:12:782 ==>> 检测【关闭大灯控制1】
2025-07-31 22:24:12:784 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:24:12:956 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:24:13:061 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:

2025-07-31 22:24:13:078 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:24:13:080 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:24:13:082 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:24:13:091 ==>> [-1]. goto init


2025-07-31 22:24:13:196 ==>>    [05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:24:13:301 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:24:13:388 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:24:13:391 ==>> 检测【关闭仪表供电】
2025-07-31 22:24:13:394 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:24:13:592 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:24:13:681 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:24:13:683 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:24:13:685 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:24:13:878 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:24:13:961 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:24:13:964 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:24:13:966 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:24:14:107 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:14:182 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:24:14:231 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:24:14:233 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:24:14:234 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:24:14:394 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:24:14:503 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:24:14:516 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:24:14:519 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:24:14:605 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:24:14:680 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:24:14:774 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:24:14:777 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:24:14:780 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:24:14:906 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:24:15:046 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:24:15:048 ==>> 该项需要延时执行
2025-07-31 22:24:15:104 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:15:661 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5017. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5018. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5018. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5018. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5019. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5019. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5019. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5020. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5020. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5020. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5021. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5021
[D][05:17:54][COMM]

2025-07-31 22:24:15:691 ==>> CAN message bat fault change: 0x0001802E->0x01B987FE 5022


2025-07-31 22:24:16:131 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:16:237 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][C

2025-07-31 22:24:16:267 ==>> OMM]------------ready to Power on Acckey 2------------


2025-07-31 22:24:16:759 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file statu

2025-07-31 22:24:16:865 ==>> s fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D

2025-07-31 22:24:16:971 ==>> ][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:1

2025-07-31 22:24:17:031 ==>> 7:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 22:24:17:135 ==>> [D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:17:241 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 22:24:18:151 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:18:679 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:24:19:058 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:24:19:061 ==>> 检测【33V输入电压ADC】
2025-07-31 22:24:19:063 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:19:164 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:19:406 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:17:57][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:17:57][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:11 mv
[D][05:17:57][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 22:24:19:622 ==>> 【33V输入电压ADC】通过,【32931mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:24:19:626 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:24:19:629 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:24:19:712 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1666mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 1990mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:24:19:918 ==>> 【TP7_VCC3V3(ADV2)】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:19:922 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:24:19:960 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:19:962 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:24:19:965 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 22:24:20:002 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:24:20:005 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:24:20:045 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:24:20:048 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:24:20:095 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:24:20:098 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:24:20:168 ==>> [D][05:17:58][COMM]9703 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:20:213 ==>> 1A A1 00 00 FC 
Get AD_V2 1636mV
Get AD_V3 1666mV
Get AD_V4 0mV
Get AD_V5 2762mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:24:20:397 ==>> 【TP7_VCC3V3(ADV2)】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:20:399 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:24:20:440 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:20:444 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:24:20:447 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 22:24:20:482 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:24:20:485 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:24:20:513 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 22:24:20:549 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:24:20:551 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:24:20:618 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:24:20:623 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:24:20:710 ==>> [D][05:17:59][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1993mV
Get AD_V7 1090mV
OVER 150


2025-07-31 22:24:20:920 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:24:20:936 ==>> 【TP7_VCC3V3(ADV2)】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:20:952 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:24:20:977 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:20:979 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:24:20:983 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 22:24:21:018 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:24:21:021 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:24:21:069 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:24:21:073 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:24:21:111 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:24:21:113 ==>> 检测【打开WIFI(1)】
2025-07-31 22:24:21:115 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:24:21:375 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10713 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59]

2025-07-31 22:24:21:435 ==>> [CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:24:21:652 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:24:21:655 ==>> 检测【清空消息队列(1)】
2025-07-31 22:24:21:656 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:24:21:859 ==>>                                    05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087842862

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541251

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:24:21:949 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:24:22:180 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:24:22:284 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:24:22:287 ==>> 检测【打开GPS(1)】
2025-07-31 22:24:22:291 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:24:22:501 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 22:24:22:566 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:24:22:570 ==>> 检测【打开GSM联网】
2025-07-31 22:24:22:573 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:24:22:696 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:24:22:801 ==>> [D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+GS

2025-07-31 22:24:22:846 ==>> MTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:24:23:105 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:24:23:108 ==>> 检测【打开仪表供电1】
2025-07-31 22:24:23:112 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:24:23:297 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:24:23:398 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:24:23:403 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:24:23:419 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:24:23:600 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:24:23:689 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:24:23:696 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:24:23:702 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:24:23:962 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][COMM]arm_hub read adc[3],val[33085]
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:24:24:187 ==>> [D][05:18:02][COMM]13726 imu init OK


2025-07-31 22:24:24:251 ==>> 【读取主控ADC采集的仪表电压】通过,【33085mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:24:24:264 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:24:24:266 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:24:24:782 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOC

2025-07-31 22:24:24:887 ==>> KET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][COMM]Main Task receive event:4
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:24:25:057 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:24:25:062 ==>> 检测【AD_V20电压】
2025-07-31 22:24:25:071 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:24:25:158 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:24:25:340 ==>> 本次取值间隔时间:171ms
2025-07-31 22:24:25:475 ==>> [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len

2025-07-31 22:24:25:520 ==>>  --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:24:25:550 ==>> 本次取值间隔时间:207ms
2025-07-31 22:24:25:640 ==>> 本次取值间隔时间:77ms
2025-07-31 22:24:25:731 ==>> 本次取值间隔时间:85ms
2025-07-31 22:24:25:735 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:24:25:840 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:24:25:901 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:24:26:130 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:24:26:145 ==>> 本次取值间隔时间:303ms
2025-07-31 22:24:26:163 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:24:26:266 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:24:26:496 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:24:26:631 ==>> 本次取值间隔时间:361ms
2025-07-31 22:24:26:861 ==>> 本次取值间隔时间:219ms
2025-07-31 22:24:26:997 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,60,,,43,25,,,40,40,,,40,41,,,40,1*7E

$GBGSV,3,2,09,33,,,39,34,,,39,39,,,38,12,,,44,1*7B

$GBGSV,3,3,09,24,,,40,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1652.332,1652.332,52.780,2097152,2097152,2097152*47

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 22:24:27:162 ==>> 本次取值间隔时间:290ms
2025-07-31 22:24:27:192 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:24:27:374 ==>> 本次取值间隔时间:211ms
2025-07-31 22:24:27:379 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:24:27:484 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:24:27:561 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:24:27:606 ==>> 1A A1 10 00 00 
Get AD_V20 1658mV
OVER 150


2025-07-31 22:24:27:804 ==>> 本次取值间隔时间:311ms
2025-07-31 22:24:27:828 ==>> 【AD_V20电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:27:831 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:24:27:835 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:24:27:941 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,60,,,42,40,,,41,41,,,41,25,,,40,1*76

$GBGSV,4,2,16,34,,,40,33,,,39,39,,,39,24,,,38,1*73

$GBGSV,4,3,16,7,,,38,12,,,37,59,,,37,11,,,36,1*40

$GBGSV,4,4,16,1,,,43,44,,,43,2,,,37,5,,,36,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.814,1616.814,51.663,2097152,2097152,2097152*48

3A A3 02 00 A3 


2025-07-31 22:24:28:001 ==>> OFF_OUT2
OVER 150


2025-07-31 22:24:28:132 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:24:28:135 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:24:28:139 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:24:28:292 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:24:28:425 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:24:28:430 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:24:28:446 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:24:28:504 ==>> 3A A3 02 01 A3 


2025-07-31 22:24:28:594 ==>> ON_OUT2
OVER 150


2025-07-31 22:24:28:702 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:24:28:705 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:24:28:708 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:24:28:977 ==>> [D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,60,,,41,40,,,41,41,,,41,25,,,40,1*71

$GBGSV,5,2,20,34,,,40,39,,,40,3,,,40,33,,,39,1*43

$GBGSV,5,3,20,59,,,39,24,,,38,7,,,38,11,,,37,1*41

$GBGSV,5,4,20,12,,,36,2,,,36,5,,,34,4,,,33,1*42

$GBGSV,5,5,20,23,,,48,1,,,41,44,,,38,32,,,36,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1588.345,1588.345,50.786,2097152,2097152,2097152*43

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:24:29:236 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:24:29:240 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:24:29:242 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:24:29:388 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:24:29:522 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:24:29:526 ==>> 检测【AD_V21电压】
2025-07-31 22:24:29:542 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:24:29:601 ==>> 1A A1 20 00 00 
Get AD_V21 1018mV
OVER 150


2025-07-31 22:24:29:769 ==>> 本次取值间隔时间:244ms
2025-07-31 22:24:29:787 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:24:29:968 ==>> 1A A1 20 00 00 
Get AD_V21 1653mV
OVER 150
$GBGGA,142433.791,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,41,,,41,60,,,40,25,,,40,1*70

$GBGSV,6,2,23,34,,,40,39,,,40,3,,,40,59,,,40,1*41

$GBGSV,6,3,23,33,,,39,1,,,38,24,,,38,7,,,38,1*73

$GBGSV,6,4,23,16,,,38,11,,,37,12,,,36,2,,,36,1*4C

$GBGSV,6,5,23,10,,,36,9,,,35,4,,,34,23,,,33,1*7D

$GBGSV,6,6,23,5,,,33,32,,,30,44,,,,1*40

$GBRMC,142433.791,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142433.791,0.000,1550.919,1550.919,49.616,2097152,2097152,2097152*51



2025-07-31 22:24:30:243 ==>> 本次取值间隔时间:443ms
2025-07-31 22:24:30:261 ==>> 【AD_V21电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:30:265 ==>> 检测【关闭仪表供电2】
2025-07-31 22:24:30:269 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:24:30:490 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:24:30:538 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:24:30:541 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:24:30:544 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:24:30:797 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!
$GBGGA,142434.591,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,60,,,40,1*7E

$GBGSV,7,2,26,25,,,40,34,,,40,39,,,40,3,,,40,1*4E

$GBGSV,7,3,26,33,,,39,7,,,39,1,,,38,24,,,38,1*76

$GBGSV,7,4,26,16,,,38,11,,,37,10,,,37,12,,,36,1*7A

$GBGSV,7,5,26,2,,,36,9,,,35,6,,,35,44,,,34,1*4F

$GBGSV,7,6,26,23,,,34,4,,,33,5,,,32,38,,,32,1*7F

$GBGSV,7,7,26,32,,,31,43,,,41,1*73

$GBRMC,142434.591,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142434.591,0.000,1537.283,1537.283,49.188,2097152,2097152,2097152*54



2025-07-31 22:24:30:842 ==>>                                          

2025-07-31 22:24:31:074 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:24:31:077 ==>> 检测【打开AccKey2供电】
2025-07-31 22:24:31:081 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:24:31:284 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:24:31:347 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:24:31:350 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:24:31:353 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:31:604 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:10][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:10][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:10][COMM]adc read battery ts volt:13 mv
[D][05:18:10][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:10][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:24:31:709 ==>>                                                    1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,40,60,,,40,1*7E

$GBGSV,7,2,27,25,,,40,34,,,40,39,,,40,3,,,4

2025-07-31 22:24:31:769 ==>> 0,1*4F

$GBGSV,7,3,27,33,,,39,7,,,38,1,,,38,24,,,38,1*76

$GBGSV,7,4,27,16,,,38,11,,,37,10,,,37,43,,,36,1*7F

$GBGSV,7,5,27,12,,,36,9,,,36,6,,,36,2,,,35,1*4C

$GBGSV,7,6,27,44,,,35,23,,,35,4,,,33,5,,,32,1*73

$GBGSV,7,7,27,38,,,32,32,,,31,13,,,16,1*7F

$GBRMC,142435.571,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142435.571,0.000,1504.885,1504.885,48.242,2097152,2097152,2097152*5F



2025-07-31 22:24:31:877 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:24:31:905 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:24:31:909 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:24:32:056 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:24:32:155 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:24:32:158 ==>> 该项需要延时执行
2025-07-31 22:24:32:752 ==>> $GBGGA,142436.551,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,40,60,,,40,1*7E

$GBGSV,7,2,27,25,,,40,34,,,40,39,,,40,3,,,40,1*4F

$GBGSV,7,3,27,33,,,39,7,,,38,1,,,38,24,,,38,1*76

$GBGSV,7,4,27,16,,,37,11,,,37,10,,,36,12,,,36,1*75

$GBGSV,7,5,27,9,,,36,6,,,36,43,,,35,2,,,35,1*4B

$GBGSV,7,6,27,44,,,35,23,,,35,4,,,33,5,,,33,1*72

$GBGSV,7,7,27,38,,,31,32,,,31,13,,,29,1*70

$GBRMC,142436.551,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142436.551,0.000,1520.156,1520.156,48.649,2097152,2097152,2097152*51



2025-07-31 22:24:32:857 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:24:33:749 ==>> $GBGGA,142437.531,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,41,,,41,40,,,40,59,,,40,60,,,40,1*7F

$GBGSV,7,2,27,25,,,40,34,,,40,39,,,40,3,,,40,1*4F

$GBGSV,7,3,27,33,,,39,7,,,39,1,,,38,24,,,38,1*77

$GBGSV,7,4,27,16,,,37,11,,,36,10,,,36,12,,,36,1*74

$GBGSV,7,5,27,9,,,36,6,,,36,2,,,36,43,,,35,1*48

$GBGSV,7,6,27,44,,,35,23,,,35,4,,,33,5,,,32,1*73

$GBGSV,7,7,27,38,,,32,32,,,32,13,,,30,1*78

$GBRMC,142437.531,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142437.531,0.000,760.189,760.189,695.211,2097152,2097152,2097152*69



2025-07-31 22:24:34:709 ==>> $GBGGA,142438.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,41,,,41,60,,,40,1*7F

$GBGSV,7,2,27,3,,,40,39,,,40,34,,,40,25,,,40,1*4F

$GBGSV,7,3,27,33,,,39,7,,,38,24,,,38,1,,,38,1*76

$GBGSV,7,4,27,16,,,37,11,,,37,10,,,36,2,,,36,1*44

$GBGSV,7,5,27,6,,,36,9,,,36,44,,,36,12,,,36,1*7D

$GBGSV,7,6,27,43,,,36,23,,,35,5,,,33,4,,,33,1*76

$GBGSV,7,7,27,38,,,32,32,,,32,13,,,30,1*78

$GBRMC,142438.511,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142438.511,0.000,764.019,764.019,698.713,2097152,2097152,2097152*6E



2025-07-31 22:24:34:861 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:24:35:165 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:24:35:171 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:24:35:184 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:35:512 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:13][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:13][COMM]adc read battery ts volt:12 mv
[D][05:18:13][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:24:35:696 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:24:35:700 ==>> 检测【打开AccKey1供电】
2025-07-31 22:24:35:703 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:24:35:709 ==>> $GBGGA,142439.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,59,,,41,41,,,41,60,,,40,1*70

$GBGSV,7,2,28,3,,,40,39,,,40,34,,,40,25,,,40,1*40

$GBGSV,7,3,28,7,,,39,1,,,39,33,,,39,24,,,38,1*79

$GBGSV,7,4,28,16,,,37,11,,,37,10,,,36,2,,,36,1*4B

$GBGSV,7,5,28,44,,,36,12,,,36,6,,,36,9,,,36,1*72

$GBGSV,7,6,28,43,,,36,23,,,35,5,,,33,4,,,33,1*79

$GBGSV,7,7,28,38,,,32,32,,,32,13,,,31,14,,,31,1*71

$GBRMC,142439.511,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142439.511,0.000,761.863,761.863,696.742,2097152,2097152,2097152*65



2025-07-31 22:24:35:889 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:24:35:970 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:24:35:974 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:24:35:979 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:24:36:102 ==>> 1A A1 00 40 00 
Get AD_V14 2529mV
OVER 150


2025-07-31 22:24:36:223 ==>> 原始值:【2529】, 乘以分压基数【2】还原值:【5058】
2025-07-31 22:24:36:290 ==>> 【读取AccKey1电压(ADV14)前】通过,【5058mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:24:36:293 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:24:36:296 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:36:618 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:15][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:15][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:15][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:15][COMM]adc read battery ts volt:10 mv
[D][05:18:15][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:24:36:708 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:24:36:859 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 22:24:36:864 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:24:36:893 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:24:36:896 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:24:37:084 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:24:37:147 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:24:37:151 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:24:37:154 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:24:37:189 ==>> 1A A1 00 40 00 
Get AD_V14 2530mV
OVER 150


2025-07-31 22:24:37:402 ==>> 原始值:【2530】, 乘以分压基数【2】还原值:【5060】
2025-07-31 22:24:37:420 ==>> 【读取AccKey1电压(ADV14)后】通过,【5060mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:24:37:426 ==>> 检测【打开WIFI(2)】
2025-07-31 22:24:37:431 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:24:37:726 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6
$GBGGA,142441.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,59,,,41,41,,,41,60,,,40,1*70

$GBGSV,7,2,28,3,,,40,39,,,40,34,,,40,25,,,40,1*40

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,33,1*7D

$GBGSV,7,7,28,32,,,33,38,,,32,14,,,32,13,,,31,1*73

$GBRMC,142441.511,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142441.511,0.000,767.026,767.026,701.463,2097152,2097152,2097152*65



2025-07-31 22:24:37:954 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:24:37:961 ==>> 检测【转刹把供电】
2025-07-31 22:24:37:965 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:24:38:180 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:24:38:228 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:24:38:235 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:24:38:244 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:24:38:331 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:24:38:362 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:24:38:407 ==>> 1A A1 00 80 00 
Get AD_V15 2402mV
OVER 150


2025-07-31 22:24:38:482 ==>> 原始值:【2402】, 乘以分压基数【2】还原值:【4804】
2025-07-31 22:24:38:500 ==>> 【读取AD_V15电压(前)】通过,【4804mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:24:38:505 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:24:38:508 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:24:38:605 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:24:38:757 ==>> $GBGGA,142442.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,40,,,41,3,,,41,59,,,41,41,,,41,1*4A

$GBGSV,8,2,29,60,,,40,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,8,3,29,7,,,39,33,,,39,24,,,38,1,,,38,1*76

$GBGSV,8,4,29,16,,,38,10,,,37,11,,,37,2,,,36,1*4B

$GBGSV,8,5,29,44,,,36,6,,,36,9,,,36,12,,,36,1*7C

$GBGSV,8,6,29,43,,,36,23,,,36,5,,,33,4,,,33,1*74

$GBGSV,8,7,29,32,,,33,13,,,32,38,,,32,14,,,32,1*7E

$GBGSV,8,8,29,45,,,36,1*79

$GBRMC,142442.511,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142442.511,0.000,766.287,766.287,700.787,2097152,2097152,2097152*6E

[D][05:18:17][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2427mV
OVER 150


2025-07-31 22:24:38:862 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:24:38:922 ==>> 原始值:【2427】, 乘以分压基数【2】还原值:【4854】
2025-07-31 22:24:38:946 ==>> 【读取AD_V16电压(前)】通过,【4854mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:24:38:950 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:24:38:954 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:39:238 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:17][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:17][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:17][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:17][COMM]adc read battery ts volt:13 mv
[D][05:18:17][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3096  volt:5442 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
+WIFISCAN:4,0,CC057790A620,-63
+WIFISCAN:4,1,F62A7D2297A3,-70
+WIFISCAN:4,2,CC057790A641,-70
+WIFISCAN:4,3,CC057790A640,-70

[D][05:18:17][CAT1]wifi scan report total[4]
                                     

2025-07-31 22:24:39:267 ==>>  

2025-07-31 22:24:39:485 ==>> 【转刹把供电电压(主控ADC)】通过,【5442mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:24:39:489 ==>> 检测【转刹把供电电压】
2025-07-31 22:24:39:494 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:39:821 ==>> $GBGGA,142443.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,41,,,42,40,,,41,3,,,41,59,,,41,1*47

$GBGSV,7,2,28,34,,,41,25,,,41,60,,,40,39,,,40,1*75

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,23,,,36,43,,,35,5,,,33,4,,,33,1*79

$GBGSV,7,7,28,32,,,33,38,,,32,14,,,32,13,,,31,1*73

$GBRMC,142443.511,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142443.511,0.000,768.516,768.516,702.826,2097152,2097152,2097152*69

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:18][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:18][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:18][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:18][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3096  volt:5442 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hu

2025-07-31 22:24:39:866 ==>> b adc read vbat adc:2387  volt:3846 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:24:40:019 ==>> 【转刹把供电电压】通过,【5442mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:24:40:023 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:24:40:028 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:24:40:184 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:24:40:300 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:24:40:304 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:24:40:310 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:24:40:415 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:24:40:506 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:24:40:543 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:24:40:547 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:24:40:552 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:24:40:657 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:24:40:747 ==>> $GBGGA,142444.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,40,,,41,3,,,41,59,,,41,25,,,41,1*48

$GBGSV,8,2,29,41,,,41,60,,,40,39,,,40,34,,,40,1*78

$GBGSV,8,3,29,33,,,40,7,,,39,24,,,38,1,,,38,1*78

$GBGSV,8,4,29,16,,,38,10,,,37,11,,,37,2,,,36,1*4B

$GBGSV,8,5,29,44,,,36,6,,,36,9,,,36,12,,,36,1*7C

$GBGSV,8,6,29,43,,,36,23,,,36,5,,,34,4,,,34,1*74

$GBGSV,8,7,29,32,,,33,38,,,32,14,,,32,13,,,31,1*7D

$GBGSV,8,8,29,19,,,58,1*78

$GBRMC,142444.511,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142444.511,0.000,768.506,768.506,702.817,2097152,2097152,2097152*6C

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 22:24:40:784 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:24:40:788 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:24:40:791 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:24:40:897 ==>> [D][05:18:19][COMM]read battery soc:255
3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:24:41:065 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:24:41:093 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:24:41:099 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:24:41:197 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:24:41:345 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:24:41:351 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:24:41:357 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:24:41:396 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:24:41:649 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:24:41:654 ==>> 检测【左刹电压测试1】
2025-07-31 22:24:41:660 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:41:717 ==>> $GBGGA,142445.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,59,,,41,25,,,41,1*46

$GBGSV,7,2,28,41,,,41,60,,,40,39,,,40,34,,,40,1*76

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,34,1*7A

$GBGSV,7,7,28,38,,,32,14,,,32,32,,,32,13,,,31,1*72

$GBRMC,142445.511,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142445.511,0.000,768.509,768.509,702.819,2097152,2097152,2097152*63



2025-07-31 22:24:41:927 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:20][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:20][COMM]adc read left brake adc:1732  volt:2283 mv
[D][05:18:20][COMM]adc read right brake adc:1729  volt:2279 mv
[D][05:18:20][COMM]adc read throttle adc:1724  volt:2272 mv
[D][05:18:20][COMM]adc read battery ts volt:12 mv
[D][05:18:20][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:24:42:187 ==>> 【左刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 22:24:42:191 ==>> 检测【右刹电压测试1】
2025-07-31 22:24:42:209 ==>> 【右刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 22:24:42:213 ==>> 检测【转把电压测试1】
2025-07-31 22:24:42:232 ==>> 【转把电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 22:24:42:247 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:24:42:250 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:24:42:307 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:24:42:507 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:24:42:512 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:24:42:519 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:24:42:599 ==>> 3A A3 04 00 A3 


2025-07-31 22:24:42:704 ==>> $GBGGA,142446.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,60,,,41,40,,,41,3,,,41,59,,,41,1*47

$GBGSV,7,2,28,41,,,41,39,,,40,34,,,40,25,,,40,1*77

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,34,1*7A

$GBGSV,7,7,28,38,,,32,14,,,32,32,,,32,13,,,31,1*72

$GBRMC,142446.511,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142446.511,0.000,768.509,768.509,702.819,2097152,2097152,2097152*60

OFF_OUT4
OVER 150


2025-07-31 22:24:42:789 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:24:42:793 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:24:42:799 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:24:42:901 ==>> [D][05:18:21][COMM]read battery soc:255
3A A3 05 00 A3 


2025-07-31 22:24:43:006 ==>> OFF_OUT5
OVER 150


2025-07-31 22:24:43:069 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:24:43:097 ==>> 检测【左刹电压测试2】
2025-07-31 22:24:43:101 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:24:43:415 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:21][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:21][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:21][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:21][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:21][COMM]adc read battery ts volt:8 mv
[D][05:18:21][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:21][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:24:43:599 ==>> 【左刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 22:24:43:605 ==>> 检测【右刹电压测试2】
2025-07-31 22:24:43:618 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 22:24:43:621 ==>> 检测【转把电压测试2】
2025-07-31 22:24:43:637 ==>> 【转把电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 22:24:43:642 ==>> 检测【晶振检测】
2025-07-31 22:24:43:647 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:24:43:715 ==>> $GBGGA,142447.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,41,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,2,28,59,,,40,39,,,40,34,,,40,25,,,40,1*7F

$GBGSV,7,3,28,7,,,39,33,,,39,24,,,38,1,,,38,1*78

$GBGSV,7,4,28,16,,,38,10,,,37,11,,,37,2,,,36,1*45

$GBGSV,7,5,28,44,,,36,6,,,36,9,,,36,12,,,36,1*72

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,33,1*7D

$GBGSV,7,7,28,14,,,32,32,,,32,13,,,31,38,,,31,1*71

$GBRMC,142447.511,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142447.511,0.000,763.335,763.335,698.088,2097152,2097152,2097152*63



2025-07-31 22:24:43:805 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 22:24:43:913 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:24:43:917 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:24:43:922 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:24:44:003 ==>> 1A A1 00 00 FC 
Get AD_V2 1636mV
Get AD_V3 1667mV
Get AD_V4 1646mV
Get AD_V5 2764mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 22:24:44:189 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:24:44:193 ==>> 检测【检测BootVer】
2025-07-31 22:24:44:217 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:24:44:587 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071541251
[D][05:18:22][FCTY]HardwareID  = 867222087842862
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = EF42521DE9C1
[D][05:18:22][FCTY]Bat         = 3964 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:22][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 50 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT

2025-07-31 22:24:44:633 ==>> 1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3683 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:24:44:729 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:24:44:734 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:24:44:739 ==>> 检测【检测固件版本】
2025-07-31 22:24:44:743 ==>>                                                                                                                                                                                                                                                                                                         7,4,28,41,43,272,41,34,42,112,40,60,41,238,40,33,37,203,39,1*79

$GBGSV,7,5,28,4,32,111,33,5,22,256,34,24,11,255,38,14,9,322,32,1*48

$GBGSV,7,6,28,38,6,193,31,13,6,201,31,12,,,37,11,,,37,1*7F

$G

2025-07-31 22:24:44:748 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:24:44:768 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:24:44:772 ==>> 检测【检测蓝牙版本】
2025-07-31 22:24:44:789 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:24:44:793 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:24:44:800 ==>> 检测【检测MoBikeId】
2025-07-31 22:24:44:811 ==>> BGSV,7,7,28,44,,,36,23,,,36,43,,,35,32,,,32,1*7C

$GBRMC,142444.518,V,,,,,,,310725,1.0,E,N,V*42

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,142444.518,0.492,0.185,0.184,0.318,1.954,2.148,14*53



2025-07-31 22:24:44:828 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:24:44:831 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:24:44:835 ==>> 检测【检测蓝牙地址】
2025-07-31 22:24:44:838 ==>> 取到目标值:EF42521DE9C1
2025-07-31 22:24:44:858 ==>> 【检测蓝牙地址】通过,【EF42521DE9C1】符合目标值【】要求!
2025-07-31 22:24:44:862 ==>> 提取到蓝牙地址:EF42521DE9C1
2025-07-31 22:24:44:866 ==>> 检测【BOARD_ID】
2025-07-31 22:24:44:888 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:24:44:892 ==>> 检测【检测充电电压】
2025-07-31 22:24:44:903 ==>> 【检测充电电压】通过,【3964mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:24:44:907 ==>> 检测【检测VBUS电压1】
2025-07-31 22:24:44:911 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:24:44:933 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:24:44:937 ==>> 检测【检测充电电流】
2025-07-31 22:24:44:948 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:24:44:952 ==>> 检测【检测IMEI】
2025-07-31 22:24:44:962 ==>> 取到目标值:867222087842862
2025-07-31 22:24:44:966 ==>> 【检测IMEI】通过,【867222087842862】符合目标值【】要求!
2025-07-31 22:24:44:978 ==>> 提取到IMEI:867222087842862
2025-07-31 22:24:44:982 ==>> 检测【检测IMSI】
2025-07-31 22:24:44:990 ==>> 取到目标值:460130071541251
2025-07-31 22:24:44:993 ==>> 【检测IMSI】通过,【460130071541251】符合目标值【】要求!
2025-07-31 22:24:45:008 ==>> 提取到IMSI:460130071541251
2025-07-31 22:24:45:012 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:24:45:015 ==>> 取到目标值:460130071541251
2025-07-31 22:24:45:024 ==>> 【校验网络运营商(移动)】通过,【460130071541251】符合目标值【】要求!
2025-07-31 22:24:45:028 ==>> 检测【打开CAN通信】
2025-07-31 22:24:45:039 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:24:45:098 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:24:45:309 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:24:45:314 ==>> 检测【检测CAN通信】
2025-07-31 22:24:45:331 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:24:45:420 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:24:45:480 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35012
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:24:45:540 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:24:45:600 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:24:45:612 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:24:45:619 ==>> 检测【关闭CAN通信】
2025-07-31 22:24:45:636 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:24:45:660 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:24:45:765 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
$GBGGA,142445.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,16,65,56,37,40,62,169,41,6,62,71,36,3,60,190,40,1*7A

$GBGSV,7,2,28,7,60,218,39,39,59,32,40,59,52,129,41,25,49,347,40,1*70

$GBGSV,7,3,28,1,48,125,39,10,47,220,37,2,45,237,36,9,45,332,36,1*40

$GBGSV,7,4,28,41,43,272,41,34,42,112,40,60,41,238,40,33,37,203,39,1*79

$GBGSV,7,5,28,4,32,111,33,5,22,256,34,24,11,255,38,14,9,322,32,1*48

$GBGSV,7,6,28,38,6,193,32,13,6,201,31,12,,,37,11,,,37,1*7C

$GBGSV,7,7,28,44,,,36,43,,,36,23,,,36,32,,,32,1*7F

$GBGSV,2,1,06,40,62,169,41,39,59,32,40,25,49,347,40,41,43,272,42,5*42

$GBGSV,2,2,06,34,42,112,39,33,37,203,40,5*7C

$GBRMC,142445.518,V,,,,,,,310725,1.0,E,N,V*43

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,142445.518,2.567,0.252,0.250,0.453,2.378,2.471,9.33

2025-07-31 22:24:45:795 ==>> 3*79



2025-07-31 22:24:45:911 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:24:45:916 ==>> 检测【打印IMU STATE】
2025-07-31 22:24:45:933 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:24:46:102 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:24:46:232 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:24:46:240 ==>> 检测【六轴自检】
2025-07-31 22:24:46:273 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:24:46:394 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:24:46:925 ==>> $GBGGA,142446.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,16,65,56,37,40,62,169,41,6,62,71,36,3,60,190,40,1*7A

$GBGSV,7,2,28,7,60,218,39,39,59,32,40,59,52,129,41,25,49,347,40,1*70

$GBGSV,7,3,28,1,48,125,38,10,47,220,36,2,45,237,36,9,45,332,36,1*40

$GBGSV,7,4,28,41,43,272,41,34,42,112,40,60,41,238,40,33,37,203,39,1*79

$GBGSV,7,5,28,4,32,111,33,5,22,256,34,24,11,255,38,14,10,322,32,1*70

$GBGSV,7,6,28,38,6,193,32,13,6,201,31,11,,,37,12,,,36,1*7D

$GBGSV,7,7,28,23,,,36,44,,,35,43,,,35,32,,,32,1*7F

$GBGSV,2,1,06,40,62,169,41,39,59,32,41,25,49,347,40,41,43,272,42,5*43

$GBGSV,2,2,06,34,42,112,39,33,37,203,40,5*7C

$GBRMC,142446.518,V,,,,,,,310725,1.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,142446.518,2.203,0.659,0.650,1.262,1.970,2.041,7.401*7F

                                         

2025-07-31 22:24:47:782 ==>> $GBGGA,142447.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,16,65,56,38,40,62,169,41,6,62,71,36,3,60,190,40,1*75

$GBGSV,7,2,28,7,60,218,39,39,59,32,40,59,52,129,41,25,49,347,40,1*70

$GBGSV,7,3,28,1,48,125,38,10,47,220,37,2,45,237,36,9,45,332,36,1*41

$GBGSV,7,4,28,41,43,272,41,34,42,112,40,60,41,238,41,33,37,203,40,1*76

$GBGSV,7,5,28,4,32,111,33,5,22,256,33,24,11,255,38,14,10,322,32,1*77

$GBGSV,7,6,28,38,6,193,31,13,6,201,31,12,,,37,11,,,37,1*7F

$GBGSV,7,7,28,44,,,36,43,,,36,23,,,36,32,,,32,1*7F

$GBGSV,2,1,06,40,62,169,41,39,59,32,41,25,49,347,40,41,43,272,43,5*42

$GBGSV,2,2,06,34,42,112,39,33,37,203,40,5*7C

$GBRMC,142447.518,V,,,,,,,310725,1.0,E,N,V*41

$GBVTG,0.00,T,,M,0.003,N,0.006,K,N*25

$GBGST,142447.518,2.259,0.211,0.209,0.360,1.902,1.956,6.382*77



2025-07-31 22:24:48:104 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:24:48:209 ==>> [D][05:18:26][COMM]Main Task receive event:142
[D][05:18:26][COMM]###### 37764 imu self test OK ######
[D][05:18:26][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-7,-9,4032]
[D][0

2025-07-31 22:24:48:240 ==>> 5:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 22:24:48:318 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:24:48:325 ==>> 检测【打印IMU STATE2】
2025-07-31 22:24:48:330 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:24:48:499 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:24:48:593 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:24:48:602 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:24:48:623 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:24:48:694 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:24:48:863 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:24:48:871 ==>> 检测【检测VBUS电压2】
2025-07-31 22:24:48:895 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:24:49:799 ==>> $GBGGA,142448.518,2301.2579631,N,11421.9432759,E,1,06,1.34,81.157,M,-1.770,M,,*56

$GBGSA,A,3,40,39,25,41,34,33,,,,,,,4.80,1.34,4.61,4*0A

$GBGSV,7,1,28,16,65,56,38,40,62,169,41,6,62,71,36,3,60,190,40,1*75

$GBGSV,7,2,28,7,60,218,39,39,59,32,40,59,52,129,41,25,49,347,41,1*71

$GBGSV,7,3,28,1,48,125,38,10,47,220,37,2,45,237,36,9,45,332,36,1*41

$GBGSV,7,4,28,41,43,272,41,34,42,112,40,60,41,238,40,33,37,203,39,1*79

$GBGSV,7,5,28,4,32,111,33,5,22,256,33,24,11,255,38,14,10,322,32,1*77

$GBGSV,7,6,28,38,6,193,32,13,6,201,32,11,,,37,44,,,36,1*7D

$GBGSV,7,7,28,12,,,36,43,,,36,23,,,36,32,,,32,1*7C

$GBGSV,2,1,06,40,62,169,41,39,59,32,41,25,49,347,40,41,43,272,42,5*43

$GBGSV,2,2,06,34,42,112,39,33,37,203,40,5*7C

$GBRMC,142448.518,A,2301.2579631,N,11421.9432759,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:18:27][GNSS]HD8040 GPS
[W][05:18:27][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:27][GNSS]GPS diff_sec 124016781, report 0x42 frame
$GBGST,142448.518,2.078,0.235,0.233,0.416,1.728,1.775,5.658*78

[D][05:18:27][COMM]Main Task receive event:131
[D][05:18:27][COMM]index:0,power_mode:0xFF
[D][05:18

2025-07-31 22:24:49:904 ==>> :27][COMM]index:1,sound_mode:0xFF
[D][05:18:27][COMM]index:2,gsensor_mode:0xFF
[D][05:18:27][COMM]index:3,report_freq_mode:0xFF
[D][05:18:27][COMM]index:4,report_period:0xFF
[D][05:18:27][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:27][COMM]index:6,normal_reset_period:0xFF
[D][05:18:27][COMM]index:7,spock_over_speed:0xFF
[D][05:18:27][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:27][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:27][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:27][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:27][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:27][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:27][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:27][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:27][COMM]index:16,imu_config_params:0xFF
[D][05:18:27][COMM]index:17,long_connect_params:0xFF
[D][05:18:27][COMM]index:18,detain_mark:0xFF
[D][05:18:27][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:27][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:27][COMM]index:21,mc_mode:0xFF
[D][05:18:27][COMM]index:22,S_mode:0xFF
[D][05:18:27][COMM]index:23,overweight:0xFF
[D][05:18:27][COMM]index:24,standstill_mode:0xFF
[D

2025-07-31 22:24:50:009 ==>> ][05:18:27][COMM]index:25,night_mode:0xFF
[D][05:18:27][COMM]index:26,experiment1:0xFF
[D][05:18:27][COMM]index:27,experiment2:0xFF
[D][05:18:27][COMM]index:28,experiment3:0xFF
[D][05:18:27][COMM]index:29,experiment4:0xFF
[D][05:18:27][COMM]index:30,night_mode_start:0xFF
[D][05:18:27][COMM]index:31,night_mode_end:0xFF
[D][05:18:27][COMM]index:33,park_report_minutes:0xFF
[D][05:18:27][COMM]index:34,park_report_mode:0xFF
[D][05:18:27][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:27][COMM]index:38,charge_battery_para: FF
[D][05:18:27][COMM]index:39,multirider_mode:0xFF
[D][05:18:27][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:27][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:27][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:27][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:27][COMM]index:44,riding_duration_config:0xFF
[D][05:18:27][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:27][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:27][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:27][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:27][COMM]index:49,mc_load_startup:0xFF
[D][05:18:27][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:27][COMM

2025-07-31 22:24:50:114 ==>> ]index:51,traffic_audio_play:0xFF
[D][05:18:27][COMM]index:52,traffic_mode:0xFF
[D][05:18:27][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:27][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:27][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:27][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:27][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:27][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:27][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:27][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:27][COMM]index:63,experiment5:0xFF
[D][05:18:27][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:27][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:27][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:27][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:27][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:27][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:27][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:27][COMM]index:72,experiment6:0xFF
[D][05:18:27][COMM]index:73,experiment7:0xFF
[D][05:18:27][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:27][COMM]index:75,zero_value_from_server:-1
[

2025-07-31 22:24:50:219 ==>> D][05:18:27][COMM]index:76,multirider_threshold:255
[D][05:18:27][COMM]index:77,experiment8:255
[D][05:18:27][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:27][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:27][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:27][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:27][COMM]index:83,loc_report_interval:255
[D][05:18:27][COMM]index:84,multirider_threshold_p2:255
[D][05:18:27][COMM]index:85,multirider_strategy:255
[D][05:18:27][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:27][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:27][COMM]index:90,weight_param:0xFF
[D][05:18:27][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:27][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:27][COMM]index:95,current_limit:0xFF
[D][05:18:27][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:27][COMM]index:100,location_mode:0xFF

[W][05:18:27][PROT]remove success[1629955107],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:27][COMM]M

2025-07-31 22:24:50:325 ==>> ain Task receive event:131 finished processing
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:1
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[294]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:27][COMM]Main Task receive event:20
[D][05:18:27][GNSS]stop event:1
[D][05:18:27][COMM]frm_peripheral_device_power

2025-07-31 22:24:50:429 ==>> on type 0.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:0------------
[D][05:18:27][COMM]------------ready to Power on Acckey 2------------
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:27][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B54EE9D238A7AA21FCF8735B1412C01A662805D0A2E839175D391945C731DE959D20DF89CD14305A1245B4FA82F97D4E6EE8C410828C5C861D45BEA547CA948A53B914846DDA86A9D33EBBF14A1DB8E72856BA2782D3E9DCBBE5EC943B8A6562992B7D4E1085AF1E14A2EE75728ADF3EA5C4EFCC44388F38CC7F738B101D57A0444AC3
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 

2025-07-31 22:24:50:489 ==>>                                                                                                                                                                           

2025-07-31 22:24:50:655 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 22:24:50:942 ==>>                                  ive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[W][05:18:29][GNSS]sing locating running
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[4],used[0]
[D][05:18:29][PROT]min_index:4, type:0x5D03, priority:3
[D][05:18:29][PROT]index:4
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:8
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===================================

2025-07-31 22:24:51:017 ==>> ========================
[D][05:18:29][PROT]Sending traceid[9999999999900007]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:29][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:29][CAT1]gsm read msg sub id: 24
[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[4],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing


2025-07-31 22:24:51:444 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:24:51:767 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071541251
[D][05:18:30][FCTY]HardwareID  = 867222087842862
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = EF42521DE9C1
[D][05:18:30][FCTY]Bat         = 3824 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:30][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 101 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3683 mv
[D][05:18:3

2025-07-31 22:24:51:827 ==>> 0][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
                                                            

2025-07-31 22:24:51:990 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:24:51:996 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:24:52:004 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:24:52:101 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:24:52:161 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 22:24:52:237 ==>> [D][05:18:30][COMM]read battery soc:255


2025-07-31 22:24:52:281 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:24:52:297 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:24:52:302 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:24:52:403 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:24:52:567 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:24:52:576 ==>> 检测【打开WIFI(3)】
2025-07-31 22:24:52:590 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:24:52:812 ==>> [D][05:18:31][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][COMM]42336 imu init OK
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:54:320 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----

2025-07-31 22:24:54:425 ==>> - get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[5],used[0]
[W][05:18:32][PROT]add success [1629955112],send_pa

2025-07-31 22:24:54:530 ==>> th[3],type[5D03],priority[4],index[5],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:32][PROT]index:5
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:9
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900008]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][CO

2025-07-31 22:24:54:635 ==>> MM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[6],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[6],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]43348 im

2025-07-31 22:24:54:741 ==>> u init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:5 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:9
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900008]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][PROT]CLEAN:0
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][COMM]read battery soc:255


2025-07-31 22:24:54:845 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:24:55:346 ==>> [D][05:18:33][GNSS]recv submsg id[1]
[D][05:18:33][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:33][GNSS]stop gps fail


2025-07-31 22:24:55:865 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45368]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]45371 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:56:153 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:24:56:163 ==>> 检测【扩展芯片hw】
2025-07-31 22:24:56:190 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:24:56:248 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 22:24:56:398 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:34][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:24:56:431 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:24:56:439 ==>> 检测【扩展芯片boot】
2025-07-31 22:24:56:449 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:24:56:454 ==>> 检测【扩展芯片sw】
2025-07-31 22:24:56:471 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:24:56:476 ==>> 检测【检测音频FLASH】
2025-07-31 22:24:56:491 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:24:56:685 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:24:56:880 ==>> [D][05:18:35][COMM]46382 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46397]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:24:57:788 ==>> [D][05:18:36][CAT1]SEND RAW data timeout
[D][05:18:36][CAT1]exec over: func id: 12, ret: -52
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 22:24:57:863 ==>>                                       [D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:58:264 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 22:24:58:871 ==>> [D][05:18:37][COMM]48407 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:24:59:889 ==>> [D][05:18:38][COMM]49419 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:25:00:258 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 22:25:00:589 ==>> [D][05:18:39][COMM]crc 108B
[D][05:18:39][COMM]flash test ok


2025-07-31 22:25:00:889 ==>> [D][05:18:39][COMM]50430 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:25:01:549 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:25:01:555 ==>> 检测【打开喇叭声音】
2025-07-31 22:25:01:562 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:25:01:698 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:40][COMM]file:A20 exist
[D][05:18:40][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:40][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 22:25:01:822 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:25:01:828 ==>> 检测【打开大灯控制】
2025-07-31 22:25:01:837 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:25:01:910 ==>> [D][05:18:40][COMM]51441 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:25:01:985 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:25:02:094 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:25:02:100 ==>> 检测【关闭仪表供电3】
2025-07-31 22:25:02:109 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:25:02:292 ==>> [D][05:18:40][COMM]read battery soc:255
[W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 22:25:02:371 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:25:02:383 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:25:02:390 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:25:02:566 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:25:02:651 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:25:02:657 ==>> 检测【读大灯电压】
2025-07-31 22:25:02:671 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:25:02:791 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[32969]


2025-07-31 22:25:02:896 ==>> [D][05:18:41][COMM]imu error,enter wait


2025-07-31 22:25:02:932 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:25:02:940 ==>> 检测【关闭大灯控制2】
2025-07-31 22:25:02:973 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:25:03:080 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:25:03:212 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:25:03:218 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:25:03:223 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:25:03:385 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:25:03:494 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:25:03:502 ==>> 检测【打开WIFI(4)】
2025-07-31 22:25:03:524 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:25:03:660 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:25:03:888 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:25:03:894 ==>> 检测【EC800M模组版本】
2025-07-31 22:25:03:901 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:04:279 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 22:25:04:922 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:05:727 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:05:972 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:06:288 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55644]
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1032].open hexlog save
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:44][COMM]file:A20 exist
[D][05:18:44][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:44][COMM]read file, len:15228, num:4
[D][05:18:44][COMM]--->crc16:0x419c
[D][05:18:44][COMM]read file success
[W][05:18:44][COMM][Audio].l:[936].close hexlog save
[D][05:18:44][COMM]accel parse set 1
[D][05:18:44][COMM][Audio]mon:9,05:18:44
[D][05:18:44][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

                                         

2025-07-31 22:25:07:009 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:07:224 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56744]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 22:25:07:954 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][CAT1]exec over: func id: 15, ret: -93
[D][05:18:46][CAT1]sub id: 15, ret: -93

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:46][SAL ]socket send fail. id[4]
[D][05:18:46][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:46][M2M ]m2m select fd[4]
[D][05:18:46][M2M ]socket[4] Link is disconnected
[D][05:18:46][M2M ]tcpclient close[4]
[D][05:18:46][SAL ]socket[4] has closed
[D][05:18:46][PROT]protocol read data ok
[E][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:46][PROT]M2M Send Fail [1629955126]
[D][05:18:46][PROT]CLEAN,SEND:5
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 22:25:08:044 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:08:308 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57773]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:46][COMM]read battery soc:255


2025-07-31 22:25:09:090 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:09:275 ==>> [D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58800]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:25:09:817 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:10:129 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:10:297 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 22:25:10:867 ==>> [D][05:18:49][CAT1]SEND RAW data timeout
[D][05:18:49][CAT1]exec over: func id: 12, ret: -52
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:49][CAT1]gsm read msg sub id: 12
[D][05:18:49][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:25:11:096 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:11:171 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:12:200 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:12:306 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:25:12:889 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:13:242 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:13:606 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:13:880 ==>> [D][05:18:52][CAT1]SEND RAW data timeout
[D][05:18:52][CAT1]exec over: func id: 12, ret: -52
[W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 12
[D][05:18:52][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:25:14:281 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:14:296 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 22:25:15:313 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:15:899 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:16:097 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:16:297 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 22:25:16:357 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:16:893 ==>> [D][05:18:55][CAT1]SEND RAW data timeout
[D][05:18:55][CAT1]exec over: func id: 12, ret: -52
[W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:55][CAT1]gsm read msg sub id: 10
[D][05:18:55][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:25:17:397 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:18:315 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 22:25:18:420 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:18:603 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:18:922 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:19:456 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:20:320 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 22:25:20:501 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:20:968 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:21:103 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:21:544 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:21:619 ==>> [D][05:19:00][COMM]f:[drv_audio_ack_receive].wait ack timeout!![71139]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM][Audio].l:[1032].open hexlog save
[D][05:19:00][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 22:25:22:316 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:25:22:592 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:23:028 ==>> [D][05:19:01][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:23:608 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:23:623 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:24:323 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:25:24:659 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:24:877 ==>> [D][05:19:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:25:25:060 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:25:699 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:26:109 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:26:337 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:25:26:732 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:27:125 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:27:768 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:28:348 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:25:28:607 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:28:805 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:29:169 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:29:850 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:30:357 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 22:25:30:896 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:31:097 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:31:202 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:31:941 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:32:361 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 22:25:32:899 ==>> [D][05:19:11][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:25:32:989 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:33:267 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:33:605 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:34:032 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:25:34:359 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 22:25:35:081 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 22:25:35:089 ==>> #################### 【测试结束】 ####################
2025-07-31 22:25:35:187 ==>> 关闭5V供电
2025-07-31 22:25:35:199 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:25:35:323 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
[W][05:19:13][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:36:099 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:25:36:189 ==>> 关闭5V供电成功
2025-07-31 22:25:36:198 ==>> 关闭33V供电
2025-07-31 22:25:36:207 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:25:36:296 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:25:36:492 ==>> [D][05:19:14][COMM]read battery soc:255
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 22:25:37:202 ==>> 关闭33V供电成功
2025-07-31 22:25:37:211 ==>> 关闭3.7V供电
2025-07-31 22:25:37:217 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:25:37:295 ==>> 6A A6 02 A6 6A 


2025-07-31 22:25:37:370 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:25:37:400 ==>> Battery OFF
OVER 150


2025-07-31 22:25:37:868 ==>>  

