2025-07-31 21:44:56:569 ==>> MES查站成功:
查站序号:P51000100531288C验证通过
2025-07-31 21:44:56:574 ==>> 扫码结果:P51000100531288C
2025-07-31 21:44:56:575 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:44:56:576 ==>> 测试参数版本:2024.10.11
2025-07-31 21:44:56:578 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:44:56:579 ==>> 检测【打开透传】
2025-07-31 21:44:56:581 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:44:56:689 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:44:57:089 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:44:57:147 ==>> 检测【检测接地电压】
2025-07-31 21:44:57:148 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:44:57:285 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:44:57:435 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:44:57:438 ==>> 检测【打开小电池】
2025-07-31 21:44:57:440 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:44:57:483 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:44:57:727 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:44:57:729 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:44:57:731 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:44:57:786 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:44:58:011 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:44:58:014 ==>> 检测【等待设备启动】
2025-07-31 21:44:58:017 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:58:262 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:44:58:443 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:44:59:027 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:59:166 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:44:59:543 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:45:00:014 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:45:00:836 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:45:00:839 ==>> 检测【产品通信】
2025-07-31 21:45:00:841 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:45:01:101 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK
[D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:01:206 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[D][05:17:51][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[W][05:17:51][PROT]remove success[1629955071],send

2025-07-31 21:45:01:251 ==>> _path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:45:01:439 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:45:01:444 ==>> 检测【初始化完成检测】
2025-07-31 21:45:01:446 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:45:01:678 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:52][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:52][COMM]----- get Acckey 1 and value:1------------
[D][05:17:52][COMM]----- get Acckey 2 and value:1------------
[D][05:17:52][COMM]SE50 init success!


2025-07-31 21:45:01:747 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:45:01:749 ==>> 检测【关闭大灯控制1】
2025-07-31 21:45:01:750 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:45:01:948 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:45:02:058 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:45:02:061 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:45:02:063 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:45:02:083 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:02:278 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:45:02:399 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:45:02:403 ==>> 检测【关闭仪表供电】
2025-07-31 21:45:02:406 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:45:02:579 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:53][COMM]set POWER 0
[D][05:17:53][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:45:03:112 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:03:201 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:45:03:204 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:45:03:205 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:45:03:352 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:45:03:639 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5010. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5011. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000

2025-07-31 21:45:03:669 ==>> E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 21:45:03:828 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:45:03:831 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:45:03:833 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:45:03:954 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]
[W][05:17:54][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:45:04:134 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:04:425 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:45:04:431 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:45:04:436 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:45:04:576 ==>> [D][05:17:55][HSDK][0] flush to flash addr:[0xE42C00] --- write len --- [256]
[W][05:17:55][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:45:04:747 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:45:04:752 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:45:04:771 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:45:04:891 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:45:04:997 ==>> [D][05:17:55][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:45:05:068 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:45:05:070 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:45:05:072 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:45:05:102 ==>> [D][05:17:55][COMM]6674 imu init OK
[D][05:17:55][C

2025-07-31 21:45:05:132 ==>> OMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:05:177 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:45:05:389 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:45:05:391 ==>> 该项需要延时执行
2025-07-31 21:45:06:149 ==>> [D][05:17:56][COMM]7685 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:06:574 ==>> [D][05:17:57][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:57][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:0------------
[D][05:17:57][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:45:07:069 ==>>                                                                                                                                                                 y 2 and value:1------------
[D][05:17:57][COMM]more than the number of battery plugs
[D][05:17:57][COMM]VBUS is 1
[D][05:17:57][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:57][COMM]file:B50 exist
[D][05:17:57][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:57][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:57][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:57][COMM]Bat auth off fail, error:-1
[D][05:17:57][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:1------------
[D][05:17:57][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:1------------
[D][05:17:57][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:57][COMM][Audio].l:[904].echo is not ready
[D][05:17:57][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:57][COMM]Main Task receive event:65
[D][05:17:57][

2025-07-31 21:45:07:174 ==>> COMM]main task tmp_sleep_event = 80
[D][05:17:57][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:57][COMM]Main Task receive event:65 finished processing
[D][05:17:57][COMM]Main Task receive event:66
[D][05:17:57][COMM]Try to Auto Lock Bat
[D][05:17:57][COMM]Main Task receive event:66 finished processing
[D][05:17:57][COMM]Main Task receive event:60
[D][05:17:57][COMM]smart_helmet_vol=255,255
[D][05:17:57][COMM]BAT CAN get state1 Fail 204
[D][05:17:57][COMM]BAT CAN get soc Fail, 204
[D][05:17:57][COMM]get soc error
[E][05:17:57][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:57][COMM]report elecbike
[W][05:17:57][PROT]remove success[1629955077],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:57][COMM]Receive Bat Lock cmd 0
[D][05:17:57][COMM]VBUS is 1
[W][05:17:57][PROT]add success [1629955077],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:57][COMM]Main Task receive event:60 finished processing
[D][05:17:57][COMM]Main Task receive event:61
[D][05:17:57][COMM][D301]:type:3, trace id:280
[D][05:17:57][COMM]id[], hw[000
[D][05:17:57][COMM]get mcMaincircuitVolt error
[D][05:17:57][COMM]get mcSubcircuitVolt e

2025-07-31 21:45:07:279 ==>> rror
[D][05:17:57][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:57][COMM]BAT CAN get state1 Fail 204
[D][05:17:57][COMM]BAT CAN get soc Fail, 204
[D][05:17:57][COMM]get bat work state err
[D][05:17:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:57][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:57][PROT]remove success[1629955077],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:57][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:57][PROT]index:2
[D][05:17:57][PROT]is_send:1
[D][05:17:57][PROT]sequence_num:2
[D][05:17:57][PROT]retry_timeout:0
[D][05:17:57][PROT]retry_times:3
[D][05:17:57][PROT]send_path:0x3
[D][05:17:57][PROT]msg_type:0x5d03
[D][05:17:57][PROT]===========================================================
[W][05:17:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955077]
[D][05:17:57][PROT]===========================================================
[D][05:17:57][PROT]Sending traceid[9999999999900003]
[D][05:17:57][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:57][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:57][PROT]ble is not inited or not 

2025-07-31 21:45:07:354 ==>> connected or cccd not enabled
[W][05:17:57][PROT]add success [1629955077],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:57][COMM]Main Task receive event:61 finished processing
[D][05:17:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:57][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:57][CAT1]power_urc_cb ret[76]
[D][05:17:57][COMM]read battery soc:255
                                                                                                 

2025-07-31 21:45:08:171 ==>> [D][05:17:58][COMM]9708 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:08:517 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020


2025-07-31 21:45:09:002 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 21:45:09:168 ==>> [D][05:17:59][COMM]10721 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:09:393 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:45:09:396 ==>> 检测【33V输入电压ADC】
2025-07-31 21:45:09:398 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:09:698 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:00][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:00][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:00][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:00][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:00][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:00][COMM]adc read battery ts volt:7 mv
[D][05:18:00][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:00][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:00][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:00][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:00][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:18:00][COMM]arm_hub adc read board id adc:3361  volt:2707 mv
[D][05:18:00][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:45:09:953 ==>> 【33V输入电压ADC】通过,【32678mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:45:09:956 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:45:09:960 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:45:10:140 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx 

2025-07-31 21:45:10:200 ==>> ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2763mV
Get AD_V6 1990mV
Get AD_V7 1089mV
OVER 150
                                         

2025-07-31 21:45:10:620 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:10:626 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:45:10:671 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:10:700 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:45:10:703 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 21:45:10:705 ==>>                      < 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088082104

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541519

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:45:10:977 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:45:10:979 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:45:11:026 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:45:11:029 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:45:11:098 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:45:11:101 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:45:11:106 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:45:11:196 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1990mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:45:11:405 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:11:408 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:45:11:442 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:11:444 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:45:11:447 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 21:45:11:490 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:45:11:492 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:45:11:552 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:45:11:555 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:45:11:618 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:45:11:621 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:45:11:697 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 21:45:11:802 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT

2025-07-31 21:45:11:907 ==>> 1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CSQ: 23,99



2025-07-31 21:45:11:912 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:11:924 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:45:11:952 ==>> OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.59.8.214"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:45:11:961 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:11:966 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:45:11:969 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 21:45:12:226 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:45:12:229 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:45:12:251 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:45:12:257 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:45:12:291 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:45:12:294 ==>> 检测【打开WIFI(1)】
2025-07-31 21:45:12:299 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:45:12:342 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:02][PROT]index:2 1629955082
[D][05:18:02][PROT]is_send:0
[D][05:18:02][PROT]sequence_num:2
[D][05:18:02][PROT]retry_timeout:0
[D][05:18:02][PROT]retry_times:3
[D][05:18:02][PROT]send_path:0x2
[D][05:18:02][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:02][PROT]===========================================================
[D][05:18:02][HSDK][0] flush to flash addr:[0xE42D00] --- write len --- [256]
[W][05:18:02][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955082]
[D][05:18:02][PROT]===========================================================
[D][05:18:02][PROT]sending traceid [9999999999900003]
[D][05:18:02][PROT]Send_TO_M2M [1629955082]
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:02][SAL ]sock send credit cnt[6]
[D][05:18:02][SAL ]sock send ind credit cnt[6]
[D][05:18:0

2025-07-31 21:45:12:447 ==>> 2][M2M ]m2m send data len[198]
[D][05:18:02][SAL ]Cellular task submsg id[10]
[D][05:18:02][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:02][CAT1]gsm read msg sub id: 15
[D][05:18:02][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:02][CAT1]Send Data To Server[198][198] ... ->:
0063B98A113311331133113311331B88B9B8D380F606FD12CBD461CC4D2D8663715310A994EF3BE11C2DAEC169DAA3A8E3D53FC4627D807852DF1209A541C34BF5AC54D2E31AB967C3C6FA544E531696727ED5ABE70341BFD4633CF4B2917046718F4F
[D][05:18:02][CAT1]<<< 
SEND OK

[D][05:18:02][CAT1]exec over: func id: 15, ret: 11
[D][05:18:02][CAT1]sub id: 15, ret: 11

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:02][PROT]M2M Send ok [1629955082]
[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][COMM]13734 imu init OK


2025-07-31 21:45:12:477 ==>> 


2025-07-31 21:45:12:552 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:45:12:840 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:45:12:843 ==>> 检测【清空消息队列(1)】
2025-07-31 21:45:12:845 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:45:13:081 ==>> [D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:03][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:45:13:127 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:45:13:130 ==>> 检测【打开GPS(1)】
2025-07-31 21:45:13:133 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:45:13:488 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:03][COMM]Open GPS Module...
[D][05:18:03][COMM]LOC_MODEL_CONT
[D][05:18:03][GNSS]start event:8
[D][05:18:03][GNSS]GPS start. ret=0
[W][05:18:03][GNSS]start cont locating
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:45:13:661 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:45:13:664 ==>> 检测【打开GSM联网】
2025-07-31 21:45:13:666 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:45:13:870 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:04][COMM]GSM test
[D][05:18:04][COMM]GSM test enable


2025-07-31 21:45:13:936 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:45:13:939 ==>> 检测【打开仪表供电1】
2025-07-31 21:45:13:941 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:45:14:215 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:04][COMM]set POWER 1
[D][05:18:04][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:45:14:514 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:45:14:517 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:45:14:521 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:45:14:674 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, ON!
[D][05:18:05][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:45:14:832 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:45:14:835 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:45:14:840 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:45:14:869 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:45:15:095 ==>>                          *74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,25,,,42,33,,,42,24,,,39,59,,,39,1*7D

$GBGSV,2,2,05,5,,,39,1*4C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:05][CAT1]<<< 
OK

$GBGST,,0.000,1699.708,1699.708,54.290,2097152,2097152,2097152*45

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[W][05:18:05][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][COMM]arm_hub read adc[3],val[33363]
[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[D][05:18:05][COMM]read battery soc:255


2025-07-31 21:45:15:186 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:45:15:401 ==>> 【读取主控ADC采集的仪表电压】通过,【33363mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:45:15:404 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:45:15:406 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:45:15:581 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:45:15:696 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:45:15:700 ==>> 检测【AD_V20电压】
2025-07-31 21:45:15:702 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:45:15:808 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:45:15:884 ==>> 1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 21:45:15:990 ==>> [D][05:18:06][HSDK]need to erase for write: is[0x0] ie[0x1E00]
[D][05:18:06][HSDK][0] flush to flash addr:[0xE42E00] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,25,,,42,33,,,42,59,,,41,24,,,40,1*7B

$GBGSV,3,2,12,60,,,40,7,,,37,16,,,37,23,,,36,1*42

$GBGSV,3,3,12,44,,,35,1,,,35,3,,,41,2,,,36,1*45

$GBRMC,,V,,,,,,,,0.0,

2025-07-31 21:45:16:020 ==>> E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.130,1596.130,51.046,2097152,2097152,2097152*49



2025-07-31 21:45:16:246 ==>> 本次取值间隔时间:430ms
2025-07-31 21:45:16:265 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:16:271 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:45:16:293 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:45:16:385 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:45:16:556 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:45:16:558 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:45:16:562 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:45:16:778 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:45:16:831 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:45:16:834 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:45:16:838 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:45:16:883 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:45:17:065 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,25,,,42,33,,,42,59,,,41,41,,,41,1*74

$GBGSV,5,2,19,24,,,40,60,,,40,39,,,40,40,,,40,1*77

$GBGSV,5,3,19,7,,,38,16,,,38,23,,,37,2,,,37,1*7B

$GBGSV,5,4,19,44,,,37,34,,,37,1,,,36,5,,,35,1*7F

$GBGSV,5,5,19,4,,,33,38,,,32,3,,,41,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1580.017,1580.017,50.540,2097152,2097152,2097152*4B

[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:45:17:118 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:45:17:124 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:45:17:128 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:45:17:275 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:45:17:401 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:45:17:404 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:45:17:408 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:45:17:580 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:45:17:678 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:45:17:682 ==>> 检测【AD_V21电压】
2025-07-31 21:45:17:685 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:45:17:792 ==>> 1A A1 20 00 00 
Get AD_V21 1645mV
OVER 150


2025-07-31 21:45:18:050 ==>> $GBGGA,134521.858,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,25,,,42,33,,,42,3,,,42,59,,,41,1*49

$GBGSV,6,2,21,41,,,41,60,,,41,40,,,41,24,,,40,1*71

$GBGSV,6,3,21,39,,,40,7,,,38,16,,,38,34,,,38,1*42

$GBGSV,6,4,21,23,,,37,44,,,37,1,,,37,2,,,36,1*74

$GBGSV,6,5,21,5,,,34,4,,,33,38,,,33,6,,,36,1*48

$GBGSV,6,6,21,10,,,36,1*71

$GBRMC,134521.858,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134521.858,0.000,1595.052,1595.052,51.023,2097152,2097152,2097152*51



2025-07-31 21:45:18:156 ==>> 本次取值间隔时间:468ms
2025-07-31 21:45:18:178 ==>> 【AD_V21电压】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:18:181 ==>> 检测【关闭仪表供电2】
2025-07-31 21:45:18:183 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:45:18:382 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:45:18:466 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:45:18:469 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:45:18:471 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:45:18:765 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!
$GBGGA,134522.558,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,25,,,42,33,,,42,3,,,41,59,,,41,1*4F

$GBGSV,6,2,24,41,,,41,60,,,41,40,,,40,24,,,40,1*75

$GBGSV,6,3,24,39,,,40,7,,,38,16,,,38,34,,,38,1*47

$GBGSV,6,4,24,44,,,38,23,,,37,1,,,37,9,,,37,1*74

$GBGSV,6,5,24,12,,,37,2,,,36,10,,,35,6,,,34,1*75

$GBGSV,6,6,24,5,,,34,4,,,33,38,,,33,13,,,33,1*7F

$GBRMC,134522.558,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134522.558,0.000,1565.051,1565.051,50.066,2097152,2097152,2097152*5F



2025-07-31 21:45:19:023 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:45:19:045 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:45:19:050 ==>> 检测【打开AccKey2供电】
2025-07-31 21:45:19:053 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:45:19:252 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:45:19:414 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:45:19:418 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:45:19:422 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:19:785 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE42F00] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:10][COMM]adc read out 24v adc:1307  volt:33057 mv
[D][05:18:10][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:10][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:10][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:10][COMM]adc read battery ts volt:10 mv
[D][05:18:10][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,134523.538,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,25,,,42,33,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,27,41,,,41,60,,,41,40,,,40,24,,,40,1*77

$GBGSV,7,3,27,39,,,40,7,,,38,16,,,38,34,,,38,1*45

$GBGSV,7,4,27,44,,,38,1,,,38,23,,,37,9,,,37,1*79

$GBGSV,7,5,27,

2025-07-31 21:45:19:830 ==>> 12,,,36,2,,,36,10,,,35,6,,,35,1*77

$GBGSV,7,6,27,5,,,34,4,,,33,38,,,33,13,,,33,1*7D

$GBGSV,7,7,27,43,,,32,8,,,32,32,,,38,1*46

$GBRMC,134523.538,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134523.538,0.000,1548.322,1548.322,49.545,2097152,2097152,2097152*54



2025-07-31 21:45:19:973 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33057mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:45:19:976 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:45:20:001 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:45:20:139 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:45:20:273 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:45:20:277 ==>> 该项需要延时执行
2025-07-31 21:45:20:707 ==>> $GBGGA,134524.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,25,,,42,33,,,42,3,,,41,59,,,41,1*4C

$GBGSV,7,2,26,41,,,41,60,,,40,40,,,40,24,,,40,1*77

$GBGSV,7,3,26,39,,,40,7,,,38,16,,,38,34,,,38,1*44

$GBGSV,7,4,26,44,,,38,1,,,38,23,,,37,9,,,36,1*79

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,10,,,35,1*75

$GBGSV,7,6,26,5,,,34,4,,,33,38,,,33,13,,,33,1*7C

$GBGSV,7,7,26,43,,,32,8,,,32,1*4D

$GBRMC,134524.518,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134524.518,0.000,1546.725,1546.725,49.492,2097152,2097152,2097152*5A



2025-07-31 21:45:21:025 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:45:21:738 ==>> $GBGGA,134525.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,25,,,42,33,,,42,3,,,41,59,,,41,1*4C

$GBGSV,7,2,26,41,,,41,60,,,40,40,,,40,24,,,40,1*77

$GBGSV,7,3,26,39,,,40,7,,,38,16,,,38,34,,,38,1*44

$GBGSV,7,4,26,1,,,38,44,,,37,23,,,37,9,,,36,1*76

$GBGSV,7,5,26,12,,,36,2,,,36,6,,,36,10,,,35,1*75

$GBGSV,7,6,26,13,,,34,5,,,33,4,,,33,38,,,33,1*7C

$GBGSV,7,7,26,43,,,32,8,,,31,1*4E

$GBRMC,134525.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134525.518,0.000,1543.540,1543.540,49.393,2097152,2097152,2097152*5D



2025-07-31 21:45:22:737 ==>> $GBGGA,134526.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,25,,,42,33,,,42,3,,,41,59,,,41,1*42

$GBGSV,7,2,28,41,,,41,60,,,40,40,,,40,24,,,40,1*79

$GBGSV,7,3,28,39,,,40,7,,,39,16,,,38,34,,,38,1*4B

$GBGSV,7,4,28,1,,,38,23,,,38,44,,,37,14,,,37,1*4A

$GBGSV,7,5,28,9,,,36,12,,,36,2,,,36,6,,,36,1*40

$GBGSV,7,6,28,10,,,35,13,,,34,5,,,34,4,,,33,1*79

$GBGSV,7,7,28,38,,,33,43,,,32,11,,,32,8,,,31,1*4A

$GBRMC,134526.518,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134526.518,0.000,1539.896,1539.896,49.278,2097152,2097152,2097152*5A



2025-07-31 21:45:23:037 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:45:23:277 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:45:23:281 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:45:23:285 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:23:588 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:14][COMM]adc read battery ts volt:15 mv
[D][05:18:14][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:45:23:693 ==>>                                                                                                                                                                                                                                                                    ,,,38,44,,,37,14,,,37,1*4A

$GBGSV,7,5,28,9,,,36,12,,,36,2,,,36,6,,,36,1*40

$GBGSV,7,6,28,10,,,35,13,,,34,5,,,33,4,,,33,1*7E

$GBGSV,7,7,28,38,,,

2025-07-31 21:45:23:738 ==>> 33,11,,,33,43,,,32,8,,,31,1*4B

$GBRMC,134527.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134527.518,0.000,1538.412,1538.412,49.228,2097152,2097152,2097152*5E



2025-07-31 21:45:23:808 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:45:23:811 ==>> 检测【打开AccKey1供电】
2025-07-31 21:45:23:814 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:45:23:968 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:45:24:097 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:45:24:101 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:45:24:106 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:45:24:180 ==>> 1A A1 00 40 00 
Get AD_V14 2664mV
OVER 150


2025-07-31 21:45:24:360 ==>> 原始值:【2664】, 乘以分压基数【2】还原值:【5328】
2025-07-31 21:45:24:385 ==>> 【读取AccKey1电压(ADV14)前】通过,【5328mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:45:24:390 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:45:24:410 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:24:748 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,134528.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,25,,,42,3,,,41,59,,,41,1*42

$GBGSV,7,2,28,41,,,41,60,,,40,40,,,40,24,,,40,1*79

$GBGSV,7,3,28,39,,,40,7,,,39,16,,,38,34,,,38,1*4B

$GBGSV,7,4,28,1,,,38,23,,,38,44,,,37,14,,,37,1*4A

$GBGSV,7,5,28,9,,,36,12,,,36,2,,,36,6,,,36,1*40

$GBGSV,7,6,28,10,,,35,13,,,34,5,,,33,4,,,33,1*7E

$GBGSV,7,7,28,38,,,33,11,,,

2025-07-31 21:45:24:792 ==>> 33,43,,,32,8,,,31,1*4B

$GBRMC,134528.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134528.518,0.000,1539.895,1539.895,49.277,2097152,2097152,2097152*5B



2025-07-31 21:45:24:931 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:45:24:934 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:45:24:939 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:45:25:068 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:45:25:217 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:45:25:221 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:45:25:226 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:45:25:282 ==>> 1A A1 00 40 00 
Get AD_V14 2659mV
OVER 150


2025-07-31 21:45:25:478 ==>> 原始值:【2659】, 乘以分压基数【2】还原值:【5318】
2025-07-31 21:45:25:515 ==>> 【读取AccKey1电压(ADV14)后】通过,【5318mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:45:25:519 ==>> 检测【打开WIFI(2)】
2025-07-31 21:45:25:522 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:45:25:779 ==>> $GBGGA,134529.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,25,,,42,3,,,41,59,,,41,1*4C

$GBGSV,8,2,29,41,,,41,60,,,40,40,,,40,24,,,40,1*77

$GBGSV,8,3,29,39,,,40,7,,,39,16,,,38,34,,,38,1*45

$GBGSV,8,4,29,1,,,38,23,,,38,44,,,38,14,,,37,1*4B

$GBGSV,8,5,29,9,,,36,12,,,36,2,,,36,6,,,36,1*4E

$GBGSV,8,6,29,10,,,36,13,,,34,11,,,34,5,,,33,1*40

$GBGSV,8,7,29,4,,,33,38,,,33,42,,,32,43,,,32,1*4C

$GBGSV,8,8,29,8,,,31,1*47

$GBRMC,134529.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134529.518,0.000,1536.834,1536.834,49.181,2097152,2097152,2097152*50

[W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:45:26:064 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:45:26:071 ==>> 检测【转刹把供电】
2025-07-31 21:45:26:077 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:45:26:262 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:45:26:412 ==>> +WIFISCAN:4,0,CC057790A621,-58
+WIFISCAN:4,1,CC057790A620,-58
+WIFISCAN:4,2,CC057790A640,-76
+WIFISCAN:4,3,CC057790A641,-77

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 21:45:26:429 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:45:26:434 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:45:26:438 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:45:26:532 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:45:26:577 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2409mV
OVER 150


2025-07-31 21:45:26:683 ==>> 原始值:【2409】, 乘以分压基数【2】还原值:【4818】
2025-07-31 21:45:26:690 ==>> $GBGGA,134530.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,25,,,42,3,,,41,59,,,41,1*4C

$GBGSV,8,2,29,41,,,41,60,,,40,40,,,40,24,,,40,1*77

$GBGSV,8,3,29,39,,,40,7,,,38,16,,,38,34,,,38,1*44

$GBGSV,8,4,29,1,,,38,23,,,38,44,,,38,14,,,37,1*4B

$GBGSV,8,

2025-07-31 21:45:26:729 ==>> 5,29,9,,,36,12,,,36,2,,,36,6,,,36,1*4E

$GBGSV,8,6,29,10,,,36,13,,,34,11,,,34,4,,,34,1*46

$GBGSV,8,7,29,5,,,33,38,,,33,43,,,32,42,,,31,1*4E

$GBGSV,8,8,29,8,,,31,1*47

$GBRMC,134530.518,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134530.518,0.000,1535.404,1535.404,49.136,2097152,2097152,2097152*54



2025-07-31 21:45:26:772 ==>> 【读取AD_V15电压(前)】通过,【4818mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:45:26:779 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:45:26:793 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:45:26:879 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:45:26:954 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:45:26:984 ==>> 1A A1 01 00 00 
Get AD_V16 2438mV
OVER 150


2025-07-31 21:45:27:044 ==>> 原始值:【2438】, 乘以分压基数【2】还原值:【4876】
2025-07-31 21:45:27:050 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:45:27:077 ==>> 【读取AD_V16电压(前)】通过,【4876mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:45:27:080 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:45:27:085 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:27:405 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE43000] --- write len --- [256]
[D][05:18:17][GNSS]recv submsg id[3]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:17][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:17][COMM]adc read battery ts volt:17 mv
[D][05:18:17][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3100  volt:5449 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:45:27:615 ==>> 【转刹把供电电压(主控ADC)】通过,【5449mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:45:27:619 ==>> 检测【转刹把供电电压】
2025-07-31 21:45:27:624 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:27:904 ==>> $GBGGA,134531.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,25,,,42,3,,,41,59,,,41,1*4C

$GBGSV,8,2,29,41,,,41,60,,,41,40,,,40,24,,,40,1*76

$GBGSV,8,3,29,39,,,40,7,,,38,16,,,38,34,,,38,1*44

$GBGSV,8,4,29,1,,,38,23,,,38,44,,,38,14,,,37,1*4B

$GBGSV,8,5,29,9,,,37,12,,,36,2,,,36,6,,,36,1*4F

$GBGSV,8,6,29,10,,,36,13,,,34,11,,,34,4,,,34,1*46

$GBGSV,8,7,29,5,,,33,38,,,33,43,,,32,42,,,31,1*4E

$GBGSV,8,8,29,8,,,31,1*47

$GBRMC,134531.518,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134531.518,0.000,767.704,767.704,702.084,2097152,2097152,2097152*65

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:18][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:18][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:18][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:18][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3104  volt:5456 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 

2025-07-31 21:45:27:950 ==>> mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:45:28:181 ==>> 【转刹把供电电压】通过,【5456mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:45:28:185 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:45:28:189 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:45:28:360 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:45:28:488 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:45:28:492 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:45:28:498 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:45:28:603 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:45:28:709 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:45:28:769 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150
$GBGGA,134532.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,59,,,42,25,,,42,60,,,41,1*7B

$GBGSV,8,2,29,3,,,41,41,,,41,40,,,40,24,,,40,1*43

$GBGSV,8,3,29,39,,,40,7,,,39,44,,,38,1,,,38,1*74

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,9,,,37,1*46

$GBGSV,8,5,29,14,,,37,2,,,36,10,,,36,12,,,36,1*44

$GBGSV,8,6,29,6,,,36,13,,,34,4,,,34,11,,,34,1*71

$GBGSV,8,7,29,5,,,33,38,,,33,42,,,32,43,,,32,1*4D

$GBGSV,8,8,29,8,,,31,1*47

$GBRMC,134532.518,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134532.518,0.000,770.561,770.561,704.697,2097152,2097152,2097152*64

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:45:28:814 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:45:28:844 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:45:28:889 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:45:28:975 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:45:28:979 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:45:28:985 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:45:29:069 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:45:29:084 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:45:29:174 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:45:29:239 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:45:29:243 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:45:29:246 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:45:29:384 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:45:29:529 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:45:29:533 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:45:29:537 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:45:29:581 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:45:29:686 ==>> $GBGGA,134533.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,25,,,42,60,,,41,3,,,41,1*47

$GBGSV,8,2,29,59,,,41,39,,,41,41,,,41,40,,,40,1*71

$GBGSV,8,3,29,24,,,40,7,,,39,1,,,39,44,,,38,1*79

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,14,,,37,1*7A

$GBGSV,8,5,29,2,,,36,10,,,36,9,,,36,12,,,36,

2025-07-31 21:45:29:731 ==>> 1*79

$GBGSV,8,6,29,6,,,36,13,,,34,4,,,34,11,,,34,1*71

$GBGSV,8,7,29,5,,,33,38,,,33,8,,,32,42,,,32,1*72

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134533.518,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134533.518,0.000,771.272,771.272,705.347,2097152,2097152,2097152*6C



2025-07-31 21:45:29:830 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:45:29:837 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:45:29:843 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:45:29:881 ==>> 3A A3 05 01 A3 


2025-07-31 21:45:29:986 ==>> ON_OUT5
OVER 150


2025-07-31 21:45:30:125 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:45:30:132 ==>> 检测【左刹电压测试1】
2025-07-31 21:45:30:154 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:30:392 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1723  volt:2271 mv
[D][05:18:20][COMM]adc read right brake adc:1727  volt:2276 mv
[D][05:18:20][COMM]adc read throttle adc:1722  volt:2270 mv
[D][05:18:20][COMM]adc read battery ts volt:9 mv
[D][05:18:20][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:45:30:702 ==>> 【左刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 21:45:30:707 ==>> 检测【右刹电压测试1】
2025-07-31 21:45:30:741 ==>> $GBGGA,134534.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,60,,,41,59,,,41,1*79

$GBGSV,8,2,29,41,,,41,3,,,40,40,,,40,24,,,40,1*42

$GBGSV,8,3,29,39,,,40,7,,,38,1,,,38,16,,,38,1*72

$GBGSV,8,4,29,34,,,38,23,,,38,44,,,37,14,,,37,1*72

$GBGSV,8,5,29,2,,,36,10,,,36,9,,,36,12,,,36,1*79

$GBGSV,8,6,29,6,,,36,5,,,34,13,,,34,4,,,34,1*44

$GBGSV,8,7,29,11,,,34,38,,,33,42,,,33,8,,,32,1*41

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134534.518,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134534.518,0.000,768.405,768.405,702.724,2097152,2097152,2097152*6D



2025-07-31 21:45:30:753 ==>> 【右刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 21:45:30:757 ==>> 检测【转把电压测试1】
2025-07-31 21:45:30:796 ==>> 【转把电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 21:45:30:799 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:45:30:803 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:45:30:876 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 21:45:31:058 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:45:31:083 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:45:31:090 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:45:31:106 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:45:31:163 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:45:31:367 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:45:31:370 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:45:31:377 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:45:31:482 ==>> 3A A3 05 00 A3 


2025-07-31 21:45:31:587 ==>> OFF_OUT5
OVER 150


2025-07-31 21:45:31:643 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:45:31:647 ==>> 检测【左刹电压测试2】
2025-07-31 21:45:31:651 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:45:31:692 ==>> $GBGGA,134535.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,3,,,41,59,,,41,1*4C

$GBGSV,8,2,29,41,,,41,60,,,40,40,,,40,24,,,40,1*77

$GBGSV,8,3,29,39,,,40,7,,,38,1,,,38,44,,,38,1*75

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,12,,,37,1*7C

$GBGSV,8,5,29,14,,,37,2,,,36,10,,,36,9,,,36,1*7E

$GBGSV,8,6,29,6,,,36,13,,,34,4,,,34,11,,,34,1*71

$GBGSV,8,

2025-07-31 21:45:31:737 ==>> 7,29,5,,,33,38,,,33,42,,,33,8,,,32,1*73

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134535.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134535.518,0.000,769.120,769.121,703.379,2097152,2097152,2097152*60



2025-07-31 21:45:31:948 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:22][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:22][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:22][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:22][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:22][COMM]adc read battery ts volt:12 mv
[D][05:18:22][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:45:32:189 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 21:45:32:193 ==>> 检测【右刹电压测试2】
2025-07-31 21:45:32:219 ==>> 【右刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 21:45:32:223 ==>> 检测【转把电压测试2】
2025-07-31 21:45:32:249 ==>> 【转把电压测试2】通过,【13】符合目标值【0】至【50】要求!
2025-07-31 21:45:32:253 ==>> 检测【晶振检测】
2025-07-31 21:45:32:259 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:45:32:451 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 21:45:32:544 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:45:32:551 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:45:32:556 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:45:32:753 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1661mV
Get AD_V4 1649mV
Get AD_V5 2763mV
Get AD_V6 1989mV
Get AD_V7 1090mV
OVER 150
$GBGGA,134536.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,41,,,41,40,,,40,24,,,40,1*7C

$GBGSV,8,3,29,39,,,40,7,,,39,1,,,38,16,,,38,1*73

$GBGSV,8,4,29,34,,,38,23,,,38,44,,,37,14,,,37,1*72

$GBGSV,8,5,29,2,,,36,10,,,36,9,,,36,12,,,36,1*79

$GBGSV,8,6,29,6,,,36,13,,,34,4,,,34,11,,,34,1*71

$GBGSV,8,7,29,5,,,33,38,,,33,42,,,33,8,,,32,1*73

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134536.518,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134536.518,0.000,769.123,769.123,703.381,2097152,2097152,2097152*65



2025-07-31 21:45:32:870 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1649mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:45:32:905 ==>> 检测【检测BootVer】
2025-07-31 21:45:32:910 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:45:33:270 ==>> [D][05:18:23][HSDK][0] flush to flash addr:[0xE43100] --- write len --- [256]
[W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071541519
[D][05:18:23][FCTY]HardwareID  = 867222088082104
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = FD2F74BF7DA7
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][COMM]read battery soc:255
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1302
[D][05:18:23][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 177 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]

2025-07-31 21:45:33:316 ==>> CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3770 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:45:33:488 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:45:33:502 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:45:33:508 ==>> 检测【检测固件版本】
2025-07-31 21:45:33:554 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:45:33:558 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:45:33:563 ==>> 检测【检测蓝牙版本】
2025-07-31 21:45:33:612 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:45:33:616 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:45:33:622 ==>> 检测【检测MoBikeId】
2025-07-31 21:45:33:673 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:45:33:679 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:45:33:686 ==>> 检测【检测蓝牙地址】
2025-07-31 21:45:33:715 ==>> 取到目标值:FD2F74BF7DA7
2025-07-31 21:45:33:729 ==>> 【检测蓝牙地址】通过,【FD2F74BF7DA7】符合目标值【】要求!
2025-07-31 21:45:33:737 ==>> 提取到蓝牙地址:FD2F74BF7DA7
2025-07-31 21:45:33:761 ==>> 检测【BOARD_ID】
2025-07-31 21:45:33:765 ==>> $GBGGA,134537.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,41,,,41,40,,,40,24,,,40,1*7C

$GBGSV,8,3,29,39,,,40,7,,,38,1,,,38,44,,,38,1*75

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,14,,,37,1*7A

$GBGSV,8,5,29,2,,,36,10,,,36,9,,,36,12,,,36,1*79

$GBGSV,8,6,29,6,,,36,13,,,34,4,,,34,11,,,34,1*71

$GBGSV,8,7,29,5,,,33,38,,,33,42,,,33,8,,,32,1*73

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134537.518,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134537.518,0.000,769.123,769.123,703.381,2097152,2097152,2097152*64



2025-07-31 21:45:33:789 ==>> 【BOARD_ID】通过,【0xD2】符合目标值【202】至【218】要求!
2025-07-31 21:45:33:796 ==>> 检测【检测充电电压】
2025-07-31 21:45:33:847 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:45:33:852 ==>> 检测【检测VBUS电压1】
2025-07-31 21:45:33:957 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:45:33:961 ==>> 检测【检测充电电流】
2025-07-31 21:45:34:067 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:45:34:071 ==>> 检测【检测IMEI】
2025-07-31 21:45:34:074 ==>> 取到目标值:867222088082104
2025-07-31 21:45:34:169 ==>> 【检测IMEI】通过,【867222088082104】符合目标值【】要求!
2025-07-31 21:45:34:173 ==>> 提取到IMEI:867222088082104
2025-07-31 21:45:34:181 ==>> 检测【检测IMSI】
2025-07-31 21:45:34:200 ==>> 取到目标值:460130071541519
2025-07-31 21:45:34:214 ==>> 【检测IMSI】通过,【460130071541519】符合目标值【】要求!
2025-07-31 21:45:34:218 ==>> 提取到IMSI:460130071541519
2025-07-31 21:45:34:222 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:45:34:228 ==>> 取到目标值:460130071541519
2025-07-31 21:45:34:254 ==>> 【校验网络运营商(移动)】通过,【460130071541519】符合目标值【】要求!
2025-07-31 21:45:34:258 ==>> 检测【打开CAN通信】
2025-07-31 21:45:34:264 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:45:34:380 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:45:34:549 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:45:34:556 ==>> 检测【检测CAN通信】
2025-07-31 21:45:34:561 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:45:34:702 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:45:34:808 ==>> $GBGGA,134538.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,41,,,41,40,,,40,24,,,40,1*7C

$GBGSV,8,3,29,39,,,40,7,,,38,1,,,38,44,,,38,1*75

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,9,,,37,1*46

$GBGSV,8,5,29,14,,,37,2,,,36,10,,,36,12,,,36,1*44

$GBGSV,8,6,29,6,,,36,5,,,34,13,,,34,4,,,34,1*44

$GBGSV,8,7,29,11,,,34,38,,,33,42,,,33,8,,,32,1*41

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134538.518,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134538.518,0.000,770.547,770.547,704.683,2097152,2097152,2097152*6B

[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36307
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:45:34:837 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:45:34:857 ==>> 检测【关闭CAN通信】
2025-07-31 21:45:34:872 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:45:34:899 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:45:34:942 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:45:34:988 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:45:35:078 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:45:35:123 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:45:35:127 ==>> 检测【打印IMU STATE】
2025-07-31 21:45:35:132 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:45:35:274 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:45:35:406 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:45:35:411 ==>> 检测【六轴自检】
2025-07-31 21:45:35:415 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:45:35:597 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:45:35:702 ==>>                                                                                                       ,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,41,,,41,40,,,40,24,,,40,1*7C

$GBGSV,8,3,29,39,,,40,7,,,38,1,,,38,44,,,38,1*75

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,9,,,3

2025-07-31 21:45:35:762 ==>> 7,1*46

$GBGSV,8,5,29,12,,,37,14,,,37,2,,,36,10,,,36,1*45

$GBGSV,8,6,29,6,,,36,5,,,34,13,,,34,4,,,34,1*44

$GBGSV,8,7,29,11,,,34,38,,,33,42,,,33,8,,,32,1*41

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134539.518,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134539.518,0.000,771.260,771.260,705.335,2097152,2097152,2097152*63



2025-07-31 21:45:36:756 ==>> $GBGGA,134540.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,39,,,41,41,,,41,40,,,40,1*71

$GBGSV,8,3,29,24,,,40,7,,,39,1,,,38,44,,,38,1*78

$GBGSV,8,4,29,16,,,38,34,,,38,23,,,38,9,,,37,1*46

$GBGSV,8,5,29,12,,,37,14,,,37,2,,,36,10,,,36,1*45

$GBGSV,8,6,29,6,,,36,5,,,34,13,,,34,4,,,34,1*44

$GBGSV,8,7,29,11,,,34,38,,,33,42,,,33,43,,,32,1*7E

$GBGSV,8,8,29,8,,,31,1*47

$GBRMC,134540.518,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134540.518,0.000,771.979,771.979,705.993,2097152,2097152,2097152*6B



2025-07-31 21:45:37:101 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:45:37:282 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:45:37:447 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38961 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-13,-6,4039]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 21:45:37:503 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:45:37:512 ==>> 检测【打印IMU STATE2】
2025-07-31 21:45:37:529 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:45:37:777 ==>> $GBGGA,134541.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,30,33,,,43,25,,,42,60,,,41,3,,,41,1*4F

$GBGSV,8,2,30,40,,,41,59,,,41,39,,,41,41,,,41,1*78

$GBGSV,8,3,30,24,,,40,7,,,39,34,,,39,1,,,38,1*76

$GBGSV,8,4,30,44,,,38,16,,,38,14,,,38,23,,,38,1*7A

$GBGSV,8,5,30,9,,,37,12,,,37,2,,,36,10,,,36,1*71

$GBGSV,8,6,30,6,,,36,5,,,34,13,,,34,4,,,34,1*4C

$GBGSV,8,7,30,11,,,34,38,,,33,42,,,33,8,,,32,1*49

$GBGSV,8,8,30,43,,,32,21,,,36,1*75

$GBRMC,134541.518,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134541.518,0.000,775.548,775.548,709.257,2097152,2097152,2097152*65

[W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:45:38:135 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:45:38:141 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:45:38:149 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:45:38:279 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:45:38:384 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:28][F

2025-07-31 21:45:38:445 ==>> CTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:45:38:710 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:45:38:719 ==>> 检测【检测VBUS电压2】
2025-07-31 21:45:38:746 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:45:38:759 ==>> $GBGGA,134542.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,25,,,42,60,,,41,3,,,41,1*47

$GBGSV,8,2,29,59,,,41,41,,,41,40,,,40,24,,,40,1*7C

$GBGSV,8,3,29,39,,,40,7,,,39,34,,,39,1,,,38,1*72

$GBGSV,8,4,29,44,,,38,16,,,38,14,,,38,23,,,38,1*72

$GBGSV,8,5,29,9,,,37,12,,,37,2,,,36,10,,,36,1*79

$GBGSV,8,6,29,6,,,36,5,,,34,13,,,34,4,,,34,1*44

$GBGSV,8,7,29,11,,,34,38,,,33,42,,,33,8,,,32,1*41

$GBGSV,8,8,29,43,,,32,1*7B

$GBRMC,134542.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134542.518,0.000,774.118,774.118,707.949,2097152,2097152,2097152*6C

[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:45:39:044 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071541519
[D][05:18:29][FCTY]HardwareID  = 867222088082104
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = FD2F74BF7DA7
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:18:29][FCTY]Ext battery vol = 6, adc = 245
[D][05:18:29][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VER

2025-07-31 21:45:39:089 ==>> SION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3770 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:45:39:297 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:45:39:650 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071541519
[D][05:18:30][FCTY]HardwareID  = 867222088082104
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = FD2F74BF7DA7
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 50 ma
[D][05:18:30][FCTY]VBUS        = 11800 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 164
[D][05:18:30][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3770 mv
[D][05

2025-07-31 21:45:39:680 ==>> :18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:45:39:785 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           0601 loss. last_tick:36301. cur_tick:41304. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C7

2025-07-31 21:45:39:816 ==>> 1E2223F->0x0008F80C71E2223F 41304


2025-07-31 21:45:39:889 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:45:40:239 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071541519
[D][05:18:30][FCTY]HardwareID  = 867222088082104
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = FD2F74BF7DA7
[D][05:18:30][FCTY]Bat         = 3684 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5300 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 139
[D][05:18:30][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 25 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY

2025-07-31 21:45:40:284 ==>> ]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3770 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:45:40:469 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:45:40:862 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  MM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_t

2025-07-31 21:45:40:968 ==>> imes:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][C

2025-07-31 21:45:41:073 ==>> AT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B559FDCB9425BB1063F4AA72D0DDB93751218F8AFEAD2194153CCE15C3C6315FE6BCFBD69F9D9370D2C3FE06641C03F0A67EE5E5C3FA63C7219E4137F093EC61055EDCAAE09E7420D0753E3957595029585FA8
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, r

2025-07-31 21:45:41:178 ==>> et: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]
[D][05:18:31][HSDK][0] flush to flash addr:[0xE43200] --- write len --- [256]
[W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071541519
[D][05:18:31][FCTY]HardwareID  = 867222088082104
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = FD2F74BF7DA7
[D][05:18:31][FCTY]Bat         = 3384 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0

2025-07-31 21:45:41:253 ==>> ,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 115
[D][05:18:31][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3770 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:45:41:358 ==>> [D][05:18:31][GNSS]recv submsg id[1]
[D][05:18:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:31][GNSS]location stop evt done evt


2025-07-31 21:45:41:539 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:45:41:548 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:45:41:574 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:45:41:677 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:45:41:752 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 21:45:41:797 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 21:45:41:835 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:45:41:840 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:45:41:844 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:45:41:887 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:45:42:134 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:45:42:140 ==>> 检测【打开WIFI(3)】
2025-07-31 21:45:42:160 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:45:42:281 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:45:42:419 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:45:42:450 ==>> 检测【扩展芯片hw】
2025-07-31 21:45:42:455 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:45:43:350 ==>> [D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:45:43:485 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:45:43:905 ==>>                                                                             [COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]more than the number of battery plugs
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:33][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:33][COMM]Bat auth off fail, error:-1
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec800m_audio_play_process

2025-07-31 21:45:44:010 ==>> ].l:[920].cmd file 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get 

2025-07-31 21:45:44:115 ==>> soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]seque

2025-07-31 21:45:44:221 ==>> nce_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]read battery soc:255


2025-07-31 21:45:44:325 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:34][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:34][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:34][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw

2025-07-31 21:45:44:355 ==>> :[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:45:44:535 ==>> [D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46042]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:35][COMM]46056 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:44:564 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:45:44:569 ==>> 检测【扩展芯片boot】
2025-07-31 21:45:44:616 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:45:44:621 ==>> 检测【扩展芯片sw】
2025-07-31 21:45:44:675 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:45:44:680 ==>> 检测【检测音频FLASH】
2025-07-31 21:45:44:690 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:45:44:869 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:45:45:266 ==>> [D][05:18:35][CAT1]SEND RAW data timeout
[D][05:18:35][CAT1]exec over: func id: 12, ret: -52


2025-07-31 21:45:45:551 ==>> [D][05:18:36][COMM]f:[drv_audio_ack_receive].wait ack timeout!![47068]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:36][COMM]47070 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:45:851 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 21:45:45:881 ==>>                                          

2025-07-31 21:45:46:561 ==>> [D][05:18:37][COMM]48081 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][COMM]f:[drv_audio_ack_receive].wait ack timeout!![48096]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:45:47:551 ==>> [D][05:18:38][COMM]49093 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:47:840 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 21:45:48:576 ==>> [D][05:18:39][COMM]50105 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:48:790 ==>> [D][05:18:39][COMM]crc 108B
[D][05:18:39][COMM]flash test ok


2025-07-31 21:45:49:564 ==>> [D][05:18:40][COMM]51117 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:49:746 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:45:49:756 ==>> 检测【打开喇叭声音】
2025-07-31 21:45:49:766 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:45:49:849 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 21:45:49:954 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:40][COMM]file:A20 exist
[D][05:18:40][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:40][COMM]f:[fr

2025-07-31 21:45:49:984 ==>> m_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:45:50:032 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:45:50:041 ==>> 检测【打开大灯控制】
2025-07-31 21:45:50:063 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:45:50:149 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:45:50:317 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:45:50:325 ==>> 检测【关闭仪表供电3】
2025-07-31 21:45:50:331 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:45:50:465 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:41][COMM]set POWER 0


2025-07-31 21:45:50:569 ==>> [D][05:18:41][COMM]52130 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:50:612 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:45:50:618 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:45:50:632 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:45:50:750 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:45:50:908 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:45:50:914 ==>> 检测【读大灯电压】
2025-07-31 21:45:50:930 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:45:51:069 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[33178]


2025-07-31 21:45:51:192 ==>> 【读大灯电压】通过,【33178mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:45:51:202 ==>> 检测【关闭大灯控制2】
2025-07-31 21:45:51:225 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:45:51:343 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:45:51:541 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:45:51:548 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:45:51:557 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:45:51:583 ==>> [D][05:18:42][COMM]53141 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:51:673 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:42][COMM]arm_hub read adc[5],val[69]


2025-07-31 21:45:51:840 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 21:45:51:850 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:45:51:873 ==>> 检测【打开WIFI(4)】
2025-07-31 21:45:51:878 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:45:52:051 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:45:52:216 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:45:52:222 ==>> 检测【EC800M模组版本】
2025-07-31 21:45:52:230 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:52:614 ==>> [D][05:18:43][COMM]54153 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:53:250 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:53:625 ==>> [D][05:18:44][COMM]55165 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:45:53:869 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 21:45:54:099 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:45:54:282 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:54:600 ==>> [D][05:18:45][COMM]imu error,enter wait


2025-07-31 21:45:55:315 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:55:987 ==>> [D][05:18:46][CAT1]exec over: func id: 15, ret: -93
[D][05:18:46][CAT1]sub id: 15, ret: -93

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:46][SAL ]socket send fail. id[4]
[D][05:18:46][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:46][M2M ]m2m select fd[4]
[D][05:18:46][M2M ]socket[4] Link is disconnected
[D][05:18:46][M2M ]tcpclient close[4]
[D][05:18:46][SAL ]socket[4] has closed
[D][05:18:46][PROT]protocol read data ok
[E][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:46][PROT]M2M Send Fail [1629955126]
[D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57359]
[D][05:18:46][COMM]accel parse set 0
[D][05:18:46][COMM][Audio].l:[1032].open hexlog save
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:46][COMM]file:A20 exist
[D]

2025-07-31 21:45:56:077 ==>> [05:18:46][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:46][COMM]read file, len:15228, num:4
[D][05:18:46][COMM]read battery soc:255
[D][05:18:46][COMM]--->crc16:0x419c
[D][05:18:46][COMM]read file success
[W][05:18:46][COMM][Audio].l:[936].close hexlog save
[D][05:18:46][COMM]accel parse set 1
[D][05:18:46][COMM][Audio]mon:9,05:18:46
[D][05:18:46][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:45:56:152 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:45:56:348 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:56:945 ==>> [D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58465]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:45:57:391 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:57:869 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 21:45:57:959 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59495]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:45:58:202 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:45:58:427 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:58:757 ==>> [D][05:18:49][CAT1]SEND RAW data timeout
[D][05:18:49][CAT1]exec over: func id: 12, ret: -52
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:49][CAT1]gsm read msg sub id: 12
[D][05:18:49][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:45:58:998 ==>> [D][05:18:49][COMM]f:[drv_audio_ack_receive].wait ack timeout!![60524]
[D][05:18:49][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:49][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:45:59:103 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:45:59:452 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:45:59:886 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 21:46:00:487 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:00:783 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:01:531 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:01:608 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:01:788 ==>> [D][05:18:52][CAT1]SEND RAW data timeout
[D][05:18:52][CAT1]exec over: func id: 12, ret: -52
[W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 10
[D][05:18:52][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:46:01:878 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 21:46:02:567 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:03:614 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:03:800 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:03:890 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 21:46:04:103 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:04:650 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:05:694 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:05:846 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:05:906 ==>>                                      55


2025-07-31 21:46:06:610 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:06:730 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:07:764 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:07:904 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:58][COMM]read battery soc:255


2025-07-31 21:46:08:811 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:09:101 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:09:782 ==>> [D][05:19:00][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:46:09:842 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:09:948 ==>> [D][05:19:00][COMM]read battery soc:255
[W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:10:888 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:11:337 ==>> [D][05:19:01][COMM]f:[drv_audio_ack_receive].wait ack timeout!![72865]
[D][05:19:01][COMM]accel parse set 0
[D][05:19:01][COMM][Audio].l:[1032].open hexlog save
[D][05:19:01][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:46:11:608 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:11:922 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 21:46:11:934 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:12:012 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:12:953 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:13:926 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 21:46:13:986 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:14:031 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:14:106 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:15:022 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:15:927 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 21:46:16:062 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:16:092 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:16:610 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:17:104 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:17:784 ==>> [D][05:19:08][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:46:17:950 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 21:46:18:145 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:18:153 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:19:111 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:19:186 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:19:946 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 21:46:20:188 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:20:233 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:21:282 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:21:606 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:21:943 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 21:46:22:249 ==>> [D][05:19:12][HSDK][0] flush to flash addr:[0xE43300] --- write len --- [256]
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:22:324 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:46:23:361 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:46:23:372 ==>> #################### 【测试结束】 ####################
2025-07-31 21:46:23:692 ==>> 关闭5V供电
2025-07-31 21:46:23:700 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:46:23:784 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:46:23:950 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 21:46:24:100 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:46:24:310 ==>> [W][05:19:14][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:46:24:707 ==>> 关闭5V供电成功
2025-07-31 21:46:24:717 ==>> 关闭33V供电
2025-07-31 21:46:24:740 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:46:24:782 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:46:24:977 ==>> [D][05:19:15][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:15][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 21:46:25:720 ==>> 关闭33V供电成功
2025-07-31 21:46:25:729 ==>> 关闭3.7V供电
2025-07-31 21:46:25:741 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:46:25:780 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:46:25:885 ==>> [D][05:19:16][CAT1]exec over: func id: 10, ret: -43
[D][05:19:16][CAT1]sub id: 10, ret: -43

[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
[D][05:19:16][M2M ]m2m gsm shut done, ret[1]
[D][05:19:16][CAT1]gsm read msg sub id: 12
[D][05:19:16][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
[D][05:19:16][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
[D][05:19:16][M2M ]g_m2m_is_idle become true


2025-07-31 21:46:26:129 ==>>  

2025-07-31 21:46:26:734 ==>> 关闭3.7V供电成功
